# This is a basic workflow to help you get started with Actions
name: Tools build

env:
  JIRA_API_URL: https://graphsql.atlassian.net/
  BRANCH_NAME: ${{ github.base_ref || github.ref_name }}

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

on:
  push:
    branches:
      - "main"
      - "tools_*"

  pull_request:
    types: [opened, synchronize, edited, ready_for_review]
    branches:
      - "main"
      - "tools_*"

jobs:
  pre-check:
    name: Check PR Title
    runs-on: ubuntu-latest
    outputs:
      error_message: ${{ env.ERROR_MESSAGE }}
    steps:
      - name: Validate PR Title
        env:
          PR_TITLE: ${{ github.event.pull_request.title }}
          PR_TITLE_REGEX_PATTERN: ${{ vars.PR_TITLE_REGEX_PATTERN }}
        if: github.event_name == 'pull_request' && github.event.pull_request
        run: |
          echo "*******************************************************************"
          echo "base_branch:${{ github.base_ref }}"
          echo "pr_title:$PR_TITLE"
          echo "*******************************************************************"
          base_branch="${{ github.base_ref }}"
          dev_subfix="_dev"
          base_branch=${base_branch//$dev_subfix/}
          matches=$(echo "$PR_TITLE" | grep -oP "$PR_TITLE_REGEX_PATTERN" || true)

          version_info=$(curl -s -u "${{ secrets.JIRA_USERNAME }}:${{ secrets.JIRA_API_TOKEN }}" -H "Content-Type: application/json" "${JIRA_API_URL}rest/api/3/project/APPS/versions?orderBy=-releaseDate")
          next_release_version=$(echo $version_info | jq -r '.[] | select(.released == false and .archived == false) | .name' | head -n 1)
          echo "Next Cloud Release Version: $next_release_version"

          comparison_url="https://api.github.com/repos/${{ github.repository }}/compare/${{ github.head_ref }}...${{ github.base_ref }}"
          comparison_info=$(curl -s -H "Authorization: Bearer ${{ secrets.GITHUB_TOKEN }}" "$comparison_url")
          ahead_by=$(echo "$comparison_info" | jq -r '.ahead_by')

          if [ "$ahead_by" -gt 0 ]; then
            echo "Base branch is ahead of the current branch: $ahead_by"
            errorMessage+="Base branch is ahead of the current branch. Please rebase your branch!\n"
          else
            echo "Base branch is not ahead of the current branch."
          fi

          if [[ -z $matches ]]; then
            errorMessage+="Invalid PR title format. Please follow the correct format: \"([)ticket-number(]) type(scope): description;\" \nExample: TOOLS-1234 feat(login): add forgot password link;\n\n"
            echo "Failed:$errorMessage"
          else
              echo "Matching strings: $matches"
              IFS=$'\n'
              for match in $matches; do
                ticket=$(echo "$match" | grep -m 1 -oP "[a-zA-Z]+-\d+" | awk 'NR==1{print $1}')
                echo "Matching: $match"
                echo "JIRA Ticket Number: $ticket"
                api_url="${JIRA_API_URL}rest/api/latest/issue/${ticket}"
                issue_info=$(curl -s -u "${{ secrets.JIRA_USERNAME }}:${{ secrets.JIRA_API_TOKEN }}" -H "Content-Type: application/json" "$api_url")
                labels=$(echo "$issue_info" | jq -r '.fields.labels[]')
                fixVersions=$(echo "$issue_info" | jq -r '.fields.fixVersions[].name')
                number_fix_versions=$(echo "$issue_info" | jq -r '.fields.fixVersions | length')
                echo "Labels: $labels"
                echo "Fix Versions: $fixVersions"
                echo "Base Branch: $base_branch"
                echo "Number of fix versions: $number_fix_versions"
                merged_banch_label="merged_"$base_branch

                if [[ "$labels" == *"merged_$base_branch"* ]]; then
                  echo "This PR has already been merged to the base branch. If you want to merge it again, please remove the \"merged_$base_branch\" label from the corresponding JIRA ticket $ticket"
                  errorMessage+="This PR has already been merged to the base branch. If you want to merge it again, please remove the \"merged_$base_branch\" label from the corresponding JIRA ticket [$ticket](https://graphsql.atlassian.net/browse/$ticket).\n"
                elif [[ "$labels" != *"$base_branch"* ]]; then
                  echo "Missing label for JIRA ticket $ticket:$base_branch. Please add the required label."
                  errorMessage+="Missing label for JIRA ticket [$ticket](${JIRA_API_URL}browse/$ticket): \"${base_branch}\". Please add the required label.\n"
                elif [[ "$base_branch" == main* ]]; then
                  if [ "${number_fix_versions}" -eq 0 ]; then
                    echo "No fix versions are assigned to the ticket"
                    errorMessage+="No fix versions are assigned to the JIRA ticket [$ticket](https://graphsql.atlassian.net/browse/$ticket).\n"
                  else
                    for fixVersion in $fixVersions; do
                      IS_RELEASED=$(echo "$issue_info" | jq -r ".fields.fixVersions[] | select(.name == \"$fixVersion\") | .released")
                      if [ "${IS_RELEASED}" = "true" ]; then
                        echo "${fixVersion} - Released"
                        errorMessage+="Version $fixVersion assigned to the JIRA ticket [$ticket](https://graphsql.atlassian.net/browse/$ticket) has already been released. Please remove the fix version to continue.\n"
                      else
                        echo "${fixVersion} - Not Released"
                      fi
                    done
                  fi
                else
                  echo "Ticket $ticket meets requirements"
                fi
              done
          fi

          if [ -n "$errorMessage" ]; then
            echo "ERROR_MESSAGE=$errorMessage" >> $GITHUB_ENV
            exit 1
          else
            echo "PR validation is done";
            if [ "${{ github.event.pull_request.rebase }}" == "true" ]; then
              echo "PR is a rebase!"
            else
              for match in $matches; do
                JIRA_TICKET=$(echo "$match" | grep -m 1 -oP "[a-zA-Z]+-\d+" | awk 'NR==1{print $1}')
                echo "JIRA_TICKET:$JIRA_TICKET"
                NEW_STATUS="PULL REQUEST"
                REST_API_URL="${JIRA_API_URL}/rest/api/2/issue/${JIRA_TICKET}/transitions"
                TICKET_INFO=$(curl -s -u "${{ secrets.JIRA_USERNAME }}:${{ secrets.JIRA_API_TOKEN }}" -H "Content-Type: application/json" "$api_url")
                CURRENT_STATUS=$(echo "$TICKET_INFO" | jq -r '.fields.status.name')
                if [ "$CURRENT_STATUS" != "Test Done" ]; then
                  TRANSITION_ID=$(curl -s -u "${{ secrets.JIRA_USERNAME }}:${{ secrets.JIRA_API_TOKEN }}" -H "Content-Type: application/json" "${REST_API_URL}" | jq --arg NEW_STATUS "${NEW_STATUS}" -r '.transitions[] | select(.to.name==$NEW_STATUS) | .id')
                  echo "TRANSITION_ID:$TRANSITION_ID"
                  curl -s -u "${{ secrets.JIRA_USERNAME }}:${{ secrets.JIRA_API_TOKEN }}" -H "Content-Type: application/json" --request POST --data "{\"transition\": {\"id\": \"${TRANSITION_ID}\"}}" "${REST_API_URL}"
                else
                  echo "We do nothing because the status for $JIRA_TICKET is Test Done"
                fi
              done
            fi
          fi

      - name: Comment on PR
        if: failure()
        uses: actions/github-script@v3
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            github.pulls.createReview({
              owner: context.repo.owner,
              repo: context.repo.repo,
              pull_number: context.payload.pull_request.number,
              event: 'COMMENT',
              body: '${{env.ERROR_MESSAGE}}'
            });

  buildnumber:
    name: Generate build number
    runs-on: ubuntu-latest
    outputs:
      build_number: ${{ env.BUILD_NUMBER }}
      hash_number: ${{ env.SHA }}
      version: ${{ env.VERSION }}
      slack_user: ${{ env.SLACK_USER }}
    env:
      USER_MAPPING: ${{ vars.GH_SLACK_USER_MAPPING }}
    steps:
      - name: Get Slack User
        id: get-slack-user-id
        run: |
          echo "ACTOR=${{ github.actor }}" >> $GITHUB_ENV
          slack_user=$(echo ${USER_MAPPING} | jq -r '. | ."${{ github.actor }}"')
          echo "SLACK_USER=$slack_user" >> $GITHUB_ENV

      - name: Get commit SHA
        id: get-commit-step
        run: |
          sha=${{ github.sha }}
          if [ "${{ github.event_name }}" == "pull_request" ]; then sha=${{ github.event.pull_request.head.sha }}; fi
          echo "SHA=$sha" >> $GITHUB_ENV

      - name: Generate build number
        id: get-buildnumber-step
        uses: onyxmueller/build-tag-number@v1
        with:
          token: ${{secrets.github_token}}
          prefix: main

      - name: Set build number to env
        run: |
          echo "BUILD_NUMBER=${{ steps.get-buildnumber-step.outputs.build_number }}" >> $GITHUB_ENV

      - name: Setup version
        run: |
          branch=${{ github.ref }}
          if [ "${{ github.event_name }}" == "pull_request" ]; then branch=${{ github.base_ref }}; fi
          version=`echo $branch | grep -oE "[[:digit:]]{1,3}\.[[:digit:]]{1,3}" | tr '.' '_' | head -1 `
          if [ -z "$version" ]; then version=${{ vars.DEFAULT_VERSION }}; fi
          echo "VERSION=$version"
          echo "VERSION=3_9" >> $GITHUB_ENV

  libs-models-test:
    name: Libs Models Test
    runs-on: ubuntu-latest
    outputs:
      statements_pct: ${{ env.statements_pct }}
      functions_pct: ${{ env.functions_pct }}
      branches_pct: ${{ env.branches_pct }}
      lines_pct: ${{ env.lines_pct }}
    needs: [buildnumber]
    steps:
      - uses: actions/checkout@v3

      - name: Setup Node 20
        uses: actions/setup-node@v3
        with:
          node-version: 20

      - name: Cache Node modules
        uses: actions/cache@v3
        env:
          cache-name: libs/models-node-modules
        with:
          path: ${{ github.workspace }}/libs/models/node_modules
          key: ${{ runner.os }}-${{ env.cache-name }}-${{ hashFiles('libs/models/package.json') }}

      - name: Set up Node modules
        run: yarn --ignore-engines
        working-directory: libs/models

      - name: Setup test
        run: source ${{ github.workspace }}/libs/models/spec/support/setup.sh

      - name: Run test
        run: yarn test
        working-directory: libs/models

      - name: End test
        run: source ${{ github.workspace }}/libs/models/spec/support/end.sh

      - name: Get coverage percentage
        working-directory: libs/models
        run: |
          statements_pct=`cat coverage/coverage-summary.json | jq -r '.total.statements.pct'`
          echo "statements_pct=$statements_pct" >> $GITHUB_ENV
          lines_pct=`cat coverage/coverage-summary.json | jq -r '.total.lines.pct'`
          echo "lines_pct=$lines_pct" >> $GITHUB_ENV
          functions_pct=`cat coverage/coverage-summary.json | jq -r '.total.functions.pct'`
          echo "functions_pct=$functions_pct" >> $GITHUB_ENV
          branches_pct=`cat coverage/coverage-summary.json | jq -r '.total.branches.pct'`
          echo "branches_pct=$branches_pct" >> $GITHUB_ENV

      - name: Generating coverage badges
        uses: jpb06/jest-badges-action@latest
        with:
          no-commit: true
          coverage-summary-path: ./libs/models/coverage/coverage-summary.json
          output-folder: ./libs/models/badges

      - name: Upload coverage badges
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.APP_TOOLS_AWS_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.APP_TOOLS_AWS_KEY }}
          AWS_REGION: "us-west-1"
        run: |
          aws s3 sync libs/models/badges s3://${{ vars.AWS_TEST_RESULT_S3_BUCKET }}/models-badges/${{ github.run_id }}-${{ github.run_attempt }} --follow-symlinks --delete
          echo "### Models Test Coverage:" >> $GITHUB_STEP_SUMMARY
          echo "![Statements](http://${{ vars.AWS_TEST_RESULT_S3_BUCKET }}.s3-website-us-west-1.amazonaws.com/models-badges/${{ github.run_id }}-${{ github.run_attempt }}/coverage-statements.svg)" >> $GITHUB_STEP_SUMMARY
          echo "![Branches](http://${{ vars.AWS_TEST_RESULT_S3_BUCKET }}.s3-website-us-west-1.amazonaws.com/models-badges/${{ github.run_id }}-${{ github.run_attempt }}/coverage-branches.svg)" >> $GITHUB_STEP_SUMMARY
          echo "![Lines](http://${{ vars.AWS_TEST_RESULT_S3_BUCKET }}.s3-website-us-west-1.amazonaws.com/models-badges/${{ github.run_id }}-${{ github.run_attempt }}/coverage-lines.svg)" >> $GITHUB_STEP_SUMMARY
          echo "![Functions](http://${{ vars.AWS_TEST_RESULT_S3_BUCKET }}.s3-website-us-west-1.amazonaws.com/models-badges/${{ github.run_id }}-${{ github.run_attempt }}/coverage-functions.svg)" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY

      - name: Generate third-party license report
        run: |
          yarn global add license-checker
          yarn licenses list
        working-directory: libs/models

  # we need to build libs/ui and libs/models together(if separate, we will have cache issue)
  libs-build:
    name: Libs Build
    runs-on: ubuntu-latest
    outputs:
      statements_pct: ${{ env.statements_pct }}
      functions_pct: ${{ env.functions_pct }}
      branches_pct: ${{ env.branches_pct }}
      lines_pct: ${{ env.lines_pct }}
    needs: [buildnumber, libs-models-test]
    steps:
      - uses: actions/checkout@v3

      - name: Setup Node 20
        uses: actions/setup-node@v3
        with:
          node-version: 20

      - name: Cache libs/models Node modules
        uses: actions/cache@v3
        env:
          cache-name: libs/models-node-modules
        with:
          path: ${{ github.workspace }}/libs/models/node_modules
          key: ${{ runner.os }}-${{ env.cache-name }}-${{ hashFiles('libs/models/package.json') }}

      - name: build libs/models
        run: |
          yarn --ignore-engines
          yarn build
        working-directory: libs/models

      - name: Cache libs/ui Node modules
        uses: actions/cache@v3
        env:
          cache-name: libs/ui-node-modules
        with:
          path: ${{ github.workspace }}/libs/ui/node_modules
          key: ${{ runner.os }}-${{ env.cache-name }}-${{ hashFiles('libs/ui/package.json') }}

      - name: build libs/ui
        run: |
          yarn --ignore-engines
          yarn workspace @tigergraph/tools-ui build

      - name: Run libs/ui test
        run: yarn workspace @tigergraph/tools-ui test

      - name: Upload libs/ui coverage
        uses: actions/upload-artifact@v4
        with:
          name: libs-ui-coverage
          path: libs/ui/coverage/

      - name: Upload libs/ui build artifact
        uses: actions/upload-artifact@v4
        with:
          name: libs-ui-build
          path: libs/ui/dist/

      - name: Get libs/ui coverage percentage
        working-directory: libs/ui
        run: |
          statements_pct=`cat coverage/coverage-report.json | jq -r '.summary.statements.pct'`
          echo "statements_pct=$statements_pct" >> $GITHUB_ENV
          lines_pct=`cat coverage/coverage-report.json | jq -r '.summary.lines.pct'`
          echo "lines_pct=$lines_pct" >> $GITHUB_ENV
          functions_pct=`cat coverage/coverage-report.json | jq -r '.summary.functions.pct'`
          echo "functions_pct=$functions_pct" >> $GITHUB_ENV
          branches_pct=`cat coverage/coverage-report.json | jq -r '.summary.branches.pct'`
          echo "branches_pct=$branches_pct" >> $GITHUB_ENV

      - name: Generate line-coverage badges
        uses: ./.github/actions/gen_coverage_badge
        if: always()
        with:
          coverage: ${{ env.lines_pct }}
          path: libs/ui/coverage/badges/coverage-lines.svg
          label: Coverage(Lines)

      - name: Generate statements-coverage badges
        uses: ./.github/actions/gen_coverage_badge
        if: always()
        with:
          coverage: ${{ env.statements_pct }}
          path: libs/ui/coverage/badges/coverage-statements.svg
          label: Coverage(Statements)

      - name: Generate functions-coverage badges
        uses: ./.github/actions/gen_coverage_badge
        if: always()
        with:
          coverage: ${{ env.functions_pct }}
          path: libs/ui/coverage/badges/coverage-functions.svg
          label: Coverage(Functions)

      - name: Generate branches-coverage badges
        uses: ./.github/actions/gen_coverage_badge
        if: always()
        with:
          coverage: ${{ env.branches_pct }}
          path: libs/ui/coverage/badges/coverage-branches.svg
          label: Coverage(Branches)

      - name: Upload libs/ui coverage badges
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.APP_TOOLS_AWS_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.APP_TOOLS_AWS_KEY }}
          AWS_REGION: "us-west-1"
        run: |
          aws s3 sync libs/ui/coverage/badges s3://${{ vars.AWS_TEST_RESULT_S3_BUCKET }}/ui-badges/${{ github.run_id }}-${{ github.run_attempt }} --follow-symlinks --delete
          echo "### ui Test Coverage:" >> $GITHUB_STEP_SUMMARY
          echo "![Statements](http://${{ vars.AWS_TEST_RESULT_S3_BUCKET }}.s3-website-us-west-1.amazonaws.com/ui-badges/${{ github.run_id }}-${{ github.run_attempt }}/coverage-statements.svg)" >> $GITHUB_STEP_SUMMARY
          echo "![Branches](http://${{ vars.AWS_TEST_RESULT_S3_BUCKET }}.s3-website-us-west-1.amazonaws.com/ui-badges/${{ github.run_id }}-${{ github.run_attempt }}/coverage-branches.svg)" >> $GITHUB_STEP_SUMMARY
          echo "![Lines](http://${{ vars.AWS_TEST_RESULT_S3_BUCKET }}.s3-website-us-west-1.amazonaws.com/ui-badges/${{ github.run_id }}-${{ github.run_attempt }}/coverage-lines.svg)" >> $GITHUB_STEP_SUMMARY
          echo "![Functions](http://${{ vars.AWS_TEST_RESULT_S3_BUCKET }}.s3-website-us-west-1.amazonaws.com/ui-badges/${{ github.run_id }}-${{ github.run_attempt }}/coverage-functions.svg)" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY

      - name: Generate third-party license report
        run: |
          yarn global add license-checker
          yarn licenses list
        working-directory: libs/ui

  insights-test:
    name: Insights test
    runs-on: ubuntu-latest
    outputs:
      statements_pct: ${{ env.statements_pct }}
      functions_pct: ${{ env.functions_pct }}
      branches_pct: ${{ env.branches_pct }}
      lines_pct: ${{ env.lines_pct }}
    needs: [buildnumber, libs-build]
    steps:
      - uses: actions/checkout@v3

      - name: Setup Node 20
        uses: actions/setup-node@v3
        with:
          node-version: 20

      - name: Cache Node modules
        uses: actions/cache@v3
        env:
          cache-name: insights-node-modules
        with:
          path: ${{ github.workspace }}/apps/insights/node_modules
          key: ${{ runner.os }}-${{ env.cache-name }}-${{ hashFiles('apps/insights/package.json') }}

      - name: Set up Node modules
        run: yarn --ignore-engines
      
      - name: Download libs/ui build
        uses: actions/download-artifact@v4
        with:
          name: libs-ui-build
          path: ./libs/ui/dist/

      - name: Run lint
        run: yarn workspace insights lint

      - name: Run prettier check
        run: |
          yarn workspace insights prettier:check

      - name: Run test
        run: |
          yarn workspace insights test

      - name: Download Coverage
        uses: actions/download-artifact@v4
        with:
          name: libs-ui-coverage
          path: ./libs/ui/coverage/

      - name: Merge test report from libs/ui and app/insights
        run: |
          yarn --ignore-engines
          yarn insights:coverage
          rm -r ./apps/insights/coverage
          mv -f ./.insights_coverage ./apps/insights/coverage

      - name: Pack coverage files
        working-directory: apps/insights
        run: |
          tar zcvf insights-coverage.tar.gz coverage
          mkdir coverage_dir
          cp insights-coverage.tar.gz coverage_dir

      - name: Upload coverage report to S3
        uses: jakejarvis/s3-sync-action@master
        # with:
        #   args: --follow-symlinks --delete
        env:
          AWS_S3_BUCKET: ${{ vars.AWS_S3_BUCKET }}
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_S3_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_S3_SECRET_ACCESS_KEY }}
          AWS_REGION: ${{ vars.AWS_REGION }}
          SOURCE_DIR: ./apps/insights/coverage_dir
          DEST_DIR: ${{ github.repository }}/${{ needs.buildnumber.outputs.hash_number }}/coverage

      - name: Get coverage percentage
        working-directory: apps/insights
        run: |
          statements_pct=`cat coverage/coverage-report.json | jq -r '.summary.statements.pct'`
          echo "statements_pct=$statements_pct" >> $GITHUB_ENV
          lines_pct=`cat coverage/coverage-report.json | jq -r '.summary.lines.pct'`
          echo "lines_pct=$lines_pct" >> $GITHUB_ENV
          functions_pct=`cat coverage/coverage-report.json | jq -r '.summary.functions.pct'`
          echo "functions_pct=$functions_pct" >> $GITHUB_ENV
          branches_pct=`cat coverage/coverage-report.json | jq -r '.summary.branches.pct'`
          echo "branches_pct=$branches_pct" >> $GITHUB_ENV

      - name: Generate line-coverage badges
        uses: ./.github/actions/gen_coverage_badge
        if: always()
        with:
          coverage: ${{ env.lines_pct }}
          path: apps/insights/coverage/badges/coverage-lines.svg
          label: Coverage(Lines)

      - name: Generate statements-coverage badges
        uses: ./.github/actions/gen_coverage_badge
        if: always()
        with:
          coverage: ${{ env.statements_pct }}
          path: apps/insights/coverage/badges/coverage-statements.svg
          label: Coverage(Statements)

      - name: Generate functions-coverage badges
        uses: ./.github/actions/gen_coverage_badge
        if: always()
        with:
          coverage: ${{ env.functions_pct }}
          path: apps/insights/coverage/badges/coverage-functions.svg
          label: Coverage(Functions)

      - name: Generate branches-coverage badges
        uses: ./.github/actions/gen_coverage_badge
        if: always()
        with:
          coverage: ${{ env.branches_pct }}
          path: apps/insights/coverage/badges/coverage-branches.svg
          label: Coverage(Branches)

      - name: Upload coverage badges
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.APP_TOOLS_AWS_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.APP_TOOLS_AWS_KEY }}
          AWS_REGION: "us-west-1"
        run: |
          aws s3 sync apps/insights/coverage/badges s3://${{ vars.AWS_TEST_RESULT_S3_BUCKET }}/insights-badges/${{ github.run_id }}-${{ github.run_attempt }} --follow-symlinks --delete
          echo "### Insights Test Coverage:" >> $GITHUB_STEP_SUMMARY
          echo "![Statements](http://${{ vars.AWS_TEST_RESULT_S3_BUCKET }}.s3-website-us-west-1.amazonaws.com/insights-badges/${{ github.run_id }}-${{ github.run_attempt }}/coverage-statements.svg)" >> $GITHUB_STEP_SUMMARY
          echo "![Branches](http://${{ vars.AWS_TEST_RESULT_S3_BUCKET }}.s3-website-us-west-1.amazonaws.com/insights-badges/${{ github.run_id }}-${{ github.run_attempt }}/coverage-branches.svg)" >> $GITHUB_STEP_SUMMARY
          echo "![Lines](http://${{ vars.AWS_TEST_RESULT_S3_BUCKET }}.s3-website-us-west-1.amazonaws.com/insights-badges/${{ github.run_id }}-${{ github.run_attempt }}/coverage-lines.svg)" >> $GITHUB_STEP_SUMMARY
          echo "![Functions](http://${{ vars.AWS_TEST_RESULT_S3_BUCKET }}.s3-website-us-west-1.amazonaws.com/insights-badges/${{ github.run_id }}-${{ github.run_attempt }}/coverage-functions.svg)" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY

      - name: Generate third-party license report
        run: |
          yarn global add license-checker
          yarn licenses list

  insights-build:
    name: Insights prod build
    runs-on: ubuntu-latest
    needs: [buildnumber, libs-build, insights-test]
    steps:
      - uses: actions/checkout@v3

      - name: Setup Node 20
        uses: actions/setup-node@v3
        with:
          node-version: 20

      - name: Cache Node modules
        uses: actions/cache@v3
        env:
          cache-name: insights-node-modules
        with:
          path: ${{ github.workspace }}/apps/insights/node_modules
          key: ${{ runner.os }}-${{ env.cache-name }}-${{ hashFiles('apps/insights/package.json') }}

      - name: Find and Replace Header Version
        uses: jacobtomlinson/gha-find-replace@v3
        with:
          find: "$JS_VERSION"
          replace: ${{ needs.buildnumber.outputs.hash_number }}
          include: "**index*.html"
          regex: false

      - name: Set up Node modules
        run: yarn --ignore-engines

      - name: Download libs/ui build
        uses: actions/download-artifact@v4
        with:
          name: libs-ui-build
          path: ./libs/ui/dist/

      - name: Build static files
        env:
          NODE_OPTIONS: "--max_old_space_size=4096"
        run: |
          export CI=false
          export NODE_OPTIONS=--max_old_space_size=4096
          yarn workspace insights build:production
          tar zcvf insights.tar.gz insights && rm -rf insights
        working-directory: apps/insights

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: tools-release-insights
          path: apps/insights/insights.tar.gz

  insights-cloud-build:
    name: Insights cloud build
    runs-on: ubuntu-latest
    needs: [buildnumber, libs-build, insights-test]
    steps:
      - uses: actions/checkout@v3

      - name: Setup Node 20
        uses: actions/setup-node@v3
        with:
          node-version: 20

      - name: Find and Replace Header Version
        uses: jacobtomlinson/gha-find-replace@v3
        with:
          find: "$JS_VERSION"
          replace: ${{ needs.buildnumber.outputs.hash_number }}
          include: "**index*.html"
          regex: false

      - name: Cache Node modules
        uses: actions/cache@v3
        env:
          cache-name: insights-node-modules
        with:
          path: ${{ github.workspace }}/apps/insights/node_modules
          key: ${{ runner.os }}-${{ env.cache-name }}-${{ hashFiles('apps/insights/package.json') }}

      - name: Set up Node modules
        run: yarn --ignore-engines

      - name: Download libs/ui build
        uses: actions/download-artifact@v4
        with:
          name: libs-ui-build
          path: ./libs/ui/dist/

      - name: Build static files
        env:
          NODE_OPTIONS: "--max_old_space_size=4096"
        run: |
          export CI=false
          export NODE_OPTIONS=--max_old_space_size=4096
          yarn workspace insights build:cloud
          tar zcvf insights-cloud.tar.gz insights && rm -rf insights
        working-directory: apps/insights

      - name: Upload cloud build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: tools-release-insights-cloud
          path: apps/insights/insights-cloud.tar.gz

  home-test-build:
    name: TigerGraph Suite test & build
    needs: [buildnumber]
    runs-on: ubuntu-latest
    outputs:
      statements_pct: ${{ env.statements_pct }}
      functions_pct: ${{ env.functions_pct }}
      branches_pct: ${{ env.branches_pct }}
      lines_pct: ${{ env.lines_pct }}
    steps:
      - uses: actions/checkout@v3

      - name: Setup Node 20
        uses: actions/setup-node@v3
        with:
          node-version: 20

      - name: Cache Node modules
        uses: actions/cache@v3
        env:
          cache-name: fot-node-modules
        with:
          path: ${{ github.workspace }}/apps/home/<USER>
          key: ${{ runner.os }}-${{ env.cache-name }}-${{ hashFiles('apps/home/<USER>') }}

      - name: Set up Node modules
        run: yarn install
        working-directory: apps/home

      - name: Parse TOOLS_RELEASE_VERSION
        id: parse_version
        run: |
          BRANCH_NAME="${{ env.BRANCH_NAME }}"
          if [[ "$BRANCH_NAME" == tools_* ]]; then
            VERSION="${BRANCH_NAME#tools_}"
          else
            VERSION="${{ vars.TOOLS_RELEASE_VERSION }}"
          fi
          echo "major=$(echo $VERSION | cut -d. -f1)" >> $GITHUB_OUTPUT
          echo "minor=$(echo $VERSION | cut -d. -f2)" >> $GITHUB_OUTPUT
          echo "revision=$(echo $VERSION | cut -d. -f3)" >> $GITHUB_OUTPUT

      - uses: microsoft/variable-substitution@v1
        with:
          files: "apps/home/<USER>/version.json"
        env:
          major: ${{ steps.parse_version.outputs.major }}
          minor: ${{ steps.parse_version.outputs.minor }}
          revision: ${{ needs.buildnumber.outputs.build_number }}
          build: ${{ steps.parse_version.outputs.revision }}
          supportedTG: ${{ vars.SUPPORTED_TG_VERSIONS }} # splite by , the last number could be x witch means any
          packageNames: "tools,gap,gst,graphql,gshell,insights" # splite by ,

      - name: Find and Replace Build Version
        uses: jacobtomlinson/gha-find-replace@v3
        with:
          find: "$BUILD_NUMBER"
          replace: ${{ needs.buildnumber.outputs.build_number }}
          include: "apps/home/<USER>/header/src/react-components/index.tsx"
          regex: false

      - name: Find and Replace Build Version
        uses: jacobtomlinson/gha-find-replace@v3
        with:
          find: "$TOOLS_RELEASE_VERSION"
          replace: ${{ vars.TOOLS_RELEASE_VERSION }}
          include: "apps/home/<USER>/header/src/react-components/index.tsx"
          regex: false

      - name: Find and Replace Build Version
        uses: jacobtomlinson/gha-find-replace@v3
        with:
          find: "$BUILD_NUMBER"
          replace: ${{ needs.buildnumber.outputs.build_number }}
          include: "apps/home/<USER>/jsdk/*.js"
          regex: false
      
      - name: Find and Replace Build Version
        uses: jacobtomlinson/gha-find-replace@v3
        with:
          find: "$TOOLS_RELEASE_VERSION"
          replace: ${{ vars.TOOLS_RELEASE_VERSION }}
          include: "apps/home/<USER>/jsdk/*.js"
          regex: false

      - name: Find and Replace Header Version
        uses: jacobtomlinson/gha-find-replace@v3
        with:
          find: "$JS_VERSION"
          replace: ${{ needs.buildnumber.outputs.hash_number }}
          include: "**index*.html"
          regex: false

      - name: lint
        run: yarn eslint
        working-directory: apps/home

      - name: prettier
        run: yarn prettier:check
        working-directory: apps/home

      - name: Unit Tests
        run: yarn test
        working-directory: apps/home

      - name: Get coverage percentage
        working-directory: apps/home
        run: |
          statements_pct=`cat coverage/coverage-summary.json | jq -r '.total.statements.pct'`
          echo "statements_pct=$statements_pct" >> $GITHUB_ENV
          lines_pct=`cat coverage/coverage-summary.json | jq -r '.total.lines.pct'`
          echo "lines_pct=$lines_pct" >> $GITHUB_ENV
          functions_pct=`cat coverage/coverage-summary.json | jq -r '.total.functions.pct'`
          echo "functions_pct=$functions_pct" >> $GITHUB_ENV
          branches_pct=`cat coverage/coverage-summary.json | jq -r '.total.branches.pct'`
          echo "branches_pct=$branches_pct" >> $GITHUB_ENV

      - name: Generating coverage badges
        uses: jpb06/jest-badges-action@latest
        with:
          no-commit: true
          coverage-summary-path: ./apps/home/<USER>/coverage-summary.json
          output-folder: ./apps/home/<USER>

      - name: Upload coverage badges
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.APP_TOOLS_AWS_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.APP_TOOLS_AWS_KEY }}
          AWS_REGION: "us-west-1"
        run: |
          aws s3 sync apps/home/<USER>//${{ vars.AWS_TEST_RESULT_S3_BUCKET }}/home-badges/${{ github.run_id }}-${{ github.run_attempt }} --follow-symlinks --delete
          echo "### Home Test Coverage:" >> $GITHUB_STEP_SUMMARY
          echo "![Statements](http://${{ vars.AWS_TEST_RESULT_S3_BUCKET }}.s3-website-us-west-1.amazonaws.com/home-badges/${{ github.run_id }}-${{ github.run_attempt }}/coverage-statements.svg)" >> $GITHUB_STEP_SUMMARY
          echo "![Branches](http://${{ vars.AWS_TEST_RESULT_S3_BUCKET }}.s3-website-us-west-1.amazonaws.com/home-badges/${{ github.run_id }}-${{ github.run_attempt }}/coverage-branches.svg)" >> $GITHUB_STEP_SUMMARY
          echo "![Lines](http://${{ vars.AWS_TEST_RESULT_S3_BUCKET }}.s3-website-us-west-1.amazonaws.com/home-badges/${{ github.run_id }}-${{ github.run_attempt }}/coverage-lines.svg)" >> $GITHUB_STEP_SUMMARY
          echo "![Functions](http://${{ vars.AWS_TEST_RESULT_S3_BUCKET }}.s3-website-us-west-1.amazonaws.com/home-badges/${{ github.run_id }}-${{ github.run_attempt }}/coverage-functions.svg)" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY

      - name: Build static files
        run: |
          yarn run build
          tar zcvf family_of_tools.tar.gz tools && rm -rf tools
          yarn run build:cloud
          tar zcvf family_of_tools-cloud.tar.gz tools && rm -rf tools
          yarn run build:testdrive
          tar zcvf family_of_tools-testdrive.tar.gz tools && rm -rf tools
        working-directory: apps/home

      - name: Generate third-party license report
        run: |
          yarn global add license-checker
          yarn licenses list

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: tools-release-family_of_tools
          path: apps/home/<USER>

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: tools-release-family_of_tools-cloud
          path: apps/home/<USER>

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: tools-release-family_of_tools-testdrive
          path: apps/home/<USER>

      - name: copy version.json to tools-version.json
        run: |
          cp apps/home/<USER>/version.json tools-version.json

      - name: Upload build information
        uses: actions/upload-artifact@v4
        with:
          name: tools-release-tools-version
          path: tools-version.json

  gsqlshell-test:
    name: GSQL Shell test
    runs-on: ubuntu-latest
    outputs:
      statements_pct: ${{ env.statements_pct }}
      functions_pct: ${{ env.functions_pct }}
      branches_pct: ${{ env.branches_pct }}
      lines_pct: ${{ env.lines_pct }}
    needs: [buildnumber, libs-build]
    steps:
      - uses: actions/checkout@v3

      - name: Setup Node 20
        uses: actions/setup-node@v3
        with:
          node-version: 20

      - name: Cache Node modules
        uses: actions/cache@v3
        env:
          cache-name: gsqlshell-node-modules
        with:
          path: ${{ github.workspace }}/apps/gshell/node_modules
          key: ${{ runner.os }}-${{ env.cache-name }}-${{ hashFiles('apps/gshell/package.json') }}

      - name: Find and Replace Header Version
        uses: jacobtomlinson/gha-find-replace@v3
        with:
          find: "$JS_VERSION"
          replace: ${{ needs.buildnumber.outputs.hash_number }}
          include: "**index*.html"
          regex: false

      - name: Set up Node modules
        run: yarn install
        working-directory: apps/gshell

      - name: Run lint
        run: yarn lint
        working-directory: apps/gshell

      - name: Run test
        run: |
          yarn test
          tar zcvf gshell-coverage.tar.gz coverage
          mkdir coverage_dir
          cp gshell-coverage.tar.gz coverage_dir
        working-directory: apps/gshell

      - name: Upload coverage report to S3
        uses: jakejarvis/s3-sync-action@master
        # with:
        #   args: --follow-symlinks --delete
        env:
          AWS_S3_BUCKET: ${{ vars.AWS_S3_BUCKET }}
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_S3_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_S3_SECRET_ACCESS_KEY }}
          AWS_REGION: ${{ vars.AWS_REGION }}
          SOURCE_DIR: ./apps/gshell/coverage_dir
          DEST_DIR: ${{ github.repository }}/${{ needs.buildnumber.outputs.hash_number }}/coverage

      - name: Get coverage percentage
        working-directory: apps/gshell
        run: |
          statements_pct=`cat coverage/coverage-summary.json | jq -r '.total.statements.pct'`
          echo "statements_pct=$statements_pct" >> $GITHUB_ENV
          lines_pct=`cat coverage/coverage-summary.json | jq -r '.total.lines.pct'`
          echo "lines_pct=$lines_pct" >> $GITHUB_ENV
          functions_pct=`cat coverage/coverage-summary.json | jq -r '.total.functions.pct'`
          echo "functions_pct=$functions_pct" >> $GITHUB_ENV
          branches_pct=`cat coverage/coverage-summary.json | jq -r '.total.branches.pct'`
          echo "branches_pct=$branches_pct" >> $GITHUB_ENV

      - name: Generating coverage badges
        uses: jpb06/jest-badges-action@latest
        with:
          no-commit: true
          coverage-summary-path: ./apps/gshell/coverage/coverage-summary.json
          output-folder: ./apps/gshell/badges

      - name: Upload coverage badges
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.APP_TOOLS_AWS_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.APP_TOOLS_AWS_KEY }}
          AWS_REGION: "us-west-1"
        run: |
          aws s3 sync apps/gshell/badges s3://${{ vars.AWS_TEST_RESULT_S3_BUCKET }}/gsqlshell-badges/${{ github.run_id }}-${{ github.run_attempt }} --follow-symlinks --delete
          echo "### GSQL Shell Test Coverage:" >> $GITHUB_STEP_SUMMARY
          echo "![Statements](http://${{ vars.AWS_TEST_RESULT_S3_BUCKET }}.s3-website-us-west-1.amazonaws.com/gsqlshell-badges/${{ github.run_id }}-${{ github.run_attempt }}/coverage-statements.svg)" >> $GITHUB_STEP_SUMMARY
          echo "![Branches](http://${{ vars.AWS_TEST_RESULT_S3_BUCKET }}.s3-website-us-west-1.amazonaws.com/gsqlshell-badges/${{ github.run_id }}-${{ github.run_attempt }}/coverage-branches.svg)" >> $GITHUB_STEP_SUMMARY
          echo "![Lines](http://${{ vars.AWS_TEST_RESULT_S3_BUCKET }}.s3-website-us-west-1.amazonaws.com/gsqlshell-badges/${{ github.run_id }}-${{ github.run_attempt }}/coverage-lines.svg)" >> $GITHUB_STEP_SUMMARY
          echo "![Functions](http://${{ vars.AWS_TEST_RESULT_S3_BUCKET }}.s3-website-us-west-1.amazonaws.com/gsqlshell-badges/${{ github.run_id }}-${{ github.run_attempt }}/coverage-functions.svg)" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY

  gsqlshell-build:
    name: GSQL Shell build
    runs-on: ubuntu-latest
    needs: [buildnumber, libs-build, gsqlshell-test]
    steps:
      - uses: actions/checkout@v3

      - name: Setup Node 20
        uses: actions/setup-node@v3
        with:
          node-version: 20

      - name: Cache Node modules
        uses: actions/cache@v3
        env:
          cache-name: gsqlshell-node-modules
        with:
          path: ${{ github.workspace }}/apps/gshell/node_modules
          key: ${{ runner.os }}-${{ env.cache-name }}-${{ hashFiles('apps/gshell/package.json') }}

      - name: Find and Replace Header Version
        uses: jacobtomlinson/gha-find-replace@v3
        with:
          find: "$JS_VERSION"
          replace: ${{ needs.buildnumber.outputs.hash_number }}
          include: "**index*.html"
          regex: false

      - name: Set up Node modules
        run: yarn install
        working-directory: apps/gshell

      - name: Build static files
        run: |
          yarn build
          tar zcvf gshell.tar.gz gshell && rm -rf gshell
          yarn build:cloud
          tar zcvf gshell-cloud.tar.gz gshell && rm -rf gshell
        working-directory: apps/gshell

      - name: Generate third-party license report
        run: |
          yarn global add license-checker
          yarn licenses list

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: tools-release-gshell
          path: apps/gshell/gshell.tar.gz

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: tools-release-gshell-cloud
          path: apps/gshell/gshell-cloud.tar.gz

  graphql-test-build:
    name: GraphQL test & build
    runs-on: ubuntu-latest
    outputs:
      statements_pct: ${{ env.statements_pct }}
      functions_pct: ${{ env.functions_pct }}
      branches_pct: ${{ env.branches_pct }}
      lines_pct: ${{ env.lines_pct }}
    needs: [buildnumber]
    steps:
      - uses: actions/checkout@v3

      - name: Setup Node 20
        uses: actions/setup-node@v3
        with:
          node-version: 20

      - name: Cache Node modules
        uses: actions/cache@v3
        env:
          cache-name: graphql-node-modules
        with:
          path: ${{ github.workspace }}/apps/graphql/node_modules
          key: ${{ runner.os }}-${{ env.cache-name }}-${{ hashFiles('apps/graphql/package.json') }}

      - name: Set up Node modules
        run: yarn install
        working-directory: apps/graphql

      - name: Find and Replace Header Version
        uses: jacobtomlinson/gha-find-replace@v3
        with:
          find: "$JS_VERSION"
          replace: ${{ needs.buildnumber.outputs.hash_number }}
          include: "**index*.html"
          regex: false

      - name: Run test
        run: yarn test
        working-directory: apps/graphql

      - name: Get coverage percentage
        working-directory: apps/graphql
        run: |
          statements_pct=`cat coverage/coverage-summary.json | jq -r '.total.statements.pct'`
          echo "statements_pct=$statements_pct" >> $GITHUB_ENV
          lines_pct=`cat coverage/coverage-summary.json | jq -r '.total.lines.pct'`
          echo "lines_pct=$lines_pct" >> $GITHUB_ENV
          functions_pct=`cat coverage/coverage-summary.json | jq -r '.total.functions.pct'`
          echo "functions_pct=$functions_pct" >> $GITHUB_ENV
          branches_pct=`cat coverage/coverage-summary.json | jq -r '.total.branches.pct'`
          echo "branches_pct=$branches_pct" >> $GITHUB_ENV

      - name: Generating coverage badges
        uses: jpb06/jest-badges-action@latest
        with:
          no-commit: true
          coverage-summary-path: ./apps/graphql/coverage/coverage-summary.json
          output-folder: ./apps/graphql/badges

      - name: Upload coverage badges
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.APP_TOOLS_AWS_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.APP_TOOLS_AWS_KEY }}
          AWS_REGION: "us-west-1"
        run: |
          aws s3 sync apps/graphql/badges s3://${{ vars.AWS_TEST_RESULT_S3_BUCKET }}/graphql-badges/${{ github.run_id }}-${{ github.run_attempt }} --follow-symlinks --delete
          echo "### GraphQL Test Coverage:" >> $GITHUB_STEP_SUMMARY
          echo "![Statements](http://${{ vars.AWS_TEST_RESULT_S3_BUCKET }}.s3-website-us-west-1.amazonaws.com/graphql-badges/${{ github.run_id }}-${{ github.run_attempt }}/coverage-statements.svg)" >> $GITHUB_STEP_SUMMARY
          echo "![Branches](http://${{ vars.AWS_TEST_RESULT_S3_BUCKET }}.s3-website-us-west-1.amazonaws.com/graphql-badges/${{ github.run_id }}-${{ github.run_attempt }}/coverage-branches.svg)" >> $GITHUB_STEP_SUMMARY
          echo "![Lines](http://${{ vars.AWS_TEST_RESULT_S3_BUCKET }}.s3-website-us-west-1.amazonaws.com/graphql-badges/${{ github.run_id }}-${{ github.run_attempt }}/coverage-lines.svg)" >> $GITHUB_STEP_SUMMARY
          echo "![Functions](http://${{ vars.AWS_TEST_RESULT_S3_BUCKET }}.s3-website-us-west-1.amazonaws.com/graphql-badges/${{ github.run_id }}-${{ github.run_attempt }}/coverage-functions.svg)" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY

      - name: Build static files
        run: |
          yarn build
          tar zcvf graphql.tar.gz graphql && rm -rf graphql
          yarn build:cloud
          tar zcvf graphql-cloud.tar.gz graphql && rm -rf graphql
        working-directory: apps/graphql

      - name: Generate third-party license report
        run: |
          yarn global add license-checker
          yarn licenses list

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: tools-release-graphql
          path: apps/graphql/graphql.tar.gz

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: tools-release-graphql-cloud
          path: apps/graphql/graphql-cloud.tar.gz

  gst-test:
    name: GST test
    runs-on: ubuntu-latest
    outputs:
      statements_pct: ${{ env.statements_pct }}
      functions_pct: ${{ env.functions_pct }}
      branches_pct: ${{ env.branches_pct }}
      lines_pct: ${{ env.lines_pct }}
    needs: [buildnumber, libs-build]
    steps:
      - uses: actions/checkout@v3

      - name: Setup Node 20
        uses: actions/setup-node@v3
        with:
          node-version: 20

      - name: Cache Node modules
        uses: actions/cache@v3
        env:
          cache-name: gst-node-modules
        with:
          path: ${{ github.workspace }}/apps/gst/node_modules
          key: ${{ runner.os }}-${{ env.cache-name }}-${{ hashFiles('apps/gst/package.json') }}

      - name: Set up Node modules
        run: yarn --ignore-engines
        working-directory: apps/gst

      - name: Install Chrome 131
        uses: browser-actions/setup-chrome@v1
        id: setup-chrome
        with:
          chrome-version: 131
          install-chromedriver: true

      - name: Run lint
        run: yarn lint
        working-directory: apps/gst

      - name: Run test
        env:
          NODE_OPTIONS: "--openssl-legacy-provider --max_old_space_size=8196"
          CHROME_BIN: ${{ steps.setup-chrome.outputs.chrome-path }}
        run: |
          export NODE_OPTIONS="--openssl-legacy-provider --max_old_space_size=8196"
          yarn test
          tar zcvf gst-coverage.tar.gz coverage
          mkdir coverage_dir
          cp gst-coverage.tar.gz coverage_dir
        working-directory: apps/gst

      - name: Upload coverage report to S3
        uses: jakejarvis/s3-sync-action@master
        # with:
        #   args: --follow-symlinks --delete
        env:
          AWS_S3_BUCKET: ${{ vars.AWS_S3_BUCKET }}
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_S3_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_S3_SECRET_ACCESS_KEY }}
          AWS_REGION: ${{ vars.AWS_REGION }}
          SOURCE_DIR: ./apps/gst/coverage_dir
          DEST_DIR: ${{ github.repository }}/${{ needs.buildnumber.outputs.hash_number }}/coverage

      - name: Get coverage percentage
        working-directory: apps/gst
        run: |
          statements_pct=`cat coverage/coverage-summary.json | jq -r '.total.statements.pct'`
          echo "statements_pct=$statements_pct" >> $GITHUB_ENV
          lines_pct=`cat coverage/coverage-summary.json | jq -r '.total.lines.pct'`
          echo "lines_pct=$lines_pct" >> $GITHUB_ENV
          functions_pct=`cat coverage/coverage-summary.json | jq -r '.total.functions.pct'`
          echo "functions_pct=$functions_pct" >> $GITHUB_ENV
          branches_pct=`cat coverage/coverage-summary.json | jq -r '.total.branches.pct'`
          echo "branches_pct=$branches_pct" >> $GITHUB_ENV

      - name: Generating coverage badges
        uses: jpb06/jest-badges-action@latest
        with:
          no-commit: true
          coverage-summary-path: ./apps/gst/coverage/coverage-summary.json
          output-folder: ./apps/gst/badges

      - name: Upload coverage badges
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.APP_TOOLS_AWS_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.APP_TOOLS_AWS_KEY }}
          AWS_REGION: "us-west-1"
        run: |
          aws s3 sync apps/gst/badges s3://${{ vars.AWS_TEST_RESULT_S3_BUCKET }}/gst-badges/${{ github.run_id }}-${{ github.run_attempt }} --follow-symlinks --delete
          echo "### GST Test Coverage:" >> $GITHUB_STEP_SUMMARY
          echo "![Statements](http://${{ vars.AWS_TEST_RESULT_S3_BUCKET }}.s3-website-us-west-1.amazonaws.com/gst-badges/${{ github.run_id }}-${{ github.run_attempt }}/coverage-statements.svg)" >> $GITHUB_STEP_SUMMARY
          echo "![Branches](http://${{ vars.AWS_TEST_RESULT_S3_BUCKET }}.s3-website-us-west-1.amazonaws.com/gst-badges/${{ github.run_id }}-${{ github.run_attempt }}/coverage-branches.svg)" >> $GITHUB_STEP_SUMMARY
          echo "![Lines](http://${{ vars.AWS_TEST_RESULT_S3_BUCKET }}.s3-website-us-west-1.amazonaws.com/gst-badges/${{ github.run_id }}-${{ github.run_attempt }}/coverage-lines.svg)" >> $GITHUB_STEP_SUMMARY
          echo "![Functions](http://${{ vars.AWS_TEST_RESULT_S3_BUCKET }}.s3-website-us-west-1.amazonaws.com/gst-badges/${{ github.run_id }}-${{ github.run_attempt }}/coverage-functions.svg)" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY

      - name: Upload coverage report
        uses: actions/upload-artifact@v4
        with:
          name: gst-coverage
          path: apps/gst/coverage

      - name: Generate third-party license report
        run: |
          yarn global add license-checker
          yarn run license
        working-directory: apps/gst

  gst-build:
    name: GST prod build
    runs-on: ubuntu-latest
    needs: [libs-build, buildnumber, gst-test]
    steps:
      - uses: actions/checkout@v3

      - name: Setup Node 20
        uses: actions/setup-node@v3
        with:
          node-version: 20

      - name: Cache Node modules
        uses: actions/cache@v3
        env:
          cache-name: gst-node-modules
        with:
          path: ${{ github.workspace }}/apps/gst/node_modules
          key: ${{ runner.os }}-${{ env.cache-name }}-${{ hashFiles('apps/gst/package.json') }}

      - name: Find and Replace Header Version
        uses: jacobtomlinson/gha-find-replace@v3
        with:
          find: "$JS_VERSION"
          replace: ${{ needs.buildnumber.outputs.hash_number }}
          include: "**index*.html"
          regex: false

      - name: Set up Node modules
        run: yarn --ignore-engines
        working-directory: apps/gst

      - name: Build static files
        env:
          NODE_OPTIONS: "--openssl-legacy-provider --max_old_space_size=8196"
        run: |
          export NODE_OPTIONS="--openssl-legacy-provider --max_old_space_size=8196"
          yarn build -c production
          cd dist && tar zcvf gst.tar.gz gst && rm -rf gst && cd ..
        working-directory: apps/gst

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: tools-release-gst
          path: apps/gst/dist/gst.tar.gz

  gst-cloud-build:
    name: GST cloud build
    runs-on: ubuntu-latest
    needs: [libs-build, buildnumber, gst-test]
    steps:
      - uses: actions/checkout@v3

      - name: Setup Node 20
        uses: actions/setup-node@v3
        with:
          node-version: 20

      - name: Cache Node modules
        uses: actions/cache@v3
        env:
          cache-name: gst-node-modules
        with:
          path: ${{ github.workspace }}/apps/gst/node_modules
          key: ${{ runner.os }}-${{ env.cache-name }}-${{ hashFiles('apps/gst/package.json') }}

      - name: Find and Replace Header Version
        uses: jacobtomlinson/gha-find-replace@v3
        with:
          find: "$JS_VERSION"
          replace: ${{ needs.buildnumber.outputs.hash_number }}
          include: "**index*.html"
          regex: false

      - name: Set up Node modules
        run: yarn --ignore-engines
        working-directory: apps/gst

      - name: Build static files
        env:
          NODE_OPTIONS: "--openssl-legacy-provider --max_old_space_size=8196"
        run: |
          export NODE_OPTIONS="--openssl-legacy-provider --max_old_space_size=8196"
          yarn build -c cloud
          cd dist && tar zcvf gst-cloud.tar.gz gst && rm -rf gst
        working-directory: apps/gst

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: tools-release-gst-cloud
          path: apps/gst/dist/gst-cloud.tar.gz

  gap-test:
    name: GAP test
    runs-on: ubuntu-latest
    outputs:
      statements_pct: ${{ env.statements_pct }}
      functions_pct: ${{ env.functions_pct }}
      branches_pct: ${{ env.branches_pct }}
      lines_pct: ${{ env.lines_pct }}
    needs: [buildnumber, libs-build]
    steps:
      - uses: actions/checkout@v3

      - name: Setup Node 20
        uses: actions/setup-node@v3
        with:
          node-version: 20

      - name: Cache Node modules
        uses: actions/cache@v3
        env:
          cache-name: gap-node-modules
        with:
          path: ${{ github.workspace }}/apps/gap/node_modules
          key: ${{ runner.os }}-${{ env.cache-name }}-18-${{ hashFiles('apps/gap/package.json') }}

      - name: Set up Node modules
        run: yarn
        working-directory: apps/gap

      - name: Run lint
        run: yarn lint
        working-directory: apps/gap

      - name: Run test
        run: |
          yarn test
          tar zcvf gap-coverage.tar.gz coverage
          mkdir coverage_dir
          cp gap-coverage.tar.gz coverage_dir
        working-directory: apps/gap

      - name: Upload coverage report to S3
        uses: jakejarvis/s3-sync-action@master
        # with:
        #   args: --follow-symlinks --delete
        env:
          AWS_S3_BUCKET: ${{ vars.AWS_S3_BUCKET }}
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_S3_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_S3_SECRET_ACCESS_KEY }}
          AWS_REGION: ${{ vars.AWS_REGION }}
          SOURCE_DIR: ./apps/gap/coverage_dir
          DEST_DIR: ${{ github.repository }}/${{ needs.buildnumber.outputs.hash_number }}/coverage

      - name: Get coverage percentage
        working-directory: apps/gap
        run: |
          statements_pct=`cat coverage/coverage-summary.json | jq -r '.total.statements.pct'`
          echo "statements_pct=$statements_pct" >> $GITHUB_ENV
          lines_pct=`cat coverage/coverage-summary.json | jq -r '.total.lines.pct'`
          echo "lines_pct=$lines_pct" >> $GITHUB_ENV
          functions_pct=`cat coverage/coverage-summary.json | jq -r '.total.functions.pct'`
          echo "functions_pct=$functions_pct" >> $GITHUB_ENV
          branches_pct=`cat coverage/coverage-summary.json | jq -r '.total.branches.pct'`
          echo "branches_pct=$branches_pct" >> $GITHUB_ENV

      - name: Generating coverage badges
        uses: jpb06/jest-badges-action@latest
        with:
          no-commit: true
          coverage-summary-path: ./apps/gap/coverage/coverage-summary.json
          output-folder: ./apps/gap/badges

      - name: Upload coverage badges
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.APP_TOOLS_AWS_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.APP_TOOLS_AWS_KEY }}
          AWS_REGION: "us-west-1"
        run: |
          aws s3 sync apps/gap/badges s3://${{ vars.AWS_TEST_RESULT_S3_BUCKET }}/gap-badges/${{ github.run_id }}-${{ github.run_attempt }} --follow-symlinks --delete
          echo "### GAP Test Coverage:" >> $GITHUB_STEP_SUMMARY
          echo "![Statements](http://${{ vars.AWS_TEST_RESULT_S3_BUCKET }}.s3-website-us-west-1.amazonaws.com/gap-badges/${{ github.run_id }}-${{ github.run_attempt }}/coverage-statements.svg)" >> $GITHUB_STEP_SUMMARY
          echo "![Branches](http://${{ vars.AWS_TEST_RESULT_S3_BUCKET }}.s3-website-us-west-1.amazonaws.com/gap-badges/${{ github.run_id }}-${{ github.run_attempt }}/coverage-branches.svg)" >> $GITHUB_STEP_SUMMARY
          echo "![Lines](http://${{ vars.AWS_TEST_RESULT_S3_BUCKET }}.s3-website-us-west-1.amazonaws.com/gap-badges/${{ github.run_id }}-${{ github.run_attempt }}/coverage-lines.svg)" >> $GITHUB_STEP_SUMMARY
          echo "![Functions](http://${{ vars.AWS_TEST_RESULT_S3_BUCKET }}.s3-website-us-west-1.amazonaws.com/gap-badges/${{ github.run_id }}-${{ github.run_attempt }}/coverage-functions.svg)" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY

      - name: Upload coverage report
        uses: actions/upload-artifact@v4
        with:
          name: gap-coverage
          path: apps/gap/coverage

      - name: Generate third-party license reportcd
        run: |
          yarn global add license-checker
          yarn run license
        working-directory: apps/gap

  gap-build:
    name: GAP prod build
    runs-on: ubuntu-latest
    needs: [libs-build, buildnumber, gap-test]
    steps:
      - uses: actions/checkout@v3

      - name: Setup Node 20
        uses: actions/setup-node@v3
        with:
          node-version: 20

      - name: Cache Node modules
        uses: actions/cache@v3
        env:
          cache-name: gap-node-modules
        with:
          path: ${{ github.workspace }}/apps/gap/node_modules
          key: ${{ runner.os }}-${{ env.cache-name }}-18-${{ hashFiles('apps/gap/package.json') }}

      - name: Find and Replace Header Version
        uses: jacobtomlinson/gha-find-replace@v3
        with:
          find: "$JS_VERSION"
          replace: ${{ needs.buildnumber.outputs.hash_number }}
          include: "**index*.html"
          regex: false

      - name: Set up Node modules
        run: yarn
        working-directory: apps/gap

      - name: Build static files
        run: |
          yarn build -c production
          cd dist && tar zcvf gap.tar.gz gap && rm -rf gap && cd ..
        working-directory: apps/gap

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: tools-release-gap
          path: apps/gap/dist/gap.tar.gz

  gap-cloud-build:
    name: GAP cloud build
    runs-on: ubuntu-latest
    needs: [libs-build, buildnumber, gap-test]
    steps:
      - uses: actions/checkout@v3

      - name: Setup Node 20
        uses: actions/setup-node@v3
        with:
          node-version: 20

      - name: Cache Node modules
        uses: actions/cache@v3
        env:
          cache-name: gap-node-modules
        with:
          path: ${{ github.workspace }}/apps/gap/node_modules
          key: ${{ runner.os }}-${{ env.cache-name }}-18-${{ hashFiles('apps/gap/package.json') }}

      - name: Set up Node modules
        run: yarn
        working-directory: apps/gap

      - name: Find and Replace Header Version
        uses: jacobtomlinson/gha-find-replace@v3
        with:
          find: "$JS_VERSION"
          replace: ${{ needs.buildnumber.outputs.hash_number }}
          include: "**index*.html"
          regex: false

      - name: Build static files
        run: |
          yarn build -c cloud
          cd dist && tar zcvf gap-cloud.tar.gz gap && rm -rf gap
        working-directory: apps/gap

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: tools-release-gap-cloud
          path: apps/gap/dist/gap-cloud.tar.gz

  publish:
    name: Publish artifacts
    needs:
      [
        buildnumber,
        home-test-build,
        insights-build,
        insights-cloud-build,
        graphql-test-build,
        gsqlshell-build,
        gst-cloud-build,
        gst-build,
        gap-build,
        gap-cloud-build,
      ]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Download UI build artifacts
        uses: actions/download-artifact@v4
        with:
          path: release
          pattern: tools-release-*
          merge-multiple: true

      - name: Print the build number
        run: echo "Build number is ${{ needs.buildnumber.outputs.build_number }}"

      - name: Upload build artifacts to S3
        uses: jakejarvis/s3-sync-action@master
        # with:
        #   args: --follow-symlinks --delete
        env:
          AWS_S3_BUCKET: ${{ vars.AWS_S3_BUCKET }}
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_S3_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_S3_SECRET_ACCESS_KEY }}
          AWS_REGION: ${{ vars.AWS_REGION }}
          SOURCE_DIR: "release"
          DEST_DIR: ${{ github.repository }}/${{ needs.buildnumber.outputs.hash_number }}/release

      - name: Add Build Number
        if: github.event_name == 'pull_request'
        run: |
          echo -e '**Build Number**\n```' | tee comment.txt
          echo -e '${{ needs.buildnumber.outputs.build_number }}' | tee -a comment.txt
          echo -e '```' | tee -a comment.txt

      - name: Calculate Checksum
        if: github.event_name == 'pull_request'
        run: |
          echo -e '**Checksum**\n```' | tee -a comment.txt
          sha256sum release/* | tee -a comment.txt
          echo -e '```' | tee -a comment.txt

      - name: Add Download Link
        if: github.event_name == 'pull_request'
        run: |
          echo -e '**Downloadable Artifacts**\n```' | tee -a comment.txt
          printf '${{vars.AWS_S3_URL}}${{ github.repository }}/${{ needs.buildnumber.outputs.hash_number }}/%s\n' release/* | tee -a comment.txt
          echo -e '```' | tee -a comment.txt

      - name: Generate PR comment
        if: github.event_name == 'pull_request'
        run: >-
          python3 scripts/pr-comment.py
          --s3_bucket ${{ vars.AWS_S3_BUCKET }}
          --region ${{ vars.AWS_REGION }}
          --repo ${{ github.repository }}
          --sha ${{ needs.buildnumber.outputs.hash_number }}
          --files_dir release
          --coverage $(grep -m 1 -o '<span.*</span>' gst-coverage/index.html | sed 's/\(<span class="strong">\|<\/span>\)//g')
          --coverage_file index.html | tee -a comment.txt

      - name: Comment on PR
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v3
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const fs = require('fs');
            github.pulls.createReview({
              owner: context.repo.owner,
              repo: context.repo.repo,
              pull_number: context.payload.pull_request.number,
              event: 'COMMENT',
              body: fs.readFileSync('comment.txt', 'utf8')
            });

      - name: Package Tools
        run: |
            curl 'https://tigergraph-build-artifacts.s3-us-west-1.amazonaws.com/tigergraph/tools/${{ needs.buildnumber.outputs.hash_number }}/release/graphql.tar.gz' -o 'graphql.tar.gz'
            curl 'https://tigergraph-build-artifacts.s3-us-west-1.amazonaws.com/tigergraph/tools/${{ needs.buildnumber.outputs.hash_number }}/release/family_of_tools.tar.gz' -o 'tools.tar.gz'
            curl 'https://tigergraph-build-artifacts.s3-us-west-1.amazonaws.com/tigergraph/tools/${{ needs.buildnumber.outputs.hash_number }}/release/gap.tar.gz' -o 'gap.tar.gz'
            curl 'https://tigergraph-build-artifacts.s3-us-west-1.amazonaws.com/tigergraph/tools/${{ needs.buildnumber.outputs.hash_number }}/release/gst.tar.gz' -o 'gst.tar.gz'
            curl 'https://tigergraph-build-artifacts.s3-us-west-1.amazonaws.com/tigergraph/tools/${{ needs.buildnumber.outputs.hash_number }}/release/insights.tar.gz' -o 'insights.tar.gz'
            curl 'https://tigergraph-build-artifacts.s3-us-west-1.amazonaws.com/tigergraph/tools/${{ needs.buildnumber.outputs.hash_number }}/release/gshell.tar.gz' -o 'gshell.tar.gz'
            curl 'https://tigergraph-build-artifacts.s3-us-west-1.amazonaws.com/tigergraph/tools/${{ needs.buildnumber.outputs.hash_number }}/release/tools-version.json' -o 'tools-version.json'
            curl 'https://app-tools-information.s3.us-west-2.amazonaws.com/3.9.x/upgrade.sh' -o 'upgrade.sh'
            tar zcvf tools-all.tar.gz graphql.tar.gz tools.tar.gz gap.tar.gz gst.tar.gz insights.tar.gz gshell.tar.gz tools-version.json

      - name: Sign package
        uses: thomasdesr/minisign-action@v1
        with:
          args: -Sm tools-all.tar.gz
          minisign_key: ${{ secrets.minisign_key }}
          password: ${{ secrets.minisign_password }}
          
      - name: Package and sign Tools package
        run: |
          tar zcvf tools-all-signed-${{ vars.TOOLS_RELEASE_VERSION }}.${{ needs.buildnumber.outputs.build_number }}.tar.gz tools-all.tar.gz.minisig tools-all.tar.gz upgrade.sh
          mkdir upgrade
          cp tools-all-signed-${{ vars.TOOLS_RELEASE_VERSION }}.${{ needs.buildnumber.outputs.build_number }}.tar.gz tools-all-signed-latest.tar.gz
          mv tools-all-signed-${{ vars.TOOLS_RELEASE_VERSION }}.${{ needs.buildnumber.outputs.build_number }}.tar.gz upgrade
          mv tools-all-signed-latest.tar.gz upgrade
      
      - name: Upload Tools package
        uses: actions/upload-artifact@v4
        with:
          name: tools-signed-package
          path: upgrade

  deploy-clouddev:
    name: Deploy cloud dev
    needs: [buildnumber, publish]
    if: needs.buildnumber.outputs.build_number != 0 && ((github.event_name == 'pull_request' && github.event.pull_request) ||  startsWith(github.ref, 'refs/heads/main'))
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Release Cloud
        id: release_cloud
        uses: ./.github/actions/deploy-clouddev
        with:
          hash_number: ${{ needs.buildnumber.outputs.hash_number }}
          aws_id: ${{ secrets.APP_TOOLS_AWS_ID }}
          aws_key: ${{ secrets.APP_TOOLS_AWS_KEY }}

  release-tgcloud:
    name: Release cloud build
    needs: [buildnumber, publish]
    if: needs.buildnumber.outputs.build_number != 0 && github.ref_name == vars.RELEASE_BRANCH
    runs-on: ubuntu-latest
    outputs:
      release_url: ${{ steps.release_cloud.outputs.release_url }}
    steps:
      - uses: actions/checkout@v3
      - name: Release Cloud
        id: release_cloud
        uses: ./.github/actions/release-cloud
        with:
          hash_number: ${{ needs.buildnumber.outputs.hash_number }}
          build_number: ${{ needs.buildnumber.outputs.build_number }}
          version: ${{ needs.buildnumber.outputs.version }}
          github_token: ${{secrets.RELEASE_GITHUB_TOKEN}}
          target_commitish: ${{ vars.target_commitish }}
          draft: true
          prerelease: false

      - name: Print url
        run: |
          echo "release_url=${{ steps.release_cloud.outputs.release_url }}"

  deploy-tgcloud:
    name: Deploy tools on tgcloud-dev
    needs: [buildnumber, release-tgcloud]
    runs-on: ubuntu-latest
    environment: "cloud_test"
    env:
      DATA: ${{ toJSON(vars) }}
    steps:
      - name: Set Variables
        run: |
          REGION=`echo $DATA | jq -r '.REGION'`
          echo "REGION=$REGION" >> $GITHUB_ENV
          ADDRESS=`echo $DATA | jq -r '.ADDRESS_${{ needs.buildnumber.outputs.version }}'`
          echo "ADDRESS=$ADDRESS" >> $GITHUB_ENV
          AWSS3BUCKET=`echo $DATA | jq -r '.AWSS3BUCKET_${{ needs.buildnumber.outputs.version }}'`
          echo "AWSS3BUCKET=$AWSS3BUCKET" >> $GITHUB_ENV
          INSIGHTSAWSS3BUCKET=`echo $DATA | jq -r '.INSIGHTSAWSS3BUCKET_${{ needs.buildnumber.outputs.version }}'`
          echo "INSIGHTSAWSS3BUCKET=$INSIGHTSAWSS3BUCKET" >> $GITHUB_ENV

      - uses: actions/checkout@v3
      - name: Deploy to S3
        uses: ./.github/actions/deploy-cloud
        with:
          build_number: ${{ needs.buildnumber.outputs.build_number }}
          version: ${{ needs.buildnumber.outputs.version }}
          environment: "cloud_test"
          github_token: ${{secrets.github_token}}
          region: ${{ env.REGION }}
          address: ${{ env.ADDRESS }}
          s3_bucket: ${{ env.AWSS3BUCKET }}
          insights_s3_bucket: ${{ env.INSIGHTSAWSS3BUCKET }}
          aws_id: ${{ secrets.APP_TOOLS_AWS_ID }}
          aws_key: ${{ secrets.APP_TOOLS_AWS_KEY }}
          staging_deployment_approvers: ${{ vars.STAGING_DEPLOYMENT_APPROVERS }}
          prod_deployment_approvers: ${{ vars.PROD_DEPLOYMENT_APPROVERS }}
          cloudfront_id: ${{ vars.CLOUDFRONT_ID }}

  deploy-appdev:
    name: Deploy tools to app-dev cluster on tgcloud-dev
    needs: [buildnumber, release-tgcloud]
    runs-on: ubuntu-latest
    environment: "cloud_dev"
    env:
      DATA: ${{ toJSON(vars) }}
    steps:
      - name: Set Variables
        run: |
          REGION=`echo $DATA | jq -r '.REGION'`
          echo "REGION=$REGION" >> $GITHUB_ENV
          ADDRESS=`echo $DATA | jq -r '.ADDRESS_${{ needs.buildnumber.outputs.version }}'`
          echo "ADDRESS=$ADDRESS" >> $GITHUB_ENV
          AWSS3BUCKET=`echo $DATA | jq -r '.AWSS3BUCKET_${{ needs.buildnumber.outputs.version }}'`
          echo "AWSS3BUCKET=$AWSS3BUCKET" >> $GITHUB_ENV
          INSIGHTSAWSS3BUCKET=`echo $DATA | jq -r '.INSIGHTSAWSS3BUCKET_${{ needs.buildnumber.outputs.version }}'`
          echo "INSIGHTSAWSS3BUCKET=$INSIGHTSAWSS3BUCKET" >> $GITHUB_ENV

      - uses: actions/checkout@v3
      - name: Deploy to S3
        uses: ./.github/actions/deploy-cloud
        with:
          build_number: ${{ needs.buildnumber.outputs.build_number }}
          version: ${{ needs.buildnumber.outputs.version }}
          environment: "cloud_dev"
          github_token: ${{secrets.github_token}}
          region: ${{ env.REGION }}
          address: ${{ env.ADDRESS }}
          s3_bucket: ${{ env.AWSS3BUCKET }}
          insights_s3_bucket: ${{ env.INSIGHTSAWSS3BUCKET }}
          aws_id: ${{ secrets.APP_TOOLS_AWS_ID }}
          aws_key: ${{ secrets.APP_TOOLS_AWS_KEY }}
          staging_deployment_approvers: ${{ vars.STAGING_DEPLOYMENT_APPROVERS }}
          prod_deployment_approvers: ${{ vars.PROD_DEPLOYMENT_APPROVERS }}
          cloudfront_id: ${{ vars.CLOUDFRONT_ID }}

  run-cloud-tools-e2e:
    name: Cloud tools e2e tests
    needs: [buildnumber, deploy-clouddev]
    if: ${{ (github.base_ref || github.ref_name) == 'main' || (github.base_ref || github.ref_name) == vars.RELEASE_BRANCH }}
    runs-on: ubuntu-22.04
    steps:
      - name: Checkout bigtest repo for create/terminate cluster
        uses: actions/checkout@v3
        with:
          repository: tigergraph/bigtest
          token: ${{ secrets.CICD_GITHUB_TOKEN }}
          ref: cloud_e2e_dev

      - name: move the cloud code to specific path
        run: |
          mkdir -p ${{github.workspace}}/../cloud
          mv -f ${{github.workspace}}/tests ./../cloud

      - name: Checkout tools repo
        uses: actions/checkout@v3

      - name: Run E2E Tests
        uses: ./.github/actions/e2e
        with:
          test_environment: "https://classic.tgcloud-dev.com"
          test_suite: "tools_dailyrun"
          rerun: 2
          cpu: 2
          cluster_name: "auto-${{github.actor}}-${{github.event.pull_request.number}}"
          tools_branch: ${{ github.head_ref || github.ref_name }}
          header: ${{ needs.buildnumber.outputs.hash_number }}

      - name: Generate and upload Allure report to S3
        if: always()
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.APP_TOOLS_AWS_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.APP_TOOLS_AWS_KEY }}
          AWS_REGION: "us-west-1"
        run: |
          pwd
          wget --no-verbose -O /tmp/allure-commandline.zip https://repo.maven.apache.org/maven2/io/qameta/allure/allure-commandline/2.19.0/allure-commandline-2.19.0.zip && unzip /tmp/allure-commandline.zip
          mkdir tests/allure-report
          allure-2.19.0/bin/allure generate ./../cloud/tests/cloud_e2e/alluredir/cloud -o tests/allure-report
          aws s3 sync tests/allure-report s3://${{ vars.AWS_TEST_RESULT_S3_BUCKET }}/allure-report/${{ github.run_id }}-${{ github.run_attempt }} --follow-symlinks --delete
          echo "### Test Report URL:" >> $GITHUB_STEP_SUMMARY
          echo "http://${{ vars.AWS_TEST_RESULT_S3_BUCKET }}.s3-website-us-west-1.amazonaws.com/allure-report/${{ github.run_id }}-${{ github.run_attempt }}/index.html" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          ls -l ./../cloud/tests/cloud_e2e/junit/
          cp /tmp/cloud_e2e/result_*/prepare_env_log* tests/ 2>/dev/null || :

      - name: Comment on PR
        if: failure()
        uses: actions/github-script@v3
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const fs = require('fs');
            github.pulls.createReview({
              owner: context.repo.owner,
              repo: context.repo.repo,
              pull_number: context.payload.pull_request.number,
              event: 'COMMENT',
              body: 'E2e test failed: http://${{ vars.AWS_TEST_RESULT_S3_BUCKET }}.s3-website-us-west-1.amazonaws.com/allure-report/${{ github.run_id }}-${{ github.run_attempt }}/index.html'
            });

  post-test-check:
    name: Check JIRA Ticket Status
    needs: [run-cloud-tools-e2e]
    runs-on: ubuntu-latest
    outputs:
      error_message: ${{ env.ERROR_MESSAGE }}
    steps:
      - name: Validate PR Title
        env:
          PR_TITLE: ${{ github.event.pull_request.title }}
          PR_TITLE_REGEX_PATTERN: ${{ vars.PR_TITLE_REGEX_PATTERN }}
        if: github.event_name == 'pull_request' && github.event.pull_request
        run: |
          echo "*******************************************************************"
          echo "base_branch:${{ github.base_ref }}"
          echo "pr_title:$PR_TITLE"
          echo "*******************************************************************"
          base_branch="${{ github.base_ref }}"
          dev_subfix="_dev"
          base_branch=${base_branch//$dev_subfix/}
          matches=$(echo "$PR_TITLE" | grep -oP "$PR_TITLE_REGEX_PATTERN" || true)

          version_info=$(curl -s -u "${{ secrets.JIRA_USERNAME }}:${{ secrets.JIRA_API_TOKEN }}" -H "Content-Type: application/json" "${JIRA_API_URL}rest/api/3/project/APPS/versions?orderBy=-releaseDate")
          next_release_version=$(echo $version_info | jq -r '.[] | select(.released == false and .archived == false) | .name' | head -n 1)
          echo "Next Cloud Release Version: $next_release_version"

          if [[ -z $matches ]]; then
            errorMessage+="Invalid PR title format. Please follow the correct format: \"([)ticket-number(]) type(scope): description;\" \nExample: TOOLS-1234 feat(login): add forgot password link;\n\n"
            echo "Failed:$errorMessage"
          else
              echo "Matching strings: $matches"
              IFS=$'\n'
              for match in $matches; do
                ticket=$(echo "$match" | grep -m 1 -oP "[a-zA-Z]+-\d+" | awk 'NR==1{print $1}')
                echo "Matching: $match"
                echo "JIRA Ticket Number: $ticket"
                api_url="${JIRA_API_URL}rest/api/latest/issue/${ticket}"
                issue_info=$(curl -s -u "${{ secrets.JIRA_USERNAME }}:${{ secrets.JIRA_API_TOKEN }}" -H "Content-Type: application/json" "$api_url")
                labels=$(echo "$issue_info" | jq -r '.fields.labels[]')
                fixVersions=$(echo "$issue_info" | jq -r '.fields.fixVersions[].name')
                number_fix_versions=$(echo "$issue_info" | jq -r '.fields.fixVersions | length')
                echo "Labels: $labels"
                echo "Fix Versions: $fixVersions"
                echo "Base Branch: $base_branch"
                echo "Number of fix versions: $number_fix_versions"
                merged_banch_label="merged_"$base_branch

                if [[ "$labels" == *"merged_$base_branch"* ]]; then
                  echo "This PR has already been merged to the base branch. If you want to merge it again, please remove the \"merged_$base_branch\" label from the corresponding JIRA ticket $ticket"
                  errorMessage+="This PR has already been merged to the base branch. If you want to merge it again, please remove the \"merged_$base_branch\" label from the corresponding JIRA ticket [$ticket](https://graphsql.atlassian.net/browse/$ticket).\n"
                elif [[ "$labels" != *"$base_branch"* ]]; then
                  echo "Missing label for JIRA ticket $ticket:$base_branch. Please add the required label."
                  errorMessage+="Missing label for JIRA ticket [$ticket](${JIRA_API_URL}browse/$ticket): \"${base_branch}\". Please add the required label.\n"
                elif [[ "$base_branch" == main* ]]; then
                  if [ "${number_fix_versions}" -eq 0 ]; then
                    echo "No fix versions are assigned to the ticket"
                    errorMessage+="No fix versions are assigned to the JIRA ticket [$ticket](https://graphsql.atlassian.net/browse/$ticket).\n"
                  else
                    for fixVersion in $fixVersions; do
                      IS_RELEASED=$(echo "$issue_info" | jq -r ".fields.fixVersions[] | select(.name == \"$fixVersion\") | .released")
                      if [ "${IS_RELEASED}" = "true" ]; then
                        echo "${fixVersion} - Released"
                        errorMessage+="Version $fixVersion assigned to the JIRA ticket [$ticket](https://graphsql.atlassian.net/browse/$ticket) has already been released. Please remove the fix version to continue.\n"
                      elif [[ "$fixVersion" == TOOLS* ]] && [ "$fixVersion" != "$next_release_version" ]; then
                        echo "${fixVersion} - Not the next release version"
                        errorMessage+="Version $fixVersion assigned to the JIRA ticket [$ticket](https://graphsql.atlassian.net/browse/$ticket) is not the next release version, please update the fix version to $next_release_version.\n"
                      else
                        echo "${fixVersion} - Not Released"
                      fi
                    done
                  fi
                else
                  echo "Ticket $ticket meets requirements"
                fi
              done
          fi

          if [ -n "$errorMessage" ]; then
            echo "ERROR_MESSAGE=$errorMessage" >> $GITHUB_ENV
            exit 1
          fi

      - name: Comment on PR
        if: failure()
        uses: actions/github-script@v3
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            github.pulls.createReview({
              owner: context.repo.owner,
              repo: context.repo.repo,
              pull_number: context.payload.pull_request.number,
              event: 'COMMENT',
              body: '${{env.ERROR_MESSAGE}}'
            });

  test-result-slack-message:
    name: Send test result slack message
    runs-on: ubuntu-latest
    needs: [run-cloud-tools-e2e, buildnumber]
    if: always() && needs.run-cloud-tools-e2e.result != 'skipped'
    steps:
      - if: needs.run-cloud-tools-e2e.result == 'success'
        run: |
          echo "test_status=:large_green_circle:" >> $GITHUB_ENV

      - if: needs.run-cloud-tools-e2e.result == 'failure'
        run: |
          echo "test_status=:red_circle:" >> $GITHUB_ENV
          echo "notify_actor=@${{ needs.buildnumber.outputs.slack_user }}" >> $GITHUB_ENV

      - name: Slack Message
        shell: bash
        run: |
          slackMessage='{
            "blocks": [
              {
                "type": "header",
                "text": {
                  "type": "plain_text",
                  "text": "cloud_${{ needs.buildnumber.outputs.build_number }} E2E Report Pytest ${{ env.test_status }} ",
                  "emoji": true
                }
              },
              {
                "type": "section",
                "text": {
                  "type": "mrkdwn",
                  "text": "<http://${{ vars.AWS_TEST_RESULT_S3_BUCKET }}.s3-website-us-west-1.amazonaws.com/allure-report/${{ github.run_id }}-${{ github.run_attempt }}/index.html|Pytest Test Report>"
                }
              },
              {
                "type": "section",
                "fields": [
                  {
                    "type": "mrkdwn",
                    "text": "*Branch:*\n${{ github.ref }}"
                  },
                  {
                    "type": "mrkdwn",
                    "text": "*Actor:*\n${{ github.actor }} ${{ env.notify_actor }}"
                  },
                  {
                    "type": "mrkdwn",
                    "text": "*Release Version:*\n<https://github.com/tigergraph/tools/releases/tag/cloud_${{ needs.buildnumber.outputs.build_number }}|cloud_${{ needs.buildnumber.outputs.build_number }}>"
                  },
                  {
                    "type": "mrkdwn",
                    "text": "*Sha:*\n${{inputs.hash_number}}"
                  }
                ]
              },
              {
                "type": "section",
                "text": {
                  "type": "mrkdwn",
                  "text": "<https://github.com/tigergraph/tools/actions/runs/${{ github.run_id }}|View Github Action>"
                }
              }
            ]
          }'
          curl -X POST ${{ secrets.APP_DEPLOYMENT_SLACK_APP_DEPLOYMENT_WEBHOOK_URL }} -H 'Content-type:application/json' \
          --data @<(cat <<EOF
          $slackMessage
          EOF
          )

  deploy-onprem:
    name: Release and deploy on-prem dev
    needs: [buildnumber, release-tgcloud]
    runs-on: ubuntu-latest
    if: needs.buildnumber.outputs.build_number != 0
    environment: onprem_${{ needs.buildnumber.outputs.version }}
    steps:
      - uses: actions/checkout@v3
      - name: Deploy On Premise
        uses: ./.github/actions/deploy-onprem
        with:
          hash_number: ${{ needs.buildnumber.outputs.hash_number }}
          build_number: ${{ needs.buildnumber.outputs.build_number }}
          github_token: ${{ secrets.github_token }}
          ssh_host: ${{ vars.HOST }}
          version: ${{ needs.buildnumber.outputs.version }}
          ssh_username: ${{ secrets.SSH_USERNAME }}
          ssh_key: ${{ secrets.FSERVERSSHKEY }}
          ssh_port: ${{ secrets.SSH_PORT }}

  slack-message:
    name: Send slack message
    runs-on: ubuntu-latest
    needs:
      [
        deploy-onprem,
        deploy-appdev,
        deploy-tgcloud,
        publish,
        buildnumber,
        release-tgcloud,
        gap-test,
        gst-test,
        libs-models-test,
        libs-build,
        home-test-build,
        insights-test,
        gsqlshell-test,
        gsqlshell-build,
        graphql-test-build,
      ]
    if: |
      always()
      && contains(needs.*.result, 'success')
    steps:
      - if: needs.deploy-onprem.result == 'success'
        run: |
          echo "onprem_status=:large_green_circle:" >> $GITHUB_ENV

      - if: needs.deploy-onprem.result == 'skipped'
        run: |
          echo "onprem_status=:white_circle:" >> $GITHUB_ENV

      - if: needs.deploy-onprem.result == 'failure'
        run: |
          echo "onprem_status=:red_circle:" >> $GITHUB_ENV

      - if: needs.deploy-appdev.result == 'success'
        run: |
          echo "appdev_status=:large_green_circle:" >> $GITHUB_ENV

      - if: needs.deploy-appdev.result == 'skipped'
        run: |
          echo "appdev_status=:white_circle:" >> $GITHUB_ENV

      - if: needs.deploy-appdev.result == 'failure'
        run: |
          echo "appdev_status=:red_circle:" >> $GITHUB_ENV

      - if: needs.deploy-tgcloud.result == 'success'
        run: |
          echo "tgcloud_status=:large_green_circle:" >> $GITHUB_ENV

      - if: needs.deploy-tgcloud.result == 'skipped'
        run: |
          echo "tgcloud_status=:white_circle:" >> $GITHUB_ENV

      - if: needs.deploy-tgcloud.result == 'failure'
        run: |
          echo "tgcloud_status=:red_circle:" >> $GITHUB_ENV

      - if: needs.publish.result == 'success'
        run: |
          echo "publish_status=:large_green_circle:" >> $GITHUB_ENV

      - if: needs.publish.result == 'skipped'
        run: |
          echo "publish_status=:red_circle:" >> $GITHUB_ENV

      - if: needs.publish.result == 'failure'
        run: |
          echo "publish_status=:red_circle:" >> $GITHUB_ENV

      - name: Slack Message
        shell: bash
        run: |
          slackMessage='{
            "blocks": [
              {
                "type": "header",
                "text": {
                  "type": "plain_text",
                  "text": "cloud_${{ needs.buildnumber.outputs.build_number }} Tools Build Report ${{ env.publish_status }}",
                  "emoji": true
                }
              },
              {
                "type": "section",
                "fields": [
                  {
                    "type": "mrkdwn",
                    "text": "*Branch:*\n${{ github.ref }}"
                  },
                  {
                    "type": "mrkdwn",
                    "text": "*Actor:*\n${{ github.actor }}"
                  },
                  {
                    "type": "mrkdwn",
                    "text": "*Release Version:*\n<https://github.com/tigergraph/tools/releases/tag/cloud_${{ needs.buildnumber.outputs.build_number }}|cloud_${{ needs.buildnumber.outputs.build_number }}>"
                  },
                  {
                    "type": "mrkdwn",
                    "text": "*Sha:*\n${{ needs.buildnumber.outputs.hash_number }}"
                  }
                ]
              },
              {
                "type": "section",
                "text": {
                  "type": "mrkdwn",
                  "text": "*Code Coverage*:\n *GST*:${{ needs.gst-test.outputs.statements_pct }}% *GAP*:${{ needs.gap-test.outputs.statements_pct }}% *IN*:${{ needs.insights-test.outputs.statements_pct }}% *MODELS*:${{ needs.libs-models-test.outputs.statements_pct }}% *UI*:${{ needs.libs-build.outputs.statements_pct }}% *HOME*:${{ needs.home-test-build.outputs.statements_pct }}% *GSQLSHELL*:${{ needs.gsqlshell-test.outputs.statements_pct }}% *GRAPHQL*:${{ needs.graphql-test-build.outputs.statements_pct }}% "
                }
              },
              {
                "type": "section",
                "text": {
                  "type": "mrkdwn",
                  "text": "*On-Premise Deployment*: ${{ env.onprem_status }} \n *App-Dev on Tgcloud Deployment*: ${{ env.appdev_status }} \n*Tgcloud-dev Deployment*: ${{ env.tgcloud_status }}\n*Tgcloud-staging Deployment*: :white_circle:\n*Tgcloud-prod Deployment*: :white_circle:"
                }
              },
              {
                "type": "section",
                "text": {
                  "type": "mrkdwn",
                  "text": "<https://github.com/tigergraph/tools/actions/runs/${{ github.run_id }}|View Github Action>"
                }
              }
            ]
          }'
          curl -X POST ${{ secrets.APP_DEPLOYMENT_SLACK_APP_DEPLOYMENT_WEBHOOK_URL }} -H 'Content-type:application/json' \
          --data @<(cat <<EOF
          $slackMessage
          EOF
          )

  # insights-web-e2e-test:
  #   name: Insights e2e tests
  #   runs-on: ubuntu-latest
  #   needs: [deploy-onprem]
  #   environment: onprem
  #   defaults:
  #     run:
  #       working-directory: ./insights
  #   steps:
  #     - name: Checkout
  #       uses: actions/checkout@v3

  #     - name: Setup Node 16
  #       uses: actions/setup-node@v3
  #       with:
  #         node-version: 16

  #     - name: Cache Node modules
  #       uses: actions/cache@v3
  #       env:
  #         cache-name: insights-node-modules
  #       with:
  #         path: ${{ github.workspace }}/apps/insights/node_modules
  #         key: ${{ runner.os }}-${{ env.cache-name }}-${{ hashFiles('apps/insights/package.json') }}

  #     - name: Find and Replace Build Version
  #       uses: jacobtomlinson/gha-find-replace@v3
  #       with:
  #         find: "$ONPREM_HOST"
  #         replace: ${{ vars.HOST }}
  #         include: "apps/insights/cypress.config.test.js"
  #         regex: false

  #     - name: Set up Node modules
  #       working-directory: apps/insights
  #       run: |
  #         yarn --ignore-engines
  #         yarn cypress install

  #     - name: Cypress run
  #       uses: cypress-io/github-action@v5
  #       with:
  #         working-directory: apps/insights
  #         install: false
  #         config-file: cypress.config.test.js
  #         headed: true
  #         runTests: true

  #     - name: Generate and upload Allure report to S3
  #       working-directory: apps/insights
  #       if: always()
  #       env:
  #         AWS_ACCESS_KEY_ID: ${{ secrets.APP_TOOLS_AWS_ID }}
  #         AWS_SECRET_ACCESS_KEY: ${{ secrets.APP_TOOLS_AWS_KEY }}
  #         AWS_REGION: "us-west-1"
  #       run: |
  #         aws s3 sync cypress/reports/html s3://${{ vars.AWS_TEST_RESULT_S3_BUCKET }}/cypress-report/${{ github.run_id }}-${{ github.run_attempt }} --follow-symlinks --delete
  #         echo "### Insights E2E Report URL:" >> $GITHUB_STEP_SUMMARY
  #         echo "http://${{ vars.AWS_TEST_RESULT_S3_BUCKET }}.s3-website-us-west-1.amazonaws.com/cypress-report/${{ github.run_id }}-${{ github.run_attempt }}/index.html" >> $GITHUB_STEP_SUMMARY
  #         echo "" >> $GITHUB_STEP_SUMMARY
