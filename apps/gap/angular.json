{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"admin-portal": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/gap", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "assets": ["src/favicon.ico", {"glob": "**/!(*.scss)", "input": "src/assets/", "output": "/assets/"}], "styles": ["src/styles.scss", "node_modules/roboto-fontface/css/roboto/roboto-fontface.css"], "scripts": ["node_modules/randomcolor/randomColor.js"], "baseHref": "/admin/", "deployUrl": "/admin/", "vendorChunk": true, "extractLicenses": false, "buildOptimizer": false, "sourceMap": true, "optimization": false, "namedChunks": true}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "6mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "10kb"}]}, "cloud": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.cloud.ts"}], "index": {"input": "src/index-cloud.html", "output": "index.html"}, "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "6mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "10kb"}]}}, "defaultConfiguration": ""}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "admin-portal:build", "host": "0.0.0.0", "proxyConfig": "proxy.conf.json"}, "configurations": {"production": {"browserTarget": "admin-portal:build:production"}, "cloud": {"browserTarget": "admin-portal:build:cloud"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "admin-portal:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "assets": ["src/favicon.ico", {"glob": "**/!(*.scss)", "input": "src/assets/", "output": "/assets/"}], "styles": ["src/styles.scss", "node_modules/roboto-fontface/css/roboto/roboto-fontface.css", "node_modules/material-design-icons/iconfont/material-icons.css"], "scripts": []}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}, "e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "e2e/protractor.conf.js", "devServerTarget": "admin-portal:serve"}, "configurations": {"production": {"devServerTarget": "admin-portal:serve:production"}, "cloud": {"devServerTarget": "admin-portal:serve:cloud"}}}}}}, "defaultProject": "admin-portal", "cli": {"analytics": false, "defaultCollection": "@angular-eslint/schematics"}}