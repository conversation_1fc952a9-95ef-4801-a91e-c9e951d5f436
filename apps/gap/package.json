{"name": "admin-portal", "description": "TigerGraph Admin Portal", "resolutions": {"follow-redirects": "~1.15.5", "d3-color": "3.1.0"}, "scripts": {"ng": "ng", "prestart": "yarn build:themes", "start": "NODE_OPTIONS=\"--openssl-legacy-provider --max_old_space_size=4096\" ng serve --disable-host-check", "start:cloud": "NODE_OPTIONS=\"--openssl-legacy-provider --max_old_space_size=4096\" ng serve --disable-host-check --configuration=cloud", "serve": "http-server -p 9877", "prebuild": "yarn build:themes", "build": "NODE_OPTIONS=\"--openssl-legacy-provider --max_old_space_size=4096\" ng build", "build:themes": "./scripts/build-themes.sh", "test": "NODE_OPTIONS=\"--openssl-legacy-provider --max_old_space_size=8192\" ng test --browsers ChromeHeadlessCI --watch false --code-coverage", "eslint": "ng lint", "stylelint": "stylelint \"src/**/*.scss\"", "lint": "npm-run-all -c eslint stylelint", "e2e": "ng e2e", "e2e-win": "yarn e2e --protractor-config=e2e/protractor-win.conf.js --no-webdriver-update", "license": "./scripts/license/report-license.sh"}, "private": true, "dependencies": {"@angular-material-components/datetime-picker": "^5.0.7", "@angular-material-components/moment-adapter": "^5.0.0", "@angular/animations": "~12.2.17", "@angular/cdk": "^12.2.13", "@angular/common": "~12.2.17", "@angular/compiler": "~12.2.17", "@angular/core": "~12.2.17", "@angular/flex-layout": "11.0.0-beta.33", "@angular/forms": "~12.2.17", "@angular/material": "^11.2.13", "@angular/material-moment-adapter": "^11.2.13", "@angular/platform-browser": "~12.2.17", "@angular/platform-browser-dynamic": "~12.2.17", "@angular/router": "~12.2.17", "@babel/traverse": "7.23.2", "@fortawesome/fontawesome-free": "^6.1.1", "@fortawesome/fontawesome-svg-core": "^1.3.0", "@fortawesome/free-regular-svg-icons": "^6.0.0", "@fortawesome/free-solid-svg-icons": "^6.0.0", "@fortawesome/react-fontawesome": "^0.1.17", "@ng-idle/core": "^11.1.0", "@ng-idle/keepalive": "^11.0.3", "@ngx-translate/core": "^10.0.2", "@ngx-translate/http-loader": "^3.0.1", "@tanstack/react-query": "^4.19.1", "@tigergraph/app-ui-lib": "^0.3.34", "@tigergraph/tools-models": "1.1.4", "@uiw/react-codemirror": "3.2.7", "baseui": "11.2.0", "chart.js": "^4.3.2", "chartjs-adapter-moment": "^1.0.1", "codemirror": "^5.65.2", "cron-validate": "^1.4.3", "d": "^1.0.2", "echarts": "^5.3.1", "echarts-for-react": "^3.0.2", "i18next": "^22.4.14", "i18next-http-backend": "^2.2.0", "inline-style-expand-shorthand": "^1.4.0", "json5": "^2.2.2", "jsrsasign": "~11.0.0", "lodash": "^4.17.15", "material-design-icons": "^3.0.1", "moment": "^2.29.4", "ng2-nouislider": "^1.8.2", "nouislider": "9.1.0", "randomcolor": "^0.5.4", "react": "^18.3.1", "react-dom": "^18.3.1", "react-flow-renderer": "^9.7.4", "react-i18next": "^12.2.0", "react-icons": "^5.2.1", "resumablejs": "^1.1.0", "roboto-fontface": "^0.10.0", "rxjs": "~6.6.7", "styletron-engine-atomic": "^1.5.0", "styletron-react": "^5.1.0", "tslib": "^2.0.0", "webpack": "5.50.0", "xterm": "4.19.0", "xterm-addon-fit": "0.5.0", "xterm-addon-search": "0.9.0", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-devkit/build-angular": "~12.2.18", "@angular-eslint/builder": "12.7.0", "@angular-eslint/eslint-plugin": "12.7.0", "@angular-eslint/eslint-plugin-template": "12.7.0", "@angular-eslint/schematics": "12.7.0", "@angular-eslint/template-parser": "12.7.0", "@angular/cli": "~12.2.18", "@angular/compiler-cli": "~12.2.17", "@angular/language-service": "~12.2.17", "@types/chart.js": "^2.7.56", "@types/codemirror": "^5.60.5", "@types/jasmine": "~3.6.0", "@types/jasminewd2": "^2.0.10", "@types/lodash": "^4.14.136", "@types/node": "^12.11.1", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@types/styletron-engine-atomic": "^1.1.1", "@types/styletron-standard": "^2.0.2", "@typescript-eslint/eslint-plugin": "4.28.2", "@typescript-eslint/parser": "4.28.2", "axios-mock-adapter": "^1.22.0", "codelyzer": "^6.0.0", "eslint": "^7.26.0", "jasmine-core": "~3.6.0", "jasmine-spec-reporter": "~5.0.0", "karma": "^6.4.1", "karma-chrome-launcher": "~3.1.0", "karma-coverage-istanbul-reporter": "~3.0.2", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "^1.5.0", "karma-junit-reporter": "^1.2.0", "karma-selenium-grid-launcher": "^0.3.0", "loader-utils": "^2.0.4", "npm-run-all": "^4.1.5", "protractor": "~7.0.0", "protractor-jasmine2-screenshot-reporter": "^0.5.0", "sass": "~1.79.6", "stylelint": "^10.1.0", "ts-node": "~7.0.0", "tslint": "~6.1.0", "typescript": "~4.5.5"}}