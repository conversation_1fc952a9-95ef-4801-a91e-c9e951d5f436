import { Component, OnDestroy, OnInit } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { Subject, timer } from 'rxjs';
import { filter, map, mergeMap, takeUntil } from 'rxjs/operators';

import { SupportLanguage } from '@assets/i18n/support-language';

import { LocalStorage, MessageBus, StatCollector, StyleManager } from '@app/core/services';
import { AppTheme, THEMES } from '@tigergraph/tools-models/theme';

/**
 * App root component.
 *
 * @export
 */
@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss']
})
export class AppComponent implements OnInit, OnDestroy {
  private destroyed = new Subject<void>();

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private collector: StatCollector,
    private titleService: Title,
    private translateService: TranslateService,
    private localStorage: LocalStorage,
    private bus: MessageBus,
    private styleManager: StyleManager
  ) { }

  ngOnInit() {
    this.setBroadcastInterval();
    this.setI18n();

    this.router.events
      .pipe(
        takeUntil(this.destroyed),
        filter(event => event instanceof NavigationEnd), // Change title only when navigation ends.
        map(() => this.route), // Switch to observing currently activated route.
        map(route => {
          // Traverse the tree to find the last activated route.
          while (route.firstChild) {
            route = route.firstChild;
          }
          return route;
        }),
        filter(route => route.outlet === 'primary'), // Use the main outlet.
        mergeMap(route => route.data) // Convert all data observable streams into one observable stream.
      )
      .subscribe(data => this.titleService.setTitle(`${data.title} - Admin Portal`));

    this.route.queryParams
      .pipe(takeUntil(this.destroyed))
      .subscribe(params => {
        const language: string = params.language;
        const theme: string = params.theme;
        this.handleTranslate(language);
        this.handleThemeChange(theme);
      });
    window.addEventListener('message', (event) => {
      if (
        !['https://tgcloud.io', 'https://classic.tgcloud-dev.com', 'http://localhost:4201',
          'https://savanna.tgcloud.io', 'https://portal.tgcloud-dev.com']
          .some(url => event.origin.startsWith(url))
        ) {
          return;
      }

      if (event.data && event.data.detail) {
        this.handleTranslate(event.data.detail.language);
        this.handleThemeChange(event.data.detail.theme);
      }
    }, false);
  }

  ngOnDestroy() {
    this.destroyed.next();
    this.destroyed.complete();
  }

  /**
   * Get live data every minute
   */
  setBroadcastInterval() {
    timer(0, 60 * 1000)
      .pipe(
        takeUntil(this.destroyed)
      )
      .subscribe(() => {
        if (this.router.url.includes('/dashboard')) {
          this.collector.getLiveData();
        }
      });
  }

  /**
   * Initialize the i18n.
   *
   */
  setI18n() {
    const supportLanguage = new SupportLanguage().getSupportLanguage();
    const languageList = supportLanguage.map(language => language.key);
    this.translateService.addLangs(languageList);
    this.translateService.setDefaultLang('en-US');
    this.translateService.use('en-US');
  }

  /**
   * Handle logic to translate page.
   *
   * @param language Language code of the language that the page needs to be translated to.
   */
   handleTranslate(language: string) {
    if (!language) {
      return;
    }

    this.translateService.use(language);
    this.localStorage.setItem('Language', language, true);
  }

  /**
   * Handle logic to change page theme.
   *
   * @param theme Theme of the page, light or dark.
   */
  handleThemeChange(theme: string) {
    if (!theme) {
      return;
    }

    const currentTheme = THEMES.find(t => t.isDark === (theme === 'dark'));
    this.bus.to<AppTheme>('AppTheme').next(currentTheme);
    this.localStorage.setItem('Theme', currentTheme, true);
    this.styleManager.removeStyle('theme');
    this.styleManager.setStyle('theme', `assets/themes/${currentTheme.href}`);
  }
}
