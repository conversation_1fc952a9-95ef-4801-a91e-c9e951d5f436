import { useStyletron } from "baseui";
import {
  StyledTable,
  StyledHead,
  StyledHeadCell,
  StyledBody,
  StyledRow,
  StyledCell,
  TableProps,
} from 'baseui/table';
import React, { useState } from "react";
import ChevronDown from 'baseui/icon/chevron-down';
import ChevronUp from 'baseui/icon/chevron-up';

import { ErrorDisplay } from "./error";
import { StyleObject } from "styletron-standard";

function ServiceRow({ customdata }: { customdata: CustomTableDataType }) {
  const [css] = useStyletron();

  const [showSubRows, setShowSubRows] = useState(false);
  return (
    <>
      <StyledRow
        className={css({
          boxShadow: "inset 0px -1px 0px #D4DADF",
          height: "56px"
        })}
      >
        {customdata.rowInfo.map((serviceCol, index) => (
          <StyledCell
            key={`service_${customdata.rowInfo[0]}_col_${index}`}
          >{
              index === 0 ?
                <div
                  onClick={() => { setShowSubRows(!showSubRows); }}
                  className={css({
                    cursor: "pointer",
                    display: "flex",
                    alignItems: "center",
                  })}
                >
                  {customdata.subRowsInfo && customdata.subRowsInfo.length ? (showSubRows ?
                    <ChevronUp
                      overrides={{
                        Svg: {
                          style: {
                            verticalAlign: "middle"
                          }
                        }
                      }}
                      size={24}
                    /> :
                    <ChevronDown
                      overrides={{
                        Svg: {
                          style: {
                            verticalAlign: "middle"
                          }
                        }
                      }}
                      size={24}
                    />) : null
                  }
                  {serviceCol}
                </div> :
                <div>{serviceCol}</div>
            }</StyledCell>
        ))}
      </StyledRow>
      {showSubRows && customdata.subRowsInfo && customdata.subRowsInfo.length ? customdata.subRowsInfo.map((subRowInfo) => (
        <StyledRow
          className={css({
            boxShadow: "inset 0px -1px 0px #D4DADF",
            height: "56px"
          })}
          key={`node_${subRowInfo[0]}}`}
        >
          {subRowInfo.map((nodeCol, index1) => (
            <StyledCell
              key={`node_${subRowInfo[0]}_col_${index1}`}
            >{nodeCol}</StyledCell>
          ))}
        </StyledRow>
      )) : null}
    </>
  );
}

type rowType = Array<JSX.Element | string>;

export type CustomTableDataType = {
  rowInfo: rowType,
  subRowsInfo?: Array<rowType>
};

export default function CustomTable(props: TableProps & { style?: StyleObject,isError?: boolean, customdata: Array<CustomTableDataType> }) {
  const [css] = useStyletron();
  const tempProps = { ...props };
  delete tempProps.isError;

  return (
    <div
      className={css({
        height: "448px",
        ...props.style,
      })}
    >
      <StyledTable
        {...tempProps}
        className={css({
          border: "0",
        })}
      >
        <StyledHead>
          {props.columns.map((col, index) => (
            <StyledHeadCell
              className={css({
                color: '#3F5870',
                border: "0",
                backgroundColor: "#F4F8FD",
                weight: 600,
                fontSize: "16px",
                lineHeight: "24px",
              })}
              key={`table_head_${index}`}>{col}</StyledHeadCell>
          ))}
        </StyledHead>
        <StyledBody
          className={css({
            overflow: "hidden",
            height: "10px",
            "::-webkit-scrollbar": {
              width: "8.12px",
              background: "opacity",
            },
            "::-webkit-scrollbar-thumb": {
              borderRadius: "3px",
              height: "149px",
              backgroundColor: "#D9D9D9",
            }
          })}
        >
          {props.customdata.map((customdata, index) => (
            <ServiceRow key={`service_row_${index}`} customdata={customdata} />
          ))}
        </StyledBody>
        {props.isError ?
          (<ErrorDisplay className={css({
            display: "contents"
          })} error={"It seems that some errors occur."} />) : null
        }
      </StyledTable>
    </div>
  );
}
