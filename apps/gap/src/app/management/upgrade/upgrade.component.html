<mat-card aria-label="Upgrade">
  <!-- Card Header -->
  <div class="title">
      <mat-icon class="title-icon" matListIcon>cloud_download</mat-icon>
      <span>{{ "Upgrade TigerGraph Suite" | translate }}</span>
  </div>
  <!-- End of Card Header -->

  <mat-divider></mat-divider>


  <mat-card-content>
    <div class="start-hint">
      <p>{{ "We allow you to upgrade your entire TigerGraph Suite (GraphStudio, Insights, AdminPortal, GSQL Shell and GraphQL) without re-install TigerGraph. You can choose one of the following options:" | translate }}</p>

      <p>1. {{ "Manual Upgrade: Perform a manual installation by uploading a downloaded TigerGraph Suite package from https://dl.tigergraph.com/download.html." | translate }}</p>

      <p>2. {{ "Online Check for Upgrade: Check if there is a newer TigerGraph Suite version available and upgrade automatically." | translate }}</p>
    </div>
  </mat-card-content>

  <mat-card-actions align="end" *ngIf="isSuperUser">
    <input type="file" hidden #upload (change)="uploadFile($event.target.files)">
    <button
      mat-button
      color="primary"
      (click)="upload.click()">
      {{ "Manual Upgrade" | translate }}</button>
    <button
      mat-button
      color="primary"
      (click)="remoteUpgradeCheck()">
      {{ "Online Check for Upgrade" | translate }}</button>
  </mat-card-actions>
</mat-card>


<ng-template #uploadFilePopover>
  <mat-dialog-content>
    <mat-icon class="setting-icon animate-circle">settings</mat-icon>
    <p class="text">Uploading TigerGraph Suite Package</p>
    <mat-progress-bar color="primary" mode="determinate" [value]="upgradeProgress$ | async"></mat-progress-bar>
    <p class="text">{{ upgradeProgress$ | async }}%</p>
  </mat-dialog-content>

  <mat-dialog-actions align="center">
    <button color="primary" mat-button mat-dialog-close (click)="cancelUpload()">
      {{ 'CANCEL' | translate }}
    </button>
  </mat-dialog-actions>
</ng-template>

<ng-template #localUpgradeCheckPopover>
  <mat-dialog-content>
    <mat-icon class="setting-icon animate-circle">settings</mat-icon>
    <p class="text">Processing uploaded files</p>
    <pre class="hint">{{upgradeProgressMessage$ | async}}</pre>
  </mat-dialog-content>
</ng-template>


<ng-template #upgradeInformationPopover>
  <mat-dialog-content>
    <mat-icon class="setting-icon animate-circle">settings</mat-icon>
    <p class="text" *ngIf="(isUpgrading$ | async) === false">Are you sure you want to proceed with the upgrade of TigerGraph Suite?</p>
    <p class="text" *ngIf="isUpgrading$ | async">Upgrade in progress...</p>
    <div class="version-info">
      <div>
        <p>Current Version</p>
        <p>{{ currentVersion }}</p>
      </div>
      <mat-icon>keyboard_arrow_right</mat-icon>
      <div [ngClass]="{'new-version': isTargetVersionNewer(), 'old-version': isTargetVersionOlderOrEqual()}">
        <p>{{ isTargetVersionNewer() ? 'New' : 'Old' }} Version</p>
        <p>{{ targetVersion }}</p>
      </div>
    </div>
    <p class="hint">To begin the upgrade process, simply click the "Upgrade" button. Please keep in mind that the upgrade process may take approximately 1-2 minutes to complete, so kindly avoid turning off your cluster during this time.</p>
    <pre class="hint">{{upgradeProgressMessage$ | async}}</pre>
  </mat-dialog-content>

  <mat-dialog-actions align="center">
    <button color="primary" mat-button mat-dialog-close [disabled]="isUpgrading$ | async">
      {{ 'CANCEL' | translate }}
    </button>
    <button mat-button color="accent" (click)="upgrade()" [disabled]="isUpgrading$ | async">
      {{ isTargetVersionNewer() ? 'Upgrade' : 'Downgrade' }}
    </button>
  </mat-dialog-actions>
</ng-template>

<ng-template #remoteUpgradeCheckPopover>
  <mat-dialog-content>
    <mat-icon class="setting-icon animate-circle">settings</mat-icon>
    <p class="text">Checking for the available upgrades for TigerGraph Suite...</p>
    <pre class="hint">{{upgradeProgressMessage$ | async}}</pre>
  </mat-dialog-content>

  <mat-dialog-actions align="center">
    <button color="primary" mat-button mat-dialog-close>
      {{ 'CANCEL' | translate }}
    </button>
  </mat-dialog-actions>
</ng-template>

<ng-template #noNewVersionPopover>
  <mat-dialog-content>
    <mat-icon class="setting-icon">settings</mat-icon>
    <p class="text">You are currently using the most recent version of TigerGraph Suite.</p>
  </mat-dialog-content>

  <mat-dialog-actions align="center">
    <button color="primary" mat-button mat-dialog-close>
      {{ 'CANCEL' | translate }}
    </button>
  </mat-dialog-actions>
</ng-template>

<ng-template #newVersionPopover>
  <mat-dialog-content>
    <mat-icon class="setting-icon" [ngClass]="{'animate-circle': (isUpgrading$ | async)}">settings</mat-icon>
    <p class="text">A newer version of TigerGraph Suite is now available for upgrade.</p>
    <p class="text" *ngIf="(isUpgrading$ | async) === false">Are you sure you want to proceed with the upgrade of TigerGraph Suite?</p>
    <p class="text" *ngIf="isUpgrading$ | async">Upgrade in progress...</p>
    <div class="version-info">
      <div>
        <p>Current Version</p>
        <p>{{ currentVersion }}</p>
      </div>
      <mat-icon>keyboard_arrow_right</mat-icon>
      <div [ngClass]="{'new-version': isTargetVersionNewer(), 'old-version': isTargetVersionOlderOrEqual()}">
        <p>{{ isTargetVersionNewer() ? 'New' : 'Old' }} Version</p>
        <p>{{ targetVersion }}</p>
      </div>
    </div>
    <p class="hint">To begin the upgrade process, simply click the "Upgrade" button. Please keep in mind that the upgrade process may take approximately 1-2 minutes to complete, so kindly avoid turning off your cluster during this time.</p>
    <pre class="hint">{{upgradeProgressMessage$ | async}}</pre>
  </mat-dialog-content>

  <mat-dialog-actions align="center">
    <button color="primary" mat-button mat-dialog-close [disabled]="isUpgrading$ | async">
      {{ 'CANCEL' | translate }}
    </button>
    <button mat-button color="accent" (click)="upgrade()" [disabled]="isUpgrading$ | async">
      {{ 'Upgrade' | translate }}
    </button>
  </mat-dialog-actions>
</ng-template>
