import { Component, OnD<PERSON>roy, TemplateRef, ViewChild } from '@angular/core';
import { AuthService } from '@app/core/services';
import { BehaviorSubject, Subject} from 'rxjs';
import { UpgradeService } from './upgrade.service';
import { takeUntil } from 'rxjs/operators';
import { HelperFunctions } from '@app/shared/utils';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { FileUploadService } from '@app/core/services/file-upload.service';
import { PostMessageService } from '@app/core/services/post-message.service';
import { compare } from 'compare-versions';

const dialogConfig: MatDialogConfig = {
  width: '648px',
  disableClose: true
};

@Component({
  selector: 'app-upgrade',
  templateUrl: './upgrade.component.html',
  styleUrls: ['./upgrade.component.scss']
})
export class UpgradeComponent implements OnD<PERSON>roy {
  @ViewChild('uploadFilePopover') uploadFilePopover: TemplateRef<any>;
  @ViewChild('localUpgradeCheckPopover') localUpgradeCheckPopover: TemplateRef<any>;
  @ViewChild('upgradeInformationPopover') upgradeInformationPopover: TemplateRef<any>;
  @ViewChild('remoteUpgradeCheckPopover') remoteUpgradeCheckPopover: TemplateRef<any>;
  @ViewChild('noNewVersionPopover') noNewVersionPopover: TemplateRef<any>;
  @ViewChild('newVersionPopover') newVersionPopover: TemplateRef<any>;

  targetVersion: string;
  currentVersion: string;
  isUpgrading$ = new BehaviorSubject(false);
  upgradeProgressMessage$ = new BehaviorSubject<string>('');
  upgradeProgress$ = new BehaviorSubject<number>(0);
  progressMessage = '';

  private destroyed = new Subject<void>();

  constructor(
    private authService: AuthService,
    private upgradeService: UpgradeService,
    private dialog: MatDialog,
    private snackBar: MatSnackBar,
    private fileUploadService: FileUploadService,
    private postMessageService: PostMessageService,
  ) { }

  ngOnDestroy() {
    this.destroyed.next();
    this.destroyed.complete();
  }

  /** Check whether the current user is super user. */
  get isSuperUser(): boolean {
    return this.authService.isSuperUser();
  }

  /** Check if target version is greater than current version. */
  isTargetVersionNewer(): boolean {
    if (!this.targetVersion || !this.currentVersion) {
      return false;
    }
    return compare(this.targetVersion, this.currentVersion, '>');
  }

  /** Check if target version is less than or equal to current version. */
  isTargetVersionOlderOrEqual(): boolean {
    if (!this.targetVersion || !this.currentVersion) {
      return false;
    }
    return compare(this.targetVersion, this.currentVersion, '<=');
  }

  async remoteUpgradeCheck() {
    this.dialog.open(this.remoteUpgradeCheckPopover, dialogConfig);

    try {
      let resSuccess = false;
      for await (const eventData of this.upgradeService.remoteUpgradeCheck()) {
        try {
          const versionInfo = JSON.parse(eventData);
          if (versionInfo.target !== undefined && versionInfo.current !== undefined) {
            resSuccess = true;

            this.targetVersion = versionInfo.target;
            this.currentVersion = versionInfo.current;
            if (compare(this.targetVersion, this.currentVersion, '<=')) {
              this.dialog.closeAll();
              this.dialog.open(this.noNewVersionPopover, dialogConfig);
            } else {
              this.dialog.closeAll();
              this.dialog.open(this.newVersionPopover, dialogConfig);
            }
          }
        } catch (e) {
          // do nothing
        }
      }

      if (!resSuccess) {
        throw new Error("Failed to upgrade Tools.");
      }
    }  catch (e) {
      this.isUpgrading$.next(false);
      this.dialog.closeAll();
      HelperFunctions.notifyError(e, 'Error', this.dialog);
      this.dialog.afterAllClosed
        .pipe(takeUntil(this.destroyed))
        .subscribe(() => {
          window.location.reload();
        });
    }
  }

  async upgrade() {
    this.isUpgrading$.next(true);
    try {
      for await (const eventData of this.upgradeService.upgrade()) {
        this.progressMessage += eventData + '\n';
        this.upgradeProgressMessage$.next(this.progressMessage);
      }
      this.snackBar.open(
        'Tools has been Upgraded! The window will reload after this message disappears.',
        'DISMISS',
        { duration: 3000 }
      ).afterDismissed().pipe(takeUntil(this.destroyed)).subscribe(() => {
        window.location.reload();
      });
    } catch (e) {
      this.isUpgrading$.next(false);
      HelperFunctions.notifyError(e, 'Error', this.dialog);
      this.dialog.afterAllClosed
        .pipe(takeUntil(this.destroyed))
        .subscribe(() => {
          window.location.reload();
        });
    }
  }

  uploadFile(files: FileList) {
    if (files.length === 0) {
      return;
    }

    if (files[0].type !== "application/x-gzip") {
      HelperFunctions.notifyError(new Error('Please upload a valid package.'), 'Error', this.dialog);
      return;
    }

    this.handleFileUploadResponse(files[0].name);
    this.fileUploadService.upload(files[0]);
  }

  cancelUpload() {
    this.fileUploadService.cancel();
  }

  private handleFileUploadResponse(fileName: string) {
    this.fileUploadService.response$
      .pipe(
        takeUntil(this.destroyed),
      )
      .subscribe(async response => {
        switch (response.status) {
          case 'oneSingleFileComplete':
            this.dialog.closeAll();
            await this.localUpgradeChecke(fileName);
            break;
          case 'cancel':
            this.snackBar.open(
              'Upload file was canceled successfully',
              'DISMISS',
              { duration: 3000 }
            );
            break;
          case 'error':
            this.dialog.closeAll();
            HelperFunctions.notifyError(new Error(response.message), 'Error', this.dialog);
            this.dialog.afterAllClosed
              .pipe(takeUntil(this.destroyed))
              .subscribe(() => {
                window.location.reload();
              });
            break;
          case 'uploadStart':
            this.dialog.open(this.uploadFilePopover, dialogConfig);
            break;
          case 'progress':
            this.upgradeProgress$.next(response.progress);
            break;
        }
      });
  }

  private async localUpgradeChecke(fileName: string) {
    this.dialog.open(this.localUpgradeCheckPopover, dialogConfig);

    try {
      let resSuccess = false;
      for await (const eventData of this.upgradeService.localUpgradeChecke(fileName)) {
        try {
          const versionInfo = JSON.parse(eventData);
          if (versionInfo.target !== undefined && versionInfo.current !== undefined) {
            resSuccess = true;

            this.targetVersion = versionInfo.target;
            this.currentVersion = versionInfo.current;
            this.showUpgradeInformation();
          }
        } catch (e) {
          this.progressMessage += eventData + '\n';
          this.upgradeProgressMessage$.next(this.progressMessage);
        }
      }

      if (!resSuccess) {
        throw new Error("Failed to upgrade Tools.");
      }
    } catch (e) {
      this.dialog.closeAll();
      HelperFunctions.notifyError(e, 'Error', this.dialog);
      this.dialog.afterAllClosed
        .pipe(takeUntil(this.destroyed))
        .subscribe(() => {
          window.location.reload();
        });
    }
  }

  private showUpgradeInformation() {
    this.dialog.closeAll();
    this.dialog.open(this.upgradeInformationPopover, dialogConfig);
  }
}
