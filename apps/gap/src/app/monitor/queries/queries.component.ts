import { HttpErrorResponse } from '@angular/common/http';
import { Component, OnDestroy, OnInit } from '@angular/core';
import * as moment from 'moment';
import { catchError, finalize, switchMap, takeUntil } from 'rxjs/operators';
import { flattenDeep } from 'lodash';
import { BehaviorSubject, EMPTY, forkJoin, Subject } from 'rxjs';

import { DialogComponent, DialogData } from '@app/core/components/dialog';
import { AuthService } from '@app/core/services';
import { GLOBAL_GRAPH_NAME } from '@app/shared/utils';
import { CurrentQuery } from './queries-current/queries-current.component';
import { HistoryQueriesInfo, QueriesService, AllGraphs, AllNodes } from './queries.service';
import { SortOption } from './queries-history/queries-history.component';
import { MatDialog } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatTabChangeEvent } from '@angular/material/tabs';
import '../../config/charts';

enum Pages {
  overview = 'Overview',
  current = 'Current Query Performance',
  history = 'Historic Query Performance'
}

@Component({
  selector: 'app-queries',
  templateUrl: './queries.component.html',
  styleUrls: ['./queries.component.scss']
})
export class QueriesComponent implements OnInit, OnDestroy {
  pages = Object.values(Pages);
  Pages = Pages;
  currentPage = Pages.overview;
  private destroyed = new Subject<void>();
  loading = new BehaviorSubject(true);

  graphs: string[];
  currentGraph: string;
  nodes: string[];
  currentNode: string;
  startTime: moment.Moment;
  endTime: moment.Moment;
  searchText = '';
  maxConcurrentQueries: number;
  currentSortOption: SortOption;

  currentQueries: CurrentQuery[] = [];
  historyQueries: HistoryQueriesInfo;

  constructor(
    private cmpService: QueriesService,
    private authService: AuthService,
    private dialog: MatDialog,
    private snackBar: MatSnackBar
  ) { }

  ngOnInit() {
    this.setTime();
    this.fetchData();
  }

  ngOnDestroy() {
    this.destroyed.next();
    this.destroyed.complete();
  }

  setTime() {
    const currentTime = new Date();
    this.startTime = moment(currentTime).add(-14, 'minutes'),
    this.endTime = moment(currentTime).add(1, 'minutes');
  }

  changePage(event: MatTabChangeEvent) {
    this.currentPage = Object.values(Pages)[event.index];
    if (this.currentPage !== Pages.history) {
      this.searchText = '';
      this.currentSortOption = SortOption.QPS;
    }
  }

  refresh() {
    this.setTime();
    this.fetchData();
  }

  handleMoreCurrent() {
    this.currentPage = Pages.current;
  }

  handleHistoryCurrent(query: CurrentQuery) {
    this.searchText = query.query;
    this.currentPage = Pages.history;
  }

  handleChangeNode(node: string) {
    this.currentNode = node;
    this.fetchData();
  }

  handleChangeGraph(graph: string) {
    this.currentGraph = graph;
  }

  changeLoading(loading: boolean) {
    this.loading.next(loading);
  }

  handleAbortCurrentQuery(query: CurrentQuery) {
    this.loading.next(true);
    this.cmpService.abortCurrentQuery(query.graph, query.requestID)
      .pipe(
        takeUntil(this.destroyed),
        finalize(() => this.loading.next(false))
      )
      .subscribe(allNodesResponse => {
        const abortQueries = allNodesResponse.reduce((prev, res) => (res.results?.[0]?.aborted_queries || []).concat(prev), []);
        if (abortQueries.length > 0) {
          this.snackBar.open(
            `Successfully abort query ${query.query}`, 'DISMISS', { duration: 3000 }
          );
          this.fetchData();
          return;
        }

        const unknownRequestIDs = allNodesResponse.reduce((prev, res) => (res.results?.[0]?.unknown_requestid || []).concat(prev), []);
        if (unknownRequestIDs.length > 0) {
          this.fetchData();
          this.catchError(`Query ${query.query} not running`, 'Error');
          return;
        }

        this.catchError(`Failed to abort query ${query.query}`, 'Error');
      });
  }

  handleMoreQPS() {
    this.currentSortOption = SortOption.QPS;
    this.currentPage = Pages.history;
  }

  handleMoreTimeout() {
    this.currentSortOption = SortOption.timeout;
    this.currentPage = Pages.history;
  }

  handleMoreLatency() {
    this.currentSortOption = SortOption.latency;
    this.currentPage = Pages.history;
  }

  private fetchData() {
    this.loading.next(true);
    // update graph
    this.graphs = [
      AllGraphs,
      ...this.authService.getGraphList().filter(graph => graph !== GLOBAL_GRAPH_NAME)
    ];
    if (!this.currentGraph || !this.graphs.includes(this.currentGraph)) {
      this.currentGraph = this.graphs[0];
    }
    // update node
    this.cmpService.getMaxConcurrentQueries()
      .pipe(
        takeUntil(this.destroyed),
        switchMap(max => {
          this.maxConcurrentQueries = max;
          return this.cmpService.getNodeList();
        }),
        switchMap(nodes => {
          this.nodes = [AllNodes, ...nodes];
          if (!this.currentNode || !this.nodes.includes(this.currentNode)) {
            this.currentNode = this.nodes[0];
          }
          return this.currentNode === AllNodes ?
                forkJoin(this.nodes.filter(node => node !== AllNodes).map(node => this.cmpService.getCurrentQueries(node))):
                this.cmpService.getCurrentQueries(this.currentNode);
        }),
        switchMap(currentQueries => {
          this.currentQueries = flattenDeep(currentQueries).map(query => {
            const tempQueryName = query.url.split('/');
            return {
              graph: tempQueryName[tempQueryName.length - 2],
              query: query.url.split('?')[0],
              node: query.node,
              elapsedTime: (query.elapsedTime / 1000).toString(),
              timeout: (
                (Date.parse(query.expirationTime.split('.')[0]) - Date.parse(query.startTime.split('.')[0])) / 1000
              ).toString(),
              user: query.user,
              requestID: query.requestid,
              startTime: new Date(moment(query.startTime.split('.')[0])
                        .add(-new Date().getTimezoneOffset(), 'minutes')
                        .unix() * 1000)
                        .toLocaleString()
            };
          });
          return this.cmpService.getHistoryQueriesData(this.startTime.unix(), this.endTime.unix());
        }),
        catchError(err => this.catchError(err, 'Error')),
        finalize(() => this.loading.next(false))
      )
      .subscribe((historyQueriesData: HistoryQueriesInfo) => {
        this.historyQueries = historyQueriesData;
      });
  }

  private catchError(error: Error | HttpErrorResponse | string, title: string) {
    if (error instanceof Error) {
      this.handleError([error.message], title);
    } else if (error instanceof HttpErrorResponse) {
      this.handleError([error.error.toString()], error.statusText);
    } else {
      this.handleError([error], title);
    }

    return EMPTY;
  }

  private handleError(messages: string[], title = 'Error') {

    this.dialog.open<DialogComponent, DialogData, void>(
      DialogComponent,
      {
        ariaLabel: `${title} dialog`,
        data: { title, messages }
      }
    );
  }
}
