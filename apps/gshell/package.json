{"name": "gsql_interactive_web_shell", "private": true, "version": "0.0.0", "scripts": {"dev": "vite", "dev:cloud": "vite --mode cloud", "build": "tsc && vite build", "build:cloud": "tsc && vite build --mode cloud", "preview": "vite preview", "prebuild:cloud": "cp ./index-cloud.html ./index.html", "lint:fix": "eslint ./src --ext .jsx,.js,.ts,.tsx --quiet --fix --ignore-path ./.gitignore", "lint:format": "prettier  --loglevel warn --write \"./**/*.{js,jsx,ts,tsx,css,md,json}\" ", "lint:check": "eslint ./src --ext .jsx,.js,.ts,.tsx", "lint": "yarn lint:format && yarn lint:check && tsc", "test": "jest", "prettier:check": "prettier . --check", "type-check": "tsc", "prepare": "cd ../../ && husky install", "cypress:open": "cypress open"}, "dependencies": {"@antv/graphin": "^2.6.6", "@antv/graphin-components": "^2.4.0", "@antv/graphin-icons": "^1.0.0", "@babel/traverse": "7.23.2", "@react-nano/use-event-source": "^0.12.0", "@tigergraph/app-ui-lib": "0.3.35", "@tigergraph/cytoscape-edgehandles": "^4.0.1", "@tigergraph/tools-models": "1.1.7", "@tigergraph/tools-ui": "0.2.3", "@types/codemirror": "^5.60.5", "@types/lodash": "^4.14.182", "@types/styletron-engine-atomic": "^1.1.1", "@types/styletron-react": "^5.0.3", "@types/styletron-standard": "^2.0.2", "@uiw/react-codemirror": "3.2.7", "ahooks": "^3.3.0", "baseui": "^11.2.0", "classnames": "^2.3.1", "codemirror": "^5.65.2", "csv-exportor": "^1.0.2", "final-form": "^4.20.7", "history": "^5.3.0", "json5": "^2.2.2", "lodash": "^4.17.21", "nanoid": "^3.3.4", "react": "^18.3.1", "react-dom": "^18.3.1", "react-colorful": "^5.6.1", "react-final-form": "^6.5.9", "react-query": "^3.34.19", "react-router-dom": "^6.3.0", "styletron-engine-atomic": "^1.4.8", "styletron-engine-monolithic": "^0.0.6", "styletron-react": "^6.0.2"}, "devDependencies": {"@jest/globals": "^29.4.3", "@types/jest": "^29.4.0", "@types/react": "18.2.22", "@types/react-dom": "18.2.22", "@typescript-eslint/eslint-plugin": "^5.23.0", "@typescript-eslint/parser": "^5.23.0", "@vitejs/plugin-react": "^1.3.0", "autoprefixer": "^10.4.7", "axe-core": "^4.4.2", "cypress": "^9.6.1", "cypress-axe": "^0.14.0", "cypress-file-upload": "^5.0.8", "cypress-wait-until": "^1.7.2", "eslint": "^8.15.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-cypress": "^2.12.1", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.5.1", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-react": "^7.29.4", "eslint-plugin-react-hooks": "^4.5.0", "eslint-plugin-simple-import-sort": "^7.0.0", "husky": ">=6", "jest": "^29.4.3", "lint-staged": ">=10", "postcss": "^8.4.13", "prettier": "^2.6.2", "tailwindcss": "^3.0.24", "ts-jest": "^29.0.5", "typescript": "^4.6.3", "vite": "^4.4.8", "vite-plugin-svgr": "^2.1.0"}, "resolutions": {"d3-color": "3.1.0"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --fix", "prettier --write", "git add"], "*.{css,scss}": ["prettier --write", "git add"]}}