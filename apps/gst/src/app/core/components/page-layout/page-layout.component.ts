import {
  Compo<PERSON>,
  <PERSON><PERSON><PERSON>roy,
  OnInit,
  ViewChild,
  TemplateRef,
  AfterViewInit,
} from '@angular/core';
import { Breakpoints, BreakpointObserver } from '@angular/cdk/layout';
import { FormGroup } from '@angular/forms';
import { MatCheckboxChange } from '@angular/material/checkbox';
import {
  MatDialog,
  MatDialogConfig,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatIconRegistry } from '@angular/material/icon';
import { MatSnackBar } from '@angular/material/snack-bar';
import { Router } from '@angular/router';
import {
  Observable,
  of,
  Subject,
  timer,
  BehaviorSubject,
  forkJoin,
  combineLatest,
} from 'rxjs';
import {
  catchError,
  delay,
  map,
  mapTo,
  takeUntil,
  tap,
  finalize,
  switchMap,
  take,
  distinctUntilChanged,
} from 'rxjs/operators';
import { TranslateService } from '@ngx-translate/core';

import { DialogData, DialogComponent } from '@app/core/components/dialog';
import {
  AuthService,
  CacheService,
  LicenseService,
  MessageBus,
  StyleManager,
} from '@app/core/services';
import { animations } from '@app/shared/animations';
import { AppTheme, THEMES } from '@tigergraph/tools-models/theme';
import { TypeCheckedStatus } from '@tigergraph/tools-models/topology';
import { SidenavStatusService } from '@app/shared/services';
import {
  ACCESS_PRIVILEGES,
  GLOBAL_GRAPH_NAME,
  GSQLPrivilege,
  PageKey,
  PAGE_NAME,
  PAGE_INDEX,
} from '@tigergraph/tools-models/utils';
import { HelperFunctions } from '@app/shared/utils';
import { License } from '@tigergraph/tools-models/license';

import { PageLayoutService } from './page-layout.service';
import { environment } from '@environments/environment';

interface ToolsHeadProps {
  license: object;
  userName: string;
  appName: string;
  appIcon: string;
  innerApp: string;
  isDark: boolean;
  language: string;
}

interface CloudHeaderProps {
  isTools: boolean;
  cloudSliderId: string;
  currentTool: string;
  handleChangeThemeCallback: () => void;
  handleChangeLanguageCallback: () => void;
}

interface CloudSliderProps {
  cloud: boolean;
  open: boolean;
}

/**
 * Component to render the page layout used by feature components.
 * The layout includes a header and a sidenav.
 * NOTE: this component is ONLY used by router.
 *
 * @export
 * @class PageLayoutComponent
 */
@Component({
  selector: 'app-page-layout',
  templateUrl: './page-layout.component.html',
  styleUrls: ['./page-layout.component.scss'],
  providers: [PageLayoutService],
  animations: [animations.indicatorRotate],
})
export class PageLayoutComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild('createGraphPopupWindow', { static: true })
  createGraphPopupWindow: TemplateRef<any>;

  userName: string;

  GLOBAL_GRAPH_NAME = GLOBAL_GRAPH_NAME;

  license$: Observable<License>;
  themeHandler: any;
  languageHandler: any;

  cloudEnv = environment.cloud;

  toggleCheckStatus = HelperFunctions.toggleCheckStatus;

  hideNav = localStorage.getItem('TG_HideNav') === 'true' ? true : false;

  closeNav = false;
  closeNav$ = this.breakpointObserver
    .observe([Breakpoints.Medium, Breakpoints.Large, Breakpoints.XLarge])
    .pipe(
      map((result) => !result.matches),
      tap((matches) => {
        this.closeNav = matches;
        this.bus.to<boolean>('AppReflow').next(matches);
      })
    );

  homeNav = {
    icon: 'home',
    label: 'Home',
    routerLink: '/home',
  };
  navs = [
    {
      id: 'design-schema',
      icon: 'schema',
      label: 'Design Schema',
      routerLink: '/schema-designer',
      privileges: ACCESS_PRIVILEGES[PageKey.SchemaDesigner],
    },
    {
      id: 'load-data',
      icon: 'loading',
      label: 'Load Data',
      routerLink: '/loading-builder',
      privileges: ACCESS_PRIVILEGES[PageKey.LoadingBuilder],
    },
    {
      id: 'explore-graph',
      icon: 'explore',
      label: 'Explore Graph',
      routerLink: '/graph-explorer',
      privileges: ACCESS_PRIVILEGES[PageKey.GraphExplorer],
    },
    {
      id: 'write-queries',
      icon: 'gsql',
      label: 'Write Queries',
      routerLink: '/query-editor',
      privileges: ACCESS_PRIVILEGES[PageKey.QueryEditor],
    },
    {
      id: 'actions',
      icon: 'settings',
      label: 'Actions',
      routerLink: '/actions',
      privileges: ACCESS_PRIVILEGES[PageKey.Actions],
    },
  ];

  loading = new BehaviorSubject<boolean>(false);
  graphListToggle = false;

  isDarkTheme = true;

  newGraphName = '';
  createGraphForm: FormGroup;
  globalVertexTypes: TypeCheckedStatus[];
  globalEdgeTypes: TypeCheckedStatus[];
  sidenavStatusChanged = new Subject();

  private hasUnsavedChanges = false;

  private createGraphPopupWindowRef: MatDialogRef<TemplateRef<any>>;

  private destroyed = new Subject<void>();

  constructor(
    private breakpointObserver: BreakpointObserver,
    private matIconRegistry: MatIconRegistry,
    private router: Router,
    private dialog: MatDialog,
    private snackBar: MatSnackBar,
    private authService: AuthService,
    private bus: MessageBus,
    private sidenavStatusService: SidenavStatusService,
    private cmptService: PageLayoutService,
    private licenseService: LicenseService,
    private translateService: TranslateService,
    private cacheService: CacheService,
    private styleManager: StyleManager
  ) {}

  /**
   * Check if an icon is svg format.
   *
   * @param {string} iconName
   * @returns {Observable<boolean>}
   * @memberof PageLayoutComponent
   */
  isSvgIcon(iconName: string): Observable<boolean> {
    return this.matIconRegistry.getNamedSvgIcon(iconName).pipe(
      mapTo(true),
      catchError(() => of(false))
    );
  }

  ngOnInit() {
    // Listen for any unsaved changes.
    this.bus
      .from<boolean>('HasUnsavedChanges')
      .pipe(takeUntil(this.destroyed))
      .subscribe((value) => (this.hasUnsavedChanges = value));

    this.bus
      .from<AppTheme>('AppTheme')
      .pipe(
        takeUntil(this.destroyed),
        // Delay so that value won't be change within a life cycle.
        delay(0),
        map((theme) => theme.isDark)
      )
      .subscribe((isDark) => (this.isDarkTheme = isDark));

    this.license$ = this.licenseService.getLicense();
    this.userName = this.authService.getUsername();

    const toolsHead: ToolsHeadProps & Element =
      document.querySelector('tools-head');
    this.themeHandler = (e: any) => {
      const currentTheme = THEMES.find(
        (t) => t.isDark === e.detail.isDarkTheme
      );
      this.bus.to<AppTheme>('AppTheme').next(currentTheme);
      this.cacheService.setItem('Theme', currentTheme, true);
      this.styleManager.removeStyle('theme');
      this.styleManager.setStyle('theme', `assets/themes/${currentTheme.href}`);
    };
    toolsHead.addEventListener('switchtheme', this.themeHandler);
    this.languageHandler = (e: any) => {
      this.translateService.use(e.detail.language);
      this.cacheService.setItem('Language', e.detail.language, true);
    };
    document.addEventListener('changelanguage', this.languageHandler);
    combineLatest([this.license$, this.bus.from<AppTheme>('AppTheme')])
      .pipe(takeUntil(this.destroyed), delay(0))
      .subscribe(([license, theme]) => {
        toolsHead.license = license;
        toolsHead.userName = this.userName;
        toolsHead.appName = 'GraphStudio';
        toolsHead.appIcon = '/studiotitle.svg';
        toolsHead.innerApp = 'studio';
        toolsHead.isDark = theme.isDark;
        toolsHead.language =
          this.cacheService.getItem('Language', true) || 'en-US';
      });
  }

  ngAfterViewInit() {
    const cloudHeader: CloudHeaderProps & Element =
      document.querySelector('#cloud-header');
    const cloudSlider: CloudSliderProps & Element =
      document.querySelector('#cloud-slider');
    if (cloudHeader && cloudSlider) {
      cloudHeader.isTools = true;
      cloudHeader.cloudSliderId = 'cloud-slider';
      cloudSlider.cloud = false;
      cloudSlider.open = false;
      cloudHeader.currentTool = 'GraphStudio';
      cloudHeader.handleChangeThemeCallback = this.themeHandler;
      cloudHeader.handleChangeLanguageCallback = this.languageHandler;
    }
  }

  ngOnDestroy() {
    const toolsHead: ToolsHeadProps & Element =
      document.querySelector('tools-head');
    toolsHead.removeEventListener('switchtheme', this.themeHandler);
    toolsHead.removeEventListener('changelanguage', this.languageHandler);
    this.destroyed.next();
    this.destroyed.complete();
    this.sidenavStatusChanged.complete();
  }

  /**
   * Toggle sidenav callback.
   *
   * @param {boolean} open
   * @memberof PageLayoutComponent
   */
  toggleSidenav(open: boolean) {
    this.sidenavStatusService.sidenavOpened = open;
    this.sidenavStatusChanged.next();
  }

  toggleHideSideNav() {
    this.hideNav = !this.hideNav;
    localStorage.setItem('TG_HideNav', this.hideNav.toString());
  }

  get useGlobal(): boolean {
    return this.currentGraph === GLOBAL_GRAPH_NAME;
  }

  /**
   * Return current graph name.
   *
   * @readonly
   * @type {string}
   * @memberof PageLayoutComponent
   */
  get currentGraph(): string {
    return this.authService.getCurrentGraph();
  }

  /**
   * Return current user role.
   *
   * @readonly
   * @type {string}
   * @memberof PageLayoutComponent
   */
  get currentRole(): string {
    return HelperFunctions.limitStringDisplayLength(
      this.authService.getUserRole(),
      6,
      6
    );
  }

  showRestOfRoles(): string {
    const roles = this.authService.getUserRoles();
    return roles ? roles.slice(1).join('\n') : '';
  }

  sidenavDisabled(index: number, privileges: GSQLPrivilege[]) {
    if (!this.currentGraph) {
      return true;
    }
    if (
      this.currentGraph === GLOBAL_GRAPH_NAME &&
      index === PAGE_INDEX[PageKey.Actions] &&
      this.authService.hasAllPrivileges(privileges)
    ) {
      return false;
    }
    if (
      this.currentGraph === GLOBAL_GRAPH_NAME &&
      index !== PAGE_INDEX[PageKey.SchemaDesigner] &&
      index !== PAGE_INDEX[PageKey.Actions]
    ) {
      return true;
    }
    const nav = this.navs[index];
    if (
      nav?.id === 'write-queries' &&
      this.authService.hasPermissionToAccessWriteQueriesPage(this.currentGraph)
    ) {
      return false;
    }
    return !this.authService.hasAllPrivileges(privileges);
  }

  hasGlobalAccess(): boolean {
    return this.graphList.includes(GLOBAL_GRAPH_NAME);
  }

  canCreateGraph(): boolean {
    return this.authService.hasPrivilege(
      GSQLPrivilege.WriteSchema,
      GLOBAL_GRAPH_NAME
    );
  }

  /**
   * User having drop graph privilege can drop graph.
   *
   * Special case globaldesigner:
   * The globaldesigner role can ony drop the graphs created by the same user,
   * so in GSQL side, drop graph privilege is not added to globaldesigner's permission list
   * but will do special permission check when dropping a graph.
   * In order to not make user confused if they see some graph has drop button but some doesn't,
   * in GraphStudio we will not hide the drop graph button for globaldesigner role.
   * User will get failure message from GSQL response if not having permission.
   *
   * @returns {boolean}
   * @memberof PageLayoutComponent
   */
  canDropGraph(): boolean {
    return (
      this.authService.hasPrivilege(
        GSQLPrivilege.DropGraph,
        GLOBAL_GRAPH_NAME
      ) || this.authService.getUserRoles().includes('globaldesigner')
    );
  }

  /**
   * Confirm when switch graph.
   *
   * @param {string} graphName
   * @param {boolean} [switchToSchemaDesign=false]
   * @memberof PageLayoutComponent
   */
  confirmSwitchGraph(graphName: string, switchToSchemaDesign = false) {
    if (this.hasUnsavedChanges) {
      const config: MatDialogConfig<DialogData> = {
        ariaLabel: 'Confirm switch graph dialog',
        data: {
          title: 'Warning',
          messages: [
            'You have unpublished changes that will be lost. ' +
              'Do you still want to switch graph?',
          ],
          actions: [
            { label: 'STAY', value: 0 },
            { label: 'SWITCH', value: 1, color: 'warn' },
          ],
        },
      };

      this.dialog
        .open<DialogComponent, DialogData, number>(DialogComponent, config)
        .afterClosed()
        .pipe(takeUntil(this.destroyed))
        .subscribe((value) => {
          if (value === 1) {
            this.switchGraph(graphName, switchToSchemaDesign);
            this.bus.to<boolean>('HasUnsavedChanges').next(false);
          }
        });
    } else {
      this.switchGraph(graphName, switchToSchemaDesign);
    }
  }

  /**
   * Drop a graph.
   *
   * @param {string} graphName
   * @memberof PageLayoutComponent
   */
  dropGraph(graphName: string) {
    const data = {
      title: 'Warning',
      messages: [
        'Drop graph is irreversible.',
        `Are you sure you want to drop graph "${graphName}"?`,
      ],
      actions: [
        { label: 'CANCEL', value: 0 },
        { label: 'CONTINUE', value: 1, color: 'warn' },
      ],
    };

    this.dialog
      .open(DialogComponent, { data: data })
      .afterClosed()
      .pipe(takeUntil(this.destroyed))
      .subscribe((value) => {
        if (value === 1) {
          this.loading.next(true);
          this.confirmDropGraph(graphName);
        }
      });
  }

  /** Create a new graph.
   *
   * @memberof PageLayoutComponent
   */
  createGraph() {
    this.cmptService
      .getGlobalVertexAndEdgeTypes()
      .pipe(takeUntil(this.destroyed))
      .subscribe(
        (types) => {
          this.globalVertexTypes = types.VertexTypes.map((vertex) => ({
            vertexOrEdgeType: vertex,
            selected: false,
          }));
          this.globalEdgeTypes = types.EdgeTypes.map((edge) => ({
            vertexOrEdgeType: edge,
            selected: false,
          }));
          this.createGraphForm = this.cmptService.buildCreateGraphForm();
          const config = {
            width: '600px',
          };
          this.createGraphPopupWindowRef = this.dialog.open(
            this.createGraphPopupWindow,
            config
          );
        },
        (err) =>
          this.handleError(
            'Fail to get global vertex and edge types. ' + err.error
          )
      );
  }

  /** Confirm create a new graph.
   *
   * @memberof PageLayoutComponent
   */
  confirmCreateGraph() {
    this.loading.next(true);
    const vertexTypes = this.globalVertexTypes
      .filter((vertex) => vertex.selected)
      .map((vertex) => vertex.vertexOrEdgeType.Name);
    const edgeTypes = this.globalEdgeTypes
      .filter((edge) => edge.selected)
      .map((edge) => edge.vertexOrEdgeType.Name);

    this.cmptService
      .createGraph(this.newGraphName, vertexTypes, edgeTypes)
      .pipe(
        takeUntil(this.destroyed),
        switchMap((response) => {
          const prevGraphName = this.currentGraph;
          this.closePopupWindow();
          this.snackBar.open(response, 'DISMISS', { duration: 3000 });

          return this.updateGraphList(prevGraphName);
        }),
        finalize(() => {
          this.newGraphName = '';
          this.loading.next(false);
        })
      )
      .subscribe(
        () => this.confirmSwitchGraph(this.newGraphName, true),
        (err) => this.handleError(err.error)
      );
  }

  /** Close popup window. */
  closePopupWindow() {
    if (this.createGraphPopupWindowRef) {
      this.createGraphPopupWindowRef.close();
    }
  }

  /** Check if all elements in a list are checked. */
  allTypesSelected(typeList: TypeCheckedStatus[]): boolean {
    return typeList.every((type) => type.selected);
  }

  /** Check all elements in a list. */
  checkAll(typeList: TypeCheckedStatus[], newStatus: MatCheckboxChange) {
    typeList.forEach((type) => (type.selected = newStatus.checked));
  }

  /** Return the graph creator. */
  getGraphCreator(graphName: string): string {
    const creator = this.authService.getGraphCreator(graphName);
    return creator ? `Created by: ${creator}` : undefined;
  }

  /**
   * Return the graph list.
   *
   * @readonly
   * @type {string[]}
   * @memberof PageLayoutComponent
   */
  get graphList(): string[] {
    return this.authService.getGraphList().sort();
  }

  /** Confirm drop a graph.
   *
   * @private
   * @param {string} graphName
   * @memberof PageLayoutComponent
   */
  private confirmDropGraph(graphName: string) {
    this.cmptService
      .dropGraph(graphName)
      .pipe(
        takeUntil(this.destroyed),
        finalize(() => this.loading.next(false))
      )
      .subscribe(
        (response) => {
          const currentGraphName = this.currentGraph;
          this.snackBar.open(response, 'DISMISS', { duration: 3000 });
          this.updateGraphList(currentGraphName).subscribe(() => {});
          this.bus.to<boolean>('UpdateGlobalTypesUsage').next(true);

          forkJoin([
            this.cmptService.dropGraphStyle(graphName),
            this.cmptService.dropLoadingJobInfo(graphName),
            this.cmptService.dropQueries(graphName),
            this.cmptService.dropExploration(graphName),
          ])
            .pipe(takeUntil(this.destroyed))
            .subscribe();
        },
        (err) => this.handleError(err.error)
      );
  }

  /** Update graph list after changes.
   *
   * @private
   * @param {string} graphName
   * @param {boolean} [switchToNewGraph=false]
   * @memberof PageLayoutComponent
   */
  private updateGraphList(graphName: string): Observable<any> {
    return this.authService.updateUserInfos().pipe(
      takeUntil(this.destroyed),
      tap(() => this.authService.setCurrentGraph(graphName))
    );
  }

  /** Error message popup.
   *
   * @private
   * @param {string} message
   * @memberof PageLayoutComponent
   */
  private handleError(message: string) {
    const data = {
      title: 'Error',
      messages: [message],
    };
    this.dialog.open(DialogComponent, { data: data });
  }

  /**
   * Switch to the selected graph.
   *
   * @private
   * @param {string} graphName
   * @param {boolean} [switchToSchemaDesign=false]
   * @memberof PageLayoutComponent
   */
  private switchGraph(graphName: string, switchToSchemaDesign = false) {
    this.authService.setCurrentGraph(graphName);

    const homeRoute = '/home';
    const actionsRoute = PageKey.Actions;
    const schemaDesignerRoute = PageKey.SchemaDesigner;
    const queryEditorRoute = PageKey.QueryEditor;

    /** After new graph creation succeeds, navigate to Design Schema automatically. */
    if (switchToSchemaDesign) {
      if (this.router.url !== schemaDesignerRoute) {
        this.router.navigateByUrl(schemaDesignerRoute);
        return;
      }
    }

    /**
     * When switch to global view,
     * go to Design Schema Page if current page is not Home or Design Schema Page.
     */
    if (
      graphName === GLOBAL_GRAPH_NAME &&
      this.router.url !== homeRoute &&
      this.router.url !== actionsRoute &&
      this.router.url !== schemaDesignerRoute
    ) {
      this.router.navigateByUrl(schemaDesignerRoute);
      return;
    }

    if (this.router.url === homeRoute) {
      return;
    }
    let hasPrivilege = false;

    /**
     * Check current page access privilege on switched graph.
     * Navigate to Home page if not having required privileges.
     */
    if (this.authService.hasAllPrivilegesByURL(this.router.url)) {
      hasPrivilege = true;
    }

    if (
      this.router.url === queryEditorRoute &&
      this.authService.hasPermissionToAccessWriteQueriesPage(this.currentGraph)
    ) {
      hasPrivilege = true;
    }

    if (hasPrivilege) {
      timer(500)
        .pipe(takeUntil(this.destroyed))
        .subscribe(() => this.bus.to<boolean>('SwitchGraph').next(true));
    } else {
      const data = {
        title: 'Warning',
        messages: [
          `You don't have the privilege to enter ${
            PAGE_NAME[this.router.url]
          } page ` + `on graph ${graphName}.`,
        ],
      };

      this.dialog
        .open(DialogComponent, { data: data })
        .afterClosed()
        .pipe(take(1))
        .subscribe(() => this.router.navigateByUrl(homeRoute));
    }
  }

  /**
   * Push a sidenav navigation event to analytics
   * @param to the router link of the page being navigated to
   */
  logNavClick(to: string): void {
    this.cmptService.logNavigationEventUsingLink(to, 'Sidenav');
  }
}
