import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { forkJoin, from, Observable, of, throwError } from 'rxjs';
import { switchMap, tap } from 'rxjs/operators';
import { intersection, merge } from 'lodash';

import {
  User,
  UserPrivilege,
  UserRole,
  UserInfo,
  GraphCreators,
} from '@tigergraph/tools-models/user';
import {
  ACCESS_PRIVILEGES,
  GLOBAL_GRAPH_NAME,
  GSQLPrivilege,
  PageKey,
} from '@tigergraph/tools-models/utils';

import { CacheService } from './cache.service';

import { MessageBus } from './message-bus.service';
import { AnalyticsService } from '@app/shared/services/analytics.service';
import { compareWithInstanceVersion, setVersion } from '@tigergraph/tools-models/utils/version';
import { simpleAuth } from '@tigergraph/tools-models';

const loginURL = '/api/auth/login';
const logOutURL = '/api/auth/logout';
const configURL = '/api/config';
const versionURL = '/api/version';

/**
 * Service to handle user authentication/authorization.
 *
 * @export
 * @class AuthService
 */
@Injectable({
  providedIn: 'root',
})
export class AuthService {
  private user: User;
  private clientIdleTimeMS = 604800000;
  private graphStatCheckIntervalMS = 10000;
  private enableDarkTheme = true;
  private isCommunity = false;

  constructor(
    private http: HttpClient,
    private cacheService: CacheService,
    private bus: MessageBus,
    private analyticsService: AnalyticsService
  ) {
    this.loadFromCache();
  }

  /**
   * Log the user in and retrieve necessary infos.
   *
   * @param {string} [username]
   * @param {string} [password]
   * @returns {Observable<void>}
   * @memberof AuthService
   */
  logIn(username: string, password: string): Observable<void> {
    this.analyticsService.track('Log In');
    return this.http
      .post<UserInfo>(loginURL, {
        username: username,
        password: password,
      })
      .pipe(switchMap((res) => this.updateUserInfos(res)));
  }

  /**
   * Log the user out and perform cleanup.
   *
   * @returns {Observable<void>}
   * @memberof AuthService
   */
  logOut(): Observable<void> {
    this.analyticsService.track('Log Out');
    this.bus.resetAll();
    this.user = undefined;
    this.cacheService.clear();
    return this.http.post<void>(logOutURL, {});
  }

  /**
   * Retrieve user's infos from the server
   * and save to cache.
   *
   * @returns {Observable<void>}
   * @memberof AuthService
   */
  updateUserInfos(userInfo?: UserInfo): Observable<void> {
    const updateUserInfosObs = userInfo
      ? of(userInfo)
      : from(simpleAuth().then((res) => res.data.results));
    this.http.get(versionURL).subscribe(
      (version) => {
        if (version['tigergraph_version']) {
          setVersion(version['tigergraph_version']);
          this.isCommunity = version['is_community_edition'];
        } else {
          console.warn(
            `failed to get tigergraph version: ${version['message']}`
          );
        }
      },
      (error) => {
        console.warn(`failed to get tigergraph version: ${error}`);
      }
    );

    return forkJoin([
      updateUserInfosObs,
      this.http.get<any>(configURL, {
        params: new HttpParams()
          .append('key', 'GUI.ClientIdleTimeSec')
          .append('key', 'GUI.GraphStatCheckIntervalSec')
          .append('key', 'GUI.EnableDarkTheme'),
      }),
    ]).pipe(
      tap(([userInfos, config]) => {
        if (userInfos.groups) {
          userInfos.groups.forEach((group) => {
            userInfos.privileges = merge(
              userInfos.privileges,
              group.privileges
            );
            userInfos.roles = merge(userInfos.roles, group.roles);
          });
        }
        this.user = new User(userInfos);
        this.clientIdleTimeMS = config['GUI.ClientIdleTimeSec'] * 1000;
        this.graphStatCheckIntervalMS =
          config['GUI.GraphStatCheckIntervalSec'] * 1000;
        if (this.graphStatCheckIntervalMS === 0) {
          this.graphStatCheckIntervalMS = 10000;
        }
        this.enableDarkTheme = config['GUI.EnableDarkTheme'];
      }),
      switchMap(() => this.isAuthorized()),
      tap(() => this.saveToCache())
    );
  }

  /**
   * Check if the user is authorized to use the app or not.
   *
   * @returns {Observable<void>}
   * @memberof AuthService
   */
  isAuthorized(): Observable<void> {
    if (!this.user) {
      return throwError({ error: 'User is not logged in.' });
    }
    return of(undefined);
  }

  /**
   * Return the username.
   *
   * @returns {string}
   * @memberof AuthService
   */
  getUsername(): string {
    return this.user.getUsername();
  }

  /**
   * Return the ClientIdleTimeMS.
   *
   * @returns {number}
   * @memberof AuthService
   */
  getClientIdleTimeMS(): number {
    return this.clientIdleTimeMS;
  }

  /**
   * Return the GraphStatCheckIntervalMS.
   *
   * @returns {number}
   * @memberof AuthService
   */
  getGraphStatCheckIntervalMS(): number {
    return this.graphStatCheckIntervalMS;
  }

  /**
   * Set the current graph the user is using.
   *
   * @param {string} [graphName]
   * @memberof AuthService
   */
  setCurrentGraph(graphName?: string) {
    const graphList = this.user.getGraphList();
    // Use requested graph if exists otherwise use one in graph list.
    if (graphList.includes(graphName)) {
      this.user.setCurrentGraph(graphName);
    } else {
      this.user.setCurrentGraph(graphList.sort()[0]);
    }

    if (this.user.getCurrentGraph()) {
      this.cacheService.setItem('GraphName', this.user.getCurrentGraph());
    }
  }

  /**
   * Return the current graph the user is using.
   *
   * @returns {string}
   * @memberof AuthService
   */
  getCurrentGraph(): string {
    return this.user.getCurrentGraph();
  }

  isSuperUser(): boolean {
    return this.user.isSuperUser();
  }

  getGraphCreator(graphName?: string): string {
    return this.user.getCreatorForGraph(graphName);
  }

  /**
   * Return the user's role for a given graph.
   *
   * @param {string} [graphName]
   * @returns {string}
   * @memberof AuthService
   */
  getUserRole(): string {
    return this.user.getUserRole();
  }

  getUserRoles(graphName?: string): string[] {
    return this.user.getUserRoles(graphName);
  }

  getAccessPrivilegesByURL(url: string): GSQLPrivilege[] {
    const u = new URL(url, window.location.origin);
    if (ACCESS_PRIVILEGES[u.pathname]) {
      return ACCESS_PRIVILEGES[u.pathname];
    }

    return [];
  }

  hasAllPrivilegesByURL(url: string, graphName?: string): boolean {
    const privileges = this.getAccessPrivilegesByURL(url);

    return this.hasAllPrivileges(privileges, graphName);
  }

  hasPrivilege(privilege: GSQLPrivilege, graphName?: string): boolean {
    return this.user.hasPrivilege(privilege, graphName);
  }

  hasAllPrivileges(privileges: GSQLPrivilege[], graphName?: string): boolean {
    return privileges.every((p) => this.hasPrivilege(p, graphName));
  }

  hasAllWriteQueriesPermssions(graphName: string) {
    // 1. super user can access all queries
    if (this.user.isSuperUser()) {
      return true;
    }

    // 2. FOR Roles
    // 2.1 graph admin can visit graph related queries
    if (['admin'].some(p => this.user.getRolesForGraph(graphName).includes(p))) {
      return true;
    }

    return false;
  }

  hasPrivilegesToQuery(
    graphName: string,
    queryName: string,
    privileges: GSQLPrivilege[]
  ) {
    if (this.hasAllWriteQueriesPermssions(graphName)) {
      return true;
    }

    if (privileges.length <= 0 || !queryName) {
      return false;
    }

    // 3.3 privileges query level
    const queryPermissions = this.user.getChildPermissionsForGraph(graphName);
    const res = intersection(
      queryPermissions[queryName]?.privileges,
      privileges
    );

    return res.length > 0;
  }

  hasPrivilegesToCreateQuery(graphName: string) {
    if (this.hasAllWriteQueriesPermssions(graphName)) {
      return true;
    }

    // 3. FOR privileges
    // 3.1 global graph CREATE_QUERY privilege
    if (
      this.user
        .getPrivilegesForGraph(GLOBAL_GRAPH_NAME)
        .includes(GSQLPrivilege.CreateQuery)
    ) {
      return true;
    }

    // 3.2 user who has graph CREATE_QUERY privilege can access graph related queries (privileges[graphName].privileges has CREATE_QUERY)
    if (
      this.user
        .getPrivilegesForGraph(graphName)
        .includes(GSQLPrivilege.CreateQuery) ||
      this.user.getPrivilegesForGraph(GLOBAL_GRAPH_NAME)
        .includes(GSQLPrivilege.CreateQuery)
    ) {
      return true;
    }

    return false;
  }

  hasPermissionToAccessWriteQueriesPage(graphName: string) {
    if (compareWithInstanceVersion('4.1.0', '<')) {
      return this.hasAllPrivilegesByURL(PageKey.QueryEditor);
    }

    if (this.hasPrivilegesToCreateQuery(graphName)) {
      return true;
    }

    // 4.2 user who has specific query permission on the graph, including `{UPDATE/DROP/INSTALL/EXECUTE}_QUERY`
    const queryPermissions = this.user.getChildPermissionsForGraph(graphName);
    const hasSubPermission = Object.values(queryPermissions)
      .map((q) => q.privileges)
      .flat()
      .find((p) => {
        return [
          GSQLPrivilege.Owner,
          GSQLPrivilege.ReadQuery,
          GSQLPrivilege.UpdateQuery,
          GSQLPrivilege.DropQuery,
          GSQLPrivilege.InstallQuery,
          GSQLPrivilege.ExecuteQuery,
        ].includes(p);
      });
    return Boolean(hasSubPermission);
  }

  /**
   * Return all graph the user has access to.
   *
   * @returns {string[]}
   * @memberof AuthService
   */
  getGraphList(): string[] {
    return this.user.getGraphList();
  }

  /**
   * Return whether or not dark theme is enabled.
   *
   * @returns {boolean}
   * @memberof AuthService
   */
  getEnableDarkTheme(): boolean {
    return this.enableDarkTheme;
  }

  isCommunityVersion(): boolean {
    return this.isCommunity;
  }

  /**
   * Save user's infos to cache.
   *
   * @private
   * @memberof AuthService
   */
  private saveToCache() {
    this.cacheService.setItem('UserRoles', this.user.getRoles());
    this.cacheService.setItem('Username', this.user.getUsername());
    this.cacheService.setItem('UserPrivilege', this.user.getPrivileges());
    this.cacheService.setItem('IsSuperUser', this.user.isSuperUser());
  }

  /**
   * Retrieve user's infos from cache.
   *
   * @private
   * @memberof AuthService
   */
  private loadFromCache() {
    const config = {
      name: this.cacheService.getItem<string>('Username'),
      creators: this.cacheService.getItem<GraphCreators>('GraphCreators'),
      roles: this.cacheService.getItem<UserRole>('UserRoles'),
      privileges: this.cacheService.getItem<UserPrivilege>('UserPrivilege'),
      isSuperUser: this.cacheService.getItem<boolean>('IsSuperUser'),
    };

    if (
      config.name !== null &&
      config.roles !== null &&
      config.privileges !== null
    ) {
      let valid = true;

      Object.keys(config.roles).forEach(
        (graphName) => (valid = valid && Array.isArray(config.roles[graphName]))
      );
      Object.keys(config.privileges).forEach(
        (graphName) =>
          (valid = valid && Array.isArray(config.privileges[graphName]))
      );

      if (valid) {
        this.user = new User(config);
      }
    }
  }
}
