<h1 class="cdk-visually-hidden">Explore Graph</h1>
<app-loading-indicator [show]="loading | async">
  <div class="explore-switcher">
    <mat-icon
      class="explore-switcher-icon"
      svgIcon="campaign">
    </mat-icon>
    <p>
      {{
        newExplore ?
          'You’re using the New Explore Graph. Enjoy improved performance and a modern UI! You can always switch back if you prefer.' :
          'Try the new Explore Graph! Experience a faster, more powerful way to explore your graph.'
      }}
    </p>
    <a
      class="explore-toggle-btn"
      (click)="onChangeExploreVersion()">
      {{ newExplore ? ' Back to Classic' : ' Try Now'}}
    </a>
  </div>
  <app-graph-explore
    [class.hidden]="!newExplore"
    class="new-graph-explore"
    [graphNames]="graphNames"
    [isEditMode]="exploreEditMode"
    [graph]="schemaGraph"
  ></app-graph-explore>
  <div fxLayout.gt-sm="row" fxLayout="column" fxLayoutGap="1px" class="max-height" ngif="!newExplore">
    <!-- Small Screen Edit Criteria Panel Header -->
    <div class="panel-header"
      fxShow
      fxHide.gt-sm="true"
      fxLayout="row"
      fxLayoutGap="2px"
      fxFlexAlign="start center">
      <h2 id="edit-criteria-button">{{ 'Edit criteria' | translate }}</h2>
      <button
        mat-icon-button
        class="toggle-btn"
        (click)="expandEditCriteriaPanel = !expandEditCriteriaPanel"
        aria-labelledby="edit-criteria-button"
        attr.aria-description="{{
            (expandEditCriteriaPanel ?
            'Click this button to hide the edit criteria' :
            'Click this button to display the edit criteria') | translate
        }}">
        <mat-icon
          [@indicatorRotate]="expandEditCriteriaPanel ? 'expanded' : 'collapsed'"
          aria-labelledby="edit-criteria-button"
          attr.aria-description="{{
              (expandEditCriteriaPanel ?
              'Click this button to hide the edit criteria' :
              'Click this button to display the edit criteria') | translate
          }}">
          expand_more
        </mat-icon>
      </button>
    </div>
    <!-- End of Small Screen Edit Criteria Panel Header -->

    <app-toolbar-container
      [class.hidden]="reflow && !expandEditCriteriaPanel"
      [config]="toolbarConfig"
      (interaction)="onInteraction($event)">
        <!-- Edit Criteria Container -->
        <div
          *ngIf="!reflow && leftPanelFlex !== 0 || expandEditCriteriaPanel"
          class="graph-explorer-controller-container"
          [ngClass.lt-md]="'panel-container'"
          fxFlex.gt-sm="{{ leftPanelFlex }}px">
          <!-- Up Panel -->
          <!-- Search Vertices input box -->
          <div *ngIf="inVerticesTab()">
            <p class="hint-text">{{ 'Search vertices by vertex id' | translate }}</p>
            <!-- Search vertex by ID -->
            <div fxLayout="row" fxLayoutAlign="center center">
              <app-vertex-type-and-id-input fxFlex="calc(100% - 40px)" fixedVertexType="false"
              [vertexTypes]="vertexTypes | async" [(ngModel)]="searchVertexContent" class="search-vertex-by-id-container"
              [id]="searchVertexByIdInputId" (modelId)="toggleChoosingVertex($event)"
              (change)="onLeftPanelChange(graphExplorerLeftPanelConfig.SearchVertexContent, $event)"
              [showInOneRow]="true">
              </app-vertex-type-and-id-input>
              <button
                mat-icon-button
                fxFlex="40px"
                [color]="primary"
                matTooltip="{{ 'Search vertex by type and id' | translate }}"
                matTooltipPosition="right"
                attr.aria-label="{{ 'Search vertex by type and id' | translate }}"
                (click)="searchVerticesInGraph()"
                [disabled]="!canCallSearchVertex()">
                <mat-icon attr.aria-label="{{ 'Search vertex by type and id' | translate }}">
                  search
                </mat-icon>
              </button>
            </div>
          </div>

          <!-- K-step Expansion input box -->
          <div *ngIf="inExpandTab()">
            <p class="hint-text">{{ 'Expand from vertices' | translate }}</p>
            <p class="mat-input-hint mat-caption">
              {{ 'Click vertices in right panel to choose expansion starting vertices. Press \"Shift\" key to choose multiple vertices at the same time.' | translate }}
            </p>
            <div>
              <span *ngFor="let endpoint of getFirstKEndpoints(5); let i = index" fxLayout="column">
                <mat-form-field fxFlex="48px" class="max-width">
                  <input matInput placeholder="{{ 'Expansion starting vertex' | translate }} {{ i + 1 }}" [(ngModel)]="endpointVertices[i]" [readonly]="true">
                </mat-form-field>
              </span>
            </div>
            <p class="hint-text" *ngIf="endpointVertices.length > 5">
              {{ 'and' | translate }} {{endpointVertices.length - 5}} {{ 'more vertices are selected.' | translate }}
            </p>
          </div>
          <button class="graph-explorer-controller-action-button" mat-raised-button color="primary" (click)="kstepExpansionInGraph()"
            [disabled]="!canCallExpand()" *ngIf="inExpandTab()">
              {{ 'Expand' | translate }}
          </button>
          <span class="mat-input-warn mat-caption" *ngIf="inExpandTab() && potentialExpansionEdgeNumber() > 5000">
            <mat-icon attr.aria-label="{{ 'Warning' | translate }}">error</mat-icon>
            {{ 'The configuration below might expand up to' | translate }} {{ endpointVertices.length }}
            <span *ngFor="let oneStepConfig of expansionStepsConfig"> * {{ oneStepConfig.neighborLimit }}</span>
            = {{ potentialExpansionEdgeNumber() }} {{ 'edges, which cannot be rendered in browser.' | translate }}
          </span>

          <!-- Find Paths input box -->
          <div *ngIf="inPathsTab()">
            <p class="hint-text">{{ 'Find paths between two vertices' | translate }}</p>
            <p class="mat-input-hint mat-caption">
              {{ 'To choose path Starting - Ending vertex, click the starting vertex or destination vertex id input box, then click one vertex from right side.' | translate }}
            </p>
          </div>
          <div fxLayout="row" *ngIf="inPathsTab()">
            <div fxFlex fxLayout="column" fxFlex="calc(100% - 30px)" >
              <p class="hint-text hint-text-mini">{{ 'Choose starting vertex' | translate }}</p>
              <app-vertex-type-and-id-input fixedVertexType="false" [vertexTypes]="vertexTypes | async"
                [(ngModel)]="startingVertexContent" (modelId)="toggleChoosingVertex($event)"
                [id]="startingVertexInputId" (change)="onLeftPanelChange(graphExplorerLeftPanelConfig.StartingVertexContent, $event)"
                [showInOneRow]="true">
              </app-vertex-type-and-id-input>

              <p class="hint-text hint-text-mini">{{ 'Choose destination vertex' | translate }}</p>
              <app-vertex-type-and-id-input fixedVertexType="false" [vertexTypes]="vertexTypes | async"
                [(ngModel)]="destinationVertexContent" (modelId)="toggleChoosingVertex($event)"
                [id]="destinationVertexInputId" (change)="onLeftPanelChange(graphExplorerLeftPanelConfig.DestinationVertexContent, $event)"
                [showInOneRow]="true">
              </app-vertex-type-and-id-input>
            </div>
            <button
              mat-icon-button
              color="accent"
              fxFlex="40px"
              matTooltip="{{ 'Swap starting and destination vertices' | translate }}"
              attr.aria-label="{{ 'Swap starting and destination vertices' | translate }}"
              class="swap-path-endpoints"
              (click)="swapPathFromAndToVertices()">
              <mat-icon attr.aria-label="{{ 'Swap starting and destination vertices' | translate }}">
                swap_vert
              </mat-icon>
            </button>
          </div>
          <button
            class="graph-explorer-controller-action-button"
            mat-raised-button color="primary"
            (click)="findPathsInGraph()"
            [disabled]="!canCallFindPath()"
            *ngIf="inPathsTab()">
              {{ 'Find paths' | translate }}
          </button>

          <!-- Find Connection Path input box -->
          <div *ngIf="inConnectionsTab()">
            <p class="hint-text">{{ 'Find connections between multiple vertices' | translate }}</p>
            <p class="mat-input-hint mat-caption ">
              {{ 'Click vertices in right panel to choose vertices. Press \"Shift\" key to choose multiple vertices at the same time.' | translate }}
            </p>
            <div>
              <span *ngFor="let endpoint of getFirstKEndpoints(5); let i = index" fxLayout="column">
                <mat-form-field fxFlex="48px" class="max-width">
                  <input matInput placeholder="Vertex {{ i + 1 }}" [(ngModel)]="endpointVertices[i]" [readonly]="true">
                </mat-form-field>
              </span>
            </div>
            <p class="hint-text" *ngIf="endpointVertices.length > 5">
              {{ 'and' | translate }} {{ endpointVertices.length - 5 }} {{ 'more vertices are selected.' | translate }}
            </p>
          </div>
          <button class="graph-explorer-controller-action-button" mat-raised-button color="primary" (click)="findConnectionsInGraph()"
            [disabled]="!canCallFindConnectionPath()" *ngIf="inConnectionsTab()">
              {{ 'Find connection paths' | translate }}
          </button>

          <!-- Run Queries selection list -->
          <div *ngIf="inQueriesTab()">
            <p class="hint-text">{{ 'Execute installed queries' | translate }}</p>
            <p class="mat-input-hint mat-caption">
              {{ 'Choose an installed query from the drop-down list, input the parameters, then run the query.' | translate }}
            </p>
            <mat-form-field class="queries-selector">
              <mat-select placeholder="{{ 'choose a query' | translate }}" [(ngModel)]="currentQueryName" (selectionChange)="onQueryChange()">
                <mat-option *ngFor="let query of queryList" [value]="query.queryName">
                  {{ query.queryName }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
          <!-- End of Up Panel -->

          <mat-divider
            class="divider"
            *ngIf="inVerticesTab() || inExpandTab() || inPathsTab() || inConnectionsTab() || inQueriesTab()">
          </mat-divider>

          <!-- Down Panel -->
          <p class="hint-text" *ngIf="inPathsTab()">
            {{ 'Configuration' | translate }}
          </p>
          <!-- Search Vertices -->
          <div *ngIf="inVerticesTab()">
            <!-- Search Vertices By Attribute Filter -->
            <p class="hint-text">{{ 'Search vertices by attribute filter' | translate }}</p>
            <!-- Random pick some vertices -->
            <div fxLayout="row" fxLayoutAlign="center center" fxLayoutGap="10px">
              <mat-form-field fxFlex="calc(100% - 120px)">
                <input matInput placeholder="{{ 'Enter a number' | translate }}" [(ngModel)]="randomPickVertexNumber" type="number"
                  (input)="onInputChange.next({ panelContent: graphExplorerLeftPanelConfig.RandomPickVertexNumber, value: randomPickVertexNumber })">
              </mat-form-field>
              <button fxFlex="110px" mat-raised-button color="primary"
                (click)="randomPickVerticesInGraph()" [disabled]="!canCallRandomPickVertex()">
                {{ 'Pick vertices' | translate }}
              </button>
            </div>
            <span class="mat-input-error mat-caption" *ngIf="!validRandomPickNumber()">
              {{ 'Please enter an integer between 1 and 500.' | translate }}
            </span>
            <span class="mat-input-warn mat-caption" *ngIf="hasAttributeFilter()">
              <mat-icon attr.aria-label="{{ 'Warning' | translate }}">warning</mat-icon> {{ 'Picking vertices with attribute filter might be slow.' | translate }}
            </span>
          </div>

          <!-- K-Step expansion steps config -->
          <div *ngIf="inExpandTab()">
            <div *ngFor="let oneStepConfig of expansionStepsConfig; let i = index">
              <!-- Expand layer title -->
              <p class="hint-text">{{ 'Expansion step' | translate }} {{i + 1}}</p>

              <div class="box-container" fxLayout="column">
                <mat-form-field fxFlex="48px" class="max-width">
                  <input matInput placeholder="{{ 'Expanding out edge limit per vertex' | translate }}" [(ngModel)]="oneStepConfig.neighborLimit"
                    min="1" max="500" type="number"
                    (input)="onInputChange.next({ panelContent: graphExplorerLeftPanelConfig.ExpansionStepsConfig + '.' + i, value: oneStepConfig.neighborLimit })">
                </mat-form-field>
                <span class="mat-input-error mat-caption" *ngIf="oneStepConfig.neighborLimit < 1 || oneStepConfig.neighborLimit > 500">
                  {{ 'Please input value between 1 and 500.' | translate }}
                </span>
                <span class="mat-input-error mat-caption" *ngIf="!isInteger(oneStepConfig.neighborLimit)">
                  {{ 'Please input an integer.' | translate }}
                </span>

                <!-- Edge types -->
                <div fxLayout="row" fxLayoutAlign="space-between center" class="section-header">
                  <p class="hint-text">{{ 'Expand through edge types' | translate }}</p>
                  <button
                    mat-icon-button
                    class="toggle-btn"
                    matTooltip="{{ 'Show/hide edge attributes' | translate }}"
                    matTooltipPosition="right"
                    attr.aria-label="{{ 'Show/hide edge attributes' | translate }}"
                    [attr.aria-expanded]="!oneStepConfig.isEdgeFilterFolded"
                    (click)="oneStepConfig.isEdgeFilterFolded = !oneStepConfig.isEdgeFilterFolded">
                    <mat-icon
                      [@indicatorRotate]="oneStepConfig.isEdgeFilterFolded ? 'collapsed' : 'expanded'"
                      attr.aria-label="{{ 'Show/hide edge attributes' | translate }}">
                      expand_more
                    </mat-icon>
                  </button>
                </div>
                <div class="box-container" *ngIf="!oneStepConfig.isEdgeFilterFolded">
                  <mat-checkbox
                    [(ngModel)]="oneStepConfig.selectAllEdgeTypes"
                    (change)="toggleBoolArray(i + '.' + graphExplorerLeftPanelConfig.EdgeTypeFilter, oneStepConfig.edgeTypeFilter)"
                    attr.aria-label="{{ 'Edge type' | translate }}">
                    {{ 'All' | translate }}
                  </mat-checkbox>
                  <div class="clear"></div>
                  <cdk-virtual-scroll-viewport
                    [style.height]="getVirtualScrollHeight(schema.edgeTypes.length)"
                    class="virtual-scroll-container" itemSize="46">
                    <div *cdkVirtualFor="let edgeType of schema.edgeTypes" class="box-container filter-box">
                      <div fxLayout="row" fxLayoutGap="5px" fxLayoutAlign="center center">
                        <div
                          fxFlex="calc(100%-40px)"
                          class="graph-entity-type"
                          matTooltip="{{ 'Edge type' | translate }} {{edgeType.name}}">
                          <mat-checkbox
                            [ngModel]="oneStepConfig.edgeTypeFilter.get(edgeType.name)"
                            (ngModelChange)="oneStepConfig.edgeTypeFilter.set(edgeType.name, $event)"
                            (change)="onExpansionStepSelectEdgeTypeChange(i)"
                            attr.aria-label="{{ 'Edge type' | translate }}">
                            {{ edgeType.name }}
                          </mat-checkbox>
                        </div>
                        <button
                          mat-icon-button fxFlex="40px"
                          [color]="primary"
                          matTooltip="{{ 'Edit filter for' | translate }} {{ edgeType.name }}"
                          matTooltipPosition="right"
                          (click)="addAttributeFilter(edgeType.name, oneStepConfig.attributeFilter)"
                          attr.aria-label="{{ 'Edit filter for' | translate }} {{ edgeType.name }}">
                          <mat-icon
                            class="svg-icon"
                            svgIcon="filter"
                            attr.aria-label="{{ 'Edit filter for' | translate }} {{ edgeType.name }}">
                          </mat-icon>
                        </button>
                      </div>
                      <div class="hint-text attribute-filter-text graph-entity-type">
                        {{ removeAlias(oneStepConfig.attributeFilter.get(edgeType.name)?.toString(), edgeType.name) }}
                      </div>
                    </div>
                  </cdk-virtual-scroll-viewport>
                  <div class="clear"></div>
                </div>

                <!-- Vertex types -->
                <div fxLayout="row" fxLayoutAlign="space-between center" class="section-header">
                  <p class="hint-text">{{ 'Expand towards vertex types' | translate }}</p>
                  <button
                    mat-icon-button
                    class="toggle-btn"
                    matTooltip="{{ 'Show/hide vertex attributes' | translate }}"
                    matTooltipPosition="right"
                    attr.aria-label="{{ 'Show/hide vertex attributes' | translate }}"
                    [attr.aria-expanded]="!oneStepConfig.isVertexFilterFolded"
                    (click)="oneStepConfig.isVertexFilterFolded = !oneStepConfig.isVertexFilterFolded">
                    <mat-icon
                      [@indicatorRotate]="oneStepConfig.isVertexFilterFolded ? 'collapsed' : 'expanded'"
                      attr.aria-label="{{ 'Show/hide vertex attributes' | translate }}">
                      expand_more
                    </mat-icon>
                  </button>
                </div>
                <div class="box-container" *ngIf="!oneStepConfig.isVertexFilterFolded">
                  <mat-checkbox
                    [(ngModel)]="oneStepConfig.selectAllVertexTypes"
                    (change)="toggleBoolArray(i + '.' + graphExplorerLeftPanelConfig.VertexTypeFilter, oneStepConfig.vertexTypeFilter)"
                    attr.aria-label="{{ 'Vertex type' | translate }}">
                    {{ 'All' | translate }}
                  </mat-checkbox>
                  <div class="clear"></div>
                  <cdk-virtual-scroll-viewport
                    [style.height]="getVirtualScrollHeight(schema.vertexTypes.length)"
                    class="virtual-scroll-container" itemSize="46">
                    <div *cdkVirtualFor="let vertexType of schema.vertexTypes" class="box-container filter-box">
                      <div fxLayout="row" fxLayoutGap="5px" fxLayoutAlign="center center">
                        <div
                          fxFlex="calc(100%-40px)"
                          class="graph-entity-type"
                          matTooltip="{{ 'Vertex type' | translate }} {{ vertexType.name }}">
                          <mat-checkbox
                            [ngModel]="oneStepConfig.vertexTypeFilter.get(vertexType.name)"
                            (ngModelChange)="oneStepConfig.vertexTypeFilter.set(vertexType.name, $event)"
                            (change)="onExpansionStepSelectVertexTypeChange(i)"
                            attr.aria-label="{{ 'Vertex type' | translate }}">
                            {{ vertexType.name }}
                          </mat-checkbox>
                        </div>
                        <button
                          mat-icon-button
                          fxFlex="40px"
                          [color]="primary"
                          matTooltip="{{ 'Edit filter for' | translate }} {{ vertexType.name }}"
                          matTooltipPosition="right"
                          (click)="addAttributeFilter(vertexType.name, oneStepConfig.attributeFilter)"
                          attr.aria-label="{{ 'Edit filter for' | translate }} {{ vertexType.name }}">
                          <mat-icon
                            class="svg-icon"
                            svgIcon="filter"
                            attr.aria-label="{{ 'Edit filter for' | translate }} {{ vertexType.name }}">
                          </mat-icon>
                        </button>
                      </div>
                      <div class="hint-text attribute-filter-text graph-entity-type">
                        {{ removeAlias(oneStepConfig.attributeFilter.get(vertexType.name)?.toString(), vertexType.name) }}
                      </div>
                    </div>
                  </cdk-virtual-scroll-viewport>
                  <div class="clear"></div>
                </div>

                <!-- Remove expansion layer -->
                <button class="graph-explorer-controller-action-button" mat-stroked-button color="warn" (click)="removeExpansionLayer(i)" [disabled]="expansionStepsConfig.length < 2">
                    {{ 'Remove expansion step' | translate }}
                </button>
              </div>
            </div>
            <!-- Add one more expansion layer -->
            <button class="graph-explorer-controller-action-button" mat-stroked-button color="accent" (click)="addExpansionLayer()">
                {{ 'Add expansion step' | translate }}
            </button>
          </div>

          <!-- Paths configurations -->
          <div class="box-container" fxLayout="column" *ngIf="inPathsTab()">
            <mat-radio-group fxFlex="56px" fxLayout="column" [(ngModel)]="searchPathMode"
              (change)="onLeftPanelChange(graphExplorerLeftPanelConfig.SearchPathMode, searchPathMode)">
              <mat-radio-button value="single shortest path" fxFlex="28px">{{ 'Show one shortest path' | translate }}</mat-radio-button>
              <mat-radio-button value="all shortest paths" fxFlex="28px">{{ 'Show all shortest paths' | translate }}</mat-radio-button>
            </mat-radio-group>
            <span class="mat-input-warn mat-caption" *ngIf="searchPathMode === 'all shortest paths' || searchPathMode === 'all paths'">
              <mat-icon attr.aria-label="{{ 'Show/hide vertex attributes' | translate }}">warning</mat-icon> {{ 'Finding' | translate }} {{ searchPathMode | translate}} {{ 'might be slow and the result graph might be too large to be visualized.' | translate }}
            </span>

            <mat-form-field fxFlex="48px" class="max-width">
              <input matInput placeholder="{{ 'Maximal path length' | translate }}" [(ngModel)]="searchPathLengthLimit" min="1" type="number"
              (input)="onInputChange.next({ panelContent: graphExplorerLeftPanelConfig.SearchPathLengthLimit, value: searchPathLengthLimit })">
            </mat-form-field>
            <span class="mat-input-error mat-caption" *ngIf="searchPathLengthLimit < 1">
              {{ 'Path length limit must be a positive number.' | translate }}
            </span>
            <span class="mat-input-error mat-caption" *ngIf="!isInteger(searchPathLengthLimit)">
              {{ 'Path length limit must be an integer.' | translate }}
            </span>
            <span class="mat-input-warn mat-caption" *ngIf="searchPathLengthLimit > 6">
              <mat-icon attr.aria-label="{{ 'Warning' | translate }}">warning</mat-icon> {{ 'Finding paths longer than 6 steps might be slow.' | translate }}
            </span>
          </div>

          <!-- Connections configurations -->
          <div class="box-container" fxLayout="column" *ngIf="inConnectionsTab()">
            <mat-form-field fxFlex="48px" class="max-width">
              <input matInput placeholder="{{ 'Maximal connection path length' | translate }}" [(ngModel)]="searchPathLengthLimit" min="1" type="number">
            </mat-form-field>
            <span class="mat-input-error mat-caption" *ngIf="searchPathLengthLimit < 1">
              {{ 'Connection path length limit must be a positive number.' | translate }}
            </span>
            <span class="mat-input-error mat-caption" *ngIf="!isInteger(searchPathLengthLimit)">
              {{ 'Connection path length limit must be an integer.' | translate }}
            </span>
            <span class="mat-input-warn mat-caption" *ngIf="searchPathLengthLimit > 6">
              <mat-icon attr.aria-label="{{ 'Warning' | translate }}">warning</mat-icon> {{ 'Finding connection paths longer than 6 steps might be slow.' | translate }}
            </span>
          </div>

          <!-- Vertex type filter title -->
          <div *ngIf="!inExpandTab()" fxLayout="row" fxLayoutAlign="space-between center" class="section-header">
            <!-- Search Vertices By Attribute Filter -->
            <p class="hint-text" *ngIf="inVerticesTab()">{{ 'Pick vertices by vertex types' | translate }}</p>

            <!-- Find path going through vertex types -->
            <p class="hint-text" *ngIf="inPathsTab()">{{ 'Paths going through vertex types' | translate }}</p>

            <!-- Find connections going through vertex types -->
            <p class="hint-text" *ngIf="inConnectionsTab()">{{ 'Connected by vertex types' | translate }}</p>

            <!-- Expand or hide the content button -->
            <button
              mat-icon-button
              *ngIf="inVerticesTab() || inPathsTab() || inConnectionsTab()"
              class="toggle-btn"
              matTooltip="{{ 'Show/hide vertex attributes' | translate }}"
              matTooltipPosition="right"
              attr.aria-label="{{ 'Show/hide vertex attributes' | translate }}"
              [attr.aria-expanded]="!isVertexFilterFolded"
              (click)="isVertexFilterFolded = !isVertexFilterFolded">
              <mat-icon
                [@indicatorRotate]="isVertexFilterFolded ? 'collapsed' : 'expanded'"
                attr.aria-label="{{ 'Show/hide vertex attributes' | translate }}">
                expand_more
              </mat-icon>
            </button>
          </div>

          <!-- Vertex types -->
          <div class="box-container" *ngIf="(inVerticesTab() || inPathsTab() || inConnectionsTab()) && !isVertexFilterFolded">
            <mat-checkbox
              [(ngModel)]="selectAllVertexTypes"
              (change)="toggleBoolArray(graphExplorerLeftPanelConfig.VertexTypeFilter, vertexTypeFilter)"
              attr.aria-label="{{ 'Vertex type' | translate }}">
              {{ 'All' | translate }}
            </mat-checkbox>
            <div class="clear"></div>
            <cdk-virtual-scroll-viewport
              [style.height]="getVirtualScrollHeight(schema.vertexTypes.length)"
              class="virtual-scroll-container" itemSize="14">
              <div *cdkVirtualFor="let vertexType of schema.vertexTypes" class="box-container filter-box">
                <div fxLayout="row" fxLayoutGap="5px" fxLayoutAlign="center center">
                  <div
                    fxFlex="calc=(100%-40px)"
                    class="graph-entity-type"
                    matTooltip="{{ 'Vertex type' | translate }} {{ vertexType.name }}">
                    <mat-checkbox
                      [ngModel]="vertexTypeFilter.get(vertexType.name)"
                      (ngModelChange)="vertexTypeFilter.set(vertexType.name, $event)"
                      (change)="onSelectVertexTypeChange()"
                      attr.aria-label="{{ 'Vertex type' | translate }}">
                      {{ vertexType.name }}
                    </mat-checkbox>
                  </div>
                  <button
                    mat-icon-button
                    fxFlex="40px"
                    [color]="primary"
                    matTooltip="{{ 'Edit filter for' | translate }} {{ vertexType.name }}"
                    matTooltipPosition="right"
                    (click)="addAttributeFilter(vertexType.name, attributeFilter)"
                    attr.aria-label="{{ 'Edit filter for' | translate }} {{ vertexType.name }}">
                    <mat-icon
                      class="svg-icon"
                      svgIcon="filter"
                      attr.aria-label="{{ 'Edit filter for' | translate }} {{ vertexType.name }}">
                    </mat-icon>
                  </button>
                </div>
                <div class="hint-text attribute-filter-text graph-entity-type">
                  {{ removeAlias(attributeFilter.get(vertexType.name)?.toString(), vertexType.name) }}
                </div>
              </div>
            </cdk-virtual-scroll-viewport>
            <div class="clear"></div>
          </div>

          <!-- Edge type filter title -->
          <div *ngIf="inPathsTab() || inConnectionsTab()" fxLayout="row" fxLayoutAlign="space-between center" class="section-header">
            <!-- Find path going through edge types -->
            <p class="hint-text" *ngIf="inPathsTab()">{{ 'Paths going through edge types' | translate }}</p>

            <!-- Find connections going through edge types -->
            <p class="hint-text" *ngIf="inConnectionsTab()">{{ 'Connected by edge types' | translate }}</p>
            <button
              mat-icon-button
              *ngIf="inPathsTab() || inConnectionsTab()"
              class="toggle-btn"
              matTooltip="{{ 'Show/hide edge attributes' | translate }}"
              matTooltipPosition="right"
              attr.aria-label="{{ 'Show/hide edge attributes' | translate }}"
              [attr.aria-expanded]="!isEdgeFilterFolded"
              (click)="isEdgeFilterFolded = !isEdgeFilterFolded">
              <mat-icon
                [@indicatorRotate]="isEdgeFilterFolded ? 'collapsed' : 'expanded'"
                attr.aria-label="{{ 'Show/hide edge attributes' | translate }}">
                expand_more
              </mat-icon>
            </button>
          </div>

          <!-- Edge types -->
          <div class="box-container" *ngIf="(inPathsTab() || inConnectionsTab()) && !isEdgeFilterFolded">
            <mat-checkbox
              [(ngModel)]="selectAllEdgeTypes"
              (change)="toggleBoolArray('edgeTypeFilter', edgeTypeFilter)"
              attr.aria-label="{{ 'Edge type' | translate }}">
              {{ 'All' | translate }}
            </mat-checkbox>
            <cdk-virtual-scroll-viewport
              [style.height]="getVirtualScrollHeight(schema.edgeTypes.length)"
              class="virtual-scroll-container" itemSize="46">
              <div *cdkVirtualFor="let edgeType of schema.edgeTypes" class="box-container filter-box">
                <div fxLayout="row" fxLayoutGap="5px" fxLayoutAlign="center center">
                  <div
                    fxFlex="calc=(100%-40px)"
                    class="graph-entity-type"
                    matTooltip="{{ 'Edge type' | translate }} {{ edgeType.name }}">
                    <mat-checkbox
                      [ngModel]="edgeTypeFilter.get(edgeType.name)"
                      (ngModelChange)="edgeTypeFilter.set(edgeType.name, $event)"
                      (change)="onSelectEdgeTypeChange()"
                      attr.aria-label="{{ 'Edge type' | translate }}">
                      {{ edgeType.name }}
                    </mat-checkbox>
                  </div>
                  <button
                    mat-icon-button
                    fxFlex="40px"
                    [color]="primary"
                    matTooltip="{{ 'Edit filter for' | translate }} {{ edgeType.name }}"
                    matTooltipPosition="right"
                    (click)="addAttributeFilter(edgeType.name, attributeFilter)"
                    attr.aria-label="{{ 'Edit filter for' | translate }} {{ edgeType.name }}">
                    <mat-icon
                      class="svg-icon"
                      svgIcon="filter"
                      attr.aria-label="{{ 'Edit filter for' | translate }} {{ edgeType.name }}">
                    </mat-icon>
                  </button>
                </div>
                <div class="hint-text attribute-filter-text graph-entity-type">
                  {{ removeAlias(attributeFilter.get(edgeType.name)?.toString(), edgeType.name) }}
                </div>
              </div>
            </cdk-virtual-scroll-viewport>
            <div class="clear"></div>
          </div>

          <!-- Run Query -->
          <div *ngIf="inQueriesTab() && currentQueryName">
            <p class="hint-text">{{ 'Enter query parameters' | translate }}</p>
            <div class="box-container query-param-container">
              <div *ngIf="selectedQueryParameters && selectedQueryParameters.length === 0">
                <p class="hint-text empty-param-hint">{{ 'no parameters' | translate }}</p>
              </div>
              <app-dynamic-form
                #appDynamicForm
                *ngIf="currentQueryName"
                [questions]="selectedQueryParameters"
                [vertexTypes]="vertexList"
                [nestedLevel]="0"
                [allowNullParams]="true">
              </app-dynamic-form>
            </div>
            <button
              class="submit"
              mat-raised-button
              color="primary"
              [disabled]="dynamicForm === undefined ? false : !dynamicForm.form?.valid"
              matTooltip="{{ 'Run query' | translate }}"
              (click)="handleRun()">
              {{ 'Run query' | translate }}
            </button>
          </div>
          <!-- End of Down Panel -->
        </div>
        <!-- End of Edit Criteria Container -->
    </app-toolbar-container>

    <mat-divider [vertical]="!reflow"></mat-divider>

    <app-visual-editor
      #visualEditor
      class="max-height"
      [class.hidden]="expandEditCriteriaPanel"
      fxFlex.gt-sm="calc(100% - {{ leftPanelFlex + 40 }}px)"
      [componentName]="'GraphExplorer'"
      (selectionChange)="onSelectionChange()"
      (touchedVertex)="onTouchOneVertex($event)"
      (graphExplorerConfigApplied)="applyOrInitializeGraphExplorerConfig($event)">
    </app-visual-editor>
  </div>
</app-loading-indicator>

<ng-template #popupWindow>
  <div>
    <!-- Popup Window Toolbar -->
    <mat-toolbar color="primary" class="popup-toolbar">
      <h2 mat-dialog-title>{{ 'Add attribute filter' | translate }}</h2>
    </mat-toolbar>
    <!-- End of Popup Window Toolbar -->

    <mat-dialog-content>
      <!-- Attribute Filter Expression -->
      <div class="attribute-filter">
        <span class="hint-text">{{ 'Attribute filter' | translate }}</span>
        <div class="box-container">
          <span>{{ removeAlias(exprModel?.toString()) || 'None' }}</span>
        </div>
      </div>
      <!-- End of Attribute Filter Expression -->

      <!-- Attribute Filter Modification -->
      <div>
        <span class="hint-text">{{ 'Build attribute filter' | translate }}</span>
        <app-expression-form [(data)]="exprModel" [config]="exprFormConfig" [schema]="schema">
        </app-expression-form>
      </div>
      <!-- End of Attribute Filter Modification -->
    </mat-dialog-content>

    <!-- Action Button -->
    <mat-dialog-actions align="end">
      <button mat-button (click)="closePopup()">
        {{ 'CANCEL' | translate }}
      </button>
      <button
        mat-button
        color="primary"
        (click)="confirmAttributeFilter()"
        [disabled]="!exprModel?.semanticCheck().success && exprModel?.toString() !== ''">
        {{ 'ADD' | translate }}
      </button>
    </mat-dialog-actions>
    <!-- End of Action Button -->
  </div>
</ng-template>
