// @ts-nocheck
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { MatDialog } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { By } from '@angular/platform-browser';
import { RouterTestingModule } from '@angular/router/testing';
import { TranslateModule } from '@ngx-translate/core';
import { BehaviorSubject, of, Subject, throwError } from 'rxjs';

import { AuthService, FileUploadResponse, FileUploadService, LicenseService, MessageBus } from '@app/core/services';

import { HomeComponent } from './home.component';
import { HomeService } from './home.service';
import { RDBMSMigrationLogicService } from './shared';
import { HttpClientModule } from '@angular/common/http';
import { share } from 'rxjs/operators';
import { setVersion } from '@tigergraph/tools-models';

describe('HomeComponent', () => {
  let component: HomeComponent;
  let fixture: ComponentFixture<HomeComponent>;
  let fileUploadService: FileUploadService;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      imports: [RouterTestingModule, TranslateModule.forRoot(), HttpClientModule],
      declarations: [HomeComponent],
      schemas: [NO_ERRORS_SCHEMA],
      providers: [
        {
          provide: MatDialog,
          useFactory: () => ({
            open: () => { }
          })
        },
        {
          provide: MatSnackBar,
          useFactory: () => ({
            open: () => { }
          })
        },
        {
          provide: AuthService,
          useFactory: () => ({
            getUserRole: () => 'superuser',
            getCurrentGraph: () => 'graph_1',
            isSuperUser: () => true,
            hasPrivilege: () => true,
            hasAllPrivileges: () => true,
            getGraphList: () => [],
            hasPermissionToAccessWriteQueriesPage: () => true,
            hasAllPrivilegesByURL: () => true,
          })
        },
        {
          provide: LicenseService,
          useFactory: () => ({
            getLicense: () => of({
              Audience: 'abc',
              StartTime: **********,
              EndTime: **********,
              Version: 'All',
              Status: 'Valid'
            })
          })
        },
        {
          provide: MessageBus,
          useFactory: () => ({
            from: () => of(),
            to: () => ({
              next: () => { }
            }),
          })
        },
        {
          provide: FileUploadService,
          useClass: class {
            response = new Subject<FileUploadResponse>();
            response$ = this.response.asObservable().pipe(share());
            upload() { }
            cancel() { }
            init() { }
          }
        }
      ]
    })
      .overrideComponent(HomeComponent, {
        set: {
          providers: [
            {
              provide: HomeService,
              useFactory: () => ({
                importSolution: () => { },
                logCurrentGraph: () => { },
                logPageView: () => { }
              })
            },
            {
              provide: RDBMSMigrationLogicService,
              useFactory: () => ({})
            },
          ]
        }
      })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(HomeComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
    fileUploadService = TestBed.inject(FileUploadService);
  });

  it('should be created', () => {
    expect(component).toBeTruthy();
  });

  describe('should render the menu', () => {
    it('in normal mode', () => {
      // Logos.
      const logoDes = fixture.debugElement.queryAll(By.css('img'));
      expect(logoDes[0].attributes['src']).toEqual('assets/img/tigergraph_light.png');
      expect(logoDes[1].attributes['src']).toEqual('assets/img/graph_studio.png');

      // Menu titles.
      const titleNames = [
        'Design Schema', 'Load Data', 'Explore Graph', 'Write Queries',
        'Migrate From Relational Database (alpha)',
        'Import An Existing Solution', 'Export Current Solution',
      ];
      const titleDes = fixture.debugElement.queryAll(By.css('.home-item-title'));
      titleDes.forEach((de, index) => {
        const el = de.nativeElement;
        const text = el.textContent.trim();
        expect(text).toBe(titleNames[index]);
      });

      // Section descriptions.
      const descs = [
        'Model your business problem as a graph schema',
        'Load data into the graph based on the data mapping',
        'Search vertices, explore neighborhoods and find paths',
        'Use GSQL language to implement your business applications',
        'Migrate schema and data from your relational database',
        'Import from a solution tarball',
        'Export the graph schema, data mapping, queries and data source as a tarball',
      ];
      const descDes = fixture.debugElement.queryAll(By.css('.hint-text'));
      descDes.forEach((de, index) => {
        const el = de.nativeElement;
        const text = el.textContent.trim();
        expect(text).toBe(descs[index]);
      });
    });

    it('cannot import, export or migratin from RDBMS', () => {
      spyOn(component, 'canImportSolution').and.returnValue(false);
      spyOn(component, 'canExportSolution').and.returnValue(false);
      spyOn(component, 'canMigrateFromRDBMS').and.returnValue(false);
      fixture.detectChanges();

      const normalTitleDes = fixture.debugElement.queryAll(By.css('.home-item-title'));
      expect(normalTitleDes.length).toBe(7);
      const disabledTitleDes = fixture.debugElement.queryAll(By.css('.home-item-title-disabled'));
      expect(disabledTitleDes.length).toBe(3);
    });
  });

  describe('should import a solution', () => {
    let dialog: MatDialog;
    let snackBar: MatSnackBar;
    let compService: HomeService;

    const files = {
      length: 1,
      item: () => new File([''], '')
    };
    const dialogRef = {
      afterClosed: () => { }
    };

    beforeEach(() => {
      dialog = TestBed.inject(MatDialog);
      snackBar = TestBed.inject(MatSnackBar);
      compService = fixture.debugElement.injector.get(HomeService);
      setVersion('3.10.0');

      spyOn(dialog, 'open').and.returnValue(dialogRef);
      spyOn(snackBar, 'open');
    });

    it('with no selected file', () => {
      spyOn(compService, 'importSolution');
      component.uploadFile(undefined);
      expect(compService.importSolution).not.toHaveBeenCalled();
    });

    it('with user select "Cancel"', () => {
      spyOn(dialogRef, 'afterClosed').and.returnValue(of(0));
      spyOn(compService, 'importSolution');

      component.uploadFile(files);
      expect(compService.importSolution).not.toHaveBeenCalled();
    });

    it('with user select "Overwrite" and success', () => {
      spyOn(dialogRef, 'afterClosed').and.returnValue(of(1));
      spyOn(compService, 'importSolution').and.returnValue(of({}));
      spyOn(fileUploadService, 'upload');

      component.uploadFile(files);

      fileUploadService.response.next({ status: 'oneSingleFileComplete', message: 'success', progress: 100 });
      expect(fileUploadService.upload).toHaveBeenCalled();
      expect(snackBar.open).toHaveBeenCalled();
    });

    it('with user select "Overwrite" and failure', () => {
      spyOn(dialogRef, 'afterClosed').and.returnValue(of(1));
      spyOn(compService, 'importSolution').and.returnValue(throwError({ error: 'error', statusText: 'Error' }));
      spyOn(<any>component, 'handleError').and.callThrough();
      spyOn(fileUploadService, 'upload');

      component.uploadFile(files);
      fileUploadService.response.next({ status: 'oneSingleFileComplete', message: 'success', progress: 100 });

      expect(fileUploadService.upload).toHaveBeenCalled();
      expect(snackBar.open).not.toHaveBeenCalled();
      expect((<any>component).handleError).toHaveBeenCalled();
    });
  });

  describe('should export the current solution', () => {
    beforeEach(() => {
      component.openExportDialog();
      component.isExportData = true;
      setVersion('3.10.0');
    });

    it('with user select "Cancel"', () => {
      spyOn(window, 'open');
      spyOn(component, 'closeExportDialog');
      component.closeExportDialog();
      expect(window.open).not.toHaveBeenCalled();
      expect(component.closeExportDialog).toHaveBeenCalled();
    });

    xit('with user select "Export"', () => {
      spyOn(window, 'open');
      spyOn(component, 'closeExportDialog');
      component.export();
      expect(window.open).toHaveBeenCalledWith(`${location.origin}/api/system/export?isExportData=true&exportGraphNames=`, '_blank');
    });
  });
});
