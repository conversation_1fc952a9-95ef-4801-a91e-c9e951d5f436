import { Injectable } from '@angular/core';
import { HttpParams, HttpClient } from '@angular/common/http';
import { Observable, forkJoin, from as promiseToObservable } from 'rxjs';
import { map } from 'rxjs/operators';

import { HttpSourceService } from '@app/core/services';
import { RDBMSMeta, RDBMSTableMeta } from '@tigergraph/tools-models/rdbms';
import {
  DataSourceLogic,
  DBLoadingJobJson,
  GSQLLoadingJobJson,
} from '@tigergraph/tools-models/loading-job';
import {
  SchemaChange,
  DBGraphStyleJson,
} from '@tigergraph/tools-models/topology';
import { HelperFunctions } from '@app/shared/utils';
import { TableSampleData } from '@app/loading-builder/loading-builder.service';
import {
  TabularParser,
  DataFormat,
  FileFormat,
} from '@tigergraph/tools-models/loading-job/data-set.interface';
import { AnalyticsService } from '@app/shared/services/analytics.service';
import {
  changeSchema,
  createGraph,
  deleteGraph,
  getRDBMSDump,
  getRDBMSMeta,
} from '@tigergraph/tools-models';

const importCheckURL = '/api/system/import-check';
const importURL = '/api/system/import';
const exportCheckURL = '/api/system/export-check';
const configURL = '/api/config';
const getLocalFileSampleDataURL = '/api/restpp/onlineparser';
const schemaStyleURL = '/api/graph-styles';
const loadingJobURL = '/api/loading-jobs';
const loadingJobInfoURL = '/api/loading-job-info/[GRAPH_NAME]';

@Injectable()
export class HomeService {
  constructor(
    private http: HttpClient,
    private httpSrc: HttpSourceService,
    private analyticsService: AnalyticsService
  ) {}

  /**
   * Import check.
   *
   * @returns {Observable<{ graphNames: string[] }>}
   * @memberof HomeService
   */
  importCheck(
    file: File | string
  ): Observable<{ graphNames: string[]; tempDirName: string }> {
    if (typeof file === 'string') {
      return this.httpSrc.post<{ graphNames: string[]; tempDirName: string }>(
        `${importCheckURL}?filename=${file}`
      );
    }
    const form = new FormData();
    form.append('file', file);
    return this.httpSrc.post<{ graphNames: string[]; tempDirName: string }>(
      importCheckURL,
      {
        body: form,
      }
    );
  }

  /**
   * Import solution with graphs from tarball.
   *
   * @returns {Observable<any>}
   * @memberof HomeService
   */
  importSolutionWithGraphs(graphNames: string[], tempDirName: string): Observable<any> {
    let params = new HttpParams();
    params = params.append('tempDirName', tempDirName);
    graphNames.forEach((graphName) => {
      params = params.append('graphName', graphName);
    });
    return this.httpSrc.post(
      HelperFunctions.concatURLAndSearchParams(importURL, params)
    );
  }

  /**
   * Import solution from tarball.
   *
   * @returns {Observable<any>}
   * @memberof HomeService
   */
  importSolution(file: File | string): Observable<any> {
    if (typeof file === 'string') {
      return this.httpSrc.post(`${importURL}?filename=${file}`);
    }
    const form = new FormData();
    form.append('file', file);
    return this.httpSrc.post(importURL, {
      body: form,
    });
  }

  exportCheck() {
    return this.httpSrc.get<{
      isEnoughSpace: boolean;
      graphSize: number;
      diskFreeSpace: number;
    }>(exportCheckURL);
  }

  /**
   * Get RDBMS meta info.
   *
   * @param {string} type
   * @param {string} server
   * @param {string} port
   * @param {string} database
   * @param {string} username
   * @param {string} password
   * @returns {Observable<RDBMSMeta>}
   * @memberof HomeService
   */
  getRDBMSMeta(
    type: string,
    server: string,
    port: string,
    database: string,
    username: string,
    password: string
  ): Observable<RDBMSMeta> {
    return promiseToObservable(
      getRDBMSMeta({
        type,
        server,
        port: Number(port),
        database,
        username: username,
        password: password,
      }).then((res) => res.data.results)
    ).pipe(
      map((response) => ({
        type: type,
        server: server,
        port: port,
        database: database,
        username: username,
        password: password,
        tableMetas: <RDBMSTableMeta[]>response,
      }))
    );
  }

  /**
   * Get RDBMS meta info.
   *
   * @param {string} type
   * @param {string} server
   * @param {string} port
   * @param {string} database
   * @param {string} username
   * @param {string} password
   * @returns {Observable<RDBMSMeta>}
   * @memberof HomeService
   */
  dumpRDBMSTable(
    type: string,
    server: string,
    port: string,
    database: string,
    username: string,
    password: string,
    tables: string[],
    limit?: number
  ): Observable<boolean> {
    return promiseToObservable(
      getRDBMSDump({
        type,
        server,
        port: Number(port),
        database,
        username,
        password,
        tableNames: tables,
        limit,
      }).then((res) => res.data.results)
    ).pipe(map(() => false));
  }

  createGraph(graphName: string): Observable<any> {
    return promiseToObservable(
      createGraph({
        GraphName: graphName,
        VertexTypes: [],
        EdgeTypes: [],
      }).then((res) => res.data.message)
    );
  }

  dropGraph(graphName: string): Observable<any> {
    return promiseToObservable(
      deleteGraph({
        graph: graphName,
      }).then((res) => res.data.message)
    );
  }

  changeSchema(graphName: string, change: SchemaChange): Observable<any> {
    return promiseToObservable(
      changeSchema({
        graph: graphName,
        schemaChange: change,
      }).then((res) => res.data.results)
    );
  }

  getDataFilePath(): Observable<string> {
    const params = new HttpParams()
      .append('key', 'System.DataRoot')
      .append('key', 'GUI.DataDirRelativePath');
    return this.http
      .get(configURL, { params: params })
      .pipe(
        map(
          (configs) =>
            `${configs['System.DataRoot']}/${configs['GUI.DataDirRelativePath']}/loading_data`
        )
      );
  }

  /**
   * Get sample data from a data set with its data parser selections.
   *
   * @param {string} fileURI
   * @param {TabularParser} dataParser
   * @returns {Observable<TableSampleData>}
   * @memberof HomeService
   */
  getSampleData(
    fileURI: string,
    dataParser: TabularParser
  ): Observable<TableSampleData> {
    const columnLimit = 1024;
    const tokenLengthLimit = 1048576;
    const body = {
      source: {
        type: 'file',
        uri: fileURI,
        dataFormat: DataFormat.CSV,
        parsing: {
          fileFormat: FileFormat.None,
          separator: DataSourceLogic.separators.find(
            (sep) => sep[1] === dataParser.separator
          )[0],
          eol: DataSourceLogic.eols.find((eol) => eol[1] === dataParser.eol)[0],
          header: dataParser.header,
          // here we use empty string to represent no quote.
          quote:
            dataParser.quote === ''
              ? undefined
              : DataSourceLogic.quotes.find(
                  (quote) => quote[1] === dataParser.quote
                )[0],
        },
      },
      size: 10,
      column_number_limit: columnLimit,
      token_length_limit: tokenLengthLimit,
    };

    return this.httpSrc
      .post<TableSampleData>(getLocalFileSampleDataURL, {
        body: JSON.stringify(body),
      })
      .pipe(
        map((response) => {
          response.header = response.header || [];
          response.data = response.data.length !== 0 ? response.data : [[]];

          return this.formatSampleData(response);
        })
      );
  }

  /**
   * Format the sample data.
   *
   * @private
   * @param {TableSampleData} data
   * @returns {TableSampleData}
   * @memberof HomeService
   */
  formatSampleData(sampleData: TableSampleData): TableSampleData {
    // Find the maximum column number.
    let maxColNumber = sampleData.header.length;
    sampleData.data.forEach(
      (row) =>
        (maxColNumber = maxColNumber < row.length ? row.length : maxColNumber)
    );

    // If the header is empty, add default header.
    for (let i = 0; i < maxColNumber; i++) {
      if (!sampleData.header[i]) {
        sampleData.header[i] = `column_${i + 1}`;
      }
    }

    // Pad additional column for mismatch row.
    for (let i = 0; i < sampleData.data.length; i++) {
      const row = sampleData.data[i];
      const length = row.length;

      if (length < maxColNumber) {
        for (let j = 0; j < maxColNumber - length; j++) {
          row.push('N/A');
        }
      }
    }

    return sampleData;
  }

  /**
   * Save the loading jobs to server.
   *
   * @param {GSQLLoadingJobJson[]} json
   * @returns {Observable<any>}
   * @memberof HomeService
   */
  saveLoadingJobs(json: GSQLLoadingJobJson[]): Observable<any> {
    return forkJoin(
      json.map((loadingJob) =>
        this.http.post(
          `${loadingJobURL}/${loadingJob.GraphName}/meta/${loadingJob.JobName}`,
          loadingJob
        )
      )
    ).pipe(map(() => false));
  }

  /**
   * Save the loading jobs information to server.
   *
   * @param {DBLoadingJobJson[]} json
   * @returns {Observable<any>}
   * @memberof HomeService
   */
  saveLoadingJobsInfo(
    json: DBLoadingJobJson[],
    graphName: string
  ): Observable<any> {
    const url = HelperFunctions.applyGraphNameToURL(
      graphName,
      loadingJobInfoURL
    );
    return this.http.put(url, json).pipe(map(() => false));
  }

  /**
   * Save the schema style to server.
   *
   * @param {string} graphName
   * @param {DBGraphStyleJson} json
   * @returns {Observable<any>}
   * @memberof HomeService
   */
  saveSchemaStyle(graphName: string, json: DBGraphStyleJson): Observable<any> {
    return this.http
      .post(`${schemaStyleURL}/local/${graphName}`, json)
      .pipe(map(() => false));
  }

  /**
   * Push new navigation action to analytics
   *
   * @param {number} to ID of the page being navigated to.
   * @param {string} from Page/location which is being navigated from.
   * @memberof HomeService
   */
  logNavigationEvent(to: number, from: string): void {
    this.analyticsService.logNavigationEvent(to, from);
  }

  /**
   * Log page view of the home page.
   *
   * @memberof HomeService
   */
  logPageView(): void {
    this.analyticsService.logPageView('Home Page');
  }
}
