<h1 class="cdk-visually-hidden">Home</h1>
<div
  fxLayout="column"
  fxLayoutAlign="center center"
  fxLayoutAlign.lt-lg="start center"
  class="container"
  [ngClass.lt-lg]="'container-small'">
  <img *ngIf="isDarkTheme" src="assets/img/tigergraph_light.png" alt="TigerGraph" class="company-logo-light">
  <img *ngIf="!isDarkTheme" src="assets/img/tigergraph_dark.png" alt="TigerGraph" class="company-logo-dark">
  <img src="assets/img/graph_studio.png" alt="GraphStudio">
  <br/><br/>

  <div
    fxLayout="row"
    fxLayoutGap="50px"
    fxLayout.lt-lg="column"
    fxLayoutGap.lt-lg="20px">
    <div fxLayout="column" fxLayoutGap="20px">
      <div
        *ngFor="let item of menuItems; index as i"
        fxLayout="row"
        fxLayoutGap="20px"
        [ngClass.gt-md]="'homepage-item-container'"
        matTooltip="{{ isAccessible(item, i) ? null : 'You will need to create or select a local graph to continue' | translate }}"
      >
        <ng-container
          [ngTemplateOutlet]="isAccessible(item, i) ? accessibleIcon : noAccessibleIcon"
          [ngTemplateOutletContext]="{ item: item, index: i }"
        >
        </ng-container>
        <div>
          <div>
            <ng-container
              [ngTemplateOutlet]="isAccessible(item, i) ? accessibleTitle : noAccessibleTitle"
              [ngTemplateOutletContext]="{ item: item, index: i }"
            >
            </ng-container>
          </div>
          <span class="hint-text">
            {{ item.subTitle | translate }}
          </span>
        </div>
      </div>

      <ng-template #accessibleIcon let-item="item" let-index="index">
        <a
          href="javascript:void(0)"
          *ngIf="isAccessible(item, index)"
          (click)="navigateTo(item, index)"
          class="migrate-href"
          role="button"
          attr.aria-label="{{ item.title }}"
          aria-describedby="home-item-title-label">
          <mat-icon
            class="home-item-icon svg-icon"
            color="primary"
            [svgIcon]="item.svgIcon"
            attr.aria-label="{{ item.title }}">
          </mat-icon>
        </a>
      </ng-template>
      <ng-template #noAccessibleIcon let-item="item" let-index="index">
        <mat-icon
          *ngIf="!isAccessible(item, index)"
          class="home-item-icon svg-icon icon-disabled"
          [svgIcon]="item.svgIcon"
          attr.aria-label="{{ item.title | translate }}">
        </mat-icon>
      </ng-template>
      <ng-template #accessibleTitle let-item="item" let-index="index">
        <a
          href="javascript:void(0)"
          class="mat-title home-item-title migrate-href"
          (click)="navigateTo(item, index)"
          role="button"
          attr.aria-label="{{ item.title | translate }}"
          aria-describedby="home-label">
          {{ item.title | translate }}
        </a>
      </ng-template>
      <ng-template #noAccessibleTitle let-item="item" let-index="index">
        <span
          class="mat-title home-item-title home-item-title-disabled">
          {{ item.title }}
        </span>
      </ng-template>

    </div>

    <div fxLayout="column" fxLayoutGap="20px">

      <!-- Migrate from relational database -->
      <div fxLayout="row" fxLayoutGap="20px" [ngClass.gt-md]="'homepage-item-container'">
        <a
          href="javascript:void(0)"
          *ngIf="canMigrateFromRDBMS() && (license | async)?.Status ==='Valid'"
          class="migrate-href"
          id="start-rdbms-migration-icon"
          (click)="startMigration()"
          matTooltip="{{ 'Migrate From Relational Database (alpha)' | translate }}"
          role="button"
          attr.aria-label="{{ 'Migrate From Relational Database (alpha)' | translate }}"
          aria-describedby="rdbms-migration-home-label">
          <mat-icon
            class="home-item-icon builtin-icon"
            color="primary"
            attr.aria-label="{{ 'Migrate From Relational Database (alpha)' | translate }}">
            all_inclusive
          </mat-icon>
        </a>
        <mat-icon
          *ngIf="!canMigrateFromRDBMS() || (license | async)?.Status !=='Valid'"
          class="home-item-icon builtin-icon icon-disabled"
          attr.aria-label="{{ 'Migrate From Relational Database (alpha)' | translate }}">
          all_inclusive
        </mat-icon>
        <div>
          <span
            *ngIf="!canMigrateFromRDBMS() || (license | async)?.Status !=='Valid'"
            class="mat-title home-item-title home-item-title-disabled"
            id="start-rdbms-migration-title" >
            {{ 'Migrate From Relational Database (alpha)' | translate }}
          </span>
          <a
            href="javascript:void(0)"
            *ngIf="canMigrateFromRDBMS() && (license | async)?.Status ==='Valid'"
            class="migrate-href mat-title home-item-title"
            (click)="startMigration()"
            id="start-rdbms-migration-title"
            role="button"
            attr.aria-label="{{ 'Migrate From Relational Database (alpha)' | translate }}"
            aria-describedby="rdbms-migration-home-label">
            {{ 'Migrate From Relational Database (alpha)' | translate }}
          </a><br/>
          <span class="hint-text" id="rdbms-migration-home-label">
            {{ 'Migrate schema and data from your relational database' | translate }}
          </span>
        </div>
      </div>

      <!-- Import solution -->
      <div fxLayout="row" fxLayoutGap="20px" [ngClass.gt-md]="'homepage-item-container'">
        <a
          *ngIf="canImportSolution() && ((license | async)?.Status ==='Valid' || isCommunityVersion())"
          href="javascript:void(0)"
          (click)="fileInput.click()"
          matTooltip="{{ 'Import An Existing Solution' | translate }}"
          role="button"
          attr.aria-label="{{ 'Import An Existing Solution' | translate }}"
          aria-describedby="import-solution-home-label">
          <mat-icon
            class="home-item-icon builtin-icon"
            color="primary"
            attr.aria-label="{{ 'Import An Existing Solution' | translate }}">
            file_download
          </mat-icon>
        </a>
        <mat-icon
          *ngIf="!canImportSolution() || ((license | async)?.Status !=='Valid' && !isCommunityVersion())"
          class="home-item-icon builtin-icon icon-disabled"
          attr.aria-label="{{ 'Import An Existing Solution' | translate }}">
          file_download
        </mat-icon>
        <div>
          <span
            *ngIf="!canImportSolution() || ((license | async)?.Status !=='Valid' && !isCommunityVersion())"
            class="mat-title home-item-title home-item-title-disabled">
            {{ 'Import An Existing Solution' | translate }}
          </span>
          <a
            *ngIf="canImportSolution() && ((license | async)?.Status ==='Valid' || isCommunityVersion())"
            class="mat-title home-item-title"
            href="javascript:void(0)"
            (click)="fileInput.click()"
            role="button"
            attr.aria-label="{{ 'Import An Existing Solution' | translate }}"
            aria-describedby="import-solution-home-label">
            {{ 'Import An Existing Solution' | translate }}
          </a><br/>
          <span class="hint-text" id="import-solution-home-label">
            {{ 'Import from a solution tarball' | translate }}
          </span><br/>
        </div>
        <input
          #fileInput type="file" hidden
          (change)="uploadFile($event.target.files)"
          accept=".gz,.tgz"
          attr.aria-label="{{ 'Import An Existing Solution' | translate }}"/>
      </div>

      <!-- Export solution -->
      <div fxLayout="row" fxLayoutGap="20px" [ngClass.gt-md]="'homepage-item-container'">
        <a
          *ngIf="canExportSolution() && ((license | async)?.Status === 'Valid' || isCommunityVersion())"
          href="javascript:void(0)"
          (click)="handleExport()"
          matTooltip="{{ 'Export Current Solution' | translate }}"
          role="button"
          attr.aria-label="{{ 'Export Current Solution' | translate }}"
          aria-describedby="export-solution-home-label">
          <mat-icon
            class="home-item-icon builtin-icon"
            color="primary"
            attr.aria-label="{{ 'Export Current Solution' | translate }}">
            file_upload
          </mat-icon>
        </a>
        <mat-icon
          *ngIf="!canExportSolution() || ((license | async)?.Status !== 'Valid' && !isCommunityVersion())"
          class="home-item-icon builtin-icon icon-disabled"
          attr.aria-label="{{ 'Export Current Solution' | translate }}">
          file_upload
        </mat-icon>
        <div>
          <span
            *ngIf="!canExportSolution() || ((license | async)?.Status !=='Valid' && !isCommunityVersion())"
            class="mat-title home-item-title home-item-title-disabled">
            {{ 'Export Current Solution' | translate }}
          </span>
          <a
            *ngIf="canExportSolution() && ((license | async)?.Status ==='Valid' || isCommunityVersion())"
            class="mat-title home-item-title"
            href="javascript:void(0)"
            (click)="handleExport()"
            role="button"
            attr.aria-label="{{ 'Export Current Solution' | translate }}"
            aria-describedby="export-solution-home-label">
            {{ 'Export Current Solution' | translate }}
          </a><br/>
          <span class="hint-text" id="export-solution-home-label">
            {{ 'Export the graph schema, data mapping, queries and data source as a tarball' | translate }}
          </span>
        </div>
      </div>

    </div>
  </div>
</div>

<ng-template #migrationWindow>
  <div>
    <!-- Popup Window Toolbar -->
    <mat-toolbar color="primary" class="popup-toolbar">
      <h2 *ngIf="currentMigrationStep === 0">{{ 'Step 1. Choose relational database type' | translate }}</h2>
      <h2 *ngIf="currentMigrationStep === 1">{{ 'Step 2. Input connection information' | translate }}</h2>
      <h2 *ngIf="currentMigrationStep === 2">{{ 'Step 3. Choose tables / attributes to be migrated' | translate }}</h2>
      <h2 *ngIf="currentMigrationStep === 3">{{ 'Step 4. Configurations' | translate }}</h2>
    </mat-toolbar>

    <mat-dialog-content fxLayout="column" fxLayoutGap="5px">
      <!-- Choose RDBMS type -->
      <div
        *ngIf="currentMigrationStep === 0"
        fxLayout="row wrap"
        fxLayoutAlign="start"
        class="migration-container"
      >
        <button
          class="rdbms-choice"
          *ngFor="let rdbms of this.supportedRDBMS, let i = index"
          mat-stroked-button
          [color]="chosenRDBMS === rdbms[0] ? 'primary' : ''"
          (click)="chosenRDBMS = rdbms[0]; rdbmsPort = rdbms[2]; rdbmsServer = rdbmsDatabase = rdbmsUsername = rdbmsPassword = ''"
          [attr.aria-label]="rdbms[1]">
          {{ rdbms[1] }}
        </button>
      </div>

      <!-- Input connection info -->
      <div
        *ngIf="currentMigrationStep === 1"
        fxLayout="column"
        class="migration-container"
      >
        <div class="box-container">
          <div fxLayout="row" fxLayoutGap="24px">
            <div fxFlex="80%">
              <div id="rdbms-server-label">{{ 'Server' | translate }}</div>
              <mat-form-field>
                <input
                  id="rdbms-server"
                  matInput required
                  [(ngModel)]="rdbmsServer"
                  aria-labelledby="rdbms-server-label">
              </mat-form-field>
            </div>

            <div>
              <div id="rdbms-port-label">{{ 'Port' | translate }}</div>
              <mat-form-field>
                <input
                  id="rdbms-port"
                  matInput type='number' required
                  [(ngModel)]="rdbmsPort"
                  aria-labelledby="rdbms-port-label">
              </mat-form-field>
            </div>
          </div>

          <div id="rdbms-database-label">{{ 'Database' | translate }}</div>
          <mat-form-field>
            <input
              id="rdbms-database"
              matInput required
              [(ngModel)]="rdbmsDatabase"
              aria-labelledby="rdbms-database-label">
          </mat-form-field>

          <div fxLayout="row" fxLayoutGap="24px">
            <div fxFlex>
              <div id="rdbms-username-label">{{ 'Username' | translate }}</div>
              <mat-form-field>
                <input
                  id="rdbms-username"
                  matInput required
                  [(ngModel)]="rdbmsUsername"
                  aria-labelledby="rdbms-username-label">
              </mat-form-field>
            </div>

            <div fxFlex>
              <div id="rdbms-password-label">{{ 'Password' | translate }}</div>
              <mat-form-field>
                <input
                  id="rdbms-password"
                  matInput type='password' required
                  [(ngModel)]="rdbmsPassword"
                  aria-labelledby="rdbms-password-label">
              </mat-form-field>
            </div>
          </div>
        </div>

      </div>

      <div
        *ngIf="currentMigrationStep === 2"
        class="migration-container"
      >
        <mat-checkbox
          [checked]="allTablesSelected()"
          (change)="updateAllTablesSelection($event)">
          {{ 'All tables' | translate }}
        </mat-checkbox>
        <div *ngFor="let tMeta of rdbmsMeta.tableMetas, let i = index" class="box-container table-container">
          <div class="migration-selector">
            <mat-checkbox
              [(ngModel)]="tablesToBeMigrated[i]">
              {{ tMeta.tableName }}
            </mat-checkbox>
          </div>
          <div
            fxLayout="row wrap"
            fxLayoutAlign="start"
            class="box-container"
          >
            <div *ngFor="let colMeta of tMeta.columns, let j = index" class="migration-selector">
              <mat-checkbox
                [(ngModel)]="attributesToBeMigrated[i][j]"
                [disabled]="!tablesToBeMigrated[i]"
                attr.aria-label="{{ tMeta.tableName }}">
                {{ colMeta.name }}
              </mat-checkbox>
            </div>
          </div>
        </div>
      </div>

      <div
        *ngIf="currentMigrationStep === 3"
        class="migration-container"
      >
        <mat-hint>{{ 'General' | translate }}</mat-hint>
        <div class="box-container table-container">
          <mat-form-field>
            <input matInput [(ngModel)]="rdbmsGraphName" placeholder="{{ 'Graph name' | translate }}">
          </mat-form-field>
          <div class="migration-selector">
            <mat-checkbox
              disabled="true"
              [(ngModel)]="foreignKeyAsEdge">
              {{ 'Treat foreign keys as edge types.' | translate }}
            </mat-checkbox>
          </div>
        </div>
        <mat-hint>Index</mat-hint>
        <div class="box-container table-container">
          <div class="migration-selector">
            <mat-checkbox
              [(ngModel)]="createIndex">
              {{ 'Create index for attributes having index in relational database.' | translate }}
            </mat-checkbox>
          </div>
        </div>
        <mat-hint>Data size</mat-hint>
        <div class="box-container table-container" fxLayout="row" fxLayoutGap="12px">
          <div class="migration-selector" style="padding-top: 16px;" fxFlex="70%">
            <mat-checkbox
              [(ngModel)]="lineLimit">
              {{ 'Limit number of records to be migrated in each table.' | translate }}
            </mat-checkbox>
          </div>
          <mat-form-field fxFlex>
            <input matInput [disabled]="!lineLimit" [(ngModel)]="limit">
          </mat-form-field>
        </div>
        <!-- Disabled for now. Will discuss how to support this in the future. -->
        <!-- <mat-hint>Escalating attributes as vertex types</mat-hint>
        <div class="box-container">
          <div *ngFor="let tMeta of refinedTablesMeta, let i = index" class="table-container">
            <div class="migration-selector">
              {{ tMeta.tableName }}
            </div>
            <div
              fxLayout="row wrap"
              fxLayoutAlign="start"
              class="box-container"
            >
              <div
                *ngFor="let colMeta of tMeta.columns, let j = index"
                class="migration-selector"
                fxLayout="row"
              >
                <mat-checkbox style="padding-top: 16px;"
                  [(ngModel)]="escalateAttributeAsVertex[i][j]"
                >
                </mat-checkbox>
                <mat-form-field style="width: 135px; margin-left: 6px;">
                  <input
                    matInput
                    [disabled]="!escalateAttributeAsVertex[i][j]"
                    [(ngModel)]="escalateAttributeAsVertexName[i][j]"
                  >
                </mat-form-field>
              </div>
            </div>
          </div>
        </div> -->
      </div>
    </mat-dialog-content>

    <mat-dialog-actions align="end">
      <button
        mat-button
        color="warn"
        (click)="cancelMigration()"
      >
        {{ 'CANCEL' | translate }}
      </button>
      <button
        mat-button
        (click)="migrationPrevStep()"
        [disabled]="currentMigrationStep === 0"
      >
        {{ 'BACK' | translate }}
      </button>
      <!-- Finish choosing RDBMS type -->
      <button
        *ngIf="currentMigrationStep === 0"
        mat-button
        id="choose-rdbms-type"
        [disabled]="chosenRDBMS === undefined"
        (click)="migrationNextStep()"
      >
        {{ 'NEXT' | translate }}
      </button>

      <!-- Finish input connection info -->
      <button
        *ngIf="currentMigrationStep === 1"
        mat-button
        id="connect-to-rdbms"
        [disabled]="rdbmsServer === '' || rdbmsPort === '' || rdbmsDatabase === '' || rdbmsUsername === '' || rdbmsPassword === ''"
        (click)="retrieveRDBMSMeta()"
      >
        {{ 'NEXT' | translate }}
      </button>

      <!-- Finish choosing tables and attributes to migrate -->
      <button
        *ngIf="currentMigrationStep === 2"
        mat-button
        id="choose-table-attribute"
        (click)="setAttributesForEscalation()"
      >
        {{ 'NEXT' | translate }}
      </button>

      <button
        mat-button
        color="primary"
        (click)="doMigration()"
        id="migrate-rdbms"
        *ngIf="currentMigrationStep === totalMigrationStep">
        {{ 'MIGRATE' | translate }}
      </button>
    </mat-dialog-actions>
  </div>
</ng-template>

<ng-template #retrieveMetadataProgressWindow>
  <mat-dialog-content fxLayout="column" fxLayoutGap="5px">
    <div fxFlex="36px" class="hint-text">
      <span fxFlex fxLayoutAlign="start">
        {{ 'Connecting to the database and retrieving the schema.' | translate }}
      </span>
    </div>
    <mat-progress-bar mode="indeterminate"></mat-progress-bar>
  </mat-dialog-content>
</ng-template>

<ng-template #migrateDataMappingProgressWindow>
  <mat-dialog-content fxLayout="column" fxLayoutGap="5px">
    <div fxFlex="36px" class="hint-text">
      <span fxFlex fxLayoutAlign="start">
        {{ migrateDataMappingProgressText | async }}
      </span>
    </div>
    <mat-progress-bar mode="determinate" [value]="migrateDataMappingProgress | async"></mat-progress-bar>
  </mat-dialog-content>
</ng-template>

<ng-template #exportPopupWindow>
  <div>
    <mat-toolbar color="primary" class="popup-toolbar">
      <ng-container *ngIf="true">
        <h2 *ngIf="true">{{ 'Export Options' | translate }}</h2>
      </ng-container>
    </mat-toolbar>

    <mat-dialog-content style="overflow-y: hidden">
      <div style="display: flex; flex-direction: column; row-gap: 12px;">
        <div [style.marginTop.px]="16" fxLayout="row" fxLayoutGap="30px">
          <mat-checkbox [ngModel]="true" [disabled]="true">{{ 'Schema, Data Mapping, Queries' | translate }}</mat-checkbox>
          <mat-checkbox [(ngModel)]="isExportData">{{ 'Data' | translate}}</mat-checkbox>
        </div>
        <div fxLayout="column" fxLayoutGap="10px">
          <div>{{ 'Select graphs to export:' | translate }}</div>
          <div fxFlex="auto">
            <mat-grid-list [cols]="3" rowHeight="20px" class="graph-list">
              <mat-grid-tile>
                <mat-checkbox
                  [checked]="allItemsSelected(graphList)"
                  (change)="checkAllItems(graphList, $event)">{{ 'All' | translate }}
                </mat-checkbox>
              </mat-grid-tile>
              <mat-grid-tile *ngFor="let graph of graphList.sort()">
                <mat-checkbox
                  [(ngModel)]="graph.checked"
                >
                  {{ graph.name }}
                </mat-checkbox>
              </mat-grid-tile>
            </mat-grid-list>
          </div>
        </div>
      </div>
    </mat-dialog-content>

    <mat-dialog-actions align="end">
      <ng-container *ngIf="true">
        <button mat-button (click)="closeExportDialog()">
          {{ 'CANCEL' | translate }}
        </button>
        <button mat-button color="primary" (click)="exportCheck()">
          {{ 'CONFIRM' | translate }}
        </button>
      </ng-container>
    </mat-dialog-actions>

  </div>
</ng-template>

<ng-template #importPopupWindow>
  <div>
    <mat-toolbar color="primary" class="popup-toolbar">
      <ng-container>
        <h2>{{ 'Import Options' | translate }}</h2>
      </ng-container>
    </mat-toolbar>

    <mat-dialog-content style="overflow-y: hidden">
      <div [style.marginTop.px]="16" style="display: flex; flex-direction: column;">
          <mat-radio-group [(ngModel)]="importGraphAll" [style.marginTop.px]="4">
            <mat-radio-button [value]="true">{{ 'Import all' | translate }}</mat-radio-button>
            <mat-radio-button [value]="false" [style.marginLeft.px]="12">{{ 'Select graph(s) to import' | translate }}</mat-radio-button>
          </mat-radio-group>
          <div *ngIf="importGraphAll" class="mat-input-warn import-warning">
            <span>
              <mat-icon attr.aria-label="{{ 'Warning' | translate }}">warning</mat-icon>
            </span>
            <span>
              {{ 'Import all solution will ERASE ALL existing data (including all graphs and Insights applications)' | translate }}
            </span>
          </div>
        <div fxLayout="column" fxLayoutGap="10px" *ngIf="!importGraphAll" [style.marginTop.px]="12">
          <div>{{ 'Select graphs to import:' | translate }}</div>
          <div fxFlex="auto">
            <mat-grid-list [cols]="3" rowHeight="20px" class="graph-list">
              <mat-grid-tile>
                <mat-checkbox [checked]="allItemsSelected(graphList)" (change)="checkAllItems(graphList, $event)">
                  {{ 'All' | translate }}
                </mat-checkbox>
              </mat-grid-tile>
              <mat-grid-tile *ngFor="let graph of graphList.sort()">
                <mat-checkbox [(ngModel)]="graph.checked">
                  {{ graph.name }}
                </mat-checkbox>
              </mat-grid-tile>
            </mat-grid-list>
          </div>
        </div>
      </div>
    </mat-dialog-content>

    <mat-dialog-actions align="end">
      <ng-container>
        <button mat-button (click)="closeImportDialog()">
          {{ 'CANCEL' | translate }}
        </button>
        <button mat-button color="primary" (click)="confirmImportSolution()">
          {{ 'CONFIRM' | translate }}
        </button>
      </ng-container>
    </mat-dialog-actions>
  </div>
</ng-template>
