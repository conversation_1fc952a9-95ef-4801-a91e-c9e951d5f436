$popup-toolbar-margin: -24px !default;
$popup-toolbar-padding: 0 24px !default;
$popup-toolbar-width: calc(100% + 48px);


mat-form-field {
  width: 100%;
}

.container {
  height: 100%;
  justify-content: normal;
  overflow: auto;
}

.container-small {
  padding: 60px 0;
}

.home-button {
  margin-top: 12px;
}

.homepage-item-container {
  height: 60px;
}

.company-logo-dark {
  height: 46px;
  padding-right: 212px;
  width: 185px;
}

.company-logo-light {
  height: 50px;
  padding-right: 210px;
  width: 200px;
}

.home-item-icon {
  font-size: 36px;
}

.home-item-title {
  font-size: 24px;
  text-decoration: none;
}

.svg-icon {
  width: 28px !important;
  height: 28px !important;
  padding: 4px 0 0 4px !important;
}

.builtin-icon {
  padding: 0 8px 0 0 !important;
}

.popup-toolbar {
  margin-left: $popup-toolbar-margin;
  margin-top: $popup-toolbar-margin;
  padding: $popup-toolbar-padding;
  width: $popup-toolbar-width;
}

.rdbms-choice {
  width: 280px;
  margin: 12px 0 0 12px;
}

.migration-container {
  margin-top: 24px;
}

.migrate-href {
  cursor: pointer;
}

.table-container {
  margin-bottom: 12px;
}

.migration-selector {
  margin: 0 12px 3px 0;
}

.graph-list {
  ::ng-deep .mat-grid-tile .mat-figure {
    justify-content: start !important;
  }

  ::ng-deep .mat-grid-tile-content {
    justify-content: start !important;
  }
}

.import-warning {
  font-weight: 400;
  line-height: 24px;
  padding: 8px 16px;
  background-color: #bf360c1a;
  margin-top: 16px;

  mat-icon {
    font-size: 16px;
    width: 16px;
    height: 16px;
    padding-right: 4px;
  }
}
