import { Component, ElementRef, <PERSON><PERSON><PERSON>t, On<PERSON><PERSON>roy, ViewChild, TemplateRef } from '@angular/core';
import { HttpErrorResponse } from '@angular/common/http';
import { MatCheckboxChange } from '@angular/material/checkbox';
import { MatDialog, MatDialogConfig, MatDialogRef } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { Router } from '@angular/router';
import { cloneDeep, take } from 'lodash';
import { Subject, BehaviorSubject, Observable, EMPTY, of, concat, forkJoin } from 'rxjs';
import { map, takeUntil, finalize, catchError, switchMap, tap, filter } from 'rxjs/operators';

import { DialogData, DialogComponent } from '@app/core/components/dialog';
import { AuthService, FileUploadService, LicenseService, MessageBus } from '@app/core/services';
import { HttpSourceErrorResponse } from '@tigergraph/tools-models/error';
import { License } from '@tigergraph/tools-models/license';
import { supportedRDBMS, RDBMSMeta, RDBMSTableMeta } from '@tigergraph/tools-models/rdbms';
import { AppTheme } from '@tigergraph/tools-models/theme/app-theme.model';
import { Graph, SchemaChange } from '@tigergraph/tools-models/topology';
import { LoadingDesignerLogicService } from '@app/shared/services';
import {
  ACCESS_PRIVILEGES, FormatValidator,
  GLOBAL_GRAPH_NAME, GSQLPrivilege, PageKey
} from '@tigergraph/tools-models/utils';
import { HelperFunctions } from '@app/shared/utils';
import { TableSampleData } from '@app/loading-builder/loading-builder.service';
import { SchemaDesignerLogicService, SchemaChangeLogicService } from '@app/schema-designer/shared';

import { HomeService } from './home.service';
import { RDBMSMigrationLogicService } from './shared';
import { TabularParser } from '@tigergraph/tools-models/loading-job/data-set.interface';
import { compareWithInstanceVersion, getVersion } from '@tigergraph/tools-models';

interface CheckList {
  name: string;
  checked: boolean;
}

/**
 * Current schema migration progress.
 *
 * @enum {number}
 */
enum SchemaMigrationProgress {
  None,
  CreateSchema,
  FinishSchemaMigration
}

/**
 * Component to render the home page.
 * Usage example:
 *   <app-home></app-home>
 *
 * @export
 * @class HomeComponent
 * @implements {OnInit}
 */
@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss'],
  providers: [
    HomeService,
    RDBMSMigrationLogicService,
    SchemaDesignerLogicService,
    SchemaChangeLogicService,
    LoadingDesignerLogicService,
  ]
})
export class HomeComponent implements OnInit, OnDestroy {
  @ViewChild('fileInput', { static: true }) fileInput: ElementRef;
  @ViewChild('migrationWindow', { static: true }) migrationWindow: TemplateRef<any>;
  @ViewChild('retrieveMetadataProgressWindow', { static: true }) retrieveMetadataProgressWindow: TemplateRef<any>;
  @ViewChild('migrateDataMappingProgressWindow', { static: true }) migrateDataMappingProgressWindow: TemplateRef<any>;
  @ViewChild('exportPopupWindow', { static: true }) exportPopupWindow: TemplateRef<any>;
  @ViewChild('importPopupWindow', { static: true }) importPopupWindow: TemplateRef<any>;

  isExportData = false;
  graphList: CheckList[] = [];
  tempDirName: string;
  graphNames: string[] = [];
  importGraphAll = true;
  isSupportImportGraphs = getVersion() && compareWithInstanceVersion('4.1.1', '>=');

  routerKeys = Object.values(PageKey);

  isDarkTheme = true;

  license = new BehaviorSubject<License>(undefined);

  totalMigrationStep = 3;
  currentMigrationStep = 0;

  supportedRDBMS = supportedRDBMS;
  chosenRDBMS: string;

  rdbmsGraphName = 'MyGraph';
  rdbmsServer = '';
  rdbmsPort = '';
  rdbmsDatabase = '';
  rdbmsUsername = '';
  rdbmsPassword = '';

  rdbmsMeta: RDBMSMeta;

  tablesToBeMigrated: boolean[] = [];
  attributesToBeMigrated: boolean[][] = [];

  // Hardcoded rule, should not be allowed to change.
  foreignKeyAsEdge = true;

  // Creating index on attributes or not. By default index will be created if
  // there is index on relational table.
  createIndex = true;

  // Limit number of records per table to be migrated. By default no limit, all data
  // will be migrated.
  lineLimit = false;
  limit = 1024;

  // Refine tableMeta.
  refinedTablesMeta: RDBMSTableMeta[] = [];
  // Whether escalating attributes as vertex types.
  // By default no attribute will be escalated as vertex type.
  escalateAttributeAsVertex: boolean[][] = [];
  // When escalating an attribute, what vertex name will be used.
  // By default use the attribute name. User can change it.
  escalateAttributeAsVertexName: string[][] = [];

  // Migrate data mapping progress.
  migrateDataMappingProgress = new BehaviorSubject<number>(0);
  migrateDataMappingProgressText = new BehaviorSubject<string>('');

  menuItems = [
    {
      title: 'Design Schema',
      subTitle: 'Model your business problem as a graph schema',
      svgIcon: 'schema',
      routerKey: PageKey.SchemaDesigner
    },
    {
      title: 'Load Data',
      subTitle: 'Load data into the graph based on the data mapping',
      svgIcon: 'loading',
      routerKey: PageKey.LoadingBuilder
    },
    {
      title: 'Explore Graph',
      subTitle: 'Search vertices, explore neighborhoods and find paths',
      svgIcon: 'explore-svg',
      routerKey: PageKey.GraphExplorer
    },
    {
      title: 'Write Queries',
      subTitle: 'Use GSQL language to implement your business applications',
      svgIcon: 'gsql',
      routerKey: PageKey.QueryEditor
    },
  ];

  private ngUnsubscribe: Subject<any> = new Subject<any>();
  private migrationWindowRef: MatDialogRef<TemplateRef<any>>;
  private retrieveMetadataProgressWindowRef: MatDialogRef<TemplateRef<any>>;
  private migrateDataMappingProgressWindowRef: MatDialogRef<TemplateRef<any>>;

  private currentSchemaMigrationProgress = SchemaMigrationProgress.None;

  private dialogConfig: MatDialogConfig;
  private popupWindowRef: MatDialogRef<TemplateRef<any>>;

  private uploadFilename: string;
  private supportExportData = getVersion() && compareWithInstanceVersion('3.10.0', '>=');

  // Holds schema migration result.
  private schemaMigrationResult: {
    schema: Graph;
    schemaChange: SchemaChange[];
    vertexTypeNameMapping: Map<string, string>;
  };

  private readonly CLOUD_EXPORT_SIZE_LIMIT = 2 * 1024 * 1024 * 1024; // 2GB in bytes

  constructor(
    private dialog: MatDialog,
    private snackBar: MatSnackBar,
    private router: Router,
    private authService: AuthService,
    private licenseService: LicenseService,
    private bus: MessageBus,
    private rdbmsMigrationService: RDBMSMigrationLogicService,
    private cmptService: HomeService,
    private fileUploadService: FileUploadService,
  ) {
    this.dialogConfig = {
      width: '648px', // 600px for width plus the left and right padding which is 24px each.
      panelClass: 'export-options-dialog'
    };
  }

  ngOnInit() {
    // Get license information.
    this.licenseService.getLicense()
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe(license => this.license.next(license));

    this.bus.from<AppTheme>('AppTheme')
      .pipe(
        takeUntil(this.ngUnsubscribe),
        map(theme => theme.isDark)
      )
      .subscribe(isDark => this.isDarkTheme = isDark);

    this.bus.from<boolean>('SwitchGraph')
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe(() => {
        // If it's in the progress of migrating RDBMS, switch to the new graph
        // and continue the migration.
        if (this.currentSchemaMigrationProgress === SchemaMigrationProgress.FinishSchemaMigration) {
          this.authService.setCurrentGraph(this.rdbmsGraphName);
          this.migrateSchemaFromRDBMS();
        }
        this.bus.resetChannel('SwitchGraph');
      });
    this.cmptService.logPageView();

    this.fileUploadService.init('solution');
    this.handleFileUploadResponse();
  }

  ngOnDestroy() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }

  /**
   * Upload the selected file to server.
   *
   * @param {FileList} files
   * @memberof HomeComponent
   */
  uploadFile(files: FileList) {
    if (files !== undefined) {
      const file = files.item(0);
      if (this.isSupportImportGraphs) {
        this.uploadFilename = file.name;
        this.fileUploadService.upload(file);
      } else {
        const data: DialogData = {
          title: 'Warning',
          messages: [
            'Import solution will ERASE ALL existing data (including all graphs and Insights applications).',
            'Do you want to continue?'
          ],
          actions: [
            { label: 'CANCEL', value: 0 },
            { label: 'OVERWRITE', value: 1, color: 'warn' }
          ]
        };

        this.dialog
          .open(DialogComponent, { data: data })
          .afterClosed()
          .pipe(takeUntil(this.ngUnsubscribe))
          .subscribe(value => {
            if (value === 1) {
              this.uploadFilename = file.name;
              if (getVersion() && compareWithInstanceVersion('3.10.0', '>=')) {
                // In the version 3.10.0, we support upload file by splitting chunks,
                // and this functionality is implemented in the FileUploadService.
                // The importSolution method will be called once receiving the complete event from the service.
                this.fileUploadService.upload(file);
              } else {
                // Before 3.10.0, we upload and import the file directly.
                this.importSolution(file);
              }
            }
            this.fileInput.nativeElement.value = '';
          });
      }
    }
  }

  handleFileUploadResponse() {
    this.fileUploadService.response$
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe(response => {
        if (response.status === 'oneSingleFileComplete') {
          if (this.isSupportImportGraphs) {
            this.importCheck(this.uploadFilename);
          } else {
            this.importSolution(this.uploadFilename);
          }
        } else if (response.status === 'error') {
          this.handleError(response.message, 'Error');
          this.bus.to('GlobalStatus').next({
            uploadSolution: {
              running: false
            }
          });
        } else if (response.status === 'uploadStart') {
          this.bus.to('GlobalStatus').next({
            uploadSolution: {
              running: true
            }
          });
        }
      });
  }

  importCheck(file: File | string) {
    this.cmptService.importCheck(file)
      .pipe(
        takeUntil(this.ngUnsubscribe),
        finalize(() =>
          this.bus.to('GlobalStatus').next({
            uploadSolution: {
              running: false
            }
          }))
      )
      .subscribe(
        ({ graphNames, tempDirName }) => {
          this.graphList =
            graphNames
              ?.filter((graph) => graph !== '1')
              .map((graph) => ({ name: graph, checked: false })) || [];
          this.tempDirName = tempDirName;
          this.openImportDialog();
        },
        (err: HttpSourceErrorResponse | string) => {
          if (typeof err === 'string') {
            this.handleError(err, 'Error');
          } else {
            this.handleError(err.error, err.statusText);
          }
        }
      );
  }

  confirmImportSolution() {
    if (!this.importGraphAll && this.graphList.every(graph => !graph.checked)) {
      this.handleError('Please select at least one graph to import.');
      return;
    }
    const data: DialogData = this.importGraphAll ? {
      title: 'Warning',
      messages: [
        'Import solution will ERASE ALL existing data (including all graphs and Insights applications).',
        'Do you want to continue?'
      ],
      actions: [
        { label: 'CANCEL', value: 0 },
        { label: 'OVERWRITE', value: 1, color: 'warn' }
      ]
    } : {
      title: 'Warning',
      messages: [
        'Are you sure you want to import the selected graphs?'
      ],
      actions: [
        { label: 'CANCEL', value: 0 },
        { label: 'CONFIRM', value: 1, color: 'warn' }
      ]
    };

    this.dialog
      .open(DialogComponent, { data: data })
      .afterClosed()
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe(value => {
        if (value === 1) {
          this.bus.to('GlobalStatus').next({
            importSolution: {
              running: true
            }
          });
          this.cmptService
            .importSolutionWithGraphs(
              this.importGraphAll ? [] : this.graphList
                .filter((graph) => graph.checked)
                .map((graph) => graph.name),
              this.tempDirName
            )
            .pipe(
              takeUntil(this.ngUnsubscribe),
              finalize(() => {
                this.bus.to('GlobalStatus').next({
                  importSolution: {
                    running: false
                  }
                });
              })
            )
            .subscribe(
              () => this.snackBar.open(`Solution is imported`, 'DISMISS', { duration: 3000 }),
              (err: HttpSourceErrorResponse | string) => {
                if (typeof err === 'string') {
                  this.handleError(err, 'Error');
                } else {
                  this.handleError(err.error, err.statusText);
                }
              }
            );
          this.closeImportDialog();
        }
      });
  }

  importSolution(file: File | string) {
    this.bus.to('GlobalStatus').next({
      importSolution: {
        running: true
      }
    });
    this.cmptService.importSolution(file)
      .pipe(
        takeUntil(this.ngUnsubscribe),
        finalize(() =>
          this.bus.to('GlobalStatus').next({
            importSolution: {
              running: false
            }
          }))
      )
      .subscribe(
        () => this.snackBar.open(`Solution is imported`, 'DISMISS', { duration: 3000 }),
        (err: HttpSourceErrorResponse | string) => {
          if (typeof err === 'string') {
            this.handleError(err, 'Error');
          } else {
            this.handleError(err.error, err.statusText);
          }
        }
      );
  }

  /**
   * Close import window.
   *
   * @memberof HomeComponent
   */
  closeImportDialog() {
    if (this.popupWindowRef) {
      this.popupWindowRef.close();
      this.fileInput.nativeElement.value = '';
      this.tempDirName = '';
    }
  }

  /**
   * Open import window.
   *
   * @memberof HomeComponent
   */
  openImportDialog() {
    this.importGraphAll = true;
    this.popupWindowRef = this.dialog.open(this.importPopupWindow, this.dialogConfig);
  }

  /**
   * Open import window.
   *
   * @memberof HomeComponent
   */
  checkImportGraphAll() {
    this.importGraphAll = !this.importGraphAll;
    this.graphList.forEach(graph => graph.checked = false);
  }

  /**
   * Close export window.
   *
   * @memberof HomeComponent
   */
  closeExportDialog() {
    if (this.popupWindowRef) {
      this.popupWindowRef.close();
    }
  }

  /**
   * Open export window.
   *
   * @memberof HomeComponent
   */
  openExportDialog() {
    this.popupWindowRef = this.dialog.open(this.exportPopupWindow, this.dialogConfig);
  }

  allItemsSelected(checkList: CheckList[]): boolean {
    return checkList.every(item => item.checked);
  }

  checkAllItems(checkList: CheckList[], newStatus: MatCheckboxChange) {
    checkList.forEach(item => item.checked = newStatus.checked);
  }

  handleExport() {
    if (this.supportExportData) {
      this.graphList = this.authService.getGraphList().filter(graph => graph !== '1').map(graph => ({ name: graph, checked: false }));
      this.openExportDialog();
    } else {
      this.export();
    }
  }

  exportCheck() {
    this.closeExportDialog();
    if (!this.supportExportData || !this.isExportData) {
      this.export();
      return;
    }

    // if export data, check if there is enough disk space.
    this.cmptService.exportCheck()
      .pipe(
        takeUntil(this.ngUnsubscribe),
        catchError(() => of({ isEnoughSpace: true, graphSize: 0, diskFreeSpace: 0 }))
      )
      .subscribe(response => {
        if (!response) {
          this.export();
          return;
        }

        if (sessionStorage.getItem('CLOUDVERSION') === 'v4' && response.graphSize > this.CLOUD_EXPORT_SIZE_LIMIT) {
          const data: DialogData = {
            title: 'Export Not Allowed',
            messages: [
              'Export is not allowed when graph size exceeds 2GB.',
              `Current graph size: ${HelperFunctions.abbreviateByte(response.graphSize)}`
            ],
            actions: [
              { label: 'CLOSE', value: 0 }
            ]
          };
          this.dialog.open(DialogComponent, { data: data });
          return;
        }

        if (response.isEnoughSpace) {
          this.export();
        } else {
          const data: DialogData = {
            title: 'Warning',
            messages: [
              `The disk space may not be enough to export the solution:
              ${HelperFunctions.abbreviateByte(response.graphSize)} is needed,
              ${HelperFunctions.abbreviateByte(response.diskFreeSpace)} is left.`,
              'Do you want to continue?'
            ],
            actions: [
              { label: 'CANCEL', value: 0 },
              { label: 'EXPORT', value: 1, color: 'warn' }
            ]
          };
          this.dialog.open(DialogComponent, { data: data })
            .afterClosed()
            .pipe(takeUntil(this.ngUnsubscribe))
            .subscribe(value => {
              if (value === 1) {
                this.export();
              }
            });
        }
      });
  }

  /**
   * Export the solution.
   *
   * @memberof HomeComponent
   */
  export() {
    const baseURL = sessionStorage.getItem('BASEURL') || window.location.origin;

    if (this.supportExportData) {
      let exportGraphNames = '';
      if (!this.allItemsSelected(this.graphList)) {
        exportGraphNames = this.graphList.filter(graph => graph.checked).map(graph => graph.name).join(',');
      }
      open(`${baseURL}/api/system/export?isExportData=${this.isExportData}&exportGraphNames=${exportGraphNames}`, '_blank');
    } else {
      open(`${baseURL}/api/system/export`, '_blank');
    }
  }

  /**
   * Check if the current installation is a community version.
   *
   * @returns {boolean}
   * @memberof HomeComponent
   */
  isCommunityVersion(): boolean {
    return this.authService.isCommunityVersion();
  }

  /**
   * If current user can export solution.
   *
   * @returns {boolean}
   * @memberof HomeComponent
   */
  canExportSolution(): boolean {
    return this.authService.hasPrivilege(GSQLPrivilege.ExportGraph);
  }

  /**
   * If current user can import solution.
   *
   * @returns {boolean}
   * @memberof HomeComponent
   */
  canImportSolution(): boolean {
    return (
      this.authService.hasAllPrivileges([
        GSQLPrivilege.WriteSchema,
        GSQLPrivilege.WriteLoadingJob,
        GSQLPrivilege.ExecuteLoadingJob,
        GSQLPrivilege.DropAll,
        GSQLPrivilege.WriteUser,
        GSQLPrivilege.WriteRole,
        GSQLPrivilege.ReadData,
        GSQLPrivilege.CreateData,
      ]) &&
      (this.authService.hasPrivilege(GSQLPrivilege.WriteQuery) ||
        // 4.1.0 and later version
        this.authService.hasPrivilege(GSQLPrivilege.CreateQuery))
    );
  }

  /**
   * If current user can migration from RDBMS.
   *
   * @returns {boolean}
   * @memberof HomeComponent
   */
  canMigrateFromRDBMS(): boolean {
    return this.authService.hasAllPrivileges([
      GSQLPrivilege.WriteSchema,
      GSQLPrivilege.WriteLoadingJob,
      GSQLPrivilege.ExecuteLoadingJob
    ]);
  }

  /** Open migration popup and start migration process. */
  startMigration() {
    // Reset migration step.
    this.currentMigrationStep = 0;

    this.migrationWindowRef = this.dialog.open(
      this.migrationWindow,
      {
        disableClose: true,
        width: '648px'
      }
    );
  }

  /** Cancel migration. */
  cancelMigration() {
    if (this.migrationWindowRef) {
      this.migrationWindowRef.close();
    }
  }

  migrationPrevStep() {
    this.currentMigrationStep--;
  }

  migrationNextStep() {
    this.currentMigrationStep++;
  }

  retrieveRDBMSMeta() {
    this.retrieveMetadataProgressWindowRef = this.dialog.open(
      this.retrieveMetadataProgressWindow,
      {
        disableClose: true,
        width: 'auto'
      }
    );

    this.cmptService.getRDBMSMeta(
      this.chosenRDBMS,
      this.rdbmsServer,
      this.rdbmsPort,
      this.rdbmsDatabase,
      this.rdbmsUsername,
      this.rdbmsPassword
    ).pipe(
      takeUntil(this.ngUnsubscribe),
      finalize(() => {
        if (this.retrieveMetadataProgressWindowRef) {
          this.retrieveMetadataProgressWindowRef.close();
        }
      }),
      catchError(error => this.catchError(error))
    ).subscribe(
      response => {
        this.rdbmsMeta = response;
        if (!this.rdbmsMeta.tableMetas) {
          this.handleError('Failed to retrieve the metadata of the chosen database.');
          return;
        }

        this.rdbmsGraphName =
          `${this.chosenRDBMS}_${HelperFunctions.fileNameToId(this.rdbmsDatabase)}`;

        // By default set to migrate everything.
        this.tablesToBeMigrated = this.rdbmsMeta.tableMetas.map(() => true);
        this.attributesToBeMigrated =
          this.rdbmsMeta.tableMetas.map(tMeta => tMeta.columns.map(() => true));

        this.migrationNextStep();
      }
    );
  }

  setAttributesForEscalation() {
    this.refinedTablesMeta = this.rdbmsMeta.tableMetas
      .map((tMeta, i) => {
        const refinedMeta = cloneDeep(tMeta);
        refinedMeta.columns = tMeta.columns.filter((_, j) => this.attributesToBeMigrated[i][j]);
        return refinedMeta;
      }).filter((_, i) => this.tablesToBeMigrated[i]);

    this.escalateAttributeAsVertexName = this.refinedTablesMeta
      .map((tMeta) => tMeta.columns.map(column => column.name));

    this.escalateAttributeAsVertex = this.escalateAttributeAsVertexName
      .map(row => row.map(_ => false));

    this.migrationNextStep();
  }

  allTablesSelected(): boolean {
    return this.tablesToBeMigrated.every(checked => checked);
  }

  updateAllTablesSelection(newStatus: MatCheckboxChange) {
    this.tablesToBeMigrated.fill(newStatus.checked);
  }

  doMigration() {
    const validationResult = FormatValidator.isName(this.rdbmsGraphName);
    if (!validationResult.success) {
      this.handleError(`Invalid graph name. ${validationResult.message}`);
      return;
    }

    this.currentSchemaMigrationProgress = SchemaMigrationProgress.CreateSchema;
    this.migrateSchemaFromRDBMS();
  }

  /**
   * Migrate schema from RDBMS.
   *
   * @returns
   * @memberof HomeComponent
   */
  migrateSchemaFromRDBMS() {
    switch (this.currentSchemaMigrationProgress) {
      case SchemaMigrationProgress.CreateSchema: {
        this.bus.to('GlobalStatus').next({
          updateSchema: {
            running: true
          }
        });

        let graphCreated = false;
        // Get schema migration result.
        this.schemaMigrationResult = this.rdbmsMigrationService.getMigratedSchema(
          this.rdbmsGraphName,
          this.refinedTablesMeta,
          this.foreignKeyAsEdge,
          this.createIndex,
          this.escalateAttributeAsVertex,
          this.escalateAttributeAsVertexName
        );

        // Create a graph first, then do schema change to finish migration from RDBMS.
        this.cmptService.createGraph(this.rdbmsGraphName)
          .pipe(
            takeUntil(this.ngUnsubscribe),
            switchMap(() => {
              graphCreated = true;
              const schemaChangeJobs = this.schemaMigrationResult.schemaChange.map(
                schemaChange => this.cmptService.changeSchema(this.rdbmsGraphName, schemaChange));
              return concat(...schemaChangeJobs);
            }),
            catchError(error => {
              this.currentSchemaMigrationProgress = SchemaMigrationProgress.None;
              return this.catchError(error, graphCreated);
            }),
            finalize(() => this.bus.to('GlobalStatus').next({
              updateSchema: {
                running: false
              }
            }))
          )
          .subscribe(() => {
            // Wait for SwitchGraph message from message bus to trigger finish migration.
            this.currentSchemaMigrationProgress = SchemaMigrationProgress.FinishSchemaMigration;
          });
        return;
      }
      case SchemaMigrationProgress.FinishSchemaMigration: {
        // Start data mapping migration.
        this.currentSchemaMigrationProgress = SchemaMigrationProgress.None;
        this.dumpSelectedTablesToCSV();
        return;
      }
    }
  }

  /**
   * Migrate data mapping from RDBMS.
   *
   * @memberof HomeComponent
   */
  dumpSelectedTablesToCSV() {
    this.migrateDataMappingProgressWindowRef = this.dialog.open(
      this.migrateDataMappingProgressWindow,
      {
        disableClose: true,
        width: '520px'
      }
    );

    // Dump selected tables as csv files.
    const dumpTableJobs = this.refinedTablesMeta.map(tMeta => this.cmptService.dumpRDBMSTable(
      this.chosenRDBMS,
      this.rdbmsServer,
      this.rdbmsPort,
      this.rdbmsDatabase,
      this.rdbmsUsername,
      this.rdbmsPassword,
      [tMeta.tableName],
      this.lineLimit ? this.limit : undefined
    )).concat([of(true)]);
    const progressIncrease = 50 / dumpTableJobs.length;
    let progress = 0;
    let currentTable = 0;
    this.migrateDataMappingProgressText.next(
      `Dumping data from table ${this.refinedTablesMeta[currentTable].tableName}.`
    );
    concat(...dumpTableJobs)
      .pipe(
        takeUntil(this.ngUnsubscribe),
        catchError(err => {
          if (this.migrateDataMappingProgressWindowRef) {
            this.migrateDataMappingProgressWindowRef.close();
          }
          return this.catchError(err, true);
        })
      )
      .subscribe((finish) => {
        if (finish) {
          this.migrateDataMapping();
        } else {
          // Update dumping table progress and message.
          if (++currentTable < this.refinedTablesMeta.length) {
            this.migrateDataMappingProgressText.next(
              `Dumping data from table "${this.refinedTablesMeta[currentTable].tableName}".`
            );
          }
          // Update progress.
          progress = progress + progressIncrease;
          this.migrateDataMappingProgress.next(progress);
        }
      });
  }

  private migrateDataMapping() {
    // Get the parsing result of all the dumped CSV files.
    this.migrateDataMappingProgressText.next(
      `Generating data mapping.`
    );

    const dataParser = new TabularParser();
    dataParser.separator = '\\1';
    dataParser.header = true;

    this.cmptService.getDataFilePath()
      .pipe(
        switchMap(filePath => forkJoin(
          // Get the sample data of all the dumped tables.
          this.refinedTablesMeta.map(tMeta => this.cmptService.getSampleData(
            `${filePath}/${this.rdbmsMeta.type}_${this.rdbmsMeta.database}_${tMeta.tableName}.csv`,
            dataParser
          ))
        )),
        takeUntil(this.ngUnsubscribe),
        catchError(err => {
          if (this.migrateDataMappingProgressWindowRef) {
            this.migrateDataMappingProgressWindowRef.close();
          }
          return this.catchError(err, true);
        })
      )
      // Here the type is referred as GSQLGraphJson[], so type mismatch error. Use any to walk around
      .subscribe(
        (sampleDatas: TableSampleData[]) => {
          const dataMappings = this.rdbmsMigrationService.getMigratedDataMappings(
            this.schemaMigrationResult,
            undefined,
            this.rdbmsMeta,
            this.refinedTablesMeta,
            this.escalateAttributeAsVertex,
            this.escalateAttributeAsVertexName,
            dataParser,
            sampleDatas
          );

          let jobs = [];
          dataMappings[0].forEach(loadingJob => {
            jobs.push(this.cmptService.saveLoadingJobs([loadingJob]));
          });

          // Get updated schema and data mapping styles.
          const schemaAndDataMappingStyles = this.rdbmsMigrationService.getSchemaAndLoadingJobStyles(
            this.schemaMigrationResult.schema,
            dataMappings
          );

          jobs = jobs.concat([
            this.cmptService.saveLoadingJobsInfo(
              schemaAndDataMappingStyles[1],
              this.schemaMigrationResult.schema.name
            ),
            this.cmptService.saveSchemaStyle(
              this.schemaMigrationResult.schema.name, schemaAndDataMappingStyles[0]
            ),
            of(true)
          ]);

          this.migrateDataMappingProgressText.next('Publishing data mapping.');
          const progressIncrease = 50 / jobs.length;
          let progress = 50;

          concat(...jobs)
            .pipe(
              takeUntil(this.ngUnsubscribe),
              catchError(err => {
                if (this.migrateDataMappingProgressWindowRef) {
                  this.migrateDataMappingProgressWindowRef.close();
                }
                return this.catchError(err, true);
              })
            )
            .subscribe((finish) => {
              if (finish) {
                if (this.migrateDataMappingProgressWindowRef) {
                  this.migrateDataMappingProgressWindowRef.close();
                }
                // Migration finished.
                if (this.migrationWindowRef) {
                  this.migrationWindowRef.close();
                }
                // Open the guidance table to go to schema design page.
                const data: DialogData = {
                  title: 'Verify graph schema',
                  messages: [
                    'GraphStudio has migrated the schema and data mapping from RDBMS.',
                    'You can go to the Design Schema page to verify the schema, '
                    + 'and go to Map Data To Graph page to verify the data mapping.',
                    'Go to Load Data page to load data, and you are ready to go'
                  ],
                  actions: [
                    { label: 'VERIFY', value: 1, color: 'primary' }
                  ]
                };

                this.dialog
                  .open(DialogComponent, { data: data, disableClose: true })
                  .afterClosed()
                  .pipe(takeUntil(this.ngUnsubscribe))
                  .subscribe(value => {
                    if (value === 1) {
                      this.router.navigate([PageKey.SchemaDesigner]);
                    }
                  });
              } else {
                // Update progress.
                progress = progress + progressIncrease;
                this.migrateDataMappingProgress.next(progress);
              }
            });
        });
  }

  /**
   * Catch error from an operation.
   *
   * @param {(Error | HttpErrorResponse)} error
   * @returns {Observable<never>}
   * @memberof HomeComponent
   */
  catchError(error: Error | HttpErrorResponse | string, dropGraph = false): Observable<any> {
    if (error instanceof Error) {
      this.handleError(error.message);
    } else if (typeof error === 'string') {
      this.handleError(error);
    } else {
      this.handleError(error.error, error.statusText);
    }

    if (dropGraph) {
      return this.cmptService.dropGraph(this.rdbmsGraphName)
        .pipe(
          switchMap(() => this.authService.updateUserInfos()),
          tap(() => this.authService.setCurrentGraph(GLOBAL_GRAPH_NAME))
        );
    }
    return EMPTY;
  }

  navigateTo(item: { routerKey: PageKey }, index: number) {
    // Utilize the current page indexing convention instead of redefining one
    this.cmptService.logNavigationEvent(index + 1, 'Home Page');

    this.router.navigateByUrl(item.routerKey);
  }

  isAccessible(item: { routerKey: PageKey }, index: number): boolean {
    const graph = this.authService.getCurrentGraph();
    if (graph === GLOBAL_GRAPH_NAME) {
      return index === 0;
    }
    if (item.routerKey === PageKey.QueryEditor) {
      return this.authService.hasPermissionToAccessWriteQueriesPage(graph);
    }

    return this.authService.hasAllPrivilegesByURL(item.routerKey);
  }

  /**
   * Show the user the error through dialog.
   *
   * @private
   * @param {string} message
   * @param {string} [title]
   * @memberof HomeComponent
   */
  private handleError(message: string, title?: string) {
    const data: DialogData = {
      title: title ? title /* istanbul ignore next */ : 'Error',
      messages: [message]
    };
    this.dialog.open(DialogComponent, { data: data });
  }
}
