import {
  AfterViewInit,
  Component,
  OnDestroy,
  OnInit,
  TemplateRef,
  ViewChild,
  HostListener,
} from '@angular/core';
import { HttpErrorResponse, HttpParams } from '@angular/common/http';
import { FormGroup } from '@angular/forms';
import {
  MatDialog,
  MatDialogConfig,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { EditorConfiguration } from 'codemirror';
import { saveAs } from 'file-saver';
import { Color } from '@tigergraph/tools-models/gvis/insights';
import * as JSZip from 'jszip';
import { isEqual, merge, union } from 'lodash';
import {
  BehaviorSubject,
  EMPTY,
  Observable,
  of,
  Subject,
  timer,
  forkJoin,
} from 'rxjs';
import {
  catchError,
  concatMap,
  debounceTime,
  distinctUntilChanged,
  finalize,
  map,
  share,
  switchMap,
  take,
  takeUntil,
  tap,
} from 'rxjs/operators';

import { DialogData, DialogComponent } from '@app/core/components/dialog';
import {
  AuthService,
  CacheService,
  Logger,
  MessageBus,
  CopierService,
} from '@app/core/services';
import {
  AdjustHorizontalModel,
  AdjustVerticalModel,
} from '@app/shared/components/adjust';
import { DynamicFormComponent } from '@app/shared/components/dynamic-form';
import { SchemaViewerComponent } from '@app/shared/components/schema-viewer';
import {
  TextEditorComponent,
  TextEditorValueChange,
} from '@app/shared/components/text-editor';
import {
  ToolbarConfig,
  ToolbarInteraction,
} from '@app/shared/components/toolbar-container';
import { VisualEditorComponent } from '@app/shared/components/visual-editor';
import {
  ChartData,
  ExplorationInfo,
} from '@app/shared/components/visual-editor/visual-editor.service';
import { ButtonBase, IconButton } from '@tigergraph/tools-models/button';
import { GlobalStatus } from '@tigergraph/tools-models/notification';
import {
  QueryFormConfiguration,
  QueryMeta,
  QueryMetaLogic,
  QuerySyntax,
} from '@tigergraph/tools-models/query';
import { QuestionBase } from '@tigergraph/tools-models/question';
import { Graph, GraphStyle } from '@tigergraph/tools-models/topology';
import { QuestionFormBuilderService } from '@app/shared/services';
import {
  FormatValidator,
  GLOBAL_GRAPH_NAME,
  GSQLPrivilege,
  stringifyJSON,
  compareWithInstanceVersion,
  getVersion,
} from '@tigergraph/tools-models/utils';
import { HelperFunctions } from '@app/shared/utils';
import {
  setGraphInfo,
  setWordsInQuery,
} from '@tigergraph/tools-models/utils/editor-mode/extension';

import { QueryEditorLogicService } from './shared';
import { getChartConfig } from './configs';
import { QueryEditorService, ToolbarButtons } from './query-editor.service';
import {
  QueryTemplates,
  SelectableItem,
} from './tree-select/tree-select.models';
import { SelectService } from './tree-select/select.service';
import { PostMessageService } from '@app/shared/services/post-message.service';

import { baseurl } from '../../app/config/config-interface';
import { handleGSQLReslt, parseRes } from '@tigergraph/tools-models';
import { Profile } from '@tigergraph/tools-ui';
import { isSupportGSQLDraft } from './shared/util';
import { environment } from '@environments/environment';

/**
 * Different multi-view split status:
 * - TopRight: top right panel full size
 * - Top: top panel full size
 * - Split: top panel and bottom panel split
 * - Bottom: bottom panel full size
 *
 * @enum {number}
 */
const enum SplitViewStatus {
  TopLeft,
  TopRight,
  Top,
  Split,
  Bottom,
}

/**
 * Different query result status:
 * - Graph: show visualization result
 * - Json: show json result
 * - Log: show log
 *
 * @enum {number}
 */
const enum QueryResultStatus {
  Schema,
  Graph,
  Json,
  Table,
  Log,
  Profile,
}

/** Different query status shown in front-end. */
const enum QueryStatus {
  Installing = 'installing',
  Disabled = 'disabled',
  Installed = 'installed',
  Draft = 'draft',
  Unknown = 'unknown',
}

/** Add query dialog status. */
const enum AddQueryDialogStatus {
  AddNewQuery,
  SaveQueryAs,
}

interface QueriesCanBeInstalled {
  queriesToBeInstalled: string[];
  queriesToBeAddedToGSQL: string[];
}

interface QueryRunStatus {
  elapsedTime: number;
  expirationTime: string;
  requestid: string;
  startTime: string;
  status: 'success' | 'aborted' | 'timeout' | 'running';
  url: string;
  user: string;
}

interface QueryAbortResult {
  aborted_queries: string[];
  unknown_requestid?: string[];
}

/**
 * Component to render the query editor interface.
 * Usage example:
 *   <app-query-editor></app-query-editor>
 *
 * @export
 * @class QueryEditorComponent
 * @implements {OnDestroy}
 * @implements {OnInit}
 */
@Component({
  selector: 'app-query-editor',
  templateUrl: './query-editor.component.html',
  styleUrls: ['./query-editor.component.scss'],
  providers: [QueryEditorService, QueryEditorLogicService],
})
export class QueryEditorComponent implements AfterViewInit, OnDestroy, OnInit {
  @ViewChild('popupWindow', { static: true }) popupWindow: TemplateRef<any>;
  @ViewChild('installQueryWindow', { static: true })
  installQueryWindow: TemplateRef<any>;
  @ViewChild('deleteDraftWindow', { static: true })
  deleteDraftWindow: TemplateRef<any>;
  @ViewChild('showQueryEndpointWindow', { static: true })
  showQueryEndpointWindow: TemplateRef<any>;
  @ViewChild('paramDialog', { static: true }) paramDialog: TemplateRef<any>;
  @ViewChild('runConfigDialog', { static: true })
  runConfigDialog: TemplateRef<any>;
  @ViewChild('queryRunningDialog', { static: true })
  queryRunningDialog: TemplateRef<any>;
  @ViewChild('redirectGShellDialog', { static: true })
  redirectGShellDialog: TemplateRef<any>;

  @ViewChild('queryEditor', { static: true }) queryEditor: TextEditorComponent;
  @ViewChild('schemaContainer', { static: true })
  schemaContainer: SchemaViewerComponent;
  @ViewChild('visualEditor', { static: true })
  graphVisualizer: VisualEditorComponent;
  @ViewChild('jsonViewer') jsonViewer: TextEditorComponent;
  @ViewChild('appDynamicForm')
  dynamicForm: DynamicFormComponent;

  query: QueryMeta;

  schema = new Graph();
  chartConfig = getChartConfig();

  queryToolbarConfig: ToolbarConfig;
  resultToolbarConfig: ToolbarConfig;

  queryEditorConfig: EditorConfiguration;
  jsonEditorConfig: EditorConfiguration;

  isPageDisabled = (sessionStorage.getItem('CLOUDVERSION') || '') === 'v4';
  cloudEditorLink = this.genCloudEditorLink();

  loading = new BehaviorSubject<boolean>(!this.isPageDisabled);
  loadingQueryContent = new BehaviorSubject<boolean>(false);
  loadingQueryList = new BehaviorSubject<boolean>(false);

  showTopRight = true;
  showTopLeft = true;
  showBottom = true;
  adjustHorizontalModel: AdjustHorizontalModel;
  adjustVerticalModel: AdjustVerticalModel;

  restPortNumber: number;

  graphName: string;
  queryList: QueryMeta[] = [];
  queriesList = new BehaviorSubject<QueryMeta[]>([]);
  queryName: string;
  queryContent: string;
  unsavedQueriesSubject = new BehaviorSubject<string[]>([]);

  queryParamPanelSubject = new BehaviorSubject<boolean>(false);
  queryParamPanelOpened: boolean;
  newQueryName: string;
  newQuerySyntax: QuerySyntax = 'GSQL';
  supportOpenCypher = getVersion() && compareWithInstanceVersion('3.9.3', '>=');
  supportGSQLReplica =
    getVersion() && compareWithInstanceVersion('3.10.0', '>=');
  supportAsyncQuery = getVersion() && compareWithInstanceVersion('4.1.1', '>=');
  supportQueryProfile = getVersion() && compareWithInstanceVersion('4.2.0', '>=');
  queryEndpoint: string;

  logContent = new BehaviorSubject<string>('No log to show');

  jsonContent: string; // json content to display
  profileData: Profile;
  isJsonResultTooLarge = false;
  private readonly MAX_JSON_SIZE = 10 * 1024 * 1024;
  private fullJsonContent = ''; // json content to download when it exceeds the size limit

  tableContent: any;

  vertexTypes: string[];
  selectedQueryParameters: QuestionBase<any>[];

  draftsTobeDeleted: string[] = [];
  queriesToBeInstalled: string[] = [];
  queriesToBeInstalledForDisplay: string[] = [];
  queriesHavingCodeCheckErrorsForDisplay: string[] = [];
  queriesHavingWrongName: string[] = [];

  reflow = false;

  cachePrefix = 'QueryEditor';
  private ngUnsubscribe: Subject<any> = new Subject<any>();

  private dialogConfig: MatDialogConfig;
  private popupWindowRef: MatDialogRef<TemplateRef<any>>;
  private installQueryWindowRef: MatDialogRef<TemplateRef<any>>;
  private paramDialogRef: MatDialogRef<TemplateRef<any>>;
  private runConfigDialogRef: MatDialogRef<TemplateRef<any>>;
  private queryRunningDialogRef: MatDialogRef<TemplateRef<any>>;

  private isPreviousGraphReady = false;
  private isLoading = true;
  private isLoadingQueryContent = false;
  private isLoadingQueryList = false;
  private schemaCreated = false;

  private currentSplitViewStatus = SplitViewStatus.Split;
  private currentQueryResult: QueryResultStatus;
  private currentAddQueryDialogStatus: AddQueryDialogStatus;

  private queryFilter = '';
  private currentCode: Subject<string> = new Subject<string>();
  private interpretedQueryMeta: QueryMeta;
  private interpretedQueryContent: string;

  runConfigPanelOpened: boolean;
  runConfigurationForm: FormGroup;
  replicationFactor: number;
  GSQLReplicaOptions: number[];

  // step 1 means choose to create empty query or choose from library, step 2 means select queries from library to install
  public addNewQueryStep = 1;
  public queryTemplates$: Observable<QueryTemplates[]>;
  public parsedTemplates$: Observable<SelectableItem[]>;
  public selectedNodes: SelectableItem[] = [];

  private enableQueryAbort = false;
  private isQueryAborting = false;
  private abortQueryReqId = '';
  private pollingStatusTimer: NodeJS.Timeout | null = null;

  constructor(
    private dialog: MatDialog,
    private snackBar: MatSnackBar,
    private authService: AuthService,
    private logger: Logger,
    private bus: MessageBus,
    private cacheService: CacheService,
    private questionFormBuilder: QuestionFormBuilderService,
    private logicService: QueryEditorLogicService,
    private cmptService: QueryEditorService,
    private copier: CopierService,
    private selectService: SelectService,
    private postMessageService: PostMessageService
  ) {
    this.dialogConfig = {
      width: '648px', // 600px for width plus the left and right padding which is 24px each.
      panelClass: 'add-new-query-dialog',
    };

    this.queryEditorConfig = {
      cursorHeight: 0,
      mode: 'text/x-gsql',
      readOnly: true,
      tabindex: -1,
      autofocus: false,
    };
    this.jsonEditorConfig = {
      cursorHeight: 0,
      mode: 'application/json',
      readOnly: true,
      tabindex: -1,
      autofocus: false,
    };

    this.adjustVerticalModel = {
      topDefaultHeight: 50,
      topDefaultHeightUnit: '%',
      topMinHeightPx: 71,
      bottomMinHeightPx: 88,
      from: this.cachePrefix,
    };
    this.adjustHorizontalModel = {
      leftDefaultWidth: 240,
      leftDefaultWidthUnit: 'px',
      leftMinWidthPx: 240,
      rightMinWidthPx: 260,
      from: this.cachePrefix,
    };

    this.queryToolbarConfig = this.cmptService.getQueryToolbarConfig();
    this.resultToolbarConfig = this.cmptService.getResultToolbarConfig();

    this.queryName = this.newQueryName = '';
    this.queryContent = this.getHint();
    this.jsonContent = '';
    this.tableContent = null;

    this.vertexTypes = [];
    this.selectedQueryParameters = [];

    this.query = null;

    this.attachSubjectCleanUp();
  }

  ngOnInit() {
    if (this.isPageDisabled) {
      return;
    }

    this.graphName = this.authService.getCurrentGraph();
    this.queryParamPanelOpened = false;

    if (this.authService.hasPrivilege(GSQLPrivilege.ReadSchema)) {
      this.getSchemaAndStyle();
    } else {
      this.syncQueryList();
      this.changeQueryResultView(QueryResultStatus.Log);
      this.schemaContainer.hasAccess = false;
      this.setLoadingStatus(false);
    }

    this.checkCodeOnChange();

    // Init toolbar to disable all buttons except expand.
    this.updateQueryToolbarStatus(undefined);

    // Push if there is unsaved queries.
    this.unsavedQueriesSubject
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe((value) => {
        this.bus.to<boolean>('HasUnsavedChanges').next(value.length > 0);
        this.postMessageService.postCanDeactivate(value.length === 0);
      });

    this.isPreviousGraphReady = true;

    this.initRunConfigurationForm();

    this.queryTemplates$ = this.cmptService.getQueryTemplates().pipe(share());

    this.parsedTemplates$ = this.queryTemplates$.pipe(
      map((data) => {
        return this.parseData(data);
      })
    );

    this.cmptService.logPageView();
  }

  ngAfterViewInit() {
    // Expose text editor instance to make it testable in e2e test.
    (<any>window).queryEditorChart = this.queryEditor;

    (<any>window).graphSchemaChart = this.schemaContainer;

    // Expose graphchart instance to make it testable in e2e test.
    (<any>window).queryResultGraphChart = this.graphVisualizer;

    this.getRestPortNumber();
    /**
     * Since server call happens to late in the life cycle check, we need to wait one tick
     * to avoid unidirectional-data-flow-violation error.
     * This technique is discussed here https://angular.io/guide/component-interaction#parent-calls-an-viewchild.
     */
    this.bus
      .from<boolean>('SwitchGraph')
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe(() => {
        this.switchGraph();
        this.bus.resetChannel('SwitchGraph');
      });

    timer(0)
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe(() => {
        this.bus
          .from<boolean>('AppReflow')
          .pipe(takeUntil(this.ngUnsubscribe), distinctUntilChanged())
          .subscribe((shouldReflow) => {
            this.reflow = shouldReflow;
            this.queryToolbarConfig.top[0].show = !this.reflow;
            this.resultToolbarConfig.left[0].show = !this.reflow;

            if (shouldReflow) {
              if (this.queryParamPanelOpened) {
                this.closeParamPanel();
                this.openParamDialog();
              } else if (this.runConfigPanelOpened) {
                this.switchRunConfigPanel('close');
                this.openRunConfigDialog();
              }

              if (this.currentSplitViewStatus !== SplitViewStatus.Bottom) {
                this.changeSplitView(SplitViewStatus.TopRight);
              }
            } else {
              if (this.paramDialogRef) {
                this.closeParamDialog();
                this.openParamPanel();
              } else if (this.runConfigDialogRef) {
                this.closeRunConfigDialog();
                this.switchRunConfigPanel('open');
              }

              this.changeSplitView(SplitViewStatus.Split);
            }
          });
      });
  }

  ngOnDestroy() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
    (<any>window).queryEditorChart = null;
    (<any>window).graphSchemaChart = null;
    (<any>window).queryResultGraphChart = null;
  }

  handleChangeGraphVersion($event) {
    if (this.authService.hasPrivilege(GSQLPrivilege.ReadSchema)) {
      this.getSchemaStyle();
    }
  }

  /**
   * Whether add query dialog is used for adding a new query.
   *
   * @readonly
   * @private
   * @type {boolean}
   * @memberof QueryEditorComponent
   */
  private get isAddingNewQuery(): boolean {
    return (
      this.currentAddQueryDialogStatus === AddQueryDialogStatus.AddNewQuery
    );
  }

  /**
   * Whether add pattern dialog is used for saving a pattern as a new pattern under a new name.
   *
   * @readonly
   * @private
   * @type {boolean}
   * @memberof QueryEditorComponent
   */
  private get isSavingQueryAs(): boolean {
    return (
      this.currentAddQueryDialogStatus === AddQueryDialogStatus.SaveQueryAs
    );
  }

  switchGraph() {
    // Clear query content and close param panel.
    this.graphName = this.authService.getCurrentGraph();
    this.queryName = this.newQueryName = '';
    this.queryContent = this.getHint();
    this.jsonContent = '';
    this.tableContent = null;

    this.vertexTypes = [];
    this.selectedQueryParameters = [];

    // Clear query list.
    this.logicService.updateQueriesMeta(this.graphName, []);
    this.queryList = [];
    this.queriesList.next([]);
    this.unsavedQueriesSubject.next(this.logicService.getUnsavedQueries());

    if (this.queryEditor) {
      this.queryEditor.updateOption('cursorHeight', 0);
      this.queryEditor.updateOption('readOnly', true);
    }

    if (!this.reflow) {
      this.closeParamPanel();
    }

    // Init toolbar to disable all buttons except expand.
    this.updateQueryToolbarStatus(undefined);

    // Load new schema.
    if (this.authService.hasPrivilege(GSQLPrivilege.ReadSchema)) {
      this.getSchemaAndStyle();
    } else {
      this.syncQueryList();
      this.changeQueryResultView(QueryResultStatus.Log);
      this.schemaContainer.hasAccess = false;
    }
  }

  /**
   * If current user can add query.
   *
   * @returns {boolean}
   * @memberof QueryEditorComponent
   */
  canAddQuery(): boolean {
    if (compareWithInstanceVersion('4.1.0', '>=')) {
      return this.authService.hasPrivilegesToCreateQuery(this.graphName);
    }

    return this.schemaCreated && this.canWriteQuery;
  }

  /**
   * If current user can install all queries.
   *
   * @returns {boolean}
   * @memberof QueryEditorComponent
   */
  canInstallAllQueries(): boolean {
    if (this.authService.hasPrivilegesToCreateQuery(this.graphName) || this.canWriteQuery) {
      return this.getQueriesCanBeInstalled().queriesToBeInstalled.length > 0;
    }

    return false;
  }

  /**
   * User can download the gsql file if there is at least one query.
   *
   * @returns {boolean}
   * @memberof QueryEditorComponent
   */
  canDownloadAllQueries(): boolean {
    return this.logicService.queriesMeta.size > 0;
  }

  /**
   * Warn user if leave the page without saving changes.
   *
   * @private
   * @returns {boolean}
   * @memberof QueryEditorComponent
   */
  @HostListener('window:beforeunload')
  private onBeforeUnload(): boolean {
    // If the current window is not the top window, let the window unload.
    if (this.postMessageService.isInIframe()) {
      return true;
    } else {
      return this.logicService.getUnsavedQueries().length === 0;
    }
  }

  /**
   * Determine if the current page can be deactivated.
   * Popup a warning to the user if there is unsaved query changes.
   *
   * @param {boolean} skipCheck
   * @returns {Observable<boolean>}
   * @memberof QueryEditorComponent
   */
  canDeactivate(skipCheck: boolean): Observable<boolean> {
    if (skipCheck) {
      return of(true);
    }

    const unsavedQueries = this.logicService.getUnsavedQueries();
    if (unsavedQueries.length === 0) {
      return of(true);
    }

    const data: DialogData = {
      title: 'Warning',
      messages: ['You have following unsaved queries:']
        .concat(unsavedQueries.map((queryName) => '- ' + queryName))
        .concat(['Do you still want to leave?']),
      actions: [
        { label: 'STAY', value: 0 },
        { label: 'LEAVE', value: 1, color: 'warn' },
      ],
    };

    return this.dialog
      .open(DialogComponent, { data: data })
      .afterClosed()
      .pipe(
        takeUntil(this.ngUnsubscribe),
        map((value) => value === 1)
      );
  }

  /**
   * Get the rest port number.
   *
   * @memberof QueryEditorComponent
   */
  getRestPortNumber() {
    this.cmptService
      .getPortNumber()
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe((port) => (this.restPortNumber = port));
  }

  /**
   * Handler for the user's interactions with the query toolbar.
   *
   * @param {ToolbarInteraction} event
   * @memberof QueryEditorComponent
   */
  onQueryInteraction(event: ToolbarInteraction) {
    switch (event.key) {
      case 'viewChange':
        if (
          this.currentSplitViewStatus === SplitViewStatus.Split ||
          this.currentSplitViewStatus === SplitViewStatus.Top
        ) {
          this.changeSplitView(SplitViewStatus.TopRight);
        } else if (this.reflow) {
          this.changeSplitView(SplitViewStatus.Top);
        } else {
          this.changeSplitView(SplitViewStatus.Split);
        }
        break;
      case 'save': {
        this.handleSave();
        break;
      }
      case 'saveAs': {
        this.openSaveQueryAsDialog();
        break;
      }
      case 'install': {
        this.preHandleInstall();
        break;
      }
      case 'console': {
        this.toggleConsole();
        break;
      }
      case 'run': {
        this.handleRun();
        break;
      }
      case 'runConfiguration': {
        this.reflow
          ? this.openRunConfigDialog()
          : this.switchRunConfigPanel('open');
        break;
      }
      case 'deleteQuery': {
        const query = this.logicService.queriesMeta.get(this.queryName);
        if (query.draftCode.length && !query.originalCode.length) {
          this.handleDeleteDraft();
        } else {
          this.handleDeleteQuery();
        }
        break;
      }
      case 'showEndpoint': {
        this.handleShowEndpoint();
        break;
      }
      case 'downloadGsql': {
        this.handleDownloadGsql();
        break;
      }
      case 'deleteDraft': {
        this.handleDeleteDraft();
        break;
      }
      case 'help': {
        this.handleHelp();
        break;
      }
    }
  }

  /**
   * Handler for the user's interactions with the result toolbar.
   *
   * @param {ToolbarInteraction} event
   * @memberof QueryEditorComponent
   */
  onResultInteraction(event: ToolbarInteraction) {
    switch (event.key) {
      case 'viewChange':
        if (this.currentSplitViewStatus === SplitViewStatus.Split) {
          this.changeSplitView(SplitViewStatus.Bottom);
        } else {
          this.changeSplitView(SplitViewStatus.Split);
        }
        break;
      case 'schema':
        this.changeQueryResultView(QueryResultStatus.Schema);
        break;
      case 'graph':
        this.changeQueryResultView(QueryResultStatus.Graph);
        break;
      case 'json':
        this.changeQueryResultView(QueryResultStatus.Json);
        break;
      case 'table':
        this.changeQueryResultView(QueryResultStatus.Table);
        break;
      case 'log':
        this.changeQueryResultView(QueryResultStatus.Log);
        break;
      case 'profile':
        this.changeQueryResultView(QueryResultStatus.Profile);
        break;
    }
  }

  filterQueryList(value?: string) {
    if (value !== undefined) {
      this.queryFilter = value;
    }
    this.queriesList.next(
      this.queryList.filter((query) =>
        query.queryName
          .toUpperCase()
          .includes(this.queryFilter.trim().toUpperCase())
      )
    );
  }

  /**
   * Handler for when code in query editor change.
   *
   * @param {TextEditorValueChange} event
   * @memberof QueryEditorComponent
   */
  onCodeChange(event: TextEditorValueChange) {
    if (!event.value) {
      return;
    }

    // Update the current query content.
    if (this.queryName !== '') {
      this.updateQueryContentAtFrontEndSide(this.queryName, event.value);
      // Update query toolbar status.
      const queryMeta = this.logicService.queriesMeta.get(this.queryName);
      this.updateQueryToolbarAndButton(queryMeta);
      // Extract words from query content for auto suggestion.
      this.extractQueryWords(event.value);
    }

    if (this.queryName && event.value) {
      this.currentCode.next(event.value);
    } else {
      this.cmptService.clearCodeCheckError();
      this.queryEditor.performLint();
    }
  }

  /**
   * Check if current query result view is schema.
   *
   * @returns {boolean}
   * @memberof QueryEditorComponent
   */
  inSchemaView(): boolean {
    return this.currentQueryResult === QueryResultStatus.Schema;
  }

  /**
   * Check if current query result view is graph.
   *
   * @returns {boolean}
   * @memberof QueryEditorComponent
   */
  inGraphView(): boolean {
    return this.currentQueryResult === QueryResultStatus.Graph;
  }

  /**
   * Check if current query result view is json.
   *
   * @returns {boolean}
   * @memberof QueryEditorComponent
   */
  inJsonView(): boolean {
    return this.currentQueryResult === QueryResultStatus.Json;
  }

  /**
   * Check if current query result view is table.
   *
   * @returns {boolean}
   * @memberof QueryEditorComponent
   */
  inTableView(): boolean {
    return this.currentQueryResult === QueryResultStatus.Table;
  }

  /**
   * Check if current query result view is profile.
   *
   * @returns {boolean}
   * @memberof QueryEditorComponent
   */
  inProfileView(): boolean {
    return this.currentQueryResult === QueryResultStatus.Profile;
  }

  /**
   * Check if current query result view is log.
   *
   * @returns {boolean}
   * @memberof QueryEditorComponent
   */
  inLogView(): boolean {
    return this.currentQueryResult === QueryResultStatus.Log;
  }

  /**
   * Close the entering query param panel.
   *
   * @memberof QueryEditorComponent
   */
  closeParamPanel() {
    this.switchRunConfigPanel('close');
    this.queryParamPanelOpened = false;
    this.queryParamPanelSubject.next(this.queryParamPanelOpened);
    this.updateQueryToolbarStatus(
      this.logicService.queriesMeta.get(this.queryName)
    );
  }

  /**
   * Open the entering query param panel.
   *
   * @memberof QueryEditorComponent
   */
  openParamPanel() {
    this.queryParamPanelOpened = true;
    this.queryParamPanelSubject.next(this.queryParamPanelOpened);
    this.updateQueryToolbarStatus(
      this.logicService.queriesMeta.get(this.queryName)
    );
  }

  /**
   * Switch the run configuration panel;
   *
   * @param {string} openOrClose
   * @memberof QueryEditorComponent
   */
  switchRunConfigPanel(openOrClose: string) {
    switch (openOrClose) {
      case 'open':
        this.runConfigPanelOpened = true;
        this.queryToolbarConfig.top[ToolbarButtons.Run].disabled = true;
        break;
      case 'close':
        // We won't save the settings if user click on close instead of save.
        this.runConfigurationForm.setValue(this.logicService.formRunConfig);
        this.runConfigPanelOpened = false;
        this.queryToolbarConfig.top[ToolbarButtons.Run].disabled = false;
        break;
    }
  }

  /**
   * Open dialog to add new query.
   *
   * @memberof QueryEditorComponent
   */
  openAddNewQueryDialog() {
    this.currentAddQueryDialogStatus = AddQueryDialogStatus.AddNewQuery;
    this.popupWindowRef = this.dialog.open(this.popupWindow, this.dialogConfig);
  }

  /**
   * Open dialog to save query as another query under a new name.
   *
   * @memberof QueryEditorComponent
   */
  openSaveQueryAsDialog() {
    this.newQueryName = this.logicService.getSaveAsQueryName(this.queryName);
    this.newQuerySyntax = this.query.syntax.includes('GSQL') ? 'GSQL' : 'CYPHER';
    this.currentAddQueryDialogStatus = AddQueryDialogStatus.SaveQueryAs;
    this.popupWindowRef = this.dialog.open(this.popupWindow, this.dialogConfig);
  }

  /**
   * Close add query window.
   *
   * @memberof QueryEditorComponent
   */
  closeAddOrSaveQueryDialog() {
    if (this.popupWindowRef) {
      this.popupWindowRef
        .afterClosed()
        .pipe(take(1))
        .subscribe(() => {
          this.addNewQueryStep = 1;
        });
      this.popupWindowRef.close();
    }
  }

  /**
   * Add a new query.
   *
   * @memberof QueryEditorComponent
   */
  addQuery() {
    // Check query name
    const result = FormatValidator.isName(this.newQueryName);
    if (!result.success) {
      this.handleError(result.message, 'Invalid query name');
      return;
    }

    // Queries cannot have duplicated names
    if (this.logicService.queriesMeta.get(this.newQueryName)) {
      this.handleError([
        'A query with the same name already exists.',
        'Please input a different query name.',
      ]);
      return;
    }

    // Check name confict with all type names from GSQL.
    this.cmptService
      .getGSQLTypeNames()
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe(
        (gsqlTypeNames) => {
          const names = gsqlTypeNames[GLOBAL_GRAPH_NAME].concat(
            gsqlTypeNames[this.graphName]
          );
          if (names.includes(this.newQueryName)) {
            this.handleError([
              `"${this.newQueryName}" is used by another GSQL object.`,
            ]);
          } else {
            this.isAddingNewQuery
              ? this.handleAddNewQuery()
              : this.handleSaveQueryAs();
          }
        },
        (err: HttpErrorResponse) => this.handleError(err.error, err.statusText)
      );
  }

  /**
   * Handler for add a new query.
   *
   * @private
   * @memberof QueryEditorComponent
   */
  private handleAddNewQuery() {
    this.closeAddOrSaveQueryDialog();

    const newGSQLQueryContent =
      `CREATE OR REPLACE DISTRIBUTED QUERY ${this.newQueryName}(/* Parameters here */) FOR GRAPH ${this.graphName} { \n` +
      `  /* Write query logic here */ \n  PRINT "${this.newQueryName} works!"; \n}`;
    const newOpenCypherQueryContent =
      `CREATE OR REPLACE DISTRIBUTED OPENCYPHER QUERY ${this.newQueryName}(/* Parameters here */) FOR GRAPH ${this.graphName} { \n` +
      `  /* Write query logic here */ \n  RETURN "${this.newQueryName} works!" \n}`;

    this.setLoadingStatus(true);
    this.cmptService
      .createQueryDraft(
        this.graphName,
        this.newQueryName,
        this.newQuerySyntax,
        this.newQuerySyntax === 'GSQL'
          ? newGSQLQueryContent
          : newOpenCypherQueryContent
      )
      .pipe(
        takeUntil(this.ngUnsubscribe),
        concatMap(() => this.authService.updateUserInfos()),
        finalize(() => this.setLoadingStatus(false))
      )
      .subscribe(
        () => {
          this.authService.setCurrentGraph(this.graphName);
          // Get the query list then show newly added query
          const newQueryName = this.newQueryName;
          this.syncQueryList(newQueryName);
          this.newQueryName = '';
        },
        (err: HttpErrorResponse) => this.showDialogOrLog(err)
      );
  }

  /**
   * Get the algorithm nodes of the selectedNodes.
   * Because only the algorithm node has algorithmName, and backend need algorithms node's name to install lib.
   */
  installScienceLibrary() {
    const algorithmsNode = this.selectedNodes.filter(
      (node) => !!node.algorithmName
    );
    const queryNames = algorithmsNode.map((node) => node.name);

    this.cmptService.logInstallScienceLibrary(queryNames);

    this.popupWindowRef.close();

    this.bus.to<GlobalStatus>('GlobalStatus').next({
      installQuery: {
        running: true,
        queries: queryNames,
      },
    });

    this.cmptService
      .installQueryTemplates(queryNames, this.graphName)
      .pipe(
        takeUntil(this.ngUnsubscribe),
        finalize(() =>
          this.bus.to<GlobalStatus>('GlobalStatus').next({
            installQuery: {
              running: false,
              queries: [],
            },
          })
        )
      )
      .subscribe(
        (res) => {
          this.syncQueryList();
          let errorMessage = '';
          const data: DialogData = {
            title: 'Installation results',
            messages: [],
          };
          if (res.success.length > 0) {
            data.messages.push('Successfully installed the following queries:');
            data.messages = data.messages.concat(
              res.success.map((item) => `- ${item.name}`)
            );
          }
          if (res.failed.length > 0) {
            data.messages.push('Failed to install the following queries:');
            data.messages = data.messages.concat(
              res.failed.map((item) => `- ${item.name}`)
            );
            errorMessage = res.failed
              .map((item) => `${item.errorMessage}\n\n`)
              .join('');
            // expose the failed log to user
            data.messages.push(errorMessage);
            this.logger.warn(errorMessage);
          }
          this.dialog.open(DialogComponent, {
            data: data,
            id: 'installation-result-dialog',
          });
          this.logContent.next('Query installation request sent successfully.');
        },
        (err) => {
          this.syncQueryList(undefined, true);
          this.showDialogOrLog(err);
        }
      );
    this.addNewQueryStep = 1;
  }

  /**
   * Replace all the old query names in query content with the new query name.
   * If the old query names can not be found then leave the query content as is.
   *
   * @private
   * @return {string}
   * @memberof QueryEditorComponent
   */
  private getQueryContentUnderNewName(): string {
    // The regex pattern matches "QUERY (arbitrary whitespace) name (arbitrary whitespace) ("
    const regex = `(QUERY\\s+)(${this.queryName})(\\s*\\()`;

    return this.queryContent.replace(
      new RegExp(regex, 'gi'),
      `$1${this.newQueryName}$3`
    );
  }

  /**
   * Show content of given query name.
   * If query has draft, show the draftCode in query meta, otherwise show the originalCode.
   *
   * @param {string} queryName
   * @memberof QueryEditorComponent
   */
  showQuery(queryName: string) {
    this.cmptService.clearCodeCheckError();
    this.queryEditor.performLint();

    if (this.logicService.queriesMeta.has(queryName)) {
      if (!this.reflow) {
        this.closeParamPanel();
      }

      this.queriesList
        .pipe(takeUntil(this.ngUnsubscribe))
        .subscribe((queryList) => {
          for (const query of queryList) {
            if (query.queryName === queryName) {
              this.query = query;
              return;
            }
          }
        });

      this.queryName = queryName;

      this.queryContent =
        this.logicService.getFrontEndQueryCode(queryName) ||
        this.logicService.getServerQueryCode(queryName);

      this.queryEditor.updateOption('cursorHeight', 1);

      // Only if user has WRITE_QUERY privilege, can she edit query
      if (this.canWriteQuery) {
        this.queryEditor.updateOption('readOnly', false);
      }

      const { syntax } = this.query;
      if (HelperFunctions.isGSQLSyntax(syntax)) {
        this.queryEditor.updateOption('mode', 'text/x-gsql');
      } else {
        this.queryEditor.updateOption('mode', 'application/x-cypher-query');
      }

      // Update query toolbar status
      const queryMeta = this.logicService.queriesMeta.get(queryName);
      this.updateQueryToolbarAndButton(queryMeta);
      if (this.reflow) {
        this.changeSplitView(SplitViewStatus.TopRight);
      }
    } else {
      this.handleError(`Query "${queryName}" is not found.`);
    }
  }

  /**
   * Check if the query content does not start with "CREATE OR REPLACE" syntax.
   *
   * @private
   * @param {QueryMeta} queryName
   * @returns {boolean}
   * @memberof QueryEditorComponent
   */
  private noMatchReplace(queryName: string): boolean {
    const pattern =
      '^\\s*create\\s+(or\\s+replace)\\s+(?:batch\\s+)?(?:distributed\\s+)?query\\s+([^\\s]+)\\s*\\(';

    let queryContent =
      this.logicService.getFrontEndQueryCode(queryName) ||
      this.logicService.getServerQueryCode(queryName);
    queryContent = this.removeCommentsInCode(queryContent);

    return !queryContent.match(new RegExp(pattern, 'i'));
  }

  private isDistributedQuery(queryName: string): boolean {
    const pattern =
      '^\\s*create\\s+(or\\s+replace\\s+)?(?:batch\\s+)?(?:distributed\\s+)query\\s+([^\\s]+)\\s*\\(';
    const queryContent =
      this.logicService.getFrontEndQueryCode(queryName) ||
      this.logicService.getServerQueryCode(queryName);

    return !!queryContent.match(new RegExp(pattern, 'i'));
  }

  /**
   * Show ACL privilege clearance warning.
   *
   * @param {boolean} [installAll=false]
   * @param {string[]} [queryNames]
   * @param {QueriesCanBeInstalled} [queries]
   * @memberof QueryEditorComponent
   */
  showACLWarning(
    installAll = false,
    queryNames?: string[],
    queries?: QueriesCanBeInstalled
  ) {
    const messages = [
      `Update ${installAll ? 'following queries' : 'a query'
      } will clear the ACL privileges on the query.`,
      'Are you sure you want to proceed?',
      'You can use "CREATE OR REPLACE" instead to avoid ACL privilege clearance.',
    ];
    if (queryNames) {
      queryNames.forEach((queryName) => messages.push(` - ${queryName}`));
    }

    const data: DialogData = {
      title: 'Warning',
      messages,
      actions: [
        { label: 'CANCEL', value: 0 },
        { label: 'CONTINUE', value: 1, color: 'warn' },
      ],
    };

    this.dialog
      .open(DialogComponent, merge({ data: data }, this.dialogConfig))
      .afterClosed()
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe((value) => {
        if (value === 1) {
          installAll ? this.installAllQueries(queries) : this.handleInstall();
        }
      });
  }

  /**
   * Pre handler for installing all queries.
   * Show warning if there are any ACL specified ones or installing ones.
   *
   * @returns
   * @memberof QueryEditorComponent
   */
  preInstallAllQueries() {
    // If any query is installing, should not start installing other queries.
    let installingQuery = false;
    this.logicService.queriesMeta.forEach((queryMeta, __) => {
      if (queryMeta.installing) {
        installingQuery = true;
      }
    });
    if (installingQuery) {
      this.handleError(
        `You can not install queries when system is installing another query.`
      );
      return;
    }

    // Get all queries that will be installed and that need to be added first.
    const queries = this.getQueriesCanBeInstalled();
    const warningQueries = union(
      queries.queriesToBeAddedToGSQL,
      queries.queriesToBeInstalled
    ).filter((queryName) => {
      const queryMeta = this.logicService.queriesMeta.get(queryName);
      return this.noMatchReplace(queryName) && queryMeta.isACLSpecified;
    });
    if (warningQueries.length > 0) {
      this.showACLWarning(true, warningQueries, queries);
    } else {
      this.installAllQueries(queries);
    }
  }

  /**
   * Install all queries that are not installed yet, already saved, and don't have
   * code check errors.
   *
   * @private
   * @param {QueriesCanBeInstalled} queries
   * @memberof QueryEditorComponent
   */
  private async installAllQueries(queries: QueriesCanBeInstalled) {
    this.queriesToBeInstalled = [];

    // Filter out the queries with the wrong name.
    this.queriesHavingWrongName = this.getQueriesHavingWrongName(
      queries.queriesToBeInstalled
    );
    queries.queriesToBeInstalled = queries.queriesToBeInstalled.filter(
      (query) => !this.queriesHavingWrongName.includes(query)
    );
    queries.queriesToBeAddedToGSQL = queries.queriesToBeAddedToGSQL.filter(
      (query) => !this.queriesHavingWrongName.includes(query)
    );

    // Holds the query drafts to be code checked before upserting to GSQL.
    const queryDraftsToCodeCheck: string[] = [];
    const queryNamesToCodeCheck: string[] = [];
    queries.queriesToBeAddedToGSQL.forEach((queryName) => {
      queryDraftsToCodeCheck.push(
        this.logicService.getServerQueryCode(queryName)
      );
      queryNamesToCodeCheck.push(queryName);
    });
    // Since 3.9.3, queries added to GSQL should also be code checked before installing.
    queries.queriesToBeInstalled.forEach((queryName) => {
      if (!queryNamesToCodeCheck.includes(queryName)) {
        queryDraftsToCodeCheck.push(
          this.logicService.getServerQueryCode(queryName)
        );
        queryNamesToCodeCheck.push(queryName);
      }
    });

    // Holds the query names that have passed the code check
    // and need to be upserted to GSQL before intalling them.
    let queriesToBeAddedToGSQL: string[] = [];
    // Holds the query names that failed to pass the code check
    // and will not be upserted to GSQL.
    const queriesHavingCodeCheckErrors: string[] = [];

    this.cmptService.logInstallAllQueries();

    this.setLoadingStatus(true);
    this.cmptService
      .checkIfPassedCodeCheck(this.graphName, queryDraftsToCodeCheck)
      .pipe(
        takeUntil(this.ngUnsubscribe),
        concatMap((responses) => {
          responses.forEach((passed, i) => {
            if (!passed) {
              queriesHavingCodeCheckErrors.push(queryNamesToCodeCheck[i]);
            }
          });
          queriesToBeAddedToGSQL = queries.queriesToBeAddedToGSQL.filter(
            (queryName) => {
              return !queriesHavingCodeCheckErrors.includes(queryName);
            }
          );
          this.queriesToBeInstalled = queries.queriesToBeInstalled.filter(
            (queryName) => {
              return !queriesHavingCodeCheckErrors.includes(queryName);
            }
          );

          this.queriesToBeInstalledForDisplay = this.limitDisplayQueryNameList([
            ...this.queriesToBeInstalled,
          ]).sort((q1, q2) => (q1 > q2 ? 1 : -1));
          this.queriesHavingCodeCheckErrorsForDisplay =
            this.limitDisplayQueryNameList([
              ...queriesHavingCodeCheckErrors,
            ]).sort((q1, q2) => (q1 > q2 ? 1 : -1));

          if (queriesToBeAddedToGSQL.length > 0) {
            const queryParams = queriesToBeAddedToGSQL.map((query) => ({
              name: query,
              needDrop: this.noMatchReplace(query),
            }));
            return this.cmptService.addQueryDraftToGSQL(
              this.graphName,
              queryParams
            );
          }
          this.installQueryWindowRef = this.dialog.open(
            this.installQueryWindow,
            this.dialogConfig
          );
          return EMPTY;
        }),
        finalize(() => this.setLoadingStatus(false)),
        catchError((err) => {
          this.showDialogOrLog(err);
          return EMPTY;
        })
      )
      .subscribe((_) => {
        this.installQueryWindowRef = this.dialog.open(
          this.installQueryWindow,
          this.dialogConfig
        );
      });
  }

  private limitDisplayQueryNameList(queryNames: string[]): string[] {
    return queryNames.map((queryName) =>
      HelperFunctions.limitStringDisplayLength(queryName, 20, 20)
    );
  }

  /**
   * Given a list of query names, filter out queries with wrong name.
   * @private
   * @param queriesToCheckName
   * @memberof QueryEditorComponent
   */
  private getQueriesHavingWrongName(queriesToCheckName: string[]): string[] {
    const queriesHavingWrongName = queriesToCheckName.filter(
      (query) => !this.queryNameInCodeIsValid(query)
    );
    queriesHavingWrongName.sort();

    return queriesHavingWrongName;
  }

  /**
   * Close add query window.
   *
   * @memberof QueryEditorComponent
   */
  closeInstallAllQueries() {
    if (this.installQueryWindowRef) {
      this.installQueryWindowRef.close();
    }
  }

  /**
   * Install all queries that can be installed.
   *
   * @memberof QueryEditorComponent
   */
  executeInstallAllQueries() {
    this.closeInstallAllQueries();
    this.installQuery(this.queriesToBeInstalled);
  }

  /**
   * Get a list of queries that can be installed and a list of queries that need to be added first.
   *
   * @memberof QueryEditorComponent
   */
  getQueriesCanBeInstalled(): QueriesCanBeInstalled {
    // Holds all queries that will be installed.
    const queriesToBeInstalled: string[] = [];
    // Holds all queries that need to be added to GSQL before installing them.
    const queriesToBeAddedToGSQL: string[] = [];

    const unsavedQueries = this.logicService.getUnsavedQueries();
    this.logicService.queriesMeta.forEach((queryMeta, queryName) => {
      // Skip already installed queries.
      if (this.queryStatus(queryMeta) === QueryStatus.Installed) {
        return;
      }

      if (compareWithInstanceVersion('4.1.0', '>=')) {
        // Skip queries that user does not have privilege to install
        if (!this.authService.hasPrivilegesToQuery(
          this.graphName,
          queryName,
          [GSQLPrivilege.Owner, GSQLPrivilege.InstallQuery]
        )) {
          return;
        }
      }

      // Skip queries that are not saved.
      if (unsavedQueries.includes(queryName)) {
        return;
      }

      // The query can be installed potentially.
      queriesToBeInstalled.push(queryName);
      // If the query draft is not empty, that means the query needs to be added to GSQL first.
      if (queryMeta.draftCode !== '') {
        queriesToBeAddedToGSQL.push(queryName);
      }
    });

    return {
      queriesToBeInstalled: queriesToBeInstalled,
      queriesToBeAddedToGSQL: queriesToBeAddedToGSQL,
    };
  }

  /**
   * Save the selected query.
   *
   * @private
   * @memberof QueryEditorComponent
   */
  private handleSave() {
    if (!this.queryName) {
      return;
    }

    // Get the current text content directly from the text editor and update front end copy
    const currentContent = this.queryEditor.getValue();
    this.updateQueryContentAtFrontEndSide(
      this.queryName,
      currentContent
    );

    if (!this.queryNameInCodeIsValid(this.queryName)) {
      this.handleError(
        `Please change the query name in code to '${this.queryName}'.`
      );
      return;
    }
    if (!this.validateGSQLQuery(currentContent)) {
      this.setLoadingStatus(false);
      this.dialog.open(this.redirectGShellDialog, this.dialogConfig);
      return;
    }

    // Check if query matches the pattern and replace "create" with "create or replace"
    const createQueryPattern = /(\s*)create(\s+(?:batch\s+)?(?:distributed\s+)?(?:opencypher\s+)?query\s+[^\s]+\s*\()/i;
    let modifiedContent = currentContent;
    if (createQueryPattern.test(currentContent)) {
      modifiedContent = currentContent.replace(createQueryPattern, '$1CREATE OR REPLACE$2');
    }

    this.setLoadingStatus(true);
    this.cmptService.updateQueryDraft(
      this.graphName,
      this.queryName,
      this.query.syntax,
      modifiedContent,
    )
    .pipe(
      takeUntil(this.ngUnsubscribe),
      finalize(() => this.setLoadingStatus(false)),
      )
    .subscribe(
      () => {
        this.logicService.updateQueryMetaDraft(
          this.queryName,
          modifiedContent
        );
        this.updateQueryContentAtFrontEndSide(
          this.queryName,
          modifiedContent
        );
        this.unsavedQueriesSubject.next(this.logicService.getUnsavedQueries());

          // UI
          this.showQueryIsSavedSnackBar(this.queryName);
          this.updateQueryToolbarAndButton(
            this.logicService.getQueryMeta(this.queryName)
          );
          if (!this.reflow) {
            this.closeParamPanel();
          }
        },
        (err: HttpErrorResponse) => this.showDialogOrLog(err)
      );

    this.cmptService.logSaveQuery(this.queryName);
  }

  /**
   * Handler for save query as another query under a different name.
   *
   * @private
   * @memberof QueryEditorComponent
   */
  private handleSaveQueryAs() {
    const newQueryContent = this.getQueryContentUnderNewName();

    this.cmptService
      .createQueryDraft(
        this.graphName,
        this.newQueryName,
        this.query.syntax.includes('GSQL') ? 'GSQL' : 'CYPHER',
        newQueryContent
      )
      .pipe(takeUntil(this.ngUnsubscribe),
        concatMap(() => this.authService.updateUserInfos()),
        finalize(() => this.setLoadingStatus(false))
      )
      .subscribe(
        () => {
          this.authService.setCurrentGraph(this.graphName);
          // UI
          this.closeAddOrSaveQueryDialog();
          this.showQueryIsSavedSnackBar(this.newQueryName);

          // Logic
          this.syncQueryList(this.newQueryName);
          this.updateQueryContentAtFrontEndSide(
            this.newQueryName,
            newQueryContent
          );
          this.newQueryName = '';
        },
        (err: HttpErrorResponse) => this.showDialogOrLog(err)
      );

    this.cmptService.logSaveQuery(this.queryName);
  }

  /**
   * Show "query is saved" snack bar on UI.
   *
   * @param queryName
   */
  private showQueryIsSavedSnackBar(queryName: string) {
    this.snackBar.open(`The query "${queryName}" is saved`, 'DISMISS', {
      duration: 3000,
    });
  }

  /**
   * Check query name in query code, return true if it's the same as in GraphStudio.
   *
   * @private
   * @memberof QueryEditorComponent
   */
  private queryNameInCodeIsValid(queryName: string): boolean {
    const queryContent =
      this.logicService.getFrontEndQueryCode(queryName) ||
      this.logicService.getServerQueryCode(queryName);

    if (!queryContent) {
      return false;
    }

    const queryCode = this.removeCommentsInCode(queryContent);

    // Grep query name
    // ?: is for non-capturing group, this regex pattern
    const validQueryPattern =
      '^\\s*create\\s+(?:(?:or\\s+replace)\\s+)?(?:batch\\s+)?(?:distributed\\s+)?query\\s+([^\\s]+)\\s*\\(';
    const matchResult = queryCode.match(new RegExp(validQueryPattern, 'i'));

    // If code doesn't match this pattern, then code is not valid.
    // The error message will be code errors, but not query name error.
    if (!matchResult) {
      return true;
    }

    return matchResult[1] === queryName;
  }

  /**
   * Remove comments of query content.
   *
   * @param queryContent
   * @returns {string}
   */
  private removeCommentsInCode(queryContent: string): string {
    // '\/\*(.|\n)*?\*\/' matches multiple lines comment
    // '(\/\/|#).*' matches single line comment
    const multiLineCommentPattern = new RegExp(/\/\*(.|\n)*?\*\//g);
    const singleLineCommentPattern = new RegExp(/(\/\/|#).*/g);
    return queryContent
      .replace(multiLineCommentPattern, '')
      .replace(singleLineCommentPattern, '');
  }

  /**
   * Remove comments of query content.
   *
   * @param queryContent
   * @returns {string}
   */
  private validateGSQLQuery(queryContent: string): boolean {
    const queryCode = this.removeCommentsInCode(queryContent);
    const forbiddenCommands = '\\b(?:INSTALL\\s+QUERY|RUN\\s+QUERY|SHOW\\s+QUERY|DROP\\s+QUERY|'
      + 'USE\\s+GRAPH|DROP\\s+GRAPH|CREATE\\s+GRAPH|DROP\\s+ALL|'
      + 'USE\\s+GLOBAL|CREATE\\s+VERTEX|DELETE\\s+VERTEX|CREATE\\s+DIRECTED\\s+EDGE|'
      + 'CREATE\\s+UNDIRECTED\\s+EDGE|DELETE\\s+EDGE|SCHEMA_CHANGE\\s+JOB|'
      + 'LOADING\\s+JOB|DROP\\s+JOB|LOADING\\s+STATUS|'
      + 'CREATE\\s+USER|SHOW\\s+USER|SHOW\\s+PRIVILEGE|GRANT\\s+ROLE|REVOKE\\s+ROLE|'
      + 'ALTER\\s+PASSWORD|DROP\\s+USER|CREATE\\s+SECRET|SHOW\\s+SECRET|DROP\\s+SECRET|'
      + 'EXPORT\\s+GRAPH|IMPORT\\s+GRAPH)\\b';
    const matchResult = queryCode.match(new RegExp(forbiddenCommands, 'i'));

    // If code doesn match this pattern, then code is not valid.
    // The error message will be code errors, but not query name error.
    return !matchResult;
  }

  /**
   * Pre handler for install query action.
   *
   * @private
   * @memberof QueryEditorComponent
   */
  private preHandleInstall() {
    const queryName = this.queryName;

    const queryMeta = this.logicService.queriesMeta.get(queryName);
    if (!queryMeta) {
      this.handleError(`Query "${queryName}" doesn't exist.`);
      return;
    }

    if (queryMeta.isACLSpecified && this.noMatchReplace(queryName)) {
      this.showACLWarning();
    } else {
      this.handleInstall();
    }
  }

  /**
   * Install the selected query.
   *
   * @private
   * @memberof QueryEditorComponent
   */
  private handleInstall() {
    const queryName = this.queryName;
    this.cmptService.logInstallQuery();

    const queryMeta = this.logicService.queriesMeta.get(queryName);
    if (!queryMeta) {
      this.handleError(`Query "${queryName}" doesn't exist.`);
      return;
    }

    // Must save query before install
    if (this.logicService.getUnsavedQueries().includes(queryName)) {
      this.handleError(
        `Please save your changes to query "${queryName}" before installing.`
      );
      return;
    }

    if (this.queryStatus(queryMeta) === QueryStatus.Installed) {
      this.handleError(
        `Query "${queryName}" is already installed.`,
        'Nothing to install'
      );
      return;
    }

    this.setLoadingStatus(true);
    this.cmptService
      .checkCode(
        this.graphName,
        this.queryName,
        this.logicService.getFrontEndQueryCode(queryName) ||
        this.logicService.getServerQueryCode(queryName),
        this.query.syntax
      )
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe(
        (errors) => {
          if (!queryMeta.isHidden && errors.errors.length > 0) {
            // If there are errors, don't proceed to install
            this.setLoadingStatus(false);
            this.handleError(
              errors.errors.map(error => error.message).join('\n'),
              'Resolve errors first'
            );
            return;
          } else {
            // The query name in code must match the query name in GraphStudio.
            if (!this.queryNameInCodeIsValid(queryName)) {
              this.setLoadingStatus(false);
              this.handleError(
                `Please change the query name in code to '${queryName}'.`
              );
              return;
            }
            if (!this.validateGSQLQuery(queryMeta.draftCode)) {
              this.setLoadingStatus(false);
              this.dialog.open(this.redirectGShellDialog, this.dialogConfig);
              return;
            }

            // If query draft exists, add query first, otherwise install query directly
            if (queryMeta.draftCode !== '') {
              this.cmptService
                .addQueryDraftToGSQL(this.graphName, [
                  { name: queryName, needDrop: this.noMatchReplace(queryName) },
                ])
                .pipe(
                  takeUntil(this.ngUnsubscribe),
                  finalize(() => this.setLoadingStatus(false))
                )
                .subscribe(
                  (result) => {
                    // Continue to install the query if added successfully, else popup error message.
                    if (
                      result.success.find((query) => query.name === queryName)
                    ) {
                      this.installQuery([queryName]);
                    } else {
                      this.setLoadingStatus(false);
                      this.handleError(
                        result.failed.find((query) => query.name === queryName)
                          .errorMessage
                      );
                    }
                  },
                  (err) => {
                    this.setLoadingStatus(false);
                    this.showDialogOrLog(err);
                  }
                );
            } else {
              this.setLoadingStatus(false);
              this.installQuery([queryName]);
            }
          }
        },
        (err: HttpErrorResponse) => {
          this.handleError(err.error, err.statusText);
          this.setLoadingStatus(false);
        }
      );
  }

  private installQuery(queryNames: string[]) {
    if (compareWithInstanceVersion('4.1.1', '>=')) {
      this.installQueryByGSQLCmd(queryNames);
      return;
    }

    this.installQueryByEndpoint(queryNames);
  }

  private installQueryByGSQLCmd(queryNames: string[]) {
    let result = '';
    let cursorIdx = 0;
    this.bus.to<any>('GlobalStatus').next({
      installQuery: {
        running: true,
        queries: queryNames,
        progress: result,
      },
    });

    const distributedQueryNames = queryNames.filter((queryName) => this.isDistributedQuery(queryName));
    const singleQueryNames = queryNames.filter((queryName) => !this.isDistributedQuery(queryName));

    this.cmptService.installQueryByGSQLCmd(this.graphName, distributedQueryNames, singleQueryNames)
      .pipe(
        takeUntil(this.ngUnsubscribe),
        finalize(() => {
          this.bus.to<GlobalStatus>('GlobalStatus').next({
            installQuery: {
              running: false,
              queries: [],
              progress: '',
            },
          });
        })
      )
      .subscribe((res) => {
        const parsedRes = parseRes(res);
        ({ cursorIdx, result } = handleGSQLReslt(cursorIdx, result, parsedRes));
        this.bus.to<any>('GlobalStatus').next({
          installQuery: {
            running: true,
            queries: queryNames,
            progress: result,
          },
        });
      }, (err) => {
        this.syncQueryList(undefined, true);
        this.snackBar.open(
          `Failed to install ${queryNames.length > 1 ? 'queries' : 'query'}`,
          'DISMISS',
          { duration: 3000 }
        );
        this.showDialogOrLog(err);
      }, () => {
        if (!result.includes('installation failed')) {
          this.snackBar.open(
            queryNames.length > 1
              ? `These queries "${HelperFunctions.joinStrAsPara(
                queryNames
              )}" are installed`
              : `The query "${queryNames[0]}" is installed`,
            'DISMISS',
            { duration: 3000 }
          );
          this.syncQueryList(undefined, true, this.afterInstallCallback.bind(this));
          this.logContent.next('Queries installed successfully');
        } else {
          this.snackBar.open(
            `Failed to install ${queryNames.length > 1 ? 'queries' : 'query'}`,
            'DISMISS',
            { duration: 3000 }
          );
          this.logContent.next(result);
        }
      });
  }

  private afterInstallCallback() {
    const queryMeta = this.logicService.queriesMeta.get(this.queryName);
    this.updateQueryContentAtFrontEndSide(
      this.queryName,
      queryMeta.originalCode
    );
  }

  /**
   * Install query.
   *
   * @private
   * @param {string[]} queryNames
   * @memberof QueryEditorComponent
   */
  private installQueryByEndpoint(queryNames: string[]) {
    this.bus.to<GlobalStatus>('GlobalStatus').next({
      installQuery: {
        running: true,
        queries: queryNames,
      },
    });

    this.cmptService
      .installQuery(this.graphName, queryNames)
      .pipe(
        takeUntil(this.ngUnsubscribe),
        finalize(() => {
          this.bus.to<GlobalStatus>('GlobalStatus').next({
            installQuery: {
              running: false,
              queries: [],
            },
          });
        })
      )
      .subscribe(
        (res) => {
          if (res.success.length === 0 && res.failedToDeleteDraft.length > 0) {
            // It fails to delete all query drafts
            this.draftsTobeDeleted = [...res.failedToDeleteDraft].sort(
              (q1, q2) => (q1 > q2 ? 1 : -1)
            );

            this.snackBar.open(
              res.failedToDeleteDraft.length > 1
                ? `Failed to delete drafts for the queries "${HelperFunctions.joinStrAsPara(
                  res.failedToDeleteDraft
                )}"`
                : `Failed to delete draft for the query "${res.failedToDeleteDraft[0]}"`,
              'DISMISS',
              { duration: 3000 }
            );
            this.logContent.next('Failed to delete query drafts');

            this.dialog.open(this.deleteDraftWindow, this.dialogConfig);
          } else if (
            res.success.length > 0 &&
            res.failedToDeleteDraft.length > 0
          ) {
            // Some query drafts have been deleted successfully and some failed.
            this.draftsTobeDeleted = [...res.failedToDeleteDraft].sort(
              (q1, q2) => (q1 > q2 ? 1 : -1)
            );

            this.snackBar.open(
              res.success.length > 1
                ? `These queries "${HelperFunctions.joinStrAsPara(
                  res.success
                )}" are installed`
                : `The query "${res.success[0]}" is installed`,
              'DISMISS',
              { duration: 3000 }
            );
            this.logContent.next(
              'Queries installed successfully, but some query drafts are failed to be deleted'
            );

            this.dialog.open(this.deleteDraftWindow, this.dialogConfig);
          } else {
            // All query drafts have been deleted successfully.
            this.snackBar.open(
              res.success.length > 1
                ? `These queries "${HelperFunctions.joinStrAsPara(
                  res.success
                )}" are installed`
                : `The query "${res.success[0]}" is installed`,
              'DISMISS',
              { duration: 3000 }
            );
            this.logContent.next('Queries installed successfully');
          }
          this.syncQueryList(undefined, true, this.afterInstallCallback.bind(this));
        },
        (err) => {
          this.syncQueryList(undefined, true);
          this.showDialogOrLog(err);
        },
        () => {
          this.bus.to<GlobalStatus>('GlobalStatus').next({
            installQuery: {
              running: false,
              queries: [],
            },
          });
        }
      );
  }

  /**
   * Run query in compiled mode or interpreted mode.
   *
   * @private
   * @memberof QueryEditorComponent
   */
  private handleRun() {
    const queryMeta = this.logicService.queriesMeta.get(this.queryName);
    if (!queryMeta) {
      return;
    }

    // If query is not saved, then cannot run query
    if (this.logicService.getUnsavedQueries().includes(this.queryName)) {
      this.handleError(`Please save query "${this.queryName}" first.`);
      return;
    }

    const interpretedMode = this.shouldRunInInterpretedMode(queryMeta);
    if (interpretedMode) {
      let queryCode =
        this.logicService.getFrontEndQueryCode(this.queryName) ||
        this.logicService.getServerQueryCode(this.queryName);
      // If run in interpreted mode, check code error first.
      this.setLoadingStatus(true);
      this.cmptService
        .checkCode(this.graphName, this.queryName, queryCode, this.query.syntax)
        .pipe(takeUntil(this.ngUnsubscribe))
        .subscribe(
          (errors) => {
            if (errors.errors.length > 0) {
              this.setLoadingStatus(false);
              this.handleError(
                'Please fix the errors before running query.',
                'Resolve errors first'
              );
              return;
            } else {
              // Replace the query code based on interpreted query syntax.
              queryCode = this.convertQueryContentToInterpret(
                queryCode,
                this.query.syntax
              );

              this.cmptService
                .getParamsForInterpretedQuery(queryCode, this.graphName)
                .pipe(takeUntil(this.ngUnsubscribe))
                .subscribe(
                  (res) => {
                    queryMeta.params = res?.params?.map((param) => {
                      if (param.paramDefaultValue) {
                        param.paramDefaultValue =
                          QueryMetaLogic.handleParamDefaultValue(
                            param.paramType,
                            param.paramDefaultValue
                          );
                      }
                      return param;
                    }) || [];
                    this.interpretedQueryContent = queryCode;
                    this.runQuery(queryMeta, true);
                  },
                  (err) => {
                    this.showDialogOrLog(err);
                    this.setLoadingStatus(false);
                    this.reflow
                      ? this.changeSplitView(SplitViewStatus.Bottom)
                      : this.changeSplitView(SplitViewStatus.Split);
                  }
                );
            }
          },
          (err: HttpErrorResponse) => {
            console.log(err.error);
            this.handleError(err.error, err.statusText);
            this.setLoadingStatus(false);
          }
        );
    } else {
      this.setLoadingStatus(true);
      this.runQuery(queryMeta);
    }
  }

  private convertQueryContentToInterpret(
    queryContent: string,
    querySyntax: QuerySyntax = 'GSQL'
  ): string {
    // Remove comments and replace the query code based on interpret query syntax.
    const pattern = new RegExp(/\bCREATE\b([\s\S]*?)\bQUERY\b[\s\S]*?\(/i);
    return this.removeCommentsInCode(queryContent).replace(
      pattern,
      HelperFunctions.isGSQLSyntax(querySyntax)
        ? 'INTERPRET QUERY ('
        : 'INTERPRET OPENCYPHER QUERY ('
    );
  }

  private shouldRunInAsyncMode(interpreted?: boolean) {
    return !interpreted && this.supportAsyncQuery;
  }

  /**
   * Run a query with no params directly or ask user to input params.
   *
   * @private
   * @param {QueryMeta} queryMeta
   * @param {boolean} [interpreted]
   * @memberof QueryEditorComponent
   */
  private runQuery(queryMeta: QueryMeta, interpreted?: boolean) {
    this.isQueryAborting = false;
    this.enableQueryAbort = false;

    // If the query has 0 params, can directly run the query.
    if (queryMeta.params.length === 0) {
      this.changeSplitView(
        this.reflow ? SplitViewStatus.Bottom : SplitViewStatus.Split
      );

      const headers = this.logicService.getHeaders();
      if (
        typeof headers['GSQL-REPLICA'] !== 'undefined' &&
        !this.GSQLReplicaConfigurable()
      ) {
        delete headers['GSQL-REPLICA'];
      }

      if (this.shouldRunInAsyncMode(interpreted)) {
        this.runAsyncQuery(queryMeta, headers);
        return;
      }

      // Create params with graph name for interpret mode.
      const runQueryObservable = interpreted
        ? this.cmptService.runQueryInInterpretedMode(
          this.interpretedQueryContent,
          new HttpParams().set('graph', this.graphName),
          headers
        )
        : this.cmptService.runQuery(this.graphName, this.queryName, headers);

      this.graphVisualizer.clearDataWithHistory();
      runQueryObservable
        .pipe(
          takeUntil(this.ngUnsubscribe),
          finalize(() => this.setLoadingStatus(false))
        )
        .subscribe(
          (response) => {
            this.handleQueryResponse(response, interpreted);
          },
          (err) => {
            this.handleRunQueryErr(err);
          }
        );
      // Log run query event to analytics
      this.cmptService.logRunQuery(this.queryName);
    } else {
      // Update query params

      this.selectedQueryParameters = this.questionFormBuilder.buildQuestions(
        queryMeta,
        this.vertexTypes,
        this.schemaCreated
      );
      if (interpreted) {
        this.interpretedQueryMeta = queryMeta;
      }
      // otherwise open the parameter input panel
      this.setLoadingStatus(false);
      this.reflow ? this.openParamDialog() : this.openParamPanel();
    }
  }

  private runAsyncQuery(queryMeta: QueryMeta, headers: HeadersInit, params?: any) {
    headers['GSQL-ASYNC'] = 'true';

    this.setLoadingStatus(false);
    this.openQueryRunningDialog();
    this.graphVisualizer.clearDataWithHistory();

    this.cmptService.runQuery(this.graphName, this.queryName, headers, params)
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe((response) => {
        this.handleAsyncQueryResponse(response);

        if (params) {
          this.graphVisualizer.highlight(
            this.questionFormBuilder.getVertexParamInputs(
              queryMeta,
              this.dynamicForm.form.value
            )
          );
        }

        if (this.reflow) {
          this.closeParamDialog();
        }
      }, (err) => {
        this.handleRunQueryErr(err);
        this.closeQueryRunningDialog();

        if (this.reflow) {
          this.closeParamDialog();
        }
      });
  }

  private handleRunQueryErr(err: HttpErrorResponse | string) {
    this.showDialogOrLog(err);
    this.handleQueryResponse(undefined, undefined);
  }

  /**
   * Run a query with input params.
   *
   * @memberof QueryEditorComponent
   */
  runQueryWithParams() {
    this.isQueryAborting = false;
    this.enableQueryAbort = false;

    const queryMeta = this.logicService.queriesMeta.get(this.queryName);
    if (!queryMeta) {
      return;
    }

    // If query is not saved, then cannot run query in either mode.
    if (this.logicService.getUnsavedQueries().includes(this.queryName)) {
      this.reflow ? this.closeParamDialog() : this.closeParamPanel();
      this.handleError(`Please save query "${this.queryName}" first.`);
      return;
    }
    this.changeSplitView(
      this.reflow ? SplitViewStatus.Bottom : SplitViewStatus.Split
    );
    this.setLoadingStatus(true);
    const headers = this.logicService.getHeaders();
    if (
      typeof headers['GSQL-REPLICA'] !== 'undefined' &&
      !this.GSQLReplicaConfigurable()
    ) {
      delete headers['GSQL-REPLICA'];
    }
    const interpretedMode = this.shouldRunInInterpretedMode(queryMeta);

    if (this.shouldRunInAsyncMode(interpretedMode)) {
      this.runAsyncQuery(queryMeta, headers, this.questionFormBuilder.buildParamsForInstalledMode(
        queryMeta.params,
        this.dynamicForm.form.value,
        this.selectedQueryParameters
      ));
      return;
    }

    const runQueryObservable = interpretedMode
      ? this.cmptService.runQueryInInterpretedMode(
        this.interpretedQueryContent,
        this.questionFormBuilder
          .buildParamsForInterpretedMode(
            this.interpretedQueryMeta.params,
            this.dynamicForm.form.value,
            false
          )
          .append('graph', this.graphName),
        headers
      )
      : this.cmptService.runQuery(
        this.graphName,
        queryMeta.queryName,
        headers,
        this.questionFormBuilder.buildParamsForInstalledMode(
          queryMeta.params,
          this.dynamicForm.form.value,
          this.selectedQueryParameters
        )
      );

    runQueryObservable
      .pipe(
        takeUntil(this.ngUnsubscribe),
        finalize(() => this.setLoadingStatus(false))
      )
      .subscribe(
        (response) => {
          this.handleQueryResponse(response, interpretedMode);
          this.graphVisualizer.highlight(
            this.questionFormBuilder.getVertexParamInputs(
              interpretedMode ? this.interpretedQueryMeta : queryMeta,
              this.dynamicForm.form.value
            )
          );
          if (this.reflow) {
            this.closeParamDialog();
          }
        },
        (err) => {
          this.showDialogOrLog(err);
          this.handleQueryResponse(undefined, undefined);
          if (this.reflow) {
            this.closeParamDialog();
          }
        }
      );
  }

  /**
   * Get hint text based on user role privilege.
   *
   * @private
   * @returns {string}
   * @memberof QueryEditorComponent
   */
  private getHint(): string {
    let warningMsg = `// Choose a query from the list on the left.`;
    if (this.queryName && !this.canWriteQuery) {
      warningMsg += `\n// WARNING: You don't have privilege to add, edit or drop a query.`;
    }
    return warningMsg;
  }

  private get canWriteQuery(): boolean {
    if (compareWithInstanceVersion('4.1.0', '>=')) {
      return this.authService.hasPrivilegesToQuery(
        this.graphName,
        this.queryName,
        [GSQLPrivilege.UpdateQuery, GSQLPrivilege.Owner]
      );
    }
    return this.authService.hasPrivilege(GSQLPrivilege.WriteQuery);
  }

  private get canReadData(): boolean {
    return this.authService.hasPrivilege(GSQLPrivilege.ReadData);
  }

  /**
   * Call server to check code on code change with throttle.
   *
   * @private
   * @memberof QueryEditorComponent
   */
  private checkCodeOnChange() {
    this.currentCode
      .pipe(takeUntil(this.ngUnsubscribe), debounceTime(500))
      .subscribe((code) => {
        this.cmptService
          .checkCode(this.graphName, this.queryName, code, this.query.syntax)
          .pipe(takeUntil(this.ngUnsubscribe))
          .subscribe(
            (errors) => {
              this.queryEditor.performLint();
              if (errors) {
                // Show error in log
                if (errors.errors.length + errors.warnings.length > 0) {
                  let errorMessage = '';
                  errors.errors.forEach((error) => {
                    errorMessage += `(${error.startLine + 1}, ${error.startColumn
                      }) Error: ${error.message}\n`;
                  });
                  errors.warnings.forEach((warning) => {
                    errorMessage += `(${warning.startLine + 1}, ${warning.startColumn
                      }) Warning: ${warning.message}\n`;
                  });
                  this.logContent.next(errorMessage);
                } else {
                  this.logContent.next('No log to show');
                }
              }
            },
            (err: HttpErrorResponse) =>
              this.handleError(err.error, err.statusText)
          );
      });
  }

  /**
   * Handle the query response, and direct to different panels based on result case:
   * - with graph structure: view graph visualization
   * - no graph structure: view json
   *
   * @private
   * @param {*} response
   * @param {boolean} interpretedMode
   *
   * @memberof QueryEditorComponent
   */
  private handleQueryResponse(response: any, interpretedMode: boolean) {
    // Remove previous results.
    this.jsonContent = '';
    this.tableContent = null;
    this.profileData = undefined;

    if (!(response || interpretedMode)) {
      return;
    }

    // Add warning message to tell user if the query is running in interpreted mode.
    if (interpretedMode) {
      this.snackBar.open(
        `The query is running in interpreted mode. You can install it to get better performance.`,
        'DISMISS',
        { duration: 10000 }
      );
    }

    this.handleQueryResult(response);
  }

  private handleQueryResult(result: any) {
    // Remove previous results.
    this.jsonContent = '';
    this.tableContent = null;

    // Add new results.
    this.updateJsonContent(result);
    this.tableContent = result;
    this.graphVisualizer.addData(result, 'gquery', false);
    this.logContent.next('Query run successfully');

    const [vertexSize, edgeSize] = this.graphVisualizer.getGraphSize();
    if (vertexSize === 0 && edgeSize === 0) {
      // If the graph is empty, show the result in json format.
      this.changeQueryResultView(QueryResultStatus.Json);
    } else {
      // Otherwise show the graph result.
      this.changeQueryResultView(QueryResultStatus.Graph);
    }
  }

  // get real restpp respone from all nodes responses
  // allRestppRes is a array of responses from all restpp nodes
  // every restpp node also returns a array,
  // but only the node that acutally handle the request will have item in it
  private getRestppResponse(allRestppRes) {
    if (Array.isArray(allRestppRes) && allRestppRes.length) {
      allRestppRes.sort((res1, res2) => {
        if (res1.error) {
          return 1;
        }
        if (res2.error) {
          return -1;
        }
        return res2.results.length - res1.results.length;
      });
      return allRestppRes[0].results;
    }

    return null;
  }

  private abortQuery() {
    this.isQueryAborting = true;
    this.cmptService.abortQuery(this.graphName, this.abortQueryReqId)
      .pipe(takeUntil(this.ngUnsubscribe), finalize(() => this.isQueryAborting = false))
      .subscribe((allRestppRes) => {
        const restppRes = this.getRestppResponse(allRestppRes);
        if (restppRes) {
          const abortRes = restppRes[0] as QueryAbortResult;
          if (abortRes.aborted_queries.length) {
            this.closeQueryRunningDialog();
            this.logContent.next('Query aborted successfully');
            this.changeQueryResultView(QueryResultStatus.Log);
            clearTimeout(this.pollingStatusTimer);
            return;
          }
        }

        this.handleError('Failed to abort query');
      }, (err) => {
        this.handleError(err);
      });
  }

  private handleAsyncQueryResponse(response: any) {
    const requestId = response.request_id || response.requestid;
    this.abortQueryReqId = requestId;

    const getResult = () => {
      this.cmptService.getQueryResult(requestId, this.graphName)
        .pipe(takeUntil(this.ngUnsubscribe), finalize(() => this.closeQueryRunningDialog()))
        .subscribe((allRestppRes) => {
          const restppRes = this.getRestppResponse(allRestppRes);
          const profileData = allRestppRes[0].profile as Profile;
          if (
            this.query.installed &&
            this.query.installMode !== 'UDF' &&
            this.supportQueryProfile &&
            profileData
          ) {
            profileData.queryName = this.queryName;
            this.profileData = profileData;
          } else {
            this.profileData = undefined;
          }
          if (restppRes) {
            this.handleQueryResult(restppRes);
            return;
          }

          this.handleRunQueryErr('Failed to get query result');
        }, (err) => this.handleRunQueryErr(err));
    };

    const pollingStatus = () => {
      this.cmptService.getQueryStatus(requestId, this.graphName)
        .subscribe((allRestppRes) => {
          const restppRes = this.getRestppResponse(allRestppRes);
          if (restppRes) {
            const res = restppRes[0] as QueryRunStatus;

            if (res.status === 'running') {
              this.enableQueryAbort = true;
            }

            if (res.status === 'success') {
              getResult();
              return;
            }

            if (res.status === 'timeout') {
              this.closeQueryRunningDialog();
              this.handleRunQueryErr('Query timeout.');
              return;
            }

            if (res.status === 'aborted') {
              this.closeQueryRunningDialog();
              this.logContent.next('Query aborted successfully');
              return;
            }
          }

          this.pollingStatusTimer = setTimeout(() => {
            pollingStatus();
          }, 1000);
        }, (err) => {
          console.error('polling query status error:', err);
        });
    };

    pollingStatus();
  }

  /**
   * Handler for delete query action.
   * Ask the the user for confirmation.
   * If agreed, call removal.
   *
   * @private
   * @memberof QueryEditorComponent
   */
  private handleDeleteQuery() {
    const data: DialogData = {
      title: 'Warning',
      messages: [
        'Deletion is irreversible.',
        'Are you sure you want to delete the selected query and draft?',
      ],
      actions: [
        { label: 'CANCEL', value: 0 },
        { label: 'CONTINUE', value: 1, color: 'warn' },
      ],
    };
    if (this.queryName !== '') {
      const query = this.logicService.queriesMeta.get(this.queryName);

      if (query.callerQueries && query.callerQueries.length !== 0) {
        const sampleCallerQueryName = HelperFunctions.limitStringDisplayLength(
          query.callerQueries[0],
          10,
          10
        );
        data.messages.push(
          `If deleted, all caller queries of query ${this.queryName} ` +
          `(e.g., ${sampleCallerQueryName}) will be disabled.`
        );
      }
    }

    this.dialog
      .open(DialogComponent, merge({ data: data }, this.dialogConfig))
      .afterClosed()
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe((value) => {
        if (value === 1) {
          this.toggleLoadingQueryContent();

          if (!this.reflow) {
            this.closeParamPanel();
          }

          this.cmptService
            .deleteQuery(this.graphName, this.queryName)
            .pipe(
              takeUntil(this.ngUnsubscribe),
              finalize(() => this.toggleLoadingQueryContent())
            )
            .subscribe(
              () => {
                this.snackBar.open(
                  `The query "${this.queryName}" is deleted`,
                  'DISMISS',
                  { duration: 3000 }
                );

                this.queryName = '';
                this.queryContent = this.getHint();
                this.queryEditor.updateOption('cursorHeight', 0);
                this.queryEditor.updateOption('readOnly', true);
                this.syncQueryList();
              },
              (err: string) => {
                this.handleError(err, 'Error');
              }
            );
        }
      });
  }

  /**
   * Show endpoint of installed queries.
   *
   * @private
   * @memberof QueryEditorComponent
   */
  private handleShowEndpoint() {
    const queryMeta = this.logicService.queriesMeta.get(this.queryName);
    this.queryEndpoint = `/restpp/query/<span>${this.graphName}</span>/<span>${this.queryName}</span>`;
    if (queryMeta.params.length !== 0) {
      const param = this.cmptService.buildParamsTemplate(queryMeta);
      this.queryEndpoint += `?${param}`;
    }
    this.dialog.open(this.showQueryEndpointWindow, this.dialogConfig);
  }

  /**
   * Download the queries as .gsql or .zip
   *
   * @public
   * @memberof QueryEditorComponent
   */
  handleDownloadGsql(all?: boolean) {
    if (all) {
      const zip = new JSZip();
      this.logicService.queriesMeta.forEach((qm) => {
        const fileName = `${qm.queryName}.gsql`;
        const value = qm.draftCode === '' ? qm.originalCode : qm.draftCode;
        const file = new File([value], fileName);
        zip.file(fileName, file);
      });
      zip.generateAsync({ type: 'blob' }).then((content: any) => {
        saveAs(content, `${this.schema.name}_Queries.zip`);
      });
    } else {
      const query = this.logicService.queriesMeta.get(this.queryName);
      const value =
        query.draftCode === '' ? query.originalCode : query.draftCode;
      const fileName = `${this.queryName}.gsql`;
      const file = new File([value], fileName);
      saveAs(file, fileName);
    }
  }

  /**
   * Get current engine side queries meta information.
   *
   * @memberof QueryEditorComponent
   */
  syncQueryList(
    newQueryName?: string,
    closeParamPanel?: boolean,
    callback?: () => void
  ) {
    this.toggleLoadingQueryList();
    this.cmptService
      .getQueryMetaList(this.graphName)
      .pipe(
        takeUntil(this.ngUnsubscribe),
        finalize(() => this.toggleLoadingQueryList())
      )
      .subscribe(
        (gsqlQueriesMeta) => {
          // Filter by graphName to make sure queries are for current graph,
          // and sort in ascending order by their names.
          gsqlQueriesMeta = gsqlQueriesMeta.sort((meta1, meta2) =>
            meta1.name > meta2.name ? 1 : -1
          );

          if (compareWithInstanceVersion('4.1.0', '>=')) {
            gsqlQueriesMeta = gsqlQueriesMeta.filter((q) =>
              this.authService.hasPrivilegesToQuery(this.graphName, q.name, [
                GSQLPrivilege.Owner,
                GSQLPrivilege.ReadQuery,
                GSQLPrivilege.UpdateQuery,
                GSQLPrivilege.DropQuery,
                GSQLPrivilege.InstallQuery,
                GSQLPrivilege.ExecuteQuery,
              ])
            );
          }

          this.logicService.updateQueriesMeta(
            this.graphName,
            gsqlQueriesMeta
          );
          this.queryList = [];
          this.logicService.queriesMeta.forEach((value, key) =>
            this.queryList.push(value)
          );
          this.filterQueryList();
          this.unsavedQueriesSubject.next(
            this.logicService.getUnsavedQueries()
          );

          // Update query toolbar status
          const queryMeta = this.logicService.queriesMeta.get(this.queryName);

          callback?.();

          this.updateQueryToolbarAndButton(queryMeta);

          if (newQueryName) {
            this.showQuery(newQueryName);
          }

          if (closeParamPanel && !this.reflow) {
            this.closeParamPanel();
          }
        },
        () => {
          this.logger.warn('Error in retrieving query list.');
        }
      );
  }

  /**
   * Handler for delete draft action.
   * Ask the the user for confirmation.
   * If agreed, call removal.
   *
   * @private
   * @memberof QueryEditorComponent
   */
  private handleDeleteDraft() {
    const data: DialogData = {
      title: 'Warning',
      messages: [
        'Deletion is irreversible.',
        'Are you sure you want to discard the draft of selected query?',
      ],
      actions: [
        { label: 'CANCEL', value: 0 },
        { label: 'CONTINUE', value: 1, color: 'warn' },
      ],
    };

    this.dialog
      .open(DialogComponent, { data: data })
      .afterClosed()
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe((value) => {
        if (value === 1) {
          this.toggleLoadingQueryContent();

          if (!this.reflow) {
            this.closeParamPanel();
          }

          this.cmptService
            .deleteQueryDraft(this.graphName, this.queryName)
            .pipe(
              takeUntil(this.ngUnsubscribe),
              finalize(() => this.toggleLoadingQueryContent())
            )
            .subscribe(
              () => {
                this.snackBar.open(
                  `The draft of "${this.queryName}" is deleted`,
                  'DISMISS',
                  { duration: 3000 }
                );

                const queryMeta = this.logicService.queriesMeta.get(
                  this.queryName
                );

                // If the query has installed code, 'delete draft' is just to discard draft.
                // If the query is just created, 'delete draft' will delete the query and draft.
                if (queryMeta.originalCode) {
                  // Discard draft and show installed code
                  this.logicService.updateQueryContentAtFrontEndSide(
                    this.queryName,
                    queryMeta.originalCode
                  );
                  this.showQuery(this.queryName);
                } else {
                  // Delete query and reset editor status
                  this.queryName = '';
                  this.queryContent = this.getHint();
                  this.queryEditor.updateOption('cursorHeight', 0);
                  this.queryEditor.updateOption('readOnly', true);
                }

                this.syncQueryList();
              },
              (err: HttpErrorResponse | string) => {
                if (typeof err === 'string') {
                  this.handleError(err, 'Error');
                  return;
                }
                this.handleError(err.error, err.statusText);
              }
            );
        }
      });
  }

  private handleHelp() {
    window.open(
      'https://dev.tigergraph.com/forum/c/gsql/opencypher/',
      '_blank'
    );
  }

  /**
   * Show dialog or log depends on error code.
   *
   * @private
   * @param {HttpErrorResponse} err
   * @memberof QueryEditorComponent
   */
  private showDialogOrLog(err: HttpErrorResponse | string) {
    if (typeof err === 'string') {
      this.logContent.next(err);
      this.changeQueryResultView(QueryResultStatus.Log);
      this.changeSplitView(
        this.reflow ? SplitViewStatus.Bottom : SplitViewStatus.Split
      );
    } else if (err.status === 400 || err.status === 500) {
      this.logContent.next(err.error);
      this.changeQueryResultView(QueryResultStatus.Log);

      this.changeSplitView(
        this.reflow ? SplitViewStatus.Bottom : SplitViewStatus.Split
      );
    } else {
      this.handleError(err.error, err.statusText);
    }
  }

  /**
   * Update query content of front-end side.
   *
   * @private
   * @param {string} queryName
   * @param {string} queryContent
   * @memberof QueryEditorComponent
   */
  private updateQueryContentAtFrontEndSide(
    queryName: string,
    queryContent: string
  ) {
    this.logicService.updateQueryContentAtFrontEndSide(queryName, queryContent);
    this.unsavedQueriesSubject.next(this.logicService.getUnsavedQueries());
  }

  switchToAllQueriesSmallScreen() {
    this.changeSplitView(SplitViewStatus.TopLeft);
  }

  switchToDefaultViewSmallScreen() {
    this.changeSplitView(SplitViewStatus.TopRight);
  }

  /**
   * Change split view status, either top view full size, split view, or bottom view full size.
   *
   * @private
   * @param {SplitViewStatus} viewStatus
   * @memberof QueryEditorComponent
   */
  private changeSplitView(viewStatus: SplitViewStatus) {
    this.currentSplitViewStatus = viewStatus;

    switch (viewStatus) {
      case SplitViewStatus.TopLeft:
        this.showTopLeft = true;
        this.showTopRight = false;
        this.showBottom = false;

        (<IconButton>this.queryToolbarConfig.top[ToolbarButtons.Expand]).icon =
          'fullscreen';
        (<IconButton>(
          this.queryToolbarConfig.top[ToolbarButtons.Expand]
        )).tooltip = 'Expand';
        this.queryToolbarConfig.top[ToolbarButtons.Console].color = '';

        break;
      case SplitViewStatus.TopRight:
        this.showTopRight = true;
        this.showBottom = false;
        this.showTopLeft = false;

        (<IconButton>this.queryToolbarConfig.top[ToolbarButtons.Expand]).icon =
          'fullscreen_exit';
        (<IconButton>(
          this.queryToolbarConfig.top[ToolbarButtons.Expand]
        )).tooltip = 'Collapse';
        this.queryToolbarConfig.top[ToolbarButtons.Console].color = '';

        this.queryEditor.triggerLoad();
        break;
      case SplitViewStatus.Top:
        this.showTopLeft = true;
        this.showTopRight = true;
        this.showBottom = false;

        (<IconButton>this.queryToolbarConfig.top[ToolbarButtons.Expand]).icon =
          'fullscreen';
        (<IconButton>(
          this.queryToolbarConfig.top[ToolbarButtons.Expand]
        )).tooltip = 'Expand';
        this.queryToolbarConfig.top[ToolbarButtons.Console].color = '';
        this.queryEditor.triggerLoad();
        break;
      case SplitViewStatus.Split:
        this.showTopRight = this.showBottom = this.showTopLeft = true;

        (<IconButton>this.queryToolbarConfig.top[ToolbarButtons.Expand]).icon =
          (<IconButton>(
            this.resultToolbarConfig.left[ToolbarButtons.Expand]
          )).icon = 'fullscreen';
        (<IconButton>(
          this.queryToolbarConfig.top[ToolbarButtons.Expand]
        )).tooltip = (<IconButton>(
          this.resultToolbarConfig.left[ToolbarButtons.Expand]
        )).tooltip = 'Expand';
        this.queryToolbarConfig.top[ToolbarButtons.Console].color = 'accent';
        break;
      case SplitViewStatus.Bottom:
        this.showBottom = true;
        this.showTopRight = false;
        this.showTopLeft = false;

        (<IconButton>(
          this.resultToolbarConfig.left[ToolbarButtons.Expand]
        )).icon = 'fullscreen_exit';
        (<IconButton>(
          this.resultToolbarConfig.left[ToolbarButtons.Expand]
        )).tooltip = 'Collapse';
        this.queryToolbarConfig.top[ToolbarButtons.Console].color = 'accent';

        if (this.inJsonView() && this.jsonViewer) {
          this.jsonViewer.triggerLoad();
        }
        break;
    }
  }

  /**
   * Switch between different query result view.
   *
   * @private
   * @param {QueryResultStatus} queryResultStatus
   * @memberof QueryEditorComponent
   */
  private changeQueryResultView(queryResultStatus: QueryResultStatus) {
    this.currentQueryResult = queryResultStatus;
    this.cacheService.setItem(
      `${this.cachePrefix}ResultViewTab_${this.schema.name}`,
      queryResultStatus
    );

    this.setButtonActivated('schema', false);
    this.setButtonActivated('graph', false);
    this.setButtonActivated('json', false);
    this.setButtonActivated('table', false);
    this.setButtonActivated('log', false);
    this.setButtonActivated('profile', false);

    switch (queryResultStatus) {
      case QueryResultStatus.Schema:
        this.setButtonActivated('schema', true);
        break;
      case QueryResultStatus.Graph:
        this.setButtonActivated('graph', true);
        break;
      case QueryResultStatus.Json:
        this.setButtonActivated('json', true);
        break;
      case QueryResultStatus.Table:
        this.setButtonActivated('table', true);
        break;
      case QueryResultStatus.Log:
        this.setButtonActivated('log', true);
        break;
      case QueryResultStatus.Profile:
        this.setButtonActivated('profile', true);
        break;
    }
  }

  /**
   * Activate or deactivate one button.
   *
   * @private
   * @param {string} key
   * @param {boolean} activated
   * @memberof QueryEditorComponent
   */
  private setButtonActivated(key: string, activated: boolean) {
    const button = <ButtonBase>(
      this.resultToolbarConfig.left.find((b) => b.key === key)
    );
    button.color = activated ? 'accent' : '';
    if (button.key === 'profile') {
      (<IconButton>(
        this.resultToolbarConfig.left[1 + QueryResultStatus.Profile]
      )).icon = activated ? 'database_search_active' : 'database_search';
    }
  }

  /**
   * Get graph schema and style.
   *
   * @private
   * @memberof QueryEditorComponent
   */
  private getSchemaAndStyle() {
    this.setLoadingStatus(true);
    this.cmptService
      .getSchema(this.graphName)
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe(
        (json) => {
          try {
            if (json) {
              this.schemaCreated = true;
              this.schema.loadFromGSQLJson(json);

              this.vertexTypes = this.schema.getAllVertexTypes();
              // Get graph schema style.
              this.getSchemaStyle();

              // Only sync query list once when schema changed.
              this.syncQueryList();
            } else {
              this.schemaCreated = false;
              // TODO: hint the user no schema is installed
              this.setLoadingStatus(false);
            }
            // Extract key words from graph for auto suggestion.
            this.extractGraphInfo(this.schema);
          } catch (error) {
            this.schemaCreated = false;
            this.handleError(error.message);
            return;
          }
        },
        (err) => {
          this.schemaCreated = false;
          this.handleError(err.error, err.statusText);
        }
      );
  }

  /**
   * Extract key words from graph for auto suggestion,
   * including graph name, edge name, vertex name, attribute name, etc.
   *
   * @private
   * @memberof QueryEditorComponent
   */
  private extractGraphInfo(graph: Graph) {
    const graphInfo = [];
    // Add graph name.
    graphInfo.push(graph.name);
    // Add the name of vertex, edge and attributes.
    const vertexAndEdgeTypes = [].concat(graph.vertexTypes, graph.edgeTypes);
    for (const item of vertexAndEdgeTypes) {
      graphInfo.push(item.name);
      if (item.primaryId?.primaryIdAsAttribute) {
        graphInfo.push(item.primaryId.name);
      }
      for (const attr of item.attributes) {
        graphInfo.push(attr.name);
      }
    }

    setGraphInfo(graphInfo);
  }

  /**
   * Extract words from query content for auto suggestion.
   *
   * @private
   * @memberof QueryEditorComponent
   */
  private extractQueryWords(queryContent: string) {
    const queryCode = this.removeCommentsInCode(queryContent);
    // Remove string in quotes.
    const cleanQueryCode = queryCode
      .replace(new RegExp(/\'(.|\n)*?\'|\"(.|\n)*?\"/g), '')
      .trim();

    // Split by any non-word character except '@'.
    let wordsInQuery = cleanQueryCode.split(/[^@\w]/);
    wordsInQuery = wordsInQuery.filter((word) => word !== '');

    setWordsInQuery(wordsInQuery);
  }

  /**
   * Get graph schema style.
   *
   * @private
   * @memberof QueryEditorComponent
   */
  private getSchemaStyle() {
    this.cmptService
      .getSchemaStyle(this.graphName)
      .pipe(
        tap((style) => {
          if (style) {
            const graphStyle = new GraphStyle().loadFromDBJson(style);
            this.schema.applyGraphStyle(graphStyle);
          }
          this.graphVisualizer.switchGraph(this.schema);

          this.cacheService.setItem(
            `${this.cachePrefix}Schema_${this.schema.name}`,
            this.schema.dumpToGSQLJson()
          );

          // Apply the previous view tab if saved.
          const prevStatus = this.cacheService.getItem(
            `${this.cachePrefix}ResultViewTab_${this.schema.name}`
          );
          if (prevStatus && this.canReadData) {
            this.changeQueryResultView(prevStatus);
          } else if (this.currentQueryResult !== QueryResultStatus.Schema) {
            this.changeQueryResultView(QueryResultStatus.Schema);
          }

          this.schemaContainer.renderChart(
            this.schema,
            new Color(),
            style &&
            style.vertexStyles &&
            this.schema.vertexTypes.filter((vt) =>
              Object.keys(style.vertexStyles).includes(vt.name)
            ).length > 0
          );
        }),
        switchMap(() => this.graphVisualizer.isReady),
        takeUntil(this.ngUnsubscribe)
      )
      .subscribe(
        () => this.setLoadingStatus(false),
        (err) => this.handleError(err.error, err.statusText)
      );
  }

  /**
   * Get one query's status given the query meta
   * - One of: 'installed', 'installing', 'draft', 'disabled', 'unknown'
   *
   * @param {QueryMeta} queryMeta
   * @returns {string}
   * @memberof QueryEditorComponent
   */
  queryStatus(queryMeta: QueryMeta): string {
    switch (true) {
      case queryMeta.installing:
        return QueryStatus.Installing;
      case queryMeta.installed &&
        queryMeta.draftCode.trim() === queryMeta.originalCode:
        return QueryStatus.Installed;
      case queryMeta.draftCode !== '' ||
        (queryMeta.draftCode === '' && queryMeta.originalCode === ''):
        return QueryStatus.Draft;
      case queryMeta.installed && !queryMeta.enabled:
        return QueryStatus.Disabled;
      case queryMeta.installed:
        return QueryStatus.Installed;
      default:
        return QueryStatus.Unknown;
    }
  }

  private updateQueryToolbarAndButton(queryMeta: QueryMeta) {
    this.updateRunQueryButtonIconAndTooltip(queryMeta);
    this.updateQueryToolbarStatus(queryMeta);
  }

  /**
   * Set loading status.
   *
   * @private
   * @param {boolean} status
   * @memberof QueryEditorComponent
   */
  private setLoadingStatus(status: boolean) {
    this.isLoading = status;
    this.loading.next(this.isLoading);
  }

  /**
   * Toggle loading query content animation.
   *
   * @private
   * @memberof QueryEditorComponent
   */
  private toggleLoadingQueryContent() {
    this.isLoadingQueryContent = !this.isLoadingQueryContent;
    this.loadingQueryContent.next(this.isLoadingQueryContent);
  }

  /**
   * Toggle loading query content animation.
   *
   * @private
   * @memberof QueryEditorComponent
   */
  private toggleLoadingQueryList() {
    this.isLoadingQueryList = !this.isLoadingQueryList;
    this.loadingQueryList.next(this.isLoadingQueryList);
  }

  /**
   * Update the run query button icon an tooltip based on its status.
   *
   * @private
   * @param {QueryMeta} queryMeta
   * @returns
   * @memberof QueryEditorComponent
   */
  private updateRunQueryButtonIconAndTooltip(queryMeta: QueryMeta) {
    if (!queryMeta) {
      return;
    }
    if (!this.shouldRunInInterpretedMode(queryMeta)) {
      (<IconButton>this.queryToolbarConfig.top[ToolbarButtons.Run]).icon =
        'play_circle_filled';
      (<IconButton>this.queryToolbarConfig.top[ToolbarButtons.Run]).tooltip =
        'Run query';
    } else {
      (<IconButton>this.queryToolbarConfig.top[ToolbarButtons.Run]).icon =
        'play_arrow';
      (<IconButton>this.queryToolbarConfig.top[ToolbarButtons.Run]).tooltip =
        'Run query in interpreted mode';
    }

    if (this.supportOpenCypher) {
      (<IconButton>this.queryToolbarConfig.top[ToolbarButtons.Help]).show =
        queryMeta.syntax === 'CYPHER';
    }
  }

  /**
   * Update the query toolbar disable status
   * - If no query specified, disable all buttons
   * - If readonly version, only enable "run query" and "show endpoint" buttons
   * - If query is not saved, enable "save" button
   * - If query is not installed, enable "install" button
   * - If query is installed, enable "run query" and "show endpoint" buttons
   * - Finally, change query status based on user roles.
   *
   * @private
   * @param {QueryMeta} queryMeta
   * @memberof QueryEditorComponent
   */
  private updateQueryToolbarDisableStatus(queryMeta: QueryMeta) {
    // Disable all operational buttons
    this.queryToolbarConfig.top[ToolbarButtons.Save].disabled = true;
    this.queryToolbarConfig.top[ToolbarButtons.SaveAs].disabled = true;
    this.queryToolbarConfig.top[ToolbarButtons.Publish].disabled = true;
    this.queryToolbarConfig.top[ToolbarButtons.Run].disabled = true;
    this.queryToolbarConfig.top[ToolbarButtons.Run].child.disabled = true;
    this.queryToolbarConfig.top[ToolbarButtons.DeleteQuery].disabled = true;
    this.queryToolbarConfig.top[ToolbarButtons.ShowEndpoint].disabled = true;
    this.queryToolbarConfig.top[ToolbarButtons.DownloadGsql].disabled = true;
    this.queryToolbarConfig.top[ToolbarButtons.DeleteDraft].disabled = true;

    if (!queryMeta) {
      return;
    }

    // Enable 'save as' if currently showing a query
    this.queryToolbarConfig.top[ToolbarButtons.SaveAs].disabled = false;
    // If not saved, enable save button only
    if (this.logicService.getUnsavedQueries().includes(queryMeta.queryName)) {
      this.queryToolbarConfig.top[ToolbarButtons.Save].disabled = false;
      return;
    }

    // If saved, enable run and download query button.
    this.queryToolbarConfig.top[ToolbarButtons.DownloadGsql].disabled = false;
    // Only enable toolbar config if run query, and if query panel already opened, don't open it again
    this.queryToolbarConfig.top[ToolbarButtons.Run].disabled =
      this.queryParamPanelOpened ||
      this.runConfigPanelOpened ||
      this.paramDialogRef !== undefined ||
      this.runConfigDialogRef !== undefined ||
      (!queryMeta.enabled && queryMeta.installed);
    this.queryToolbarConfig.top[ToolbarButtons.Run].child.disabled =
      this.queryParamPanelOpened || this.paramDialogRef !== undefined;

    // If not installed, enable "install query".
    if (this.queryStatus(queryMeta) !== QueryStatus.Installed) {
      this.queryToolbarConfig.top[ToolbarButtons.Publish].disabled = false;
    }

    // If the query is being installed, disable "install query".
    if (this.queryStatus(queryMeta) === QueryStatus.Installing) {
      this.queryToolbarConfig.top[ToolbarButtons.Publish].disabled = true;
    }

    // If query is installed, enable "show query endpoint" and delete query button.
    if (queryMeta.installed) {
      this.queryToolbarConfig.top[ToolbarButtons.DeleteQuery].disabled = false;
      this.queryToolbarConfig.top[ToolbarButtons.ShowEndpoint].disabled = false;
    }

    // If query is added from GSQL side, and not installed yet, enable delete.
    // If gsql support draft query, enable delete query
    if (
      this.queryStatus(queryMeta) === QueryStatus.Unknown ||
      isSupportGSQLDraft()
    ) {
      this.queryToolbarConfig.top[ToolbarButtons.DeleteQuery].disabled = false;
    }

    // Enable `Discard draft` button
    if (
      this.queryStatus(queryMeta) === QueryStatus.Draft ||
      queryMeta.draftCode !== ''
    ) {
      this.queryToolbarConfig.top[ToolbarButtons.DeleteDraft].disabled = false;
    }
  }

  /**
   * Disable corresponding toolbar buttons according to user's privileges.
   *
   * @private
   * @memberof QueryEditorComponent
   */
  private updateQueryToolbarDisableStatusBasedOnUserPrivilege() {
    const queryMeta = this.logicService.queriesMeta.get(this.queryName);

    // User without WRITE_QUERY privilege cannot edit, publish or save query.
    if (!this.canWriteQuery) {
      this.queryToolbarConfig.top[ToolbarButtons.Save].disabled = true;
      this.queryToolbarConfig.top[ToolbarButtons.SaveAs].disabled = true;
      this.queryToolbarConfig.top[ToolbarButtons.Publish].disabled = true;
      this.queryToolbarConfig.top[ToolbarButtons.DeleteQuery].disabled = true;
      this.queryToolbarConfig.top[ToolbarButtons.DeleteDraft].disabled = true;
    }

    // User who cannot run read-only query or DML query.
    if (queryMeta) {
      if (
        this.queryStatus(queryMeta) !== QueryStatus.Installed &&
        !this.canWriteQuery
      ) {
        this.queryToolbarConfig.top[ToolbarButtons.Run].disabled = true;
        this.queryToolbarConfig.top[ToolbarButtons.Run].child.disabled = true;
      }
    }
  }

  private updateQueryToolbarDisableStatusFor410Version(queryMeta?: QueryMeta) {
    if (!queryMeta) {
      return;
    }
    const canSave = this.authService.hasPrivilegesToQuery(
      this.graphName,
      queryMeta.queryName,
      [GSQLPrivilege.Owner, GSQLPrivilege.UpdateQuery]
    );
    if (!canSave) {
      this.queryToolbarConfig.top[ToolbarButtons.Save].disabled = true;
    }
    if (!this.canAddQuery()) {
      this.queryToolbarConfig.top[ToolbarButtons.SaveAs].disabled = true;
    }

    const canInstall = this.authService.hasPrivilegesToQuery(
      this.graphName,
      queryMeta.queryName,
      [GSQLPrivilege.Owner, GSQLPrivilege.InstallQuery]
    );
    if (!canInstall) {
      this.queryToolbarConfig.top[ToolbarButtons.Publish].disabled = true;
    }

    const canDelete = this.authService.hasPrivilegesToQuery(
      this.graphName,
      queryMeta.queryName,
      [GSQLPrivilege.Owner, GSQLPrivilege.DropQuery]
    );
    if (!canDelete) {
      this.queryToolbarConfig.top[ToolbarButtons.DeleteQuery].disabled = true;
      this.queryToolbarConfig.top[ToolbarButtons.DeleteDraft].disabled = true;
    }

    const canExecute = this.authService.hasPrivilegesToQuery(
      this.graphName,
      queryMeta.queryName,
      [GSQLPrivilege.Owner, GSQLPrivilege.ExecuteQuery]
    ) && this.authService.hasPrivilegesToQuery(
      this.graphName,
      queryMeta.queryName,
      [GSQLPrivilege.Owner, GSQLPrivilege.ReadQuery]
    );
    if (!canExecute) {
      this.queryToolbarConfig.top[ToolbarButtons.Run].disabled = true;
      this.queryToolbarConfig.top[ToolbarButtons.Run].child.disabled = true;
    }
  }

  /**
   * Show the user the error through dialog.
   *
   * @private
   * @param {string | string[]} message
   * @param {string} [title]
   * @memberof QueryEditorComponent
   */
  private handleError(message: string | string[], title?: string) {
    if (typeof message === 'string') {
      message = [message];
    }

    const data: DialogData = {
      title: title ? title : 'Error',
      messages: message,
    };
    this.dialog.open(DialogComponent, { data: data });
  }

  /**
   * Attach clean up signal for the subjects.
   *
   * @private
   * @memberof LoadingExecutorComponent
   */
  private attachSubjectCleanUp() {
    this.queriesList.pipe(takeUntil(this.ngUnsubscribe));
    this.unsavedQueriesSubject.pipe(takeUntil(this.ngUnsubscribe));
    this.queryParamPanelSubject.pipe(takeUntil(this.ngUnsubscribe));
  }

  /**
   * Query should run in interpreted mode if:
   *   - query draft saved in GraphStudio (installed or not).
   *   - query added from gsql but not installed.
   *
   * @private
   * @param {QueryMeta} queryMeta
   * @returns {boolean}
   * @memberof QueryEditorComponent
   */
  private shouldRunInInterpretedMode(queryMeta: QueryMeta): boolean {
    return (
      !this.logicService.getUnsavedQueries().includes(queryMeta.queryName) &&
      (this.queryStatus(queryMeta) === QueryStatus.Draft ||
        this.queryStatus(queryMeta) === QueryStatus.Unknown)
    );
  }

  /**
   * Copy url to clipboard.
   *
   * @param {string} url
   * @memberof QueryEditorComponent
   */
  copyToClipboard(url: string, element: any) {
    const plainUrl = this.convertToPlainText(url);
    let msg = '';
    if (this.copier.copyText(plainUrl)) {
      msg = 'Endpoint copied';
    } else {
      msg = 'Copy failed. Please try again!';
    }

    element._elementRef.nativeElement.focus();

    this.snackBar.open(msg, 'DISMISS', { duration: 3000 });
  }

  /**
   * Convert html to plain text.
   *
   * @param {string} text
   * @memberof QueryEditorComponent
   */
  private convertToPlainText(text: string) {
    return text
      .replace(/<[^>]+>/gm, '')
      .replace(/&lt;/gm, '<')
      .replace(/&gt;/gm, '>');
  }

  /**
   * Init the run configuration form value.
   *
   * @private
   * @memberof QueryEditorComponent
   */
  private initRunConfigurationForm() {
    const localRunConfiguration = this.cacheService.getItem(
      `${this.cachePrefix}RunConfiguration}`
    );

    if (localRunConfiguration) {
      this.logicService.runConfiguration = localRunConfiguration;
    }
    this.getDefaultConfiguration()
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe((data) => {
        this.logicService.runConfiguration = {
          timeOut: {
            value: data.timeOut,
            defaultValue: data.timeOut,
            limit: data.timeOutLimit,
          },
          runMemory: {
            value: data.runMemory,
            defaultValue: data.runMemory,
            limit: data.memoryLimit,
          },
          GSQLReplica: {
            value: data.GSQLReplica,
            defaultValue: data.GSQLReplica,
            limit: data.GSQLReplicaLimit,
          },
          queryProfile: data.queryProfile,
        };

        this.runConfigurationForm = this.cmptService.createConfigForm(
          data,
          this.logicService.replicationFactor
        );
      });
  }

  private getDefaultConfiguration(): Observable<QueryFormConfiguration> {
    return forkJoin([
      this.cmptService.getRunQueryTimeout(),
      this.cmptService.getRunQueryMemoryLimit(),
      this.cmptService.getReplicationFactor(),
    ]).pipe(
      map(([timeOut, runMemory, replicationFactor]) => {
        this.logicService.replicationFactor = replicationFactor;
        this.replicationFactor = replicationFactor;
        // e.g. if the cluster is 2*2, the options are 1, 2
        this.GSQLReplicaOptions = Array.from(
          { length: replicationFactor },
          (_, i) => i + 1
        );
        return {
          timeOut: timeOut,
          runMemory: runMemory === 0 ? null : runMemory,
          timeOutLimit: true,
          memoryLimit: runMemory !== 0,
          queryProfile: true,
          GSQLReplica: 1,
          GSQLReplicaLimit: false,
        };
      })
    );
  }

  /**
   * Handle the run configuration.
   *
   * @param {QueryFormConfiguration} queryFormConfiguration
   * @memberof QueryEditorComponent
   */
  handleRunConfiguration(formRunConfiguration: QueryFormConfiguration) {
    if (
      this.runConfigurationForm.errors['timeOutError'] ||
      this.runConfigurationForm.errors['runMemoryError'] ||
      this.runConfigurationForm.errors['GSQLReplicaError']
    ) {
      return;
    }
    this.logicService.updateRunconfiguration(formRunConfiguration);
    this.cacheService.setItem(
      `${this.cachePrefix}RunConfiguration}`,
      formRunConfiguration
    );
    this.reflow
      ? this.closeRunConfigDialog()
      : this.switchRunConfigPanel('close');
  }

  /**
   * Handle the reset of the run configuration
   * by getting new default setting from server and close the panel.
   *
   * @memberof QueryEditorComponent
   */
  handleResetRunConfiguration() {
    this.getDefaultConfiguration()
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe((defaultConfig) => {
        this.logicService.updateDefaultRunconfiguration(defaultConfig);
        this.logicService.updateRunconfiguration(defaultConfig);
        this.runConfigurationForm.setValue(defaultConfig);
      });
  }

  /**
   * Abort the value change in form when user chooses the no limitation option.
   *
   * @memberof QueryEditorComponent
   */
  resetTimeOutFormValue() {
    this.runConfigurationForm.patchValue({
      timeOut: this.logicService.runConfiguration.timeOut.value,
    });
  }

  /**
   * Abort the value change in form when user chooses the no limitation option.
   *
   * @memberof QueryEditorComponent
   */
  resetRunMemoryFormValue() {
    this.runConfigurationForm.patchValue({
      runMemory: this.logicService.runConfiguration.runMemory.value,
    });
  }

  changeQueryProfile() {
    this.logicService.runConfiguration.queryProfile = !this.logicService
      .runConfiguration.queryProfile;
    this.resultToolbarConfig.left[1 + QueryResultStatus.Profile].show = this.logicService
      .runConfiguration.queryProfile;
  }

  GSQLReplicaConfigurable() {
    return this.query.installed && this.isDistributedQuery(this.queryName);
  }

  queryProfileConfigurable() {
    return this.supportQueryProfile;
  }

  /**
   * Open parameter panel dialog.
   *
   * @memberof QueryEditorComponent
   */
  openParamDialog() {
    const dialogConfig = {
      width: '400px',
      minHeight: '50px',
    };
    this.paramDialogRef = this.dialog.open(this.paramDialog, dialogConfig);

    this.updateQueryToolbarStatus(
      this.logicService.queriesMeta.get(this.queryName)
    );

    this.paramDialogRef
      .afterClosed()
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe(() => {
        this.paramDialogRef = undefined;
        // If the dialog is closed in small screen view, update toolbar buttons.
        if (this.reflow) {
          this.updateQueryToolbarStatus(
            this.logicService.queriesMeta.get(this.queryName)
          );
        }
      });
  }

  /**
   * Close parameter panel dialog.
   *
   * @memberof QueryEditorComponent
   */
  closeParamDialog() {
    this.paramDialogRef.close();
  }

  /**
   * Open run configurations dialog.
   *
   * @memberof QueryEditorComponent
   */
  openRunConfigDialog() {
    this.queryToolbarConfig.top[ToolbarButtons.Run].disabled = true;
    const dialogConfig = {
      width: '400px',
      minHeight: '50px',
    };
    this.runConfigDialogRef = this.dialog.open(
      this.runConfigDialog,
      dialogConfig
    );
    this.runConfigDialogRef
      .afterClosed()
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe(() => {
        this.runConfigDialogRef = undefined;
        // If the dialog is closed in small screen view, update toolbar buttons.
        if (this.reflow) {
          this.queryToolbarConfig.top[ToolbarButtons.Run].disabled = false;
        }
      });
  }

  /**
   * Close run configurations dialog.
   *
   * @memberof QueryEditorComponent
   */
  closeRunConfigDialog() {
    this.runConfigDialogRef.close();
  }

  /**
   * Update query toolbar disable status based on user privilege.
   *
   * @memberof QueryEditorComponent
   */
  updateQueryToolbarStatus(queryMeta: QueryMeta) {
    this.updateQueryToolbarDisableStatus(queryMeta);
    if (compareWithInstanceVersion('4.1.0', '<')) {
      this.updateQueryToolbarDisableStatusBasedOnUserPrivilege();
    } else {
      this.updateQueryToolbarDisableStatusFor410Version(queryMeta);
    }
  }

  private toggleConsole() {
    if (this.reflow) {
      this.changeSplitView(SplitViewStatus.Bottom);
    } else {
      if (this.currentSplitViewStatus === SplitViewStatus.Split) {
        this.changeSplitView(SplitViewStatus.Top);
      } else {
        this.changeSplitView(SplitViewStatus.Split);
      }
    }
  }

  /**
   * tree-select component has common interface, query templates should be parsed
   *
   * @param data query templates got from backend
   * @returns array of SelectableItem
   */
  public parseData(data: QueryTemplates[]): SelectableItem[] {
    return data.map((srcItem) => {
      let item: SelectableItem;
      const tooltip = this.parseToolTip(srcItem);
      item = new SelectableItem(
        srcItem.name,
        srcItem.algorithmName || '',
        tooltip,
        this.selectService
      );
      const children = []
        .concat(srcItem.algorithms || [])
        .concat(srcItem.subs || []);
      item.children = this.parseData(children);
      return item;
    });
  }

  /**
   * tooltip consists of three partstree part, name, description, schemaConstraints
   * name use algorithmName, if not exist, use name
   *
   * @param item queryTemplate get from backend
   * @returns
   */
  public parseToolTip(item: QueryTemplates) {
    const name = item.algorithmName ? `Name: ${item.algorithmName}` : '';
    const description = item.description
      ? '\n\nDescription: ' + item.description
      : '';
    const schemaConstraints = item.schemaConstraints
      ? '\n\nSchemaConstraints: ' + item.schemaConstraints
      : '';
    return name + description + schemaConstraints;
  }

  public nextStep() {
    this.addNewQueryStep = 2;
  }

  openQueryRunningDialog() {
    this.queryRunningDialogRef = this.dialog.open(this.queryRunningDialog, {
      disableClose: true,
      width: '400px',
      panelClass: 'query-running-dialog-panel'
    });
  }

  closeQueryRunningDialog() {
    if (this.queryRunningDialogRef) {
      this.queryRunningDialogRef.close();
    }
  }

  private isJsonTooLarge(result: string): boolean {
    const jsonSize = new Blob([result]).size;
    return jsonSize > this.MAX_JSON_SIZE;
  }

  private updateJsonContent(result: any) {
    this.fullJsonContent = typeof result !== 'string' ? stringifyJSON(result, null, 2) : result;

    if (this.isJsonTooLarge(this.fullJsonContent)) {
      this.isJsonResultTooLarge = true;
      this.jsonContent = 'The query result is too large to display (exceeds 10MB). Please download the file to view the complete result.';
    } else {
      this.isJsonResultTooLarge = false;
      this.jsonContent = this.fullJsonContent;
    }
  }

  downloadJsonResult() {
    const blob = new Blob([this.fullJsonContent], { type: 'application/json' });
    saveAs(blob, `query_result_${this.queryName || 'unnamed'}.json`);
  }

  genCloudEditorLink() {
    const cloudEnv = sessionStorage.getItem('CLOUDENV');
    const workspaceId = sessionStorage.getItem('CURRENTCLUSTERID');

    let domain = 'savanna.tgcloud.io';
    if (cloudEnv === 'dev') {
      domain = 'test-portal.tgcloud-dev.com';
    } else if (cloudEnv === 'uat') {
      domain = 'portal.tgcloud-dev.com';
    }

    return `https://${domain}/editor?tab=queries` + (workspaceId ? `&workspace_id=${workspaceId}` : '');
  }

  openGSQLShell() {
    const newWindow = open(`/gsql/`, '_blank');

    if (newWindow) {
      const data = { queryContent: this.queryContent };

      const messageHandler = (event: MessageEvent) => {
        if (event.source === newWindow && event.data === 'ready') {
          newWindow.postMessage(data, '*');
          window.removeEventListener('message', messageHandler);
        }
      };

      window.addEventListener('message', messageHandler);
    }
  }
}
