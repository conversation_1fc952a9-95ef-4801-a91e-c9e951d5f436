import { Injectable } from '@angular/core';
import {
  HttpClient,
  HttpParams,
  HttpErrorResponse,
} from '@angular/common/http';
import { forkJoin, from, Observable, of, throwError } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import * as CodeMirror from 'codemirror';

import { HttpSourceService, Logger } from '@app/core/services';
import { ToolbarConfig } from '@app/shared/components/toolbar-container';
import { ButtonBase, IconButton } from '@tigergraph/tools-models/button';
import { CompilationError } from '@tigergraph/tools-models/error';
import {
  GsqlQueryMeta,
  QueryMeta,
  QueryParamVertexType,
  QueryParamListType,
  AddQueriesToGSQLResult,
  InstallQueriesResult,
  InstallQueryTemplatesResult,
  QueryFormConfiguration,
  QuerySyntax,
} from '@tigergraph/tools-models/query';
import {
  FormGroup,
  FormControl,
  ValidatorFn,
  AbstractControl,
  ValidationErrors,
  Validators,
} from '@angular/forms';
import {
  DBGraphStyleJson,
  GSQLGraphJson,
} from '@tigergraph/tools-models/topology';
import { HelperFunctions } from '@app/shared/utils';
import {
  gsqlEditorMode,
  gsqlDictionary,
} from '@tigergraph/tools-models/utils/editor-mode/gsql-code-mode';
import { cypherDictionary } from '@tigergraph/tools-models/utils/editor-mode/cypher-code-mode';
import { getHint } from '@tigergraph/tools-models/utils/editor-mode/extension';
import { QueryTemplates } from './tree-select/tree-select.models';
import { AnalyticsService } from '@app/shared/services/analytics.service';
import {
  checkQuery,
  compareWithInstanceVersion,
  getGraphSchema,
  getQueriesInfo,
  getTypeNames,
  getVersion,
  runInterpret,
  stringifyJSON,
} from '@tigergraph/tools-models';
import _ from 'lodash';
import { isSupportGSQLDraft } from './shared/util';

const url = {
  addQueryDrafts: '/api/queries/[GRAPH_NAME]/gsql/add',
  config: '/api/config',
  installQueries: '/api/queries/[GRAPH_NAME]/gsql/install',
  queryMetaList: '/api/queries/[GRAPH_NAME]/info',
  queryMeta: '/api/queries/[GRAPH_NAME]/info/[QUERY_NAME]',
  queryDraft: '/api/queries/[GRAPH_NAME]/info/[QUERY_NAME]/draft',
  runQuery: '/api/restpp/query/[GRAPH_NAME]/[QUERY_NAME]',
  queryStatus: '/api/restpp/query_status/[GRAPH_NAME]',
  queryResult: '/api/restpp/query_result',
  abortQuery: '/api/restpp/abortquery/[GRAPH_NAME]',
  schemaStyle: '/api/graph-styles/local/[GRAPH_NAME]',
  userIcons: '/api/data/user_icons',
  graphAlgorithm: '/api/graph-algorithm',
  InstallGraphAlgorithm: '/api/graph-algorithm/[GRAPH_NAME]/install',
};

/**
 * Toolbar buttons.
 *
 * @export
 * @enum {number}
 */
export const enum ToolbarButtons {
  Expand,
  Save,
  SaveAs,
  Publish,
  Console,
  Run,
  DeleteQuery,
  ShowEndpoint,
  DownloadGsql,
  DeleteDraft,
  Help,
}

export const runQueryConfigValidator = (
  control: AbstractControl,
  replicationFactor: number
): ValidationErrors => {
  const timeOut = control.get('timeOut');
  const runMemory = control.get('runMemory');
  const timeOutLimit = control.get('timeOutLimit');
  const memoryLimit = control.get('memoryLimit');
  const GSQLReplica = control.get('GSQLReplica');
  const GSQLReplicaLimit = control.get('GSQLReplicaLimit');
  let timeOutError = false;
  let runMemoryError = false;
  let GSQLReplicaError = false;

  if (timeOutLimit.value) {
    timeOutError = !(
      timeOut.value !== null &&
      timeOut.value >= 1 &&
      timeOut.value <= 2147483647
    );
  }
  if (memoryLimit.value) {
    runMemoryError = !(
      runMemory.value !== null &&
      runMemory.value >= 1 &&
      runMemory.value <= 2147483647
    );
  }
  if (GSQLReplicaLimit.value) {
    GSQLReplicaError = !(
      GSQLReplica.value !== null &&
      GSQLReplica.value >= 0 &&
      GSQLReplica.value <= replicationFactor
    );
  }

  return {
    timeOutError: timeOutError,
    runMemoryError: runMemoryError,
    GSQLReplicaError: GSQLReplicaError,
  };
};

/**
 * Query Editor helper service that provides static/dynamic data and external interaction.
 *
 * @export
 * @class QueryEditorService
 */
@Injectable()
export class QueryEditorService {
  private queryToolbarConfig: ToolbarConfig;
  private resultToolbarConfig: ToolbarConfig;
  private compilationError: CompilationError;

  constructor(
    private logger: Logger,
    private http: HttpClient,
    private httpSrc: HttpSourceService,
    private analyticsService: AnalyticsService
  ) {
    this.buildToolbarConfig();
    this.clearCodeCheckError();
    this.initCodeMirror();
  }

  /**
   * Get the restpp port number.
   *
   * @returns {Observable<number>}
   * @memberof QueryEditorService
   */
  getPortNumber(): Observable<number> {
    return this.http
      .get<any>(url.config, {
        params: new HttpParams().set('key', 'Nginx.Port'),
      })
      .pipe(map((config) => config['Nginx.Port']));
  }

  /**
   * Get the default query running time out value from RESTPP.
   *
   * @returns {Observable<number>}
   * @memberof QueryEditorService
   */
  getRunQueryTimeout(): Observable<number> {
    return this.http
      .get(url.config, {
        params: new HttpParams().set(
          'key',
          'RESTPP.Factory.DefaultQueryTimeoutSec'
        ),
      })
      .pipe(map((config) => config['RESTPP.Factory.DefaultQueryTimeoutSec']));
  }

  /**
   * Get the default query run memory limit value from GPE.
   *
   * @returns {Observable<number>}
   * @memberof QueryEditorService
   */
  getRunQueryMemoryLimit(): Observable<number> {
    return this.http
      .get(url.config, {
        params: new HttpParams().set('key', 'GPE.QueryLocalMemLimitMB'),
      })
      .pipe(
        map((config) => config['GPE.QueryLocalMemLimitMB']),
        // If bad request, use 0 for the default value
        catchError(() => of(0))
      );
  }

  getReplicationFactor(): Observable<number> {
    return this.http
      .get<{
        [index: string]: {
          HostID: string;
          Partition: number;
          Replica: number;
        }[];
      }>(url.config, {
        params: new HttpParams().set('key', 'GSE.BasicConfig.Nodes'),
      })
      .pipe(
        map((res) => _.maxBy(res['GSE.BasicConfig.Nodes'], 'Replica').Replica),
        catchError(() => of(1))
      );
  }

  /**
   * Get the list of user uploaded icons from the server.
   *
   * @returns {Observable<string[]>}
   * @memberof QueryEditorService
   */
  getUserUploadedIconNames(): Observable<string[]> {
    return this.http
      .get<any[]>(url.userIcons)
      .pipe(map((res) => res.map((icon) => icon.name)));
  }

  /**
   * Get the current schema from the server.
   *
   * @param {string} graphName
   * @returns {Observable<GSQLGraphJson>}
   * @memberof QueryEditorService
   */
  getSchema(graphName: string): Observable<GSQLGraphJson> {
    return from(
      getGraphSchema({
        graph: graphName,
      }).then((res) => res.data.results)
    );
  }

  /**
   * Get the current schema style from the server.
   *
   * @param {string} graphName
   * @returns {Observable<DBGraphStyleJson>}
   * @memberof QueryEditorService
   */
  getSchemaStyle(graphName: string): Observable<DBGraphStyleJson> {
    return this.http
      .get<DBGraphStyleJson>(
        HelperFunctions.applyGraphNameToURL(graphName, url.schemaStyle)
      )
      .pipe(
        catchError((err: HttpErrorResponse) => {
          if (err.status === 404) {
            return of({
              vertexStyles: {},
              edgeStyles: {},
            });
          } else {
            return throwError(err);
          }
        })
      );
  }

  getGSQLTypeNames(): Observable<{ [graphName: string]: string[] }> {
    // fix this type
    return from(
      getTypeNames().then(
        (res) => res.data.results as { [graphName: string]: string[] }
      )
    );
  }

  /**
   * Get query meta as a list from server.
   *
   * @param {string} graphName
   * @returns {Observable<GsqlQueryMeta[]>}
   * @memberof QueryEditorService
   */
  getQueryMetaList(graphName): Observable<GsqlQueryMeta[]> {
    const targetUrl = HelperFunctions.replacePlaceholderInUrl(
      url.queryMetaList,
      {
        '[GRAPH_NAME]': graphName,
      }
    );

    return this.http.get<GsqlQueryMeta[]>(targetUrl);
  }

  /**
   * Generate default content for a new query
   *
   * @param queryName the name of the new query
   * @param graphName the current graph of the user
   * @returns default content for the new query
   * @memberof QueryEditorService
   */
  generateNewQueryContent(queryName: string, graphName: string): string {
    return (
      `CREATE QUERY ${queryName}(/* Parameters here */) FOR GRAPH ${graphName} { \n` +
      `  /* Write query logic here */ \n  PRINT "${queryName} works!"; \n}`
    );
  }

  /**
   * Create a query draft.
   *
   * @param {string} graphName
   * @param {string} queryName
   * @param {string} queryContent
   *
   * @returns {Observable<any>}
   * @memberof QueryEditorService
   */
  createQueryDraft(
    graphName: string,
    queryName: string,
    querySyntax: QuerySyntax,
    queryContent: string,
    graphUpdate = false
  ): Observable<any> {
    const targetUrl = HelperFunctions.replacePlaceholderInUrl(url.queryDraft, {
      '[GRAPH_NAME]': graphName,
      '[QUERY_NAME]': queryName,
    });

    return this.httpSrc.post(targetUrl, {
      body: JSON.stringify({
        name: queryName,
        syntax: querySyntax,
        code: queryContent,
        graphUpdate: graphUpdate,
      }),
    });
  }

  /**
   * Update a query draft.
   *
   * @param {string} graphName
   * @param {string} queryName
   * @param {string} queryContent
   *
   * @returns {Observable<any>}
   * @memberof QueryEditorService
   */
  updateQueryDraft(
    graphName: string,
    queryName: string,
    querySyntax: string,
    queryContent: string,
    graphUpdate = false
  ): Observable<any> {
    const targetUrl = HelperFunctions.replacePlaceholderInUrl(url.queryDraft, {
      '[GRAPH_NAME]': graphName,
      '[QUERY_NAME]': queryName,
    });

    return this.httpSrc.put(targetUrl, {
      body: JSON.stringify({
        name: queryName,
        code: queryContent,
        syntax: querySyntax,
        graphUpdate: graphUpdate,
      }),
    });
  }

  /**
   * Delete a query draft.
   *
   * @param {string} graphName
   * @param {string} queryName
   *
   * @returns {Observable<any>}
   * @memberof QueryEditorService
   */
  deleteQueryDraft(graphName: string, queryName: string): Observable<any> {
    const targetUrl = HelperFunctions.replacePlaceholderInUrl(url.queryDraft, {
      '[GRAPH_NAME]': graphName,
      '[QUERY_NAME]': queryName,
    });

    return this.httpSrc.delete(targetUrl);
  }

  /**
   * Add query drafts to GSQL.
   *
   * @param {string} graphName
   * @param {{ name: string, needDrop: boolean}[]} queries
   * @returns {Observable<any>}
   * @memberof QueryEditorService
   */
  addQueryDraftToGSQL(
    graphName: string,
    queries: { name: string; needDrop: boolean }[]
  ): Observable<AddQueriesToGSQLResult> {
    const targetUrl = HelperFunctions.replacePlaceholderInUrl(
      url.addQueryDrafts,
      {
        '[GRAPH_NAME]': graphName,
      }
    );
    return this.httpSrc.post(targetUrl, {
      body: JSON.stringify(queries),
      headers: {
        'content-type': 'application/json',
      },
    });
  }

  /**
   * Install all queries to GSQL.
   *
   * @param {string} graphName
   * @param {string[]} queryNames
   * @returns {Observable<any>}
   * @memberof QueryEditorService
   */
  installQuery(
    graphName: string,
    queryNames: string[]
  ): Observable<InstallQueriesResult> {
    const targetUrl = HelperFunctions.replacePlaceholderInUrl(
      url.installQueries,
      {
        '[GRAPH_NAME]': graphName,
      }
    );

    let params = new HttpParams();

    queryNames.forEach((queryName) => {
      params = params.append('queryName', queryName);
    });

    return this.httpSrc.post(
      HelperFunctions.concatURLAndSearchParams(targetUrl, params)
    );
  }

  installQueryByGSQLCmd(graphName: string, queryNames: string[], singleQueryNames?: string[]) {
    let cmdText = `USE GRAPH ${graphName}`;
    // Install distributed queries
    if (queryNames?.length > 0) {
      cmdText += `\nINSTALL QUERY ${queryNames.join(',')}`;
    }
    // Install single queries to improve performance and enable query profiling
    if (singleQueryNames?.length > 0) {
      cmdText += `\nINSTALL QUERY -SINGLE ${singleQueryNames.join(',')}`;
    }
    return this.httpSrc.post<any>('/api/gsql-command', {
      body: JSON.stringify({ command: cmdText, Cookie: { fromGsqlServer: false } })
    });
  }

  /**
   * Create the configuration form for the component.
   *
   * @param {QueryFormConfiguration} config
   * @returns {FormGroup}
   * @memberof QueryEditorService
   */
  createConfigForm(
    config: QueryFormConfiguration,
    replicationFactor: number
  ): FormGroup {
    const fg = new FormGroup(
      {
        timeOut: new FormControl(config.timeOut, [
          Validators.max(2147483647),
          Validators.min(1),
          Validators.required,
        ]),
        runMemory: new FormControl(config.runMemory, [
          Validators.max(2147483647),
          Validators.min(1),
          Validators.required,
        ]),
        timeOutLimit: new FormControl(config.timeOutLimit),
        memoryLimit: new FormControl(config.memoryLimit),
        GSQLReplica: new FormControl(config.GSQLReplica, [
          Validators.max(replicationFactor),
          Validators.min(1),
          Validators.required,
        ]),
        GSQLReplicaLimit: new FormControl(config.GSQLReplicaLimit),
        queryProfile: new FormControl(config.queryProfile),
      },
      {
        validators: (control: AbstractControl) =>
          runQueryConfigValidator(control, replicationFactor),
      }
    );
    return fg;
  }

  /**
   * Run query.
   *
   * @param {string} graphName
   * @param {string} queryName
   * @param {HeadersInit} headers
   * @param {any} [params]
   * @returns {Observable<any>}
   * @memberof QueryEditorService
   */
  runQuery(
    graphName: string,
    queryName: string,
    headers: HeadersInit,
    params?: any
  ): Observable<any> {
    const targetUrl = HelperFunctions.replacePlaceholderInUrl(url.runQuery, {
      '[GRAPH_NAME]': graphName,
      '[QUERY_NAME]': queryName,
    });

    return params
      ? this.httpSrc.post(targetUrl, {
        headers: headers,
        body: stringifyJSON(params),
      })
      : this.httpSrc.get(targetUrl, {
        headers: headers,
      });
  }

  getQueryStatus(requestId: string, graphName: string): Observable<any> {
    const targetUrl = HelperFunctions.replacePlaceholderInUrl(url.queryStatus, {
      '[GRAPH_NAME]': graphName,
    });

    return this.httpSrc.get(`${targetUrl}?requestid=${requestId}&node=ALL&graph=${graphName}`);
  }

  getQueryResult(requestId: string, graphName: string): Observable<any> {
    return this.httpSrc.get(`${url.queryResult}?requestid=${requestId}&node=ALL&graph=${graphName}`);
  }

  abortQuery(graphName: string, requestId: string): Observable<any> {
    const targetUrl = HelperFunctions.replacePlaceholderInUrl(url.abortQuery, {
      '[GRAPH_NAME]': graphName,
    });

    return this.httpSrc.get(`${targetUrl}?requestid=${requestId}&node=ALL&graph=${graphName}`);
  }

  /**
   * Get params for running interpreted query.
   *
   * @param {string} queryContent
   * @param {string} graph
   * @returns {Observable<any>}
   * @memberof QueryEditorService
   */
  getParamsForInterpretedQuery(
    queryContent: string,
    graph: string
  ): Observable<any> {
    return from(
      getQueriesInfo({
        queryParams: {
          graph,
        },
        queryBody: queryContent,
      }).then((res) => res.data.results)
    );
  }

  /**
   * Run query in interpreted mode.
   *
   * @param {string} queryContent
   * @param {HttpParams} params
   * @param {HeadersInit} headers
   *
   * @returns {Observable<any>}
   * @memberof QueryEditorService
   */
  runQueryInInterpretedMode(
    queryContent: string,
    params: HttpParams,
    headers: HeadersInit
  ): Observable<any> {
    return from(
      runInterpret({
        // fix type here
        params: new URLSearchParams(params.toString()) || undefined,
        queryBody: queryContent,
        headers: headers,
      }).then((res) => res.data.results)
    );
  }

  /**
   * Check the queries if they have passed the code check.
   *
   * @param {string} graph
   * @param {string[]} queryDrafts
   * @returns {Observable<boolean[]>}
   */
  checkIfPassedCodeCheck(
    graph: string,
    queryDrafts: string[]
  ): Observable<boolean[]> {
    return forkJoin(
      queryDrafts.map((code) => {
        return from(
          checkQuery({
            graph,
            code,
          }).then((res) => res.data.results)
        ).pipe(map((response) => response['errors'].length === 0));
      })
    );
  }

  /**
   * Call the server to get code errors.
   *
   * @param {string} graphName
   * @param {string} queryName
   * @param {string} code
   * @returns {Observable<CompilationError>}
   * @memberof QueryEditorService
   */
  checkCode(
    graphName: string,
    queryName: string,
    code: string,
    syntax: QuerySyntax = 'GSQL'
  ): Observable<CompilationError> {
    // If the code is not a valid query or currently it is not viewing a query, don't check the code
    if (!code || !queryName || queryName === '') {
      return of({ errors: [], warnings: [] });
    }

    // Get the code check error or warning by calling GSQL server
    return from(
      checkQuery({ code, graph: graphName }).then((res) => res.data.results)
    ).pipe(
      map((response) => {
        this.compilationError = {
          errors: response['errors']
            .filter((error) => typeof error === 'object')
            .map((error) =>
              this.normalizeErrorOrWarning(code, error, syntax)
            ),
          warnings: response['warnings']
            .filter((error) => typeof error === 'object')
            .map((warning) =>
              this.normalizeErrorOrWarning(code, warning, syntax)
            ),
        };

        return this.compilationError;
      })
    );
  }

  /**
   * Clear compilation errors and warnings.
   *
   * @memberof QueryEditorService
   */
  clearCodeCheckError() {
    this.compilationError = {
      errors: [],
      warnings: [],
    };
  }

  /* istanbul ignore next */
  /**
   * Normalize the error or warning from different response format:
   * 1. Only contains a message: mark the whole query
   * 2. Only contains start position: mark until token ends
   * 3. Contains both start and end positions: mark the range
   *
   * @private
   * @param {string} code
   * @param {*} error
   * @returns {{
   *     startLine: number;
   *     startColumn: number;
   *     endLine: number;
   *     endColumn: number;
   *     message: string;
   *   }}
   * @memberof QueryEditorService
   */
  private normalizeErrorOrWarning(
    code: string,
    error: any,
    syntax: QuerySyntax
  ): {
    startLine: number;
    startColumn: number;
    endLine: number;
    endColumn: number;
    message: string;
  } {
    const lines = code.split('\n');
    const message = error.msg;
    let markStart: number;
    let markStop: number;

    // For cypher query, some errors are generated after the query has gone through the transformation stages
    // these types of errors will probably not have the correct line number, so we just mark the first line
    if (syntax === 'CYPHER' && error.errorcode !== 9999) {
      return {
        startLine: 0,
        startColumn: 0,
        endLine: 0,
        endColumn: 0,
        message: message,
      };
    }

    // Has startLine, startColumn, endLine, endColumn
    if (
      'startLine' in error &&
      'startColumn' in error &&
      'endLine' in error &&
      'endColumn' in error
    ) {
      return {
        startLine: error.startLine,
        startColumn: error.startColumn,
        endLine: error.endLine,
        endColumn: error.endColumn,
        message: message,
      };
    }

    // Has start and stop index
    if ('startindex' in error && 'stopindex' in error) {
      markStart = error.startindex;
      markStop = error.stopindex + 1;
    }

    if (!markStart) {
      // If it's parsing error, mark until token ends
      if ('line' in error) {
        const line = error.line - 1; // parsing error line number is always actual line number + 1
        const pos = error.charpositioninline;
        let endPos = pos;

        if (line < lines.length) {
          for (; endPos < lines[line].length; endPos++) {
            if ([' ', '\t', '\n'].includes(lines[line].charAt(endPos))) {
              break;
            }
          }
        }

        return {
          startLine: line,
          startColumn: pos,
          endLine: line,
          endColumn: endPos,
          message: message,
        };
      } else {
        // Nothing there, mark the whole code
        return {
          startLine: 0,
          startColumn: 0,
          endLine: lines.length - 1,
          endColumn: lines[lines.length - 1].length,
          message: message,
        };
      }
    } else {
      // Contains both start position and end position
      const prefixStart = code.substring(0, markStart);
      const prefixStop = code.substring(0, markStop);
      return {
        startLine: prefixStart.split('\n').length - 1,
        startColumn: markStart - prefixStart.lastIndexOf('\n') - 1,
        endLine: prefixStop.split('\n').length - 1,
        endColumn: markStop - prefixStop.lastIndexOf('\n') - 1,
        message: message,
      };
    }
  }

  /**
   * Delete a query with the given query name.
   *
   * @param {string} graphName
   * @param {string} queryName
   * @returns {Observable<any>}
   * @memberof QueryEditorService
   */
  deleteQuery(graphName: string, queryName: string): Observable<any> {
    const targetUrl = HelperFunctions.replacePlaceholderInUrl(url.queryMeta, {
      '[GRAPH_NAME]': graphName,
      '[QUERY_NAME]': queryName,
    });

    return this.httpSrc.delete(targetUrl);
  }

  /**
   * Return the query toolbar configurations.
   *
   * @returns {ToolbarConfig}
   * @memberof QueryEditorService
   */
  getQueryToolbarConfig(): ToolbarConfig {
    return this.queryToolbarConfig;
  }

  /**
   * Return the result toolbar configurations.
   *
   * @returns {ToolbarConfig}
   * @memberof QueryEditorService
   */
  getResultToolbarConfig(): ToolbarConfig {
    return this.resultToolbarConfig;
  }

  buildParamsTemplate(queryMeta: QueryMeta): string {
    const paramTemplate: string[] = [];

    queryMeta.params.forEach((queryParam) => {
      const paramName = queryParam.paramName;
      switch (queryParam.paramType.type) {
        case 'VERTEX': {
          paramTemplate.push(
            `<span>${paramName}</span>=${HelperFunctions.escapeHtml(
              '<VERTEX_ID>'
            )}`
          );
          if ((<QueryParamVertexType>queryParam.paramType).vertexType === '*') {
            paramTemplate.push(
              `<span>${paramName}.type</span>=${HelperFunctions.escapeHtml(
                '<VERTEX_TYPE>'
              )}`
            );
          }
          break;
        }
        case 'LIST': {
          const elementType = (<QueryParamListType>queryParam.paramType)
            .elementType;
          if (elementType.type === 'VERTEX') {
            if ((<QueryParamVertexType>elementType).vertexType === '*') {
              paramTemplate.push(
                `<span>${paramName}[0]</span>=${HelperFunctions.escapeHtml(
                  `<VERTEX_ID>`
                )}`
              );
              paramTemplate.push(
                `<span>${paramName}[0].type</span>=${HelperFunctions.escapeHtml(
                  `<VERTEX_TYPE>`
                )}`
              );
              paramTemplate.push(
                `<span>${paramName}[1]</span>=${HelperFunctions.escapeHtml(
                  `<VERTEX_ID>`
                )}`
              );
              paramTemplate.push(
                `<span>${paramName}[1].type</span>=${HelperFunctions.escapeHtml(
                  `<VERTEX_TYPE>`
                )}&...`
              );
            } else {
              paramTemplate.push(
                `<span>${paramName}</span>=${HelperFunctions.escapeHtml(
                  `<VERTEX_ID>`
                )}`
              );
              paramTemplate.push(
                `<span>${paramName}</span>=${HelperFunctions.escapeHtml(
                  `<VERTEX_ID>`
                )}&...`
              );
            }
          } else {
            paramTemplate.push(
              `<span>${paramName}</span>=${HelperFunctions.escapeHtml(
                `<${elementType.type}>`
              )}`
            );
            paramTemplate.push(
              `<span>${paramName}</span>=${HelperFunctions.escapeHtml(
                `<${elementType.type}>`
              )}&...`
            );
          }
          break;
        }
        default: {
          paramTemplate.push(
            `<span>${paramName}</span>=${HelperFunctions.escapeHtml(
              `<${queryParam.paramType.type}>`
            )}`
          );
        }
      }
    });
    return paramTemplate.join('&');
  }

  /**
   * Build the toolbar configurations.
   *
   * @private
   * @memberof QueryEditorService
   */
  private buildToolbarConfig() {
    // The query top toolbar buttons.
    const supportOpenCypher =
      getVersion() && compareWithInstanceVersion('3.9.3', '>=');
    const queryToolbarTop: ButtonBase[] = [
      new IconButton({
        key: 'viewChange',
        icon: 'fullscreen',
        tooltip: 'expand',
        show: true,
      }),
      new IconButton({
        key: 'save',
        icon: 'save',
        tooltip: 'Save query draft',
      }),
      new IconButton({
        key: 'saveAs',
        icon: 'save-as',
        tooltip: 'Save query draft under a different name',
      }),
      new IconButton({
        key: 'install',
        icon: 'publish',
        tooltip: 'Install query',
      }),
      new IconButton({
        key: 'console',
        icon: 'video_label',
        tooltip: 'Console',
        color: 'accent',
      }),
      new IconButton({
        key: 'run',
        icon: 'play_arrow',
        tooltip: 'Run query in interpreted mode',
        child: new IconButton({
          key: 'subRun',
          icon: 'arrow_drop_down',
          tooltip: 'Options',
          menu: [
            new IconButton({
              key: 'runConfiguration',
              icon: 'settings',
              label: 'Run Configuration',
            }),
          ],
        }),
      }),
      new IconButton({
        key: 'deleteQuery',
        icon: 'delete_forever',
        tooltip: 'Delete query and draft',
      }),
      new IconButton({
        key: 'showEndpoint',
        icon: 'link',
        tooltip: 'Show query endpoint',
      }),
      new IconButton({
        key: 'downloadGsql',
        icon: 'get_app',
        tooltip: 'Download',
      }),
      new IconButton({
        key: 'deleteDraft',
        icon: 'delete_sweep',
        tooltip: 'Discard draft',
        show: !isSupportGSQLDraft(),
      }),
    ];
    if (supportOpenCypher) {
      queryToolbarTop.push(
        new IconButton({
          key: 'help',
          icon: 'help',
          tooltip: 'Get help',
        })
      );
    }

    this.queryToolbarConfig = {
      top: queryToolbarTop,
    };

    // The result left toolbar buttons.
    const resultToolbarLeft: ButtonBase[] = [
      new IconButton({
        key: 'viewChange',
        icon: 'fullscreen',
        tooltip: 'Expand',
        tooltipPosition: 'right',
        show: true,
      }),
      new IconButton({
        key: 'schema',
        icon: 'schema',
        tooltip: 'View schema',
        tooltipPosition: 'right',
      }),
      new IconButton({
        key: 'graph',
        icon: 'graph',
        tooltip: 'Visualize graph result',
        tooltipPosition: 'right',
      }),
      new IconButton({
        key: 'json',
        icon: 'json',
        tooltip: 'View JSON result',
        tooltipPosition: 'right',
      }),
      new IconButton({
        key: 'table',
        icon: 'table',
        tooltip: 'View table result',
        tooltipPosition: 'right',
      }),
      new IconButton({
        key: 'log',
        icon: 'assignment',
        tooltip: 'View logs',
        tooltipPosition: 'right',
      }),
      new IconButton({
        key: 'profile',
        icon: 'database_search',
        tooltip: 'View profile result',
        tooltipPosition: 'right',
        show: getVersion() && compareWithInstanceVersion('4.2.0', '>='),
      }),
    ];

    this.resultToolbarConfig = {
      left: resultToolbarLeft,
      asTab: true,
    };
  }

  /**
   * Initialize CodeMirror settings.
   *
   * @private
   * @memberof QueryEditorService
   */
  private initCodeMirror() {
    // Add customized mode for gsql.
    (<any>CodeMirror).defineSimpleMode('text/x-gsql', gsqlEditorMode());
    // Register our custom plugin.
    CodeMirror.registerHelper('hint', 'text/x-gsql', (editor) =>
      getHint(editor, gsqlDictionary)
    );
    CodeMirror.registerHelper('lint', 'text/x-gsql', (text) =>
      this.getLint(text)
    );
    CodeMirror.registerHelper('hint', 'cypher', (editor) =>
      getHint(editor, cypherDictionary)
    );
    CodeMirror.registerHelper('lint', 'cypher', (text) => this.getLint(text));
  }

  /**
   * Retrieve the lint information to display.
   *
   * @private
   * @param {string} text
   * @returns {{
   *     message: string,
   *     severity: string,
   *     from: CodeMirror.Position,
   *     to: CodeMirror.Position,
   *   }[]}
   * @memberof QueryEditorService
   */
  private getLint(text: string): {
    message: string;
    severity: string;
    from: CodeMirror.Position;
    to: CodeMirror.Position;
  }[] {
    const found: {
      message: string;
      severity: string;
      from: CodeMirror.Position;
      to: CodeMirror.Position;
    }[] = [];

    this.compilationError.errors.forEach((error) => {
      found.push({
        message: error.message,
        severity: 'error',
        from: CodeMirror.Pos(error.startLine, error.startColumn),
        to: CodeMirror.Pos(error.endLine, error.endColumn),
      });
    });

    this.compilationError.warnings.forEach((warning) => {
      found.push({
        message: warning.message,
        severity: 'warning',
        from: CodeMirror.Pos(warning.startLine, warning.startColumn),
        to: CodeMirror.Pos(warning.endLine, warning.endColumn),
      });
    });

    return found;
  }

  /**
   * Get The Query Templates from backend
   *
   * @memberof QueryEditorService
   */
  getQueryTemplates(): Observable<QueryTemplates[]> {
    return this.http.get<QueryTemplates[]>(url.graphAlgorithm);
  }

  /**
   * Install the query templates
   *
   * @memberof QueryEditorService
   */
  installQueryTemplates(
    queryNames: string[],
    graphName: string
  ): Observable<InstallQueryTemplatesResult> {
    const targetUrl = HelperFunctions.replacePlaceholderInUrl(
      url.InstallGraphAlgorithm,
      {
        '[GRAPH_NAME]': graphName,
      }
    );

    let params = new HttpParams();

    queryNames.forEach((queryName) => {
      params = params.append('queryName', queryName);
    });
    return this.httpSrc.post(
      HelperFunctions.concatURLAndSearchParams(targetUrl, params)
    );
  }

  /**
   * Log page view of query editor.
   *
   * @memberof QueryEditorService
   */
  logPageView(): void {
    this.analyticsService.logPageView('Query Editor');
  }

  /**
   * Log "Save Query" event to analytics.
   *
   * @param queryName the name of the saved query
   * @memberof QueryEditorService
   */
  logSaveQuery(queryName: string): void {
    this.analyticsService.logSaveQuery(queryName);
  }

  /**
   * Log "Run Query" event to analytics
   *
   * @param queryName name of the query being run
   * @memberof QueryEditorService
   */
  logRunQuery(queryName: string): void {
    this.analyticsService.logRunQuery(queryName);
  }

  /**
   * Log "Install Query" event to analytics
   *
   * @memberof QueryEditorService
   */
  logInstallQuery(): void {
    this.analyticsService.track('Install Query', {
      Type: 'Single',
    });
  }

  /**
   * Log "Install All Queries" event to analytics
   *
   * @memberof QueryEditorService
   */
  logInstallAllQueries(): void {
    this.analyticsService.track('Install Query', {
      Type: 'All',
    });
  }

  /**
   * Log "Install Queries From Data Science Library" event to analytics
   *
   * @memberof QueryEditorService
   */
  logInstallScienceLibrary(queryNames: string[]): void {
    this.analyticsService.track('Install Query', {
      Type: 'Data Science Library',
      'Query Names': queryNames,
    });
  }
}
