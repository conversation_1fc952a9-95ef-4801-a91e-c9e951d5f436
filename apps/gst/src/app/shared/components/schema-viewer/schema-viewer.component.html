<app-toolbar-container
  [config]="toolbarConfig"
  (interaction)="openPopupWindow($event.key)">
  <!-- if user doesn't has the privilege to access, hide the chart -->
  <app-schema-graph-chart
    #schemaGraphContainer
    [hidden]="!hasAccess"
    [graph]="schema"
    [graphEvents]="graphEvents"
    [globalTypes]="globalTypes"
    [showVertexTypes]="showVertexTypes"
    [showEdgeTypes]="showEdgeTypes"
    [isSchemaDesign]="isSchemaDesign">
  </app-schema-graph-chart>
</app-toolbar-container>



<!-- warning text for privilege based control -->
<div *ngIf="!hasAccess" class="main-panel-warning-container mat-input-warn" style="left: 36px;">
  <mat-icon attr.aria-label="{{ 'Warning' | translate }}">warning</mat-icon>
  <span>{{ warningContent }}</span>
</div>

<div
class="visual-information-button"
  *ngIf="!isSchemaDesign && !hidden">
  <button
    mat-mini-fab
    color="accent"
    matTooltip="{{ 'Schema information' | translate }}"
    matTooltipPosition="above"
    attr.aria-label="{{ 'Schema information' | translate }}"
    (click)="openPopupWindow('schemaInfo')">
    <mat-icon attr.aria-label="{{ 'Schema information' | translate }}">description</mat-icon>
  </button>
</div>
<!-- end of search-box for vertex and edge-->

<ng-template #popupWindow>
  <div>
  <mat-toolbar color="primary" class="popup-toolbar">
    <h2 mat-dialog-title>{{ getPopupTitle() | translate }}</h2>
    <p id="dialogDescription" hidden="true" *ngIf="selectedEvent === 'schemaInfo'">
      {{ 'This dialog contains a schema information transcript' | translate }}
    </p>
  </mat-toolbar>

  <!-- Visual Information Content -->
  <mat-dialog-content *ngIf="selectedEvent === 'schemaInfo'">
    <br/>
    <!-- Vertex Information -->
    <h3>{{ 'Vertex information' | translate }}:</h3>
    <mat-list role="list" attr.aria-label="{{ 'Vertex information' | translate }}" >
      <ng-container *ngFor="let vertex of graphRef.current.getNodes()">
        <mat-list-item *ngIf="vertex.type !== 'gs_file' && vertex.type !== 'gs_s3' && vertex.type !== 'gs_url' && vertex.type !== 'gs_virtual'">
          <div matSubHeader matLine role="listitem">{{ ('Vertex' | translate) + ': ' + vertex.type }}</div>
          <div matLine *ngFor="let attr of vertex.attrs | keyvalue">
            <div matLine role="listitem">{{ attr.key + ': ' + attr.value }}</div>
          </div>
          <div matLine *ngIf="globalTypes">
            <div matline>
              <div *ngIf="globalTypes.indexOf(vertex.type) !== -1; else notLocal">{{ 'Is a global type' | translate }}</div>
              <ng-template #notLocal matLine>
                <div matLine role="listitem">{{ 'Not a global type' | translate }}</div>
              </ng-template>
            </div>
          </div>
          <hr matLine />
        </mat-list-item>
      </ng-container>
    </mat-list>
    <!-- End of Vertex Information -->

      <!-- Edge Information -->
      <h3>{{ 'Edge information' | translate }}:</h3>
      <mat-list role="list" attr.aria-label="{{ 'Edge information' | translate }}" >
        <ng-container *ngFor="let edge of graphRef.current.getLinks()">
          <mat-list-item *ngIf="!edge.type.includes('gs_data_source_map')">
            <div matSubHeader matLine role="listitem">{{ ('Edge' | translate) + ': ' + edge.type }}</div>
            <div matLine role="listitem">{{ ('Source' | translate) + ': ' + edge.source.type }}</div>
            <div matLine role="listitem">{{ ('Target' | translate) + ': ' + edge.target.type }}</div>
            <div matLine *ngFor="let attr of edge.attrs | keyvalue">
              <div matLine role="listitem">{{ attr.key + ': ' + attr.value }}</div>
            </div>
            <div matLine *ngIf="globalTypes">
              <div matline>
                <div role= "listitem" *ngIf="globalTypes.indexOf(edge.type) !== -1; else notLocal">{{ 'Is a global type' | translate }}</div>
                <ng-template #notLocal matLine>
                  <div matLine role="listitem">{{ 'Not a global type' | translate }}</div>
                </ng-template>
              </div>
            </div>
            <hr matLine/>
          </mat-list-item>
        </ng-container>
      </mat-list>
      <!-- Edge Information -->
    </mat-dialog-content>
    <!-- End of Visual Information Content -->

    <!-- Search Vertex or Edge in Popup -->
    <mat-dialog-content *ngIf="selectedEvent === 'search'" class="form-container">
      <mat-form-field class="max-width">
        <input
          matInput
          (input)="searchVertexAndEdge()"
          placeholder="{{ 'Search for vertex or edge' | translate }}"
          [(ngModel)]="searchText"
          [matAutocomplete]="autoSearch"
          attr.aria-label="{{ 'Search for vertex or edge' | translate }}">
      </mat-form-field>
      <mat-error *ngIf="searchedEdgeName.length === 0 && searchText && searchedVertexName.length === 0">
        {{ 'No result found' | translate }}
      </mat-error>
      <mat-autocomplete #autoSearch="matAutocomplete">
        <mat-option
          *ngFor="let option of searchedVertexName.concat(searchedEdgeName)"
          [value]="option"
          [innerHTML]="emphasize(option)">
        </mat-option>
      </mat-autocomplete>
    </mat-dialog-content>
    <!-- End of Search Vertex or Edge in Popup -->

    <mat-dialog-actions align="end">
      <button mat-button mat-dialog-close (click)="onClosePopup()">
        {{ 'CLOSE' | translate }}
      </button>
      <button
        mat-button
        *ngIf="selectedEvent === 'search'"
        color="primary"
        [disabled]="!searchText || invalidSearch()"
        (click)="searchWithSmallScreen()">
        {{ 'SEARCH' | translate }}
      </button>
    </mat-dialog-actions>
  </div>
</ng-template>
