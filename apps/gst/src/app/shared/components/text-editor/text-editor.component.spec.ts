import { ComponentFixture, discardPeriodicTasks, fakeAsync, TestBed, tick, waitForAsync } from '@angular/core/testing';

import { TextEditorComponent, TEXT_EDITOR_VALUE_ACCESSOR } from './text-editor.component';
import { TextEditorService } from './text-editor.service';

describe('TextEditorComponent', () => {
  let component: TextEditorComponent;
  let fixture: ComponentFixture<TextEditorComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [TextEditorComponent]
    })
      .overrideComponent(TextEditorComponent, {
        set: {
          providers: [
            {
              provide: TextEditorService,
              useFactory: () => ({
                getConfig: () => { }
              })
            }
          ]
        }
      })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(TextEditorComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should be created with default config', () => {
    expect(component).toBeTruthy();
    expect((<any>component).textEditor).toBeDefined();
  });

  it('should be created with input config', () => {
    component.config = { readOnly: true };
    expect(component).toBeTruthy();
    expect((<any>component).textEditor).toBeDefined();
  });

  it('should use existing component', () => {
    expect(TEXT_EDITOR_VALUE_ACCESSOR.useExisting()).toEqual(TextEditorComponent);
  });

  it('should return the inner value', () => {
    expect(component.value).toBe('');
  });

  it('should set new value', () => {
    spyOn(<any>component, 'onChange');
    component.value = 'abc';

    expect(component.value).toBe('abc');
    expect((<any>component).onChange).toHaveBeenCalled();
  });

  it('should not set new value if it is the same as inner value', () => {
    spyOn(<any>component, 'onChange');
    component.value = '';

    expect(component.value).toBe('');
    expect((<any>component).onChange).not.toHaveBeenCalled();
  });

  it('should update new options', () => {
    spyOn((<any>component).textEditor, 'setOption');
    component.updateOption('readonly', false);
    expect((<any>component).textEditor.setOption).toHaveBeenCalledWith('readonly', false);
  });

  it('should perform lint', () => {
    spyOn((<any>component).textEditor, 'performLint');
    component.performLint();
    expect((<any>component).textEditor.performLint).toHaveBeenCalled();
  });

  it('should trigger to load hidden line', fakeAsync(() => {
    spyOn((<any>component).textEditor, 'getScrollInfo').and.returnValue({ left: 1, top: 1 });
    spyOn((<any>component).textEditor, 'scrollTo');
    component.triggerLoad();

    tick(1);
    expect((<any>component).textEditor.scrollTo).toHaveBeenCalledWith(1, 1);
    discardPeriodicTasks();
  }));

  describe('should set new value to text editor and clear history', () => {
    let doc: any;

    beforeEach(() => {
      doc = {
        clearHistory: () => { }
      };
    });

    it('with a value', () => {
      spyOn((<any>component).textEditor, 'setValue');
      spyOn((<any>component).textEditor, 'getDoc').and.returnValue(doc);
      spyOn(doc, 'clearHistory');

      component.writeValue('abc');
      expect((<any>component).textEditor.setValue).toHaveBeenCalledWith('abc');
      expect(doc.clearHistory).toHaveBeenCalled();
    });

    it('with no value', () => {
      spyOn((<any>component).textEditor, 'setValue');
      spyOn((<any>component).textEditor, 'getDoc').and.returnValue(doc);
      spyOn(doc, 'clearHistory');

      component.writeValue(undefined);
      expect((<any>component).textEditor.setValue).toHaveBeenCalledWith('');
      expect(doc.clearHistory).toHaveBeenCalled();
    });
  });

  it('should not set new value if there is no text editor', () => {
    const doc = {
      clearHistory: () => { }
    };
    spyOn((<any>component).textEditor, 'setValue');
    spyOn((<any>component).textEditor, 'getDoc').and.returnValue(doc);
    spyOn(doc, 'clearHistory');

    (<any>component).textEditor = undefined;
    component.writeValue('abc');
    expect(doc.clearHistory).not.toHaveBeenCalled();
  });

  it('should implement registerOnChange', () => {
    component.registerOnChange((value: any) => { });
    expect(typeof (<any>component).onChange).toBe('function');
  });

  it('should implement registerOnTouched', () => {
    component.registerOnTouched(() => undefined);
    expect(typeof (<any>component).onTouched).toBe('function');
  });

  it('should set and emit new value from the text editor', fakeAsync(() => {
    spyOn((<any>component).textEditor, 'on').and.callFake((ev, fn) => {
      if (ev === 'change') {
        fn();
      }
    });
    spyOn((<any>component).textEditor, 'getValue').and.returnValue('abc');

    component.change.subscribe(event => expect(event.value).toBe('abc'));
    (<any>component).handleTextEditorEvent();
    tick(2000);
    expect(component.value).toBe('abc');
  }));

  it('should show hint on input read', fakeAsync(() => {
    const editor = {
      state: { completionActive: false },
      showHint: () => { }
    };
    spyOn(editor, 'showHint');
    spyOn((<any>component).textEditor, 'on').and.callFake((ev, fn) => {
      if (ev === 'inputRead') {
        addEventListener('keydown', () => fn([editor]));
        dispatchEvent(new KeyboardEvent('keydown', { key: 'a' }));
      }
    });

    (<any>component).handleTextEditorEvent();
    tick(2000);
    expect(editor.showHint).toHaveBeenCalled();
    discardPeriodicTasks();
  }));
});
