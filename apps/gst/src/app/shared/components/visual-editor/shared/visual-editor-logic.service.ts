import { Injectable } from '@angular/core';
import { ExternalGraph, ExternalNode, ExternalLink } from '@tigergraph/tools-models/gvis/insights';
import {
  cloneDeep, differenceWith, isEqual,
  reverse, unionWith, uniqWith, assign
} from 'lodash';

import { VertexTypeAndId } from '@app/shared/components/vertex-type-and-id-input';
import { History } from '@tigergraph/tools-models/history';
import { Edge, Graph, Vertex } from '@tigergraph/tools-models/topology';
import { FormatValidator, ValidateResult } from '@tigergraph/tools-models/utils';
import { HelperFunctions } from '@app/shared/utils';
import { DataType } from '@tigergraph/tools-models/data';
import { createOfflineCompileUrlResolver } from '@angular/compiler';
import { getNodeID } from '@tigergraph/tools-models';

/**
 * Logic service for Visual Editor component.
 *
 * @export
 * @class VisualEditorLogicService
 */
@Injectable()
export class VisualEditorLogicService {
  // Graph schema.
  public schema: Graph;
  // Graph object.
  public graph: ExternalGraph;
  // <key = edge type name, value = list of discriminator attributes names>
  public edgeDiscriminatorMap: {
    [key: string]: string[]
  };
  // Records the graph object history.
  private history: History<ExternalGraph>;
  // Difference between new graph and current graph.
  private diff = 1;

  constructor() {
    // Init an empty schema
    this.schema = new Graph();
    this.history = new History<ExternalGraph>();
    this.graph = this.initializeGraph();
    this.edgeDiscriminatorMap = {};
    // At the beginning, always put an empty graph to avoid undefined redo / undo issue.
    this.history.update(this.graph);
  }

  /**
   * Update the schema.
   *
   * @memberof VisualEditorLogicService
   */
  setSchema(schema: Graph) {
    this.schema = schema;
    this.edgeDiscriminatorMap = this.getEdgeDiscriminatorMap;
    // Clear history and set the first state.
    this.graph = this.initializeGraph();
    this.history.clear();
    this.history.update(this.graph);
  }

  /**
   * Get the graph object and the difference between new and old graph.
   *
   * @returns {{graph: ExternalGraph, diff: number}}
   * @memberof VisualEditorLogicService
   */
  getGraph(): { graph: ExternalGraph, diff: number } {
    return { graph: this.cloneGraph(), diff: this.diff };
  }

  /**
   * Return a vertex from the graph with given type and id.
   *
   * @param {string} vertexType
   * @param {string} vertexId
   * @returns {ExternalNode}
   * @memberof VisualEditorLogicService
   */
  getVertex(vertexType: string, vertexId: string): ExternalNode {
    const vertex = this.graph.nodes.find(
      node => node.type === vertexType && node.id === vertexId
    );
    if (vertex !== undefined) {
      return cloneDeep(vertex);
    }
  }

  /**
   * Return an edge from the graph with given type, source and target.
   *
   * @param {string} edgeType
   * @returns {ExternalLink}
   * @memberof VisualEditorLogicService
   */
  getEdge(
    edgeType: string,
    source: { type: string, id: string },
    target: { type: string, id: string },
    attrs: {
      [key: string]: string | number | boolean | Object
    }
  ): ExternalLink {
    const edge = this.graph.links.find(link => {
      return link.type === edgeType &&
        link.source.type === source.type && link.source.id === source.id &&
        link.target.type === target.type && link.target.id === target.id &&
        this.areDiscriminatorAttributesEqual(edgeType, attrs, link.attrs);
    });
    if (edge !== undefined) {
      return cloneDeep(edge);
    }
  }

  /**
   * Return all vertex types.
   *
   * @returns {string[]}
   * @memberof VisualEditorLogicService
   */
  getAllVertexTypes(): string[] {
    return this.schema.getAllVertexTypes();
  }

  /**
   * Return all edge types.
   *
   * @returns {string[]}
   * @memberof VisualEditorLogicService
   */
  getAllEdgeTypes(): string[] {
    return this.schema.getAllEdgeTypes();
  }

  /**
   * Return the edge types that match the two input vetex types as their two endpoint
   * vertex types.
   *
   * @param {string} srcVType
   * @param {string} tgtVType
   * @returns {string[], boolean}
   * @memberof VisualEditorLogicService
   */
  getMatchEdgeTypes(srcVType: string, tgtVType: string): {
    matchedEdgeTypes: string[],
    shouldReverseEdge: boolean
  } {
    const allEdgeTypes = this.schema.getAllEdgeTypes();
    let matchedEdgeTypes = this.getMatchEdgeTypesHelper(allEdgeTypes, srcVType, tgtVType);
    let shouldReverseEdge = false;
    if (matchedEdgeTypes.length === 0) {
      matchedEdgeTypes = this.getMatchEdgeTypesHelper(allEdgeTypes, tgtVType, srcVType);
      if (matchedEdgeTypes.length > 0) {
        shouldReverseEdge = true;
      }
    }

    return { matchedEdgeTypes, shouldReverseEdge };
  }

  /**
   * Return the edge types that match the from and to vetex type.
   *
   * @param {string[]} allEdgeTypes
   * @param {string} srcVType
   * @param {string} tgtVType
   * @returns {string[]}
   * @memberof VisualEditorLogicService
   */
  getMatchEdgeTypesHelper(allEdgeTypes: string[], srcVType: string, tgtVType: string): string[] {
    return allEdgeTypes.filter(edgeType => {
      const edges = this.getSchemaEdge(edgeType);
      // TODO: Currently, for an undirected edge type defined as V1-(e)-V2, when inserting
      // the edge the API call has to be /V1/id1/e/V2/id2. In the future when RESTPP support
      // also inserting as /V2/id2/e/V1/id1, we can loose the constriant here.
      return edges.fromToVertexTypePairs.filter(
        pair => pair.from === srcVType && pair.to === tgtVType
      ).length > 0;
    });
  }

  /**
   * Return a vertex from schema with a given name.
   *
   * @param {string} name
   * @returns {Vertex}
   * @memberof VisualEditorLogicService
   */
  getSchemaVertex(name: string): Vertex {
    const vertex = this.schema.getVertex(name);
    if (vertex !== undefined) {
      return vertex.clone();
    }
  }

  /**
   * Return an edge from schema with a given name.
   *
   * @param {string} name
   * @param {string} checkReverse
   * @returns {Edge}
   * @memberof VisualEditorLogicService
   */
  getSchemaEdge(name: string, checkReverse?: boolean): Edge {
    const edge = this.schema.getEdge(name, checkReverse);

    if (edge !== undefined) {
      return edge.clone();
    }
  }

  /**
   * Forward to next graph result.
   *
   * @returns {{graph: ExternalGraph, diff: number}}
   * @memberof VisualEditorLogicService
   */
  redo(): { graph: ExternalGraph, diff: number } {
    const curGraph = this.cloneGraph();
    this.graph = this.history.redo();
    this.calculateDiff(curGraph, this.graph);
    return { graph: this.graph, diff: this.diff };
  }

  /**
   * Backward to previous graph result.
   *
   * @returns {{graph: ExternalGraph, diff: number}}
   * @memberof VisualEditorLogicService
   */
  undo(): { graph: ExternalGraph, diff: number } {
    const curGraph = this.cloneGraph();
    this.graph = this.history.undo();
    this.calculateDiff(curGraph, this.graph);
    return { graph: this.graph, diff: this.diff };
  }

  /**
   * Add new data to graph.
   *
   * @param {ExternalGraph} newGraph
   * @memberof VisualEditorLogicService
   */
  addData(newGraph: ExternalGraph) {
    if (!newGraph) {
      return;
    }

    // add missing nodes
    const nodeCache = new Set<string>();
    newGraph.nodes.forEach((node) => {
      nodeCache.add(getNodeID(node));
    });
    newGraph.links.forEach((link) => {
      if (!nodeCache.has(getNodeID(link.source))) {
        newGraph.nodes.push(link.source);
        nodeCache.add(getNodeID(link.source));
      }
      if (!nodeCache.has(getNodeID(link.target))) {
        newGraph.nodes.push(link.target);
        nodeCache.add(getNodeID(link.target));
      }
    });

    const graph = this.cloneGraph();
    graph.nodes = this.unionVertexArray(newGraph.nodes, graph.nodes);
    graph.links = this.unionEdgeArray(newGraph.links, graph.links);
    this.calculateDiff(this.graph, graph);
    this.graph = this.history.update(graph);
  }

  /**
   * Clear the current data without updating history.
   *
   * @memberof VisualEditorLogicService
   */
  clearData() {
    this.graph = this.initializeGraph();
  }

  clearDataWithHistory() {
    this.clearData();
    this.graph = this.history.update(this.graph);
  }

  /**
   * Add a vertex to the graph.
   *
   * @param {ExternalNode} vertex
   * @param {boolean} [update=true]
   * @returns {ValidateResult}
   * @memberof VisualEditorLogicService
   */
  addVertex(vertex: ExternalNode, update: boolean = true): ValidateResult {
    const graph = this.cloneGraph();
    const cloneVertex = cloneDeep(vertex);
    graph.nodes = this.unionVertexArray([cloneVertex], graph.nodes);
    return this.semanticCheckAndUpdateGraph(graph, cloneVertex, true, update);
  }

  /**
   * Add an edge to the graph.
   *
   * @param {ExternalLink} edge
   * @param {boolean} [update=true]
   * @returns {ValidateResult}
   * @memberof VisualEditorLogicService
   */
  addEdge(edge: ExternalLink, update: boolean = true): ValidateResult {
    const graph = this.cloneGraph();
    const cloneEdge = cloneDeep(edge);
    graph.links = this.unionEdgeArray([cloneEdge], graph.links);
    return this.semanticCheckAndUpdateGraph(graph, cloneEdge, false, update);
  }

  /**
   * Update a vertex.
   *
   * @param {ExternalNode} vertex
   * @param {boolean} [update=true]
   * @returns {ValidateResult}
   * @memberof VisualEditorLogicService
   */
  updateVertex(vertex: ExternalNode, update: boolean = true): ValidateResult {
    const graph = this.cloneGraph();
    const cloneVertex = cloneDeep(vertex);

    // Update the vertex.
    for (let i = 0; i < graph.nodes.length; i++) {
      if (
        graph.nodes[i].type === vertex.type &&
        graph.nodes[i].id === vertex.id
      ) {
        graph.nodes[i] = cloneVertex;
        break;
      }
    }

    return this.semanticCheckAndUpdateGraph(graph, cloneVertex, true, update);
  }

  /**
   * Update an edge.
   *
   * @param {ExternalLink} edge
   * @param {boolean} [update=true]
   * @returns {ValidateResult}
   * @memberof VisualEditorLogicService
   */
  updateEdge(
    edge: ExternalLink,
    update: boolean = true
  ): ValidateResult {
    const graph = this.cloneGraph();
    const cloneEdge = cloneDeep(edge);

    // Update the edge.
    for (let i = 0; i < graph.links.length; i++) {
      if (
        graph.links[i].type === edge.type &&
        graph.links[i].source.type === edge.source.type &&
        graph.links[i].source.id === edge.source.id &&
        graph.links[i].target.type === edge.target.type &&
        graph.links[i].target.id === edge.target.id &&
        this.areDiscriminatorAttributesEqual(edge.type, edge.attrs, graph.links[i].attrs)
      ) {
        graph.links[i] = cloneEdge;
        break;
      }
    }

    return this.semanticCheckAndUpdateGraph(graph, cloneEdge, false, update);
  }

  /**
   * Refresh the graph exploration.
   *
   * @returns {string}
   * @memberof VisualEditorLogicService
   */
  getRefreshExplorationQuery(): string {
    const graph = this.cloneGraph();
    let allVertices = '';
    let allEdges = '';

    // Collect all vertices. Not only vertices in graph, but also edge endpoints.
    let vertexIdTypeList: VertexTypeAndId[] = graph.nodes.map(
      node => ({ type: node.type, id: node.id })
    );
    graph.links.forEach(link => {
      vertexIdTypeList.push(link.source);
      vertexIdTypeList.push(link.target);
    });

    vertexIdTypeList = uniqWith(
      vertexIdTypeList,
      (a: VertexTypeAndId, b: VertexTypeAndId) => a.type === b.type && a.id === b.id
    );

    vertexIdTypeList.forEach(node =>
      allVertices += `  @@allVertices += VertexTuple("${node.type}", "${
        HelperFunctions.escapeGSQLString(node.id)}");\n`
    );

    graph.links.forEach(link =>
      allEdges += `  @@allEdges += EdgeTuple(` +
        `"${link.source.type}#${HelperFunctions.escapeGSQLString(link.source.id)}",` +
        `"${HelperFunctions.escapeGSQLString(this.getDiscriminatorValuesString(link))}",` +
        `"${link.target.type}#${HelperFunctions.escapeGSQLString(link.target.id)}", "${link.type}");\n`
    );

    let ifStmt = '';

    this.schema.edgeTypes.forEach((schemaEdge, index) => {
      if (index === 0) {
        ifStmt += `                   IF e.type == "${schemaEdge.name}" THEN\n`;
      } else {
        ifStmt += `                   ELSE IF e.type == "${schemaEdge.name}" THEN\n`;
      }

      if (!schemaEdge.discriminatorCount) {
        ifStmt += `                     @@edgeToAttributes += (e -> "")\n`;
        return;
      }

      const edgeAttrs = schemaEdge.attributes;
      let edgeConcatenation = '';
      edgeAttrs.forEach((attr, i) => {
        if (!attr.isDiscriminator) {
          return;
        }
        if (attr.type.getDataType() === DataType.Datetime) {
          edgeConcatenation += `to_string(datetime_format(e.${attr.name}))`;
        } else {
          edgeConcatenation += `to_string(e.${attr.name})`;
        }

        if (i < schemaEdge.discriminatorCount - 1) {
          edgeConcatenation += ` + "#" + `;
        }
      });
      ifStmt += `                     @@edgeToAttributes += (e -> ${edgeConcatenation})\n`;
    });
    ifStmt += `                   END;\n`;

    const queryContent =
    `INTERPRET QUERY () FOR GRAPH ${this.schema.name} SYNTAX V1 {
      TYPEDEF TUPLE <vType string, vId string> VertexTuple;
      TYPEDEF TUPLE <srcKey string, discriminatorValues string, tgtKey string, eType string> EdgeTuple;
      SetAccum<VertexTuple> @@allVertices;
      MapAccum<vertex, string> @@vertexToKey;
      MapAccum<edge, string> @@edgeToAttributes;
      SetAccum<vertex> @@vertexResult;
      SetAccum<string> @@ids;
      SetAccum<vertex> @@oneVertex;
      SetAccum<EdgeTuple> @@allEdges;
      SetAccum<edge> @@edgeResult;\n` +
      allVertices +
      `foreach vertexTuple in @@allVertices do
        @@ids.clear();
        @@ids += vertexTuple.vId;
        @@oneVertex = to_vertex_set(@@ids, vertexTuple.vType);
        @@vertexResult += @@oneVertex;
        foreach v in @@oneVertex do
           @@vertexToKey += (v -> vertexTuple.vType + "#" + vertexTuple.vId);
        end;
      end;

      VertexResult = { @@vertexResult };
      PRINT VertexResult;\n` +
      `result = SELECT tgt FROM VertexResult:src -(:e)-> :tgt
                WHERE @@vertexResult.contains(tgt)
                ACCUM\n` +
      ifStmt +
      allEdges +
      `result = SELECT tgt FROM VertexResult:src -(:e)-> :tgt
               WHERE @@vertexResult.contains(tgt)
               ACCUM if (
                @@allEdges.contains(
                  EdgeTuple(
                    @@vertexToKey.get(src),
                    @@edgeToAttributes.get(e),
                    @@vertexToKey.get(tgt),
                    e.type))
                  ) THEN
                    @@edgeResult += e
               end;
      PRINT @@edgeResult;
    }`;

    return queryContent;
  }

  /**
   * Only retain selected vertices and edges.
   *
   * @param {{type: string, id: string}[]} vertices
   * @param {{
   *       type: string,
   *       source: {
   *         type: string,
   *         id: string
   *       },
   *       target: {
   *         type: string,
   *         id: string
   *       }
   *     }[]} edges
   * @memberof VisualEditorLogicService
   */
  retain(
    vertices: { type: string, id: string }[],
    edges: {
      type: string,
      source: {
        type: string,
        id: string
      },
      target: {
        type: string,
        id: string
      },
      attrs?: {
        [key: string]: string | number | boolean | Object
      }
    }[]
  ) {
    const graph = this.cloneGraph();
    const result = this.initializeGraph();

    // Record edge source and target vertices
    const endpointVertices = new Map<string, Set<string>>();

    // Retain the vertices.
    vertices.forEach((vertex) => {
      for (let i = 0; i < graph.nodes.length; i++) {
        if (
          graph.nodes[i].type === vertex.type &&
          graph.nodes[i].id === vertex.id
        ) {
          result.nodes = result.nodes.concat(graph.nodes.splice(i, 1));
          break;
        }
      }
    });

    // Retain the edges.
    edges.forEach((edge) => {
      for (let i = 0; i < graph.links.length; i++) {
        if (
          graph.links[i].type === edge.type &&
          graph.links[i].source.type === edge.source.type &&
          graph.links[i].source.id === edge.source.id &&
          graph.links[i].target.type === edge.target.type &&
          graph.links[i].target.id === edge.target.id &&
          this.areDiscriminatorAttributesEqual(edge.type, graph.links[i].attrs, edge.attrs)
        ) {
          // Retain the edge itself
          result.links = result.links.concat(graph.links.splice(i, 1));
          // Collect the edge source and target vertex
          if (!endpointVertices.has(edge.source.type)) {
            endpointVertices.set(edge.source.type, new Set<string>());
          }
          if (!endpointVertices.has(edge.target.type)) {
            endpointVertices.set(edge.target.type, new Set<string>());
          }
          endpointVertices.get(edge.source.type).add(edge.source.id);
          endpointVertices.get(edge.target.type).add(edge.target.id);
          break;
        }
      }
    });

    // Retain the edge endpoint vertices
    endpointVertices.forEach((ids, vType) => {
      ids.forEach((vid) => {
        for (let i = 0; i < graph.nodes.length; i++) {
          if (graph.nodes[i].type === vType && graph.nodes[i].id === vid) {
            result.nodes = result.nodes.concat(graph.nodes.splice(i, 1));
            break;
          }
        }
      });
    });

    this.calculateDiff(this.graph, result);
    this.graph = this.history.update(result);
  }

  /**
   * Remove selected vertices and edges.
   *
   * @param {{type: string, id: string}[]} vertices
   * @param {string[]} edges
   *
   * @memberof VisualEditorLogicService
   */
  remove(
    vertices: { type: string, id: string }[],
    edges: {
      type: string,
      source: {
        type: string,
        id: string
      },
      target: {
        type: string,
        id: string
      },
      attrs?: {
        [key: string]: string | number | boolean | Object
      }
    }[]
  ) {
    const graph = this.cloneGraph();

    // Remove the edges.
    edges.forEach((edge) => {
      for (let i = 0; i < graph.links.length; i++) {
        if (
          graph.links[i].type === edge.type &&
          graph.links[i].source.type === edge.source.type &&
          graph.links[i].source.id === edge.source.id &&
          graph.links[i].target.type === edge.target.type &&
          graph.links[i].target.id === edge.target.id &&
          this.areDiscriminatorAttributesEqual(edge.type, graph.links[i].attrs, edge.attrs)
        ) {
          graph.links.splice(i, 1);
          break;
        }
      }
    });

    // Remove the vertices.
    vertices.forEach((vertex) => {
      // Remove the vertex itself.
      for (let i = 0; i < graph.nodes.length; i++) {
        if (
          graph.nodes[i].type === vertex.type &&
          graph.nodes[i].id === vertex.id
        ) {
          graph.nodes.splice(i, 1);
          break;
        }
      }
      // Remove all edges starting from or pointing at this vertex type.
      for (let i = 0; i < graph.links.length;) {
        if (
          (
            graph.links[i].source.type === vertex.type &&
            graph.links[i].source.id === vertex.id
          ) ||
          (
            graph.links[i].target.type === vertex.type &&
            graph.links[i].target.id === vertex.id
          )
        ) {
          graph.links.splice(i, 1);
        } else {
          i++;
        }
      }
    });

    this.calculateDiff(this.graph, graph);
    this.graph = this.history.update(graph);
  }

  /**
   * Remove all vertices and edges and update history.
   *
   * @memberof VisualEditorLogicService
   */
  removeAll() {
    const graph = this.initializeGraph();
    this.calculateDiff(this.graph, graph);
    this.graph = this.history.update(graph);
  }

  /**
   * Return an initialization of a graph object.
   *
   * @private
   * @returns {ExternalGraph}
   * @memberof VisualEditorLogicService
   */
  private initializeGraph(): ExternalGraph {
    return { nodes: [], links: [] };
  }

  /**
   * Clone the graph object.
   *
   * @private
   * @returns {ExternalGraph}
   * @memberof VisualEditorLogicService
   */
  private cloneGraph(): ExternalGraph {
    return cloneDeep(this.graph);
  }

  /**
   * Creates an object which maps each edge type to a list of
   * its discriminator attributes.
   *
   * @returns {{
   *  [key: string]: string[]
   * }}
   */
  private get getEdgeDiscriminatorMap(): {
    [key: string]: string[]
  } {
    const edgeDiscriminatorMap: {
      [key: string]: string[]
    } = {};
    this.schema.edgeTypes.forEach(edgeType => {
      edgeDiscriminatorMap[edgeType.name] = [];

      if (!edgeType.compositeDiscriminator) {
        return;
      }

      edgeDiscriminatorMap[edgeType.name] = edgeType.compositeDiscriminator;
    });

    return edgeDiscriminatorMap;
  }

  /**
   * Combine two vertex arrays and remove duplicate.
   * Combination is in order so the first duplicate will remain.
   *
   * @private
   * @param {ExternalNode[]} vertexArray
   * @param {ExternalNode[]} newVertexArray
   * @returns {ExternalNode[]}
   * @memberof VisualEditorLogicService
   */
  private unionVertexArray(
    vertexArrayA: ExternalNode[],
    vertexArrayB: ExternalNode[]
  ): ExternalNode[] {
    const vertexComparison =
      (a: ExternalNode, b: ExternalNode) => a.type === b.type && a.id === b.id;

    // Reverse the array so latest element will be kept with unique operation.
    let cloneA = reverse(cloneDeep(vertexArrayA));
    let cloneB = reverse(cloneDeep(vertexArrayB));
    // Remove duplications.
    cloneA = uniqWith(cloneA, vertexComparison);
    cloneB = uniqWith(cloneB, vertexComparison);

    // Merge the vertices, and use extend instead of replace.
    const vertexSort = (a: ExternalNode, b: ExternalNode) => {
      if (a.type === b.type && a.id === b.id) {
        return 0;
      }
      if (a.type > b.type || (a.type === b.type && a.id > b.id)) {
        return 1;
      }
      return -1;
    };
    cloneA.sort(vertexSort);
    cloneB.sort(vertexSort);

    const result: ExternalNode[] = [];
    const lenA = cloneA.length;
    const lenB = cloneB.length;
    let i = 0, j = 0;
    while (i < lenA || j < lenB) {
      if (i === lenA) {
        result.push(cloneB[j++]);
        continue;
      }
      if (j === lenB) {
        result.push(cloneA[i++]);
        continue;
      }
      const comp = vertexSort(cloneA[i], cloneB[j]);
      switch (comp) {
        case 0: {
          if (cloneB[j].attrs) {
            cloneA[i].attrs = assign(cloneB[j].attrs, cloneA[i].attrs);
          }
          result.push(cloneA[i]);
          i++;
          j++;
          continue;
        }
        case 1: {
          result.push(cloneB[j++]);
          continue;
        }
        case -1: {
          result.push(cloneA[i++]);
          continue;
        }
      }
    }

    return result;
  }

  /**
   * Combine two edge arrays and remove duplicate.
   * Combination is in order so the first duplicate will remain.
   *
   * @private
   * @param {ExternalLink[]} edgeArray
   * @param {ExternalLink[]} newEdgeArray
   * @returns {ExternalLink[]}
   * @memberof VisualEditorLogicService
   */
  private unionEdgeArray(
    edgeArrayA: ExternalLink[],
    edgeArrayB: ExternalLink[]
  ): ExternalLink[] {
    const edgeComparison = (a: ExternalLink, b: ExternalLink) =>
    a.type === b.type && (
      (
        a.source.id === b.source.id && a.source.type === b.source.type &&
        a.target.id === b.target.id && a.target.type === b.target.type &&
        this.areDiscriminatorAttributesEqual(a.type, a.attrs, b.attrs)
      ) ||
      (
        a.source.id === b.target.id && a.source.type === b.target.type &&
        a.target.id === b.source.id && a.target.type === b.source.type &&
        a.directed === false && b.directed === false &&
        this.areDiscriminatorAttributesEqual(a.type, a.attrs, b.attrs)
      )
    );

    // Reverse the array so latest element will be kept with unique operation.
    let cloneA = reverse(cloneDeep(edgeArrayA));
    let cloneB = reverse(cloneDeep(edgeArrayB));
    // Remove duplications.
    cloneA = uniqWith(cloneA, edgeComparison);
    cloneB = uniqWith(cloneB, edgeComparison);

    return unionWith(cloneA, cloneB, edgeComparison);
  }

  /**
   * Check if discriminator attributes are equal to the input attributes.
   *
   * @param {string} edgeTypeName
   * @param {{
   *  [key: string]: string | number | boolean | Object
   * }} inputAttrs
   * @param {{
   *  [key: string]: string | number | boolean | Object
   * }} linkAttrs
   * @returns {boolean}
   */
  private areDiscriminatorAttributesEqual(
    edgeTypeName: string,
    inputAttrs: {
      [key: string]: string | number | boolean | Object
    },
    linkAttrs: {
      [key: string]: string | number | boolean | Object
    }
  ): boolean {
    const edgeType = this.getSchemaEdge(edgeTypeName, true);
    const discriminatorAttrNames = edgeType.discriminatorCount > 0 ?
      edgeType.compositeDiscriminator : [];

    return discriminatorAttrNames.every(
      attrName => inputAttrs[attrName] === linkAttrs[attrName]
    );
  }

  /**
   * Calculate the difference between current graph and new graph.
   * The difference is the percentage of the number of vertices and edges that change
   * compare with the length of new graph.
   *
   * @private
   * @param {ExternalGraph} curGraph
   * @param {ExternalGraph} newGraph
   * @memberof VisualEditorLogicService
   */
  private calculateDiff(curGraph: ExternalGraph, newGraph: ExternalGraph) {
    const vertexDiff = differenceWith(
      newGraph.nodes.map(node => node.type + '#' + node.id),
      curGraph.nodes.map(node => node.type + '#' + node.id),
      isEqual
    ).length;
    const edgeDiff = differenceWith(
      newGraph.links.map(link =>
        link.type + '#' +
        link.source.type + '#' +
        link.source.id + '#' +
        link.target.type + '#' +
        link.target.id + '#' + this.getDiscriminatorValuesString(link)
      ),
      curGraph.links.map(link =>
        link.type + '#' +
        link.source.type + '#' +
        link.source.id + '#' +
        link.target.type + '#' +
        link.target.id + '#' + this.getDiscriminatorValuesString(link)
      ),
      isEqual
    ).length;

    this.diff = (vertexDiff + edgeDiff) / (newGraph.nodes.length + newGraph.links.length);
    this.diff = isNaN(this.diff) ? 0 : this.diff;
  }

  /**
   * Get the string value of the discriminator attributes
   * concatenated with the '#' character.
   *
   * @param {ExternalLink} link
   * @returns {string}
   */
  private getDiscriminatorValuesString(link: ExternalLink): string {
    const schemaEdge = this.getSchemaEdge(link.type, true);
    if (!schemaEdge.discriminatorCount) {
      return '';
    }

    return schemaEdge.compositeDiscriminator.map(attrName => link.attrs[attrName]).join('#');
  }

  /**
   * Semantic check one element in the graph. If pass, update the graph to the new status.
   *
   * @private
   * @param {ExternalGraph} graph
   * @param {(ExternalNode | ExternalLink)} element
   * @param {boolean} isVertex
   * @param {boolean} update
   * @returns {ValidateResult}
   * @memberof VisualEditorLogicService
   */
  private semanticCheckAndUpdateGraph(
    graph: ExternalGraph,
    element: ExternalNode | ExternalLink,
    isVertex: boolean,
    update: boolean
  ): ValidateResult {
    let semanticCheckResult: ValidateResult;
    const attributeTypesMap = new Map<string, string>();

    if (isVertex) {
      // Check vertex id cannot be empty string.
      if ((<ExternalNode>element).id === '') {
        return {
          success: false,
          message: 'Vertex id cannot be empty.'
        };
      }

      const schemaVertex = this.getSchemaVertex(element.type);
      // Add type for each attribute.
      schemaVertex.attributes.forEach(
        attr => attributeTypesMap.set(attr.name, attr.type.name)
      );
      // Add type for primary id.
      attributeTypesMap.set(
        schemaVertex.primaryId.name,
        schemaVertex.primaryId.type.name
      );

      const vertexAttrs = cloneDeep(element.attrs);
      // Add the value for primary id.
      vertexAttrs[schemaVertex.primaryId.name] = (<ExternalNode>element).id;

      semanticCheckResult = this.checkAttribute(vertexAttrs, attributeTypesMap);
    } else {
      const schemaEdge = this.getSchemaEdge(element.type);
      // Add type for each attribute.
      schemaEdge.attributes.forEach(
        attr => attributeTypesMap.set(attr.name, attr.type.name)
      );

      const edgeAttrs = cloneDeep(element.attrs);
      semanticCheckResult = this.checkAttribute(edgeAttrs, attributeTypesMap);
    }

    if (!semanticCheckResult.success) {
      return semanticCheckResult;
    }

    if (update) {
      this.calculateDiff(this.graph, graph);
      this.graph = this.history.update(graph);
    }

    return {
      success: true
    };
  }

  /**
   * Check the validity of the attributes.
   *
   * @private
   * @param {*} attributes
   * @param {Map<string, string>} attributeTypesMap
   * @returns {ValidateResult}
   * @memberof VisualEditorLogicService
   */
  private checkAttribute(
    attributes: any,
    attributeTypesMap: Map<string, string>
  ): ValidateResult {
    let validateResult: ValidateResult = { success: true };
    for (const name of Array.from(attributeTypesMap.keys())) {
      switch (attributeTypesMap.get(name)) {
        case 'INT':
          validateResult = FormatValidator.isInt(attributes[name].toString());
          break;
        case 'UINT':
          validateResult = FormatValidator.isUInt(attributes[name].toString());
          break;
        case 'DOUBLE':
        case 'FLOAT':
          validateResult = FormatValidator.isReal(attributes[name].toString());
          break;
        case 'BOOL':
          validateResult = FormatValidator.isBoolean(attributes[name].toString());
          break;
        case 'DATETIME':
          validateResult = FormatValidator.isDatetime(attributes[name].toString());
          break;
        default:
          break;
      }

      if (!validateResult.success) {
        validateResult = {
          success: false,
          message:
            `Attribute "${name}"'s value "${attributes[name]}" ` +
            `is invalid ${attributeTypesMap.get(name)}.`
        };
        return validateResult;
      }
    }

    return validateResult;
  }
}
