$popup-toolbar-margin: -24px !default;
$layout-button-size: 50px;
$layout-button-img-size: 28px;
$zoom-button-size: 28px;
$md-breakpoint-max-width: 1279px !default;
$sm-breakpoint-max-width: 959px !default;

.toolbar-container {
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 100%;

  ::ng-deep .graph-switcher {
    @media(max-width: $md-breakpoint-max-width) {
      top: 40px;
    }
  }
}

.popup-toolbar {
  font-size: 24px;
  margin-left: $popup-toolbar-margin;
  margin-top: $popup-toolbar-margin;
  padding: 0 24px;
  width: calc(100% + 48px);
}

.layout-container {
  overflow: hidden;
  border-radius: 2px;
  margin: 12px;
}

.box-container {
  overflow: hidden;
  text-overflow: ellipsis;
}

.attribute-container {
  height: calc(100vh - 350px);
}

.color-config-container {
  margin: 5px 0;
}

.color-container {
  height: 40px;
  width: 40px;
}

.color-filter-container {
  overflow-x: auto;
  margin-bottom: 8px;
}

.color-hex {
  height: 40px;
}

.list-container {
  min-height: 200px;
  overflow-y: auto;
}

.color-icon-container {
  margin: 0 8px 0 16px;
  height: 24px;
  width: 24px;
}

.config-filter {
  margin: 12px 0;
}

.config-text {
  margin-bottom: 0;
}

.description-box {
  padding: 6px 0;
  height: 160px;

  ::ng-deep .mat-form-field-infix {
    border: 0;
  }
}

.detail-list-container {
  margin-top: 8px;

  ::ng-deep button.mat-list-item {
    height: auto;
    font-size: 14px;
    position: relative;

    .mat-list-item-content {
      align-items:unset;
    }

    .detail-col {
      padding: 15px 0;
      display: flex;
      flex-direction: column;

      .detail-col-middle {
        flex: 2;
      }
    }

    .detail-close-button {
      position: absolute;
      top: 0;
      right: 0;
    }
  }
}

.detail-image {
  margin: 15px;
  width: 240px;
}

.detail-image-sm {
  margin: 15px 0;
  padding-right: 20px;
  width: 100% !important;
}

.edge-preview {
  margin-top: 10px;
  text-align: center;
}

.edge-preview-bar {
  height: 2px;
}

.edge-preview-label {
  padding-top: 1em;
}

.edge-preview p {
  margin-top: -8px;
}

.empty-list-hint {
  padding-left: 16px;
}

.file-title {
  font-size: 18px;
}

.font-size-slider {
  margin: 16px 24px 0;
  width: calc(100% - 48px);
}

.form-container {
  margin-top: 12px;
  max-height: calc(65vh - 12px);
}

.gap {
  margin-top: 22px;
}

.geospatial-error-text {
  margin: -12px 0 15px;
  font-size: 12px;
}

.hint-text {
  margin-bottom: 0;
}

.img-preview {
  margin-top: 16px;
}

.layout-container {
  border-radius: 2px;
  margin: 12px;
}

.layout-selector-page {
  max-height: 100vh;
  max-width: 100vh;
  overflow: hidden;
}

.list-header {
  font-size: 14px;
  font-weight: 500;
  margin-top: 12px;
  margin-bottom: 8px;
  padding-bottom: 8px;
  padding-left: 16px;
}

.popup-toolbar {
  margin-left: $popup-toolbar-margin;
  margin-top: $popup-toolbar-margin;
  padding: 0 24px;
  width: calc(100% + 48px);
}

.preview-container {
  padding: 0 48px 12px;
  text-align: center;
}

.size-text {
  text-align: center;
}

.vertex-preview {
  border-radius: 50%;
  height: 52px;
  width: 52px;
  text-align: center;
}

.visualization-config-container {
  margin: 0 16px 8px;
}

.config-vertex-edge-type-list {
  height: 25px !important;
  font-size: 14px !important;
}

.bottom-left-button {
  position: absolute;
  left: 6px;
  bottom: 40px;

  @media(max-width: $sm-breakpoint-max-width) {
    left: 10px;
    bottom: 10px;
  }
}

.bottom-right-buttons {
  position: fixed;
  right: 15px;
  bottom: 15px;
}

.switch-layout {
  .layout-button {
    border-radius: 5px;
    .mat-icon-button {
      height: $layout-button-size;
      width: $layout-button-size;
      line-height: $layout-button-size;
    }
    .icon-with-text {
      span {
        line-height: 1em;
      }
      img {
        width: $layout-button-img-size;
        height: $layout-button-img-size;
      }
    }
  }
}

.layout-img {
  width: $layout-button-img-size;
  height: $layout-button-img-size;
  margin-left: 15px;
  filter: invert(94%) sepia(0%) saturate(24%) hue-rotate(45deg) brightness(105%) contrast(105%);
}

.zoom-buttons {
  border-radius: 5px;
  .mat-icon-button {
    width: $zoom-button-size;
    height: $zoom-button-size;
    line-height: $zoom-button-size;
  }
}

.geo-style {
  color: #000000;
}

.sample-value {
  margin-left: 16px;
}

.reflow-formatting-label {
  padding-top: 16px;
}

.reflow-formatting-field {
  padding-left: 25px;
}

.vis-info {
  [role=listitem] {
    white-space: normal !important;
  }
}

.shortcuts-menu-container {
  position: absolute;
  left: 10px;
  bottom: 10px;
  z-index: 10;
}
