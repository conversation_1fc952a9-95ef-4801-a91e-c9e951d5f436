// @ts-nocheck
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { discardPeriodicTasks, fakeAsync, tick, ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { FormGroup } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { of, throwError } from 'rxjs';
import * as _ from 'lodash';
import { TranslateModule } from '@ngx-translate/core';

import { AuthService, CacheService, Logger, MessageBus } from '@app/core/services';
import * as expr from '@tigergraph/tools-models/expression';
import { HelperFunctions } from '@app/shared/utils';

import { VisualEditorLogicService } from './shared';
import { VisualEditorService, ToolbarButtons } from './visual-editor.service';
import { VisualEditorComponent } from './visual-editor.component';
import { createGraphChartDataLink } from '@tigergraph/tools-models/gvis/insights';

class MockGraph {
  name = 'MyGraph';
  vertexTypes = [];
  edgeTypes = [];

  dumpToGSQLJson() {
    return {
      GraphName: 'MyGraph',
      VertexTypes: [],
      EdgeTypes: []
    };
  }

  getEdge() { }
}

class MockVisualEditorLogicService {
  schema = new MockGraph();

  setSchema() { }
  addData() { }
  addVertex() { }
  addEdge() { }
  updateVertex() { }
  updateEdge() { }
  clearData() { }
  getGraph() { }
  undo() { }
  redo() { }
  getAllVertexTypes() { }
  getAllEdgeTypes() { }
  getMatchEdgeTypes() { }
  getVertex() { }
  getEdge() { }
  getSchemaVertex() { }
  getSchemaEdge() { }
  retain() { }
  remove() { }
  removeAll() { }
}

class MockVisualEditorService {
  getVertex() { }
  getEdge() { }
  getMultiEdge() { }
  upsertVertex() { }
  upsertEdge() { }
  deleteVertex() { }
  deleteEdge() { }
  deleteMultiEdge() { }
  convertValueType() { }
  getOneVertexNeighbors() { }
  circleProcess() { }
  getToolbarConfig() {
    const toolbarTop = [
      { key: 'open' },
      { key: 'save' },
      { key: 'export' },
      { key: 'search' },
      { key: 'show' },
      { key: 'hide' },
      { key: 'undo' },
      { key: 'redo' },
      { key: 'settings' },
      { key: 'visInfo' },
      { key: 'viewAttributes' },
      { key: 'separator' },
      { key: 'switch' },
      { key: 'addVertex' },
      { key: 'addEdge' },
      { key: 'editAttributes' },
      { key: 'delete' },
      { key: 'separator' },
      { key: 'zoomIn' },
      { key: 'zoomOut' },
      { key: 'refresh' },
      { key: 'changeLayout', label: 'Force' }
    ];

    return {
      top: toolbarTop
    };
  }
  getShortcuts() { }
  getChartLayout() { }
  getAddEdgeLabel() { }
  buildAttributeQuestions() { }
  buildOptions() { }
  getExpressionFormConfig() { }
  buildExpressionFormConfig() { }
  getUdtList() { return of([]); }
  getSavedExplorationList() { return of([]); }
  getSavedExploration() { return of(); }
  saveExploration() { return of(); }
  deleteExploration() { return of(); }
  buildSaveExplorationForm() { return new FormGroup({}); }
}

class MockGraphRef {
  selectElements() { }
  selectNodes() { }
  selectNodesByTypes() { }
  selectEdges() { }
  selectEdgesByTypes() { }
  unselectElements() { }
  unselectNodes() { }
  unselectEdges() { }
  selectedElements() { }
  selectedNodes() { }
  selectedEdges() { }
  getNodes() { }
  getNodesByTypes() { }
  getLinks() { }
  getLinksByTypes() { }
  centerGraph() { }
  centerNodes() { }
  centerLinks() { }
  centerNodesByTypes() { }
  centerLinksByTypes() { }
  graphPositions() { }
  lockNodes() { }
  unlockNodes() { }
  getGraphCollection() {}
  setPositions() { }
  onClick() { }
  onDoubleClick() { }
  onHover() { }
  onSelect() { }
  onSelectNodes() { }
  onSelectEdges() { }
  onPositionChange() { }
  highlightLabel() { }
  removeHighlightedLabel() { }
  displayNodeMessage() { }
  displayEdgeMessage() { }
  displayNodeProgress() { }
  displayNodeInfiniteProgress() { }
  removeNodeMessage() { }
  removeEdgeMessage() { }
  removeAllMessages() { }
  removeNodeProgress() { }
  removeAllProgress() { }
  setNodeStyle() { }
  setNodesStyle() { }
  setNodesStyleByType() { }
  setNodesStyleByTypes() { }
  setLinkStyle() { }
  setLinksStyle() { }
  setLinksStyleByType() { }
  setLinksStyleByTypes() { }
  exportPng() { }
  runLayout() { }
  setDrawMode() { }
}

class MockDynamicForm {
  form = {
    value: {}
  };
}

describe('VisualEditorComponent', () => {
  let component: VisualEditorComponent;
  let fixture: ComponentFixture<VisualEditorComponent>;
  let logicService: VisualEditorLogicService;
  let compService: VisualEditorService;
  let dialog: MatDialog;
  let snackBar: MatSnackBar;
  let graphRef: { current: MockGraphRef };
  let dynamicForm: MockDynamicForm;
  let cacheService: CacheService;
  const udtList = [
    {
      name: 'testTuple',
      fields: [
        {
          fieldName: 'f1',
          fieldType: 'INT'
        }
      ]
    }
  ];

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      imports: [TranslateModule.forRoot()],
      declarations: [VisualEditorComponent],
      providers: [
        {
          provide: MatDialog,
          useFactory: () => ({
            open: () => { }
          })
        },
        {
          provide: MatSnackBar,
          useFactory: () => ({
            open: () => { }
          })
        },
        {
          provide: AuthService,
          useFactory: () => ({
            getUserRole: () => 'superuser',
            getCurrentGraph: () => '',
            getUsername: () => 'user1',
            hasPrivilege: () => true,
            hasAllPrivileges: () => true,
            hasPrivilegesToQuery: () => true,
            hasPrivilegesToCreateQuery: () => true,
          })
        },
        {
          provide: Logger,
          useFactory: () => ({
            log: () => { }
          })
        },
        {
          provide: MessageBus,
          useFactory: () => ({
            from: () => of(),
            to: () => { }
          })
        },
        {
          provide: CacheService,
          useFactory: () => ({
            getItem: () => { }
          })
        }
      ],
      schemas: [NO_ERRORS_SCHEMA]
    })
      .overrideComponent(VisualEditorComponent, {
        set: {
          providers: [
            {
              provide: VisualEditorLogicService,
              useClass: MockVisualEditorLogicService
            },
            {
              provide: VisualEditorService,
              useClass: MockVisualEditorService
            }
          ]
        }
      })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(VisualEditorComponent);
    component = fixture.componentInstance;
    graphRef = component.dataGraphContainer.graphRef = { current: <any>new MockGraphRef() };
    fixture.detectChanges();
  });

  beforeEach(() => {
    logicService = fixture.debugElement.injector.get(VisualEditorLogicService);
    compService = fixture.debugElement.injector.get(VisualEditorService);
    dialog = TestBed.inject(MatDialog);
    snackBar = TestBed.inject(MatSnackBar);
    cacheService = fixture.debugElement.injector.get(CacheService);
  });

  it('should be created', () => {
    expect(component).toBeTruthy();
  });

  it('should be initialized', () => {
    expect((<any>component).dialogConfig).toEqual({
      width: '648px',
      ariaDescribedBy: 'dialogDescription',
      autoFocus: false
    });
    expect(component.vertex).toBeDefined();
    expect(component.edge).toBeDefined();
  });

  it('should set new schema', () => {
    const vertex1 = {
      name: 'vType1',
      primaryId: {
        name: 'id',
        type: {
          name: 'STRING'
        }
      },
      style: {
        icon: 'abc',
        fillColor: '#123456'
      },
      attributes: [
        {
          name: 'attr1',
          type: {
            name: 'STRING'
          }
        }
      ]
    };
    const vertex2 = {
      name: 'vType2',
      primaryId: {
        name: 'id',
        type: {
          name: 'STRING'
        }
      },
      attributes: []
    };
    const edge1 = {
      name: 'eType1',
      style: {
        fillColor: '#ff00ff'
      },
      attributes: [
        {
          name: 'attr1',
          type: {
            name: 'DATETIME'
          }
        }
      ]
    };
    const edge2 = {
      name: 'eType2',
      hasReverseEdge: true,
      reverseEdge: 'reverse_eType2',
      attributes: []
    };
    const schema = {
      vertexTypes: [vertex1, vertex2],
      edgeTypes: [edge1, edge2]
    };
    const expectedAttributes = {
      names: ['vType1', 'vType2', 'eType1', 'eType2'],
      data: {
        vType1: {
          name: 'vType1',
          type: 'vertex',
          attributes: [
            {
              name: 'vertex type',
              checked: false
            },
            {
              name: 'primary id',
              checked: true
            },
            {
              name: 'attr1',
              checked: false
            }
          ]
        },
        vType2: {
          name: 'vType2',
          type: 'vertex',
          attributes: [
            {
              name: 'vertex type',
              checked: false
            },
            {
              name: 'primary id',
              checked: true
            }
          ]
        },
        eType1: {
          name: 'eType1',
          type: 'edge',
          attributes: [
            {
              name: 'edge type',
              checked: true
            },
            {
              name: 'attr1',
              checked: false
            }
          ]
        },
        eType2: {
          name: 'eType2',
          type: 'edge',
          attributes: [
            {
              name: 'edge type',
              checked: true
            }
          ]
        },
      }
    };

    spyOn(logicService, 'setSchema');
    spyOn(<any>component, 'getAttributesList').and.callThrough();
    spyOn(<any>component, 'renderChart');

    spyOn(logicService, 'getAllVertexTypes').and.returnValue([vertex1.name, vertex2.name]);
    // @ts-ignore
    spyOn(logicService, 'getSchemaVertex').and.returnValues(vertex1, vertex2);
    spyOn(logicService, 'getAllEdgeTypes').and.returnValue([edge1.name, edge2.name]);
    // @ts-ignore
    spyOn(logicService, 'getSchemaEdge').and.returnValues(edge1, edge2);
    // @ts-ignore
    spyOn(logicService, 'getGraph').and.returnValue({ graph: { nodes: { length: 1 } } });

    logicService.schema = <any>schema; // directly set the schema in mock logic service.
    component.switchGraph(<any>schema);
    expect(logicService.setSchema).toHaveBeenCalledWith(schema);
    expect((<any>component).getAttributesList).toHaveBeenCalled();
    expect((<any>component).schemaReverseEdgeMap.get('reverse_eType2')).toBe('eType2');
    expect(component.attributesForSelected).toEqual(<any>expectedAttributes);
  });

  it('should get the udt list', () => {
    spyOn(compService, 'getUdtList').and.returnValue(of(udtList));
    (<any>component).getUdtList();
    expect(compService.getUdtList).toHaveBeenCalled();
    expect(component.udtList).toBeDefined();
  });

  describe('should add new data to the graph', () => {
    const graph = {
      nodes: [
        {
          type: 'vType1',
          id: 'vId1'
        },
        {
          type: 'vType2',
          id: 'vId2'
        }
      ],
      links: [
        {
          type: 'eType1',
          source: {
            type: 'vType1',
            id: 'vId1'
          },
          target: {
            type: 'vType1',
            id: 'vId1'
          }
        },
        {
          type: 'reverse_eType2',
          source: {
            type: 'vType2',
            id: 'vId2'
          },
          target: {
            type: 'vType1',
            id: 'vId1'
          }
        }
      ]
    };
    const attributes = {
      names: ['vType1', 'vType2', 'eType1', 'eType2'],
      data: {
        vType1: {
          name: 'vType1',
          type: 'vertex',
          attributes: [
            {
              name: 'type',
              checked: false
            },
            {
              name: 'id',
              checked: true
            }
          ]
        },
        vType2: {
          name: 'vType2',
          type: 'vertex',
          attributes: [
            {
              name: 'type',
              checked: false
            },
            {
              name: 'id',
              checked: true
            }
          ]
        },
        eType1: {
          name: 'eType1',
          type: 'edge',
          attributes: [
            {
              name: 'type',
              checked: true
            }
          ]
        },
        eType2: {
          name: 'eType2',
          type: 'edge',
          attributes: [
            {
              name: 'type',
              checked: true
            }
          ]
        },
      }
    };

    beforeEach(() => {
      (<any>component).schemaReverseEdgeMap.set('reverse_eType2', 'eType2');
      component.attributesForSelected = <any>attributes;
      component.componentName = 'GraphExplorer';
      spyOn(<any>component, 'getCurrentGraphResult').and.returnValue({});
    });

    it('with no data', () => {
      const parser = () => { };
      spyOn((<any>component).graphParser, 'getParser').and.returnValue(parser);
      spyOn(<any>component, 'exceedGraphSize');

      component.addData({}, 'abc', true);
      expect((<any>component).exceedGraphSize).not.toHaveBeenCalled();
    });

    describe('with success', () => {
      const node1 = {
        type: 'vType1',
        id: 'vId1'
      };
      const node2 = {
        type: 'vType2',
        id: 'vId2'
      };
      const link1 = {
        type: 'eType1',
        source: {
          type: 'vType1',
          id: 'vId1'
        },
        target: {
          type: 'vType1',
          id: 'vId1'
        }
      };
      const link2 = {
        type: 'reverse_eType2',
        source: {
          type: 'vType2',
          id: 'vId2'
        },
        target: {
          type: 'vType1',
          id: 'vId1'
        }
      };

      beforeEach(() => {
        const parser = () => graph;
        spyOn((<any>component).graphParser, 'getParser').and.returnValue(parser);

        // @ts-ignore
        spyOn(logicService, 'getGraph').and.returnValue({ graph: graph });
        spyOn(logicService, 'addData');

        spyOn(<any>component, 'getNodes').and.returnValue([]);
        spyOn(<any>component, 'getLinks').and.returnValue([]);
        // @ts-ignore
        spyOn(logicService, 'getSchemaEdge').and.returnValue({ hasReverseEdge: false });
        // @ts-ignore
        spyOn(logicService.schema, 'getEdge').and.returnValue({ compositeDiscriminator: [] });

        spyOn(graphRef.current, 'setNodesStyleByTypes');
        spyOn(graphRef.current, 'setLinksStyleByTypes');
        spyOn(graphRef.current, 'runLayout');
      });

      it('with new data', fakeAsync(() => {
        (<any>component).getLinks = jasmine.createSpy().and.returnValue([createGraphChartDataLink(link1)]);

        component.addData({}, 'abc', true);
        tick(101);

        expect((<any>component).graphLayout).toBe('Force');
        expect(logicService.addData).toHaveBeenCalledWith(graph);

        expect(logicService.getSchemaEdge).toHaveBeenCalledTimes(3);

        expect((<any>component).getNodes).toHaveBeenCalledTimes(1);
        expect((<any>component).getLinks).toHaveBeenCalledTimes(1);

        expect(graphRef.current.setNodesStyleByTypes).toHaveBeenCalled();
        expect(graphRef.current.setLinksStyleByTypes).toHaveBeenCalled();
        expect(graphRef.current.runLayout).toHaveBeenCalled();

        discardPeriodicTasks();
      }));

      it('with previous saved data and no selection', fakeAsync(() => {
        spyOn(<any>component, 'showLabels');
        const prevSelectionsAndLayout = {
          selections: {
            links: [],
            nodes: []
          },
          layout: 'Force'
        };

        component.addData({}, 'static', false, undefined, true, prevSelectionsAndLayout);
        tick(101);

        expect((<any>component).graphLayout).toBe('Force');
        expect(logicService.addData).toHaveBeenCalledWith(graph);

        expect(logicService.getSchemaEdge).not.toHaveBeenCalled();
        expect((<any>component).showLabels).toHaveBeenCalled();

        expect((<any>component).getNodes).toHaveBeenCalledTimes(1);
        expect((<any>component).getLinks).toHaveBeenCalledTimes(1);

        discardPeriodicTasks();
      }));

      it('with previous saved data, selections and layout', fakeAsync(() => {
        spyOn(<any>component, 'renderChart').and.callThrough();
        spyOn(<any>component, 'showLabels');
        const prevSelectionsAndLayout = {
          selections: {
            links: [link2],
            nodes: [node1, node2]
          },
          layout: 'tree'
        };

        component.addData({}, 'static', false, undefined, true, prevSelectionsAndLayout);
        tick(101);

        expect((<any>component).graphLayout).toBe('tree');
        expect((<any>component).renderChart).toHaveBeenCalledWith({ graph: graph }, undefined, true);

        expect((<any>component).getNodes).toHaveBeenCalledTimes(1);
        expect((<any>component).getLinks).toHaveBeenCalledTimes(1);
        expect(graphRef.current.runLayout).toHaveBeenCalled();

        discardPeriodicTasks();
      }));
    });

    it('with failure', () => {
      const parser = () => graph;
      spyOn((<any>component).graphParser, 'getParser').and.returnValue(parser);
      spyOn(logicService, 'getGraph').and.returnValue({
        graph: {
          // @ts-ignore
          nodes: { length: 6000 },
          // @ts-ignore
          links: { length: 11000 }
        }
      });
      spyOn(<any>component, 'handleError').and.callThrough();
      spyOn(dialog, 'open');

      component.addData({}, 'abc', true);
      expect((<any>component).handleError).toHaveBeenCalledWith(
        `Unable to render graph since graph size is too large.`,
        'Warning'
      );
      expect(dialog.open).toHaveBeenCalled();
    });
  });

  it('should render new vertices from an initial vertex', fakeAsync(() => {

    // @ts-ignore
    spyOn(logicService, 'getGraph').and.returnValue({
      graph: {
        nodes: [
          { type: 'vType1', id: 'vId1' },
          { type: 'vType2', id: 'vId2' },
          { type: 'vType3', id: 'vId3' },
          { type: 'vType4', id: 'vId4' }
        ],
          links: []
        }
    });
    spyOn(compService, 'circleProcess');
    spyOn(graphRef.current, 'setNodesStyleByTypes');
    spyOn(graphRef.current, 'setLinksStyleByTypes');
    spyOn(graphRef.current, 'runLayout');

    (<any>component).renderChart({}, { x: 10, y: 20 });
    tick(101);
    expect(graphRef.current.setNodesStyleByTypes).toHaveBeenCalled();
    expect(graphRef.current.setLinksStyleByTypes).toHaveBeenCalled();
    discardPeriodicTasks();
  }));

  describe('should re-run the layout', () => {
    beforeEach(() => {
      component.componentName = 'GraphExplorer';
    });

    it('with force layout and 100% data difference and large vertex number', fakeAsync(() => {
      const nodes = [];
      let i = 0;
      while (i < 100) {
        nodes.push({ type: `vType${i}`, id: `vId${i}` });
        i++;
      }
      // @ts-ignore
      spyOn(logicService, 'getGraph').and.returnValue({
        graph: { nodes: nodes, links: []}
      });
      spyOn(graphRef.current, 'setNodesStyleByTypes');
      spyOn(graphRef.current, 'setLinksStyleByTypes');
      spyOn(graphRef.current, 'runLayout');

      (<any>component).renderChart({ data: {}, diff: 1 });
      tick(101);
      expect(graphRef.current.setNodesStyleByTypes).toHaveBeenCalled();
      expect(graphRef.current.setLinksStyleByTypes).toHaveBeenCalled();
      discardPeriodicTasks();
    }));

    it('with force layout and 100% data difference', fakeAsync(() => {
      const nodes = [];
      let i = 0;
      while (i < 40) {
        nodes.push({ styles: { base: {} } });
        i++;
      }
      // @ts-ignore
      spyOn(logicService, 'getGraph').and.returnValue({
        graph: { nodes: nodes, links: [] }
      });
      spyOn(graphRef.current, 'setNodesStyleByTypes');
      spyOn(graphRef.current, 'setLinksStyleByTypes');
      spyOn(graphRef.current, 'runLayout');

      (<any>component).renderChart({ data: {}, diff: 1 });
      tick(101);
      expect(graphRef.current.setNodesStyleByTypes).toHaveBeenCalled();
      expect(graphRef.current.setLinksStyleByTypes).toHaveBeenCalled();
      discardPeriodicTasks();
    }));
  });

  it('should remove the prefix before attribute variable in expression string', () => {
    const exprString = '(vType.id == "vId1")';
    component.currentSelectedName = 'vType';
    const res = component.removeAlias(exprString);
    expect(res).toBe('(id == "vId1")');
  });

  it('should return empty string when removing the prefix in a empty string', () => {
    const exprString = '';
    const res = component.removeAlias(exprString);
    expect(res).toBe('None');
  });

  it('should clear the graph', () => {
    spyOn(logicService, 'clearData');
    component.clearData();
    expect(logicService.clearData).toHaveBeenCalled();
  });

  it('should update color of vertex and edge', fakeAsync(() => {
    component.visualizationConfigOfAllTypes.set(
      'vType1', {
        color: [{
          filter: <any>{
            evaluate: (node) => {
              return {
                applicable: node.attrs.count === 10,
                value: node.attrs.count === 10,
              };
            },
          },
          colorValue: '#000000'
        }],
        size: undefined
      }
    );
    component.visualizationConfigOfAllTypes.set(
      'eType1', {
        color: [{
          filter: <any>{
            evaluate: (link) => {
              return {
                applicable: link.attrs.flag,
                value: link.attrs.flag,
              };
            },
          },
          colorValue: '#000000'
        }],
        size: undefined
      }
    );
    const nodes = [
      {
        exType: 'vType1',
        exID: 'vId1',
        attrs: {
          'count': 10
        },
        styles: {
          base: {
            fillColor: undefined
          }
        }
      },
      {
        exType: 'vType1',
        exID: 'vId2',
        attrs: {
          'count': 20
        },
        styles: {
          base: {
            fillColor: undefined
          }
        }
      }
    ];
    const links = [
      {
        exType: 'eType1',
        source: {
          exType: 'vType1',
          exID: 'vId1',
        },
        target: {
          exType: 'vType1',
          exID: 'vId1',
        },
        attrs: {
          'flag': true
        },
        styles: {
          base: {
            fillColor: undefined
          }
        }
      },
      {
        exType: 'eType1',
        source: {
          exType: 'vType1',
          exID: 'vId2',
        },
        target: {
          exType: 'vType1',
          exID: 'vId2',
        },
        attrs: {
          'flag': false
        },
        styles: {
          base: {
            fillColor: undefined
          }
        }
      }
    ];
    spyOn(<any>component, 'getNodes').and.returnValue(nodes);
    spyOn(<any>component, 'getLinks').and.returnValue(links);
    spyOn(graphRef.current, 'setNodesStyle');
    spyOn(graphRef.current, 'setLinksStyle');
    // @ts-ignore
    spyOn(logicService, 'getSchemaVertex').and.returnValue({ style: { fillColor: '#666666' } });
    // @ts-ignore
    spyOn(logicService, 'getSchemaEdge').and.returnValue({ style: { fillColor: '#666666' } });

    component.componentName = 'GraphExplorer';
    (<any>component).renderChart({});
    tick(101);
    expect(graphRef.current.setNodesStyle).toHaveBeenCalledWith([
      {
        node: {
          type: 'vType1',
          id: 'vId1',
        },
        style: {
          width: '80px',
          height: '80px',
          'background-color': '#000000',
        },
      },
      {
        node: {
          type: 'vType1',
          id: 'vId2',
        },
        style: {
          width: '80px',
          height: '80px',
          'background-color': '#666666',
        },
      },
    ]);
    expect(graphRef.current.setLinksStyle).toHaveBeenCalledWith([
      {
        link: {
          type: 'eType1',
          source: {
            type: 'vType1',
            id: 'vId1',
          },
          target: {
            type: 'vType1',
            id: 'vId1',
          },
          attrs: {
            flag: true,
          },
          directed: undefined,
        },
        style: {
          width: 2,
          'line-color': '#000000',
          'target-arrow-color': '#000000',
        },
      },
      {
        link: {
          type: 'eType1',
          source: {
            type: 'vType1',
            id: 'vId2',
          },
          target: {
            type: 'vType1',
            id: 'vId2',
          },
          attrs: {
            flag: false,
          },
          directed: undefined,
        },
        style: {
          width: 2,
          'line-color': '#666666',
          'target-arrow-color': '#666666',
        },
      },
    ]);
  }));

  it('should update the size of vertex and edge', fakeAsync(() => {
    component.visualizationConfigOfAllTypes.set(
      'vType1', {
        color: [],
        size: <any>{
          evaluate: (node) => {
            return {
              applicable: node.attrs['@intAccum'] === 30,
              value: node.attrs['@intAccum'],
            };
          },
        }
      }
    );
    component.visualizationConfigOfAllTypes.set(
      'eType1', {
        color: [],
        size: <any>{
          evaluate: (link) => {
            return {
              applicable: true,
              value: link.attrs.times,
            };
          },
        }
      }
    );
    const nodes = [
      {
        exType: 'vType1',
        exID: 'vId1',
        attrs: {
          '@intAccum': 30
        },
        styles: {
          base: {
            radius: undefined
          }
        }
      },
      {
        exType: 'vType1',
        exID: 'vId2',
        attrs: {
          '@intAccum': 10
        },
        styles: {
          base: {
            radius: undefined
          }
        }
      }
    ];
    const links = [
      {
        exType: 'eType1',
        source: {
          exType: 'vType1',
          exID: 'vId1',
        },
        target: {
          exType: 'vType1',
          exID: 'vId1',
        },
        attrs: {
          'times': 5
        },
        styles: {
          base: {
            radius: undefined
          },
          hovered: {
            radius: undefined
          },
          selected: {
            radius: undefined
          }
        }
      },
      {
        exType: 'eType1',
        source: {
          exType: 'vType1',
          exID: 'vId2',
        },
        target: {
          exType: 'vType1',
          exID: 'vId2',
        },
        attrs: {
          'times': 30
        },
        styles: {
          base: {
            radius: undefined
          },
          hovered: {
            radius: undefined
          },
          selected: {
            radius: undefined
          }
        }
      }
    ];
    spyOn(<any>component, 'getNodes').and.returnValue(nodes);
    spyOn(<any>component, 'getLinks').and.returnValue(links);
    spyOn(graphRef.current, 'setNodesStyle');
    spyOn(graphRef.current, 'setLinksStyle');
    // @ts-ignore
    spyOn(logicService, 'getSchemaVertex').and.returnValue({ style: { fillColor: '#666666' } });
    // @ts-ignore
    spyOn(logicService, 'getSchemaEdge').and.returnValue({ style: { fillColor: '#666666' } });

    component.componentName = 'GraphExplorer';
    (<any>component).renderChart({});
    tick(101);
    expect(graphRef.current.setNodesStyle).toHaveBeenCalledWith([
      {
        node: {
          type: 'vType1',
          id: 'vId1',
        },
        style: {
          width: '60px',
          height: '60px',
          'background-color': '#666666',
        },
      },
      {
        node: {
          type: 'vType1',
          id: 'vId2',
        },
        style: {
          width: '80px',
          height: '80px',
          'background-color': '#666666',
        },
      },
    ]);
    expect(graphRef.current.setLinksStyle).toHaveBeenCalledWith([
      {
        link: {
          type: 'eType1',
          source: {
            type: 'vType1',
            id: 'vId1',
          },
          target: {
            type: 'vType1',
            id: 'vId1',
          },
          attrs: {
            'times': 5,
          },
          directed: undefined,
        },
        style: {
          width: 5,
          'line-color': '#666666',
          'target-arrow-color': '#666666',
        },
      },
      {
        link: {
          type: 'eType1',
          source: {
            type: 'vType1',
            id: 'vId2',
          },
          target: {
            type: 'vType1',
            id: 'vId2',
          },
          attrs: {
            'times': 30,
          },
          directed: undefined,
        },
        style: {
          width: 30,
          'line-color': '#666666',
          'target-arrow-color': '#666666',
        },
      }
    ]);
  }));

  it('should return the current graph size', () => {
    spyOn(logicService, 'getGraph').and.returnValue({
      graph: {
        // @ts-ignore
        nodes: { length: 100 },
        // @ts-ignore
        links: { length: 200 }
      }
    });
    expect(component.getGraphSize()).toEqual([100, 200]);
  });

  it('should return the current selections', () => {
    // @ts-ignore
    spyOn(graphRef.current, 'selectedNodes').and.returnValue([]);
    // @ts-ignore
    spyOn(graphRef.current, 'selectedEdges').and.returnValue([]);
    expect(component.getCurrentSelections()).toEqual({ nodes: [], links: [] });
  });

  it('should highlight selected vertices', fakeAsync(() => {
    const nodes = [
      {
        exType: 'vType1',
        exID: 'vId1',
        styles: {
          base: {
            radius: undefined,
            lineColor: undefined,
            lineWidth: undefined
          }
        }
      },
      {
        exType: 'vType2',
        exID: 'vId2',
        styles: {
          base: {
            radius: undefined,
            lineColor: undefined,
            lineWidth: undefined
          }
        }
      }
    ];
    spyOn(<any>component, 'getNodes').and.returnValue(nodes);
    spyOn(graphRef.current, 'setNodesStyle');
    spyOn(graphRef.current, 'runLayout');

    component.highlight([{ type: 'vType1', id: 'vId1' }]);
    tick(101);

    expect(graphRef.current.setNodesStyle).toHaveBeenCalledWith([
      {
        node: {
          type: 'vType1',
          id: 'vId1',
        },
        style: {
          'border-color': '#FFF03F',
          'border-width': '10px',
        },
      },
      {
        node: {
          type: 'vType2',
          id: 'vId2',
        },
        style: {
          'border-color': 'transparent',
          'border-width': '0',
        },
      },
    ]);
    expect(graphRef.current.runLayout).toHaveBeenCalled();
  }));

  describe('should update the graph chart settings', () => {
    const attributes = {
      names: ['vType1', 'vType2'],
      data: {
        vType1: {
          name: 'vType1',
          type: 'vertex',
          attributes: [
            {
              name: 'type',
              checked: false
            },
            {
              name: 'id',
              checked: true
            }
          ]
        },
        vType2: {
          name: 'vType2',
          type: 'vertex',
          attributes: [
            {
              name: 'type',
              checked: false
            },
            {
              name: 'id',
              checked: true
            }
          ]
        }
      }
    };
    const visualizationConfigs = new Map<string, any>([
      ['vType1', {
        color: [],
        size: {}
      }]
    ]);
    const numberFormatting = {
      thousandsSeparator: {
        enabled: true,
        value: ','
      },
      unifyDecimalSpaces: {
        enabled: true,
        value: '10'
      },
      decimalSeparator: {
        enabled: true,
        value: '.'
      }
    };

    beforeEach(() => {
      spyOn(<any>component, 'saveSettingsToCacheService');
      spyOn(<any>component, 'renderChart');
      spyOn(logicService, 'getGraph').and.returnValue({});
    });

    it('without previous saved configs', () => {
      component.tmpAttributesForSelected = <any>attributes;
      component.newVisualizationConfigOfAllTypes = visualizationConfigs;
      component.numberFormattingForShowing = numberFormatting;
      component.newLabelFontSize = 20;
      component.showAttributesWhenMouseHover = false;

      component.updateSettings();

      expect((<any>component).attributesForSelected).toEqual(attributes);
      expect(component.visualizationConfigOfAllTypes).toEqual(visualizationConfigs);
      expect(component.numberFormatting).toEqual(numberFormatting);
      expect(component.labelFontSize).toBe(20);
      expect((<any>component).renderChart).toHaveBeenCalledWith({});
      expect((<any>component).saveSettingsToCacheService).toHaveBeenCalled();
    });

    it('with previous saved configs', () => {
      const configs = {
        attributesForSelected: <any>attributes,
        visualizationConfigOfAllTypes: visualizationConfigs,
        numberFormatting: numberFormatting,
        showAttributesWhenMouseHover: true,
        labelFontSize: 15
      };

      component.updateSettings(configs);

      expect((<any>component).attributesForSelected).toEqual(attributes);
      expect(component.visualizationConfigOfAllTypes).toEqual(visualizationConfigs);
      expect(component.numberFormatting).toEqual(numberFormatting);
      expect(component.labelFontSize).toBe(15);
      expect((<any>component).renderChart).toHaveBeenCalledWith({}, undefined, true);
      expect((<any>component).saveSettingsToCacheService).not.toHaveBeenCalled();
    });

    describe('number formatting', () => {
      describe('should init', () => {
        const constructElement = (attrs: Record<string, any>) => ({
          data: (field: string | number) => {
            if (['nodeID', 'type'].includes(field as string)) {
              return field;
            }
            return { ...attrs };
          }
        });
        const element = {
          data: (field: string | number) => {
            return field;
          }
        };
        beforeEach(() => {
          spyOn(graphRef.current, 'setNodesStyleByTypes');
          spyOn(graphRef.current, 'setLinksStyleByTypes');
          spyOn(HelperFunctions, 'numberFormattingForAttrs').and.callFake(function (value) { return Number(value); });
          spyOn((<any>component).cacheService, 'getItem').and.returnValue(undefined);
        });

        it('node label setting without attribute', () => {
          const attrs = [];
          const res = (<any>component).getNodeLabelStyle('vType', attrs);

          expect(res.style.label(element)).toBe('vType');
          expect(HelperFunctions.numberFormattingForAttrs).not.toHaveBeenCalled();
        });

        it('node label setting with data', () => {
          let attrs: any[] = ['primary id', 'vertex type', 'a', 'b'];
          let res = (<any>component).getNodeLabelStyle('vType', attrs);
          const ele = constructElement({ a: 'a', b: 'b', 1: 1, 2: 2 });

          expect(res.style.label(ele)).toBe('Vertex id: nodeID\nVertex type: type\na: a\nb: b');
          expect(HelperFunctions.numberFormattingForAttrs).toHaveBeenCalledTimes(0);

          attrs = ['primary id', 'vertex type', 1, 2];
          res = (<any>component).getNodeLabelStyle('vType', attrs);
          expect(res.style.label(ele)).toBe('Vertex id: nodeID\nVertex type: type\n1: 1\n2: 2');
          expect(HelperFunctions.numberFormattingForAttrs).toHaveBeenCalledTimes(2);
        });

        it('link label setting without data', () => {
          const attrs = [];
          const res = (<any>component).getLinkLabelStyle('eType', attrs);

          expect(res.style.label(element)).toBe('eType');
          expect(HelperFunctions.numberFormattingForAttrs).not.toHaveBeenCalled();
        });

        it('link label setting with data', () => {
          let attrs: any[] = ['edge type', 'a', 'b'];
          let res = (<any>component).getLinkLabelStyle('eType', attrs);
          const ele = constructElement({ a: 'a', b: 'b', 1: 1, 2: 2 });

          expect(res.style.label(ele)).toBe('Edge type: type\na: a\nb: b');
          expect(HelperFunctions.numberFormattingForAttrs).toHaveBeenCalledTimes(0);

          attrs = ['edge type', 1, 2];
          res = (<any>component).getLinkLabelStyle('eType', attrs);
          expect(res.style.label(ele)).toBe('Edge type: type\n1: 1\n2: 2');
          expect(HelperFunctions.numberFormattingForAttrs).toHaveBeenCalledTimes(2);
        });
      });
    });
  });

  describe('should toggle open saved exploration mode', () => {
    const dialogRef = {
      close: () => { },
      keydownEvents: () => of(),
      afterClosed: () => of()
    };
    beforeEach(() => {
      component.toolbarConfig = compService.getToolbarConfig();
    });

    it('with on', () => {
      spyOn(dialog, 'open').and.returnValue(dialogRef);
      component.onInteraction({ key: 'open' });
      expect(component.selectedEvent).toBe('open');
      expect(dialog.open).toHaveBeenCalled();
      expect((<any>component.toolbarConfig.top[ToolbarButtons.Open]).color).toBe('accent');
    });

    it('with off', () => {
      spyOn(dialog, 'open').and.returnValue(dialogRef);

      component.onInteraction({ key: 'open' });
      (<any>component).toggleButtonStatus();
      expect((<any>component.toolbarConfig.top[ToolbarButtons.Open]).color).toBe('');
    });
  });

  describe('should get saved exploration list', () => {
    beforeEach(() => {
      spyOn(<any>component, 'toggleLoading');
      spyOn(dialog, 'open').and.returnValue({
        keydownEvents: () => of(),
        afterClosed: () => of()
      });
    });

    it('with success', () => {
      spyOn(<any>component, 'getSavedExplorationList').and.callThrough();
      component.onInteraction({ key: 'open' });
      component.savedExplorationList$.subscribe(value => expect(value).toEqual([]));
      expect((<any>component).toggleLoading).toHaveBeenCalled();
    });

    it('with failure', () => {
      spyOn(<any>component, 'handleError');
      spyOn(compService, 'getSavedExplorationList')
        .and.returnValue(throwError({ error: 'error', statusText: 'Error' }));

      component.onInteraction({ key: 'open' });
      expect((<any>component).handleError).toHaveBeenCalled();
      expect((<any>component).toggleLoading).toHaveBeenCalled();
    });
  });

  describe('should apply saved exploration', () => {
    let dialogSpy: jasmine.Spy;
    let getSpy: jasmine.Spy;
    const dialogRef = {
      keydownEvents: () => of(),
      afterClosed: () => of(1)
    };

    beforeEach(() => {
      dialogSpy = spyOn(dialog, 'open');
      getSpy = spyOn(compService, 'getSavedExploration');
      spyOn(component, 'clearData');
      spyOn(component, 'addData');
      spyOn(component, 'updateSettings');
      spyOn(<any>component, 'closePopupWindow');
      spyOn(<any>component, 'handleError');
      spyOn(<any>component, 'toggleLoading');
      spyOn(<any>component, 'saveSettingsToCacheService');
    });

    it('with cancel', () => {
      const dialogRefCancel = {
        keydownEvents: () => of(),
        afterClosed: () => of(0)
      };
      dialogSpy.and.returnValue(dialogRefCancel);
      spyOn(<any>component, 'confirmApplyExploration');

      component.applyExploration();
      expect((<any>component).confirmApplyExploration).not.toHaveBeenCalled();
    });

    xit('with success after choosing continue', () => {
      const result = {
        schema: {
          GraphName: 'MyGraph',
          VertexTypes: [],
          EdgeTypes: []
        },
        data: {
          explorationResult: {
            latestGraph: {
              nodes: [],
              links: []
            },
            latestSelections: {
              nodes: [],
              links: []
            },
            latestLayout: 'tree'
          },
          graphChartConfig: {
            attributesForSelected: {
              names: [],
              data: {}
            },
            numberFormatting: {
              thousandsSeparator: {
                enabled: true,
                value: ','
              },
              unifyDecimalSpaces: {
                enabled: true,
                value: '10'
              },
              decimalSeparator: {
                enabled: true,
                value: '.'
              }
            },
            labelFontSize: 20,
            visualizationConfigOfAllTypes: {
              v1: {
                color: [],
                size: {
                  type: 'IntConstant',
                  value: 55
                }
              }
            }
          },
          graphExplorerConfig: {}
        }
      };
      const configMap = new Map<any, any>([
        ['v1', {
          color: [],
          size: 55
        }]
      ]);
      dialogSpy.and.returnValue(dialogRef);
      getSpy.and.returnValue(of(result));
      spyOn(_, 'isEqual').and.returnValue(true);
      spyOn(HelperFunctions, 'convertObjectToMap').and.returnValue(configMap);
      component.componentName = 'GraphExplorer';
      component.attributesForSelected.names = ['v1'];
      spyOn(<any>component, 'hasChangedAttribute').and.returnValue(false);
      spyOn(expr, 'parseExprJson').and.returnValue(55);

      component.applyExploration();
      expect(dialog.open).toHaveBeenCalled();
      expect(component.clearData).toHaveBeenCalled();
      expect(component.addData).toHaveBeenCalledWith(
        {
          nodes: [],
          links: []
        },
        'static',
        false,
        undefined,
        true,
        {
          selections: {
            nodes: [],
            links: []
          },
          layout: 'tree'
        }
      );
      expect(expr.parseExprJson).toHaveBeenCalled();
      expect(component.updateSettings).toHaveBeenCalledWith({
        attributesForSelected: {
          names: [],
          data: {}
        },
        numberFormatting: {
          thousandsSeparator: {
            enabled: true,
            value: ','
          },
          unifyDecimalSpaces: {
            enabled: true,
            value: '10'
          },
          decimalSeparator: {
            enabled: true,
            value: '.'
          }
        },
        labelFontSize: 20,
        visualizationConfigOfAllTypes: configMap
      });
      expect((<any>component).saveSettingsToCacheService).toHaveBeenCalled();
      component.graphExplorerConfigApplied.subscribe(value => expect(value).toEqual({}));
      expect((<any>component).closePopupWindow).toHaveBeenCalled();
      expect((<any>component).toggleLoading).toHaveBeenCalledTimes(2);
      expect((<any>component).handleError).not.toHaveBeenCalled();
    });

    it('with failure after choosing continue', () => {
      dialogSpy.and.returnValue(dialogRef);
      getSpy.and.returnValue(throwError({ error: 'error', statusText: 'Error' }));

      component.applyExploration();
      expect(component.clearData).not.toHaveBeenCalled();
      expect(component.addData).not.toHaveBeenCalled();
      expect(component.updateSettings).not.toHaveBeenCalled();
      expect((<any>component).closePopupWindow).not.toHaveBeenCalled();
      expect((<any>component).toggleLoading).toHaveBeenCalledTimes(2);
      expect((<any>component).handleError).toHaveBeenCalled();
    });



    it('with not in graph explorer page', () => {
      const result = {
        schema: {
          GraphName: 'MyGraph',
          VertexTypes: [],
          EdgeTypes: []
        },
        data: {
          explorationResult: {
            latestGraph: {
              nodes: [],
              links: []
            },
            latestSelections: {
              nodes: [],
              links: []
            },
            latestLayout: 'tree'
          },
          graphChartConfig: null,
          graphExplorerConfig: null
        }
      };
      dialogSpy.and.returnValue(dialogRef);
      getSpy.and.returnValue(of(result));
      spyOn(component.graphExplorerConfigApplied, 'next');
      component.componentName = 'QueryEditor';

      component.applyExploration();
      expect(component.graphExplorerConfigApplied.next).not.toHaveBeenCalled();
    });

    it('with no configurations and in graph explorer page', () => {
      const result = {
        schema: {
          GraphName: 'MyGraph',
          VertexTypes: [],
          EdgeTypes: []
        },
        data: {
          explorationResult: {
            latestGraph: {},
            latestSelections: {},
            latestLayout: 'tree'
          },
          graphChartConfig: null,
          graphExplorerConfig: null
        }
      };
      dialogSpy.and.returnValue(dialogRef);
      getSpy.and.returnValue(of(result));
      spyOn(<any>component, 'applyExplorationResult');
      spyOn(<any>component, 'applyExplorationChartConfig');
      spyOn(component.graphExplorerConfigApplied, 'next');
      component.componentName = 'GraphExplorer';

      component.applyExploration();
      expect((<any>component).saveSettingsToCacheService).not.toHaveBeenCalled();
    });
  });

  describe('should delete exploration', () => {
    let dialogSpy: jasmine.Spy;
    let deleteSpy: jasmine.Spy;
    const dialogRef = {
      keydownEvents: () => of(),
      afterClosed: () => of(1)
    };

    beforeEach(() => {
      dialogSpy = spyOn(dialog, 'open');
      deleteSpy = spyOn(compService, 'deleteExploration');
      spyOn(<any>component, 'handleError');
      spyOn(<any>component, 'toggleLoading');
      spyOn(<any>component, 'getSavedExplorationList');
      spyOn(snackBar, 'open');
    });

    it('with cancel', () => {
      const dialogRefCancel = {
        keydownEvents: () => of(),
        afterClosed: () => of(0)
      };
      dialogSpy.and.returnValue(dialogRefCancel);
      spyOn(<any>component, 'confirmDeleteExploration');

      component.deleteExploration('file1');
      expect((<any>component).confirmDeleteExploration).not.toHaveBeenCalled();
    });

    it('with continue and success', () => {
      dialogSpy.and.returnValue(dialogRef);
      deleteSpy.and.returnValue(of({ error: false }));
      component.checkedExploration = 'file1';

      component.deleteExploration('file1');
      expect(component.checkedExploration).toBeUndefined();
      expect((<any>component).getSavedExplorationList).toHaveBeenCalled();
      expect(snackBar.open).toHaveBeenCalledWith(
        'Exploration result "file1" is deleted',
        'DISMISS',
        { duration: 3000 }
      );
      expect((<any>component).toggleLoading).toHaveBeenCalledTimes(2);
      expect((<any>component).handleError).not.toHaveBeenCalled();
    });

    it('with failure', () => {
      dialogSpy.and.returnValue(dialogRef);
      deleteSpy.and.returnValue(throwError({ error: 'error', statusText: 'Error' }));
      component.checkedExploration = 'file1';

      component.deleteExploration('file1');
      expect(component.checkedExploration).toEqual('file1');
      expect((<any>component).getSavedExplorationList).not.toHaveBeenCalled();
      expect(snackBar.open).not.toHaveBeenCalled();
      expect((<any>component).toggleLoading).toHaveBeenCalledTimes(2);
      expect((<any>component).handleError).toHaveBeenCalled();
    });
  });

  describe('should toggle save exploration mode', () => {
    const dialogRef = {
      close: () => { },
      keydownEvents: () => of(),
      afterClosed: () => of()
    };
    beforeEach(() => {
      component.toolbarConfig = compService.getToolbarConfig();
      spyOn(<any>component, 'getPreviewImage');
    });

    it('with on', () => {
      spyOn(dialog, 'open').and.returnValue(dialogRef);
      component.onInteraction({ key: 'save' });
      expect(component.selectedEvent).toBe('save');
      expect(dialog.open).toHaveBeenCalled();
      expect(component.saveExplorationForm).toBeDefined();
      expect((<any>component.toolbarConfig.top[ToolbarButtons.Save]).color).toBe('accent');
    });

    it('with off', () => {
      spyOn(dialog, 'open').and.returnValue(dialogRef);

      component.onInteraction({ key: 'save' });
      (<any>component).toggleButtonStatus();
      expect((<any>component.toolbarConfig.top[ToolbarButtons.Save]).color).toBe('');
    });
  });

  it('should get exploration result preview image', () => {
    component.toolbarConfig = compService.getToolbarConfig();
    component.dataGraphContainer.containerRef = {
      nativeElement: {
        clientWidth: 600,
        clientHeight: 400,
      }
    };
    spyOn(graphRef.current, 'exportPng');
    spyOn(dialog, 'open').and.returnValue({
      keydownEvents: () => of(),
      afterClosed: () => of()
    });

    component.onInteraction({ key: 'save' });
    expect(graphRef.current.exportPng).toHaveBeenCalled();
  });

  describe('should save exploration', () => {
    let getListSpy: jasmine.Spy;
    let dialogSpy: jasmine.Spy;
    let saveSpy: jasmine.Spy;
    const explorationList = [
      {
        fileName: 'test1'
      },
      {
        fileName: 'test2'
      }
    ];

    beforeEach(() => {
      getListSpy = spyOn(compService, 'getSavedExplorationList');
      dialogSpy = spyOn(dialog, 'open');
      saveSpy = spyOn(compService, 'saveExploration');
      spyOn(<any>component, 'handleError');
      spyOn(<any>component, 'toggleLoading');
      spyOn(snackBar, 'open');
    });

    it('with success when using non existing name', () => {
      getListSpy.and.returnValue(of(explorationList));
      saveSpy.and.returnValue(of({ error: false }));
      spyOn(<any>component, 'getCurrentGraphResult').and.returnValue({});
      spyOn(cacheService, 'getItem').and.returnValue(null);
      spyOn(<any>component, 'closePopupWindow');
      component.explorationNameToSave = 'test3';
      component.explorationDescriptionToSave = 'dis';

      component.saveExploration();
      expect(dialog.open).not.toHaveBeenCalled();
      expect((<any>component).closePopupWindow).toHaveBeenCalled();
      expect(snackBar.open).toHaveBeenCalledWith(
        'Exploration result "test3" is saved',
        'DISMISS',
        { duration: 3000 }
      );
      expect((<any>component).toggleLoading).toHaveBeenCalledTimes(2);
      expect(component.explorationNameToSave).toEqual('');
      expect(component.explorationDescriptionToSave).toEqual('');
    });

    it('with failure when using non existing name', () => {
      getListSpy.and.returnValue(of(explorationList));
      saveSpy.and.returnValue(throwError({ error: 'error', statusText: 'Error' }));
      spyOn(<any>component, 'getCurrentGraphResult').and.returnValue({});
      spyOn(cacheService, 'getItem').and.returnValue(null);
      spyOn(<any>component, 'closePopupWindow');
      component.explorationNameToSave = 'test3';
      component.explorationDescriptionToSave = 'dis';

      component.saveExploration();
      expect((<any>component).closePopupWindow).not.toHaveBeenCalled();
      expect(snackBar.open).not.toHaveBeenCalled();
      expect((<any>component).toggleLoading).toHaveBeenCalledTimes(2);
      expect(component.explorationNameToSave).toEqual('test3');
      expect(component.explorationDescriptionToSave).toEqual('dis');
      expect((<any>component).handleError).toHaveBeenCalled();
    });

    it('with get saved exploration list failed', () => {
      getListSpy.and.returnValue(throwError({ error: 'err', statusText: 'Error' }));

      component.saveExploration();
      expect(dialog.open).not.toHaveBeenCalled();
      expect((<any>component).handleError).toHaveBeenCalled();
      expect((<any>component).toggleLoading).toHaveBeenCalledTimes(2);
    });

    it('with overwrite when using an existing name', () => {
      const dialogRef = {
        keydownEvents: () => of(),
        afterClosed: () => of(1)
      };
      dialogSpy.and.returnValue(dialogRef);
      getListSpy.and.returnValue(of(explorationList));
      spyOn(<any>component, 'confirmSaveExploration');
      component.explorationNameToSave = 'test1';

      component.saveExploration();
      expect((<any>component).confirmSaveExploration).toHaveBeenCalled();
      expect((<any>component).toggleLoading).toHaveBeenCalledTimes(1);
    });
  });

  it('should set new selected layout on selection change', () => {
    component.onLayoutSelection('tree');
    expect(component.graphLayout).toBe('tree');
  });

  it('should retrieve the image URL of the selected layout', () => {
    component.chartLayout = ['Force', 'tree'];

    component.onLayoutSelection('tree');
    expect(component.getLayoutImageURL(component.graphLayout)).toBe('assets/img/layout-icons/tree-layout-icon.png');
  });

  describe('should toggle vertex search mode', () => {
    const dialogRef = {
      close: () => { },
      keydownEvents: () => of(),
      afterClosed: () => of()
    };

    beforeEach(() => {
      component.toolbarConfig = compService.getToolbarConfig();
    });

    it('with on', () => {
      spyOn(dialog, 'open').and.returnValue(dialogRef);

      component.onInteraction({ key: 'search' });
      expect(component.selectedEvent).toBe('search');
      expect(dialog.open).toHaveBeenCalled();
      expect((<any>component.toolbarConfig.top[ToolbarButtons.Search]).color).toBe('accent');
    });

    it('with off', () => {
      spyOn(dialog, 'open').and.returnValue(dialogRef);

      component.onInteraction({ key: 'search' });
      (<any>component).toggleButtonStatus();
      expect((<any>component.toolbarConfig.top[ToolbarButtons.Search]).color).toBe('');
    });
  });

  describe('should search for vertices', () => {
    beforeEach(() => {
      component.searchKeyword = 'abc';
    });

    it('with matched id', () => {
      spyOn(<any>component, 'getNodes').and.returnValue([
        { exID: 'vId1' },
        { exID: 'abc' }
      ]);
      spyOn(<any>component, 'focusElementsInNewGraph');

      component.searchByVertexId = true;
      component.searchByVertexAttrs = false;
      component.searchVertex();
      expect((<any>component).focusElementsInNewGraph).toHaveBeenCalledWith({
        nodes: [{ exID: 'abc' }],
        links: []
      });
    });

    it('with matched attribute value', () => {
      spyOn(<any>component, 'getNodes').and.returnValue([
        {
          exID: 'vId1',
          attrs: {}
        },
        {
          exID: 'vId2',
          attrs: {
            a: '123',
            b: 'abc'
          }
        }
      ]);
      spyOn(<any>component, 'focusElementsInNewGraph');

      component.searchByVertexId = false;
      component.searchByVertexAttrs = true;
      component.searchVertex();
      expect((<any>component).focusElementsInNewGraph).toHaveBeenCalledWith({
        nodes: [{
          exID: 'vId2',
          attrs: {
            a: '123',
            b: 'abc'
          }
        }],
        links: []
      });
    });

    it('with no match', () => {
      spyOn(<any>component, 'getNodes').and.returnValue([
        { exID: 'vId1', attrs: {} },
        { exID: 'vId2', attrs: {} }
      ]);
      spyOn(<any>component, 'focusElementsInNewGraph');
      component.searchVertex();
      expect((<any>component).focusElementsInNewGraph).not.toHaveBeenCalled();
    });
  });

  describe('should hide all unselected elements', () => {
    it('when something is selected', () => {
      const selections = {
        nodes: [{
          type: 'vType1',
          id: 'vId1'
        }],
        links: [{
          type: 'eType1',
          source: {
            type: 'vType1',
            id: 'vId1'
          },
          target: {
            type: 'vType1',
            id: 'vId1'
          }
        }]
      };
      spyOn(graphRef.current, 'selectedNodes').and.returnValue(selections.nodes);
      spyOn(graphRef.current, 'selectedEdges').and.returnValue(selections.links);
      spyOn(logicService, 'retain');
      spyOn(<any>component, 'renderChart');

      component.onInteraction({ key: 'show' });
      expect(logicService.retain).toHaveBeenCalled();
      expect((<any>component).renderChart).toHaveBeenCalled();
    });

    it('when nothing is selected', () => {
      spyOn(graphRef.current, 'selectedNodes').and.returnValue([]);
      spyOn(graphRef.current, 'selectedEdges').and.returnValue([]);
      spyOn(logicService, 'retain');
      spyOn(<any>component, 'renderChart');

      component.onInteraction({ key: 'show' });
      expect(logicService.retain).not.toHaveBeenCalled();
      expect((<any>component).renderChart).not.toHaveBeenCalled();
    });
  });

  describe('should hide all selected elements', () => {
    it('when something is selected', () => {
      spyOn(logicService, 'getGraph').and.returnValue({
        graph: {
          nodes: [],
        }
      });
      const selections = {
        nodes: [{
          type: 'vType1',
          id: 'vId1'
        }],
        links: [{
          type: 'eType1',
          source: {
            type: 'vType1',
            id: 'vId1'
          },
          target: {
            type: 'vType1',
            id: 'vId1'
          }
        }]
      };
      spyOn(graphRef.current, 'selectedNodes').and.returnValue(selections.nodes);
      spyOn(graphRef.current, 'selectedEdges').and.returnValue(selections.links);
      spyOn(logicService, 'remove');
      spyOn(<any>component, 'renderChart');

      component.selectionChange.subscribe(value => expect(value).toEqual([[], []]));
      component.onInteraction({ key: 'hide' });
      expect(logicService.remove).toHaveBeenCalled();
      expect((<any>component).renderChart).toHaveBeenCalled();
    });

    it('when nothing is selected', () => {
      spyOn(graphRef.current, 'selectedNodes').and.returnValue([]);
      spyOn(graphRef.current, 'selectedEdges').and.returnValue([]);
      spyOn(logicService, 'removeAll');
      spyOn(<any>component, 'renderChart');

      component.selectionChange.subscribe(value => expect(value).toEqual([[], []]));
      component.onInteraction({ key: 'hide' });
      expect(logicService.removeAll).toHaveBeenCalled();
      expect((<any>component).renderChart).toHaveBeenCalled();
    });
  });

  it('should undo to previous graph', () => {
    spyOn(logicService, 'getGraph').and.returnValue({
      graph: {
        nodes: [],
      }
    });
    spyOn(logicService, 'undo').and.returnValue({ graph: {}, diff: 1 });
    spyOn(<any>component, 'renderChart');
    component.onInteraction({ key: 'undo' });
    expect(logicService.undo).toHaveBeenCalled();
    expect((<any>component).renderChart).toHaveBeenCalledWith({ graph: {}, diff: 1 });
  });

  it('should redo to next graph', () => {
    spyOn(logicService, 'getGraph').and.returnValue({});
    spyOn(logicService, 'redo').and.returnValue({ graph: {}, diff: 1 });
    spyOn(<any>component, 'renderChart');
    component.onInteraction({ key: 'redo' });
    expect(logicService.redo).toHaveBeenCalled();
    expect((<any>component).renderChart).toHaveBeenCalledWith({ graph: {}, diff: 1 });
  });

  describe('should toggle add vertex mode', () => {
    beforeEach(() => {
      component.toolbarConfig = compService.getToolbarConfig();
    });

    it('with on', () => {
      (<any>component).toggleAddVertexMode();
      expect((<any>component.toolbarConfig.top[ToolbarButtons.AddVertex]).color).toBe('accent');
    });

    it('with off', () => {
      (<any>component).toggleAddVertexMode();
      (<any>component).toggleAddVertexMode();
      expect((<any>component.toolbarConfig.top[ToolbarButtons.AddVertex]).color).toBe('');
    });
  });

  it('should populate new attribute list depend on the selected vertex', () => {
    const schemaVertex = {
      primaryId: {
        hasDefaultValue: true,
        defaultValue: 'abc'
      },
      attributes: [
        {
          name: 'a',
          hasDefaultValue: true,
          defaultValue: '123'
        },
        {
          name: 'b',
          hasDefaultValue: false
        }
      ]
    };
    spyOn(logicService, 'getSchemaVertex').and.returnValue(schemaVertex);
    spyOn(compService, 'buildAttributeQuestions');

    component.onVertexTypeChange('vType1');
    expect(compService.buildAttributeQuestions).toHaveBeenCalled();
  });

  describe('should retrieve attribute from dynamic form', () => {
    it('with Unsigned Integer and Float type', () => {
      const schemaVertex = {
        attributes: [
          {
            name: 'a',
            type: {
              name: 'UINT'
            },
            hasDefaultValue: false
          },
          {
            name: 'b',
            type: {
              name: 'FLOAT'
            },
            hasDefaultValue: false
          }
        ]
      };
      const vertex = {
        type: 'INT',
        id: 123,
        attrs: {}
      };
      dynamicForm = component.dynamicForm = <any>new MockDynamicForm();
      dynamicForm.form.value = {
        'a': '123',
        'b': '1.00'
      };

      spyOn(compService, 'convertValueType').and.callFake(function (value) { return Number(value); });
      component.retrieveAttributeFromForm(<any>schemaVertex, <any>vertex);

      expect(vertex.attrs['a']).toBe(123);
      expect(vertex.attrs['b']).toBe(1.00);

    });

    it('with Integer and Double type', () => {
      const schemaVertex = {
        attributes: [
          {
            name: 'a',
            type: {
              name: 'INT'
            },
            hasDefaultValue: false
          },
          {
            name: 'b',
            type: {
              name: 'DOUBLE'
            },
            hasDefaultValue: false
          }
        ]
      };
      const vertex = {
        type: 'INT',
        id: 123,
        attrs: {}
      };
      dynamicForm = component.dynamicForm = <any>new MockDynamicForm();
      dynamicForm.form.value = {
        'a': '123',
        'b': '1.00'
      };

      spyOn(compService, 'convertValueType').and.callFake(function (value) { return Number(value); });
      component.retrieveAttributeFromForm(<any>schemaVertex, <any>vertex);

      expect(vertex.attrs['a']).toBe(123);
      expect(vertex.attrs['b']).toBe(1.00);

    });

    it('with Bool type', () => {
      const schemaVertex = {
        attributes: [{
          name: 'a',
          type: {
            name: 'BOOL'
          },
          hasDefaultValue: false
        }]
      };
      const vertex = {
        type: 'INT',
        id: 123,
        attrs: {}
      };
      dynamicForm = component.dynamicForm = <any>new MockDynamicForm();
      dynamicForm.form.value = {
        'a': 'true',
      };

      spyOn(compService, 'convertValueType').and.callFake(function (value) { return (value === 'true'); });
      component.retrieveAttributeFromForm(<any>schemaVertex, <any>vertex);

      expect(vertex.attrs['a']).toBe(true);
    });

    describe('with LIST type', () => {
      it('with base type value', () => {
        const schemaVertex = {
          attributes: [
            {
              name: 'a',
              type: {
                name: 'LIST',
                valueTypeName: 'INT'
              },
              hasDefaultValue: false
            }
          ]
        };
        const vertex = {
          type: 'INT',
          id: 123,
          attrs: {}
        };
        dynamicForm = component.dynamicForm = <any>new MockDynamicForm();
        dynamicForm.form.value = {
          'a': ['1', '2', '3']
        };

        spyOn(compService, 'convertValueType').and.callFake(function (value) { return Number(value); });
        component.retrieveAttributeFromForm(<any>schemaVertex, <any>vertex);

        expect(vertex.attrs['a']).toEqual([1, 2, 3]);
      });

      it('with UDT type value', () => {
        const schemaVertex = {
          attributes: [
            {
              name: 'a',
              type: {
                name: 'LIST',
                valueTypeName: 'UDT',
                valueTypeTupleName: 'testTuple'
              },
              hasDefaultValue: false
            }
          ]
        };
        const vertex = {
          type: 'INT',
          id: '123',
          attrs: {}
        };
        dynamicForm = component.dynamicForm = <any>new MockDynamicForm();
        dynamicForm.form.value = {
          'a': [{ 'f1': '0' }, { 'f1': '1' }, { 'f1': '2' }]
        };

        component.retrieveAttributeFromForm(<any>schemaVertex, <any>vertex);
        expect(vertex.attrs['a']).toEqual([{ 'f1': '0' }, { 'f1': '1' }, { 'f1': '2' }]);
      });
    });

    describe('with SET type', () => {
      it('with base type value', () => {
        const schemaVertex = {
          attributes: [
            {
              name: 'a',
              type: {
                name: 'SET',
                valueTypeName: 'INT'
              },
              hasDefaultValue: false
            }
          ]
        };
        const vertex = {
          type: 'INT',
          id: 123,
          attrs: {}
        };
        dynamicForm = component.dynamicForm = <any>new MockDynamicForm();
        dynamicForm.form.value = {
          'a': ['1', '1', '2', '3', '2']
        };

        spyOn(compService, 'convertValueType').and.callFake(function (value) { return Number(value); });
        component.retrieveAttributeFromForm(<any>schemaVertex, <any>vertex);

        expect(vertex.attrs['a']).toEqual([1, 2, 3]);
      });

      it('with UDT type value', () => {
        const schemaVertex = {
          attributes: [
            {
              name: 'a',
              type: {
                name: 'SET',
                valueTypeName: 'UDT',
                valueTypeTupleName: 'testTuple'
              },
              hasDefaultValue: false
            }
          ]
        };
        const vertex = {
          type: 'INT',
          id: 123,
          attrs: {}
        };
        dynamicForm = component.dynamicForm = <any>new MockDynamicForm();
        dynamicForm.form.value = {
          'a': [{ 'f1': '0' }, { 'f1': '1' }]
        };

        component.retrieveAttributeFromForm(<any>schemaVertex, <any>vertex);
        expect(vertex.attrs['a']).toEqual([{ 'f1': '0' }, { 'f1': '1' }]);
      });
    });

    describe('with MAP type', () => {
      it('with basic type value', () => {
        const schemaVertex = {
          attributes: [
            {
              name: 'a',
              type: {
                name: 'MAP',
                keyTypeName: 'INT',
                valueTypeName: 'STRING'
              },
              hasDefaultValue: false
            }
          ]
        };
        const vertex = {
          type: 'INT',
          id: 123,
          attrs: {}
        };
        dynamicForm = component.dynamicForm = <any>new MockDynamicForm();
        dynamicForm.form.value = {
          'a': [
            {
              key: '101',
              value: 'value'
            },
            {
              key: '102',
              value: 'test'
            }]
        };

        spyOn(compService, 'convertValueType').and.callFake(function (value, type) {
          return type === 'INT' ? Number(value) : value;
        });
        component.retrieveAttributeFromForm(<any>schemaVertex, <any>vertex);
        expect(vertex.attrs['a']).toEqual({ 101: 'value', 102: 'test' });
      });

      it('with UDT type value', () => {
        const schemaVertex = {
          attributes: [
            {
              name: 'a',
              type: {
                name: 'MAP',
                keyTypeName: 'INT',
                valueTypeName: 'UDT',
                valueTypeTupleName: 'testTuple'
              },
              hasDefaultValue: false
            }
          ]
        };
        const vertex = {
          type: 'INT',
          id: 123,
          attrs: {}
        };
        dynamicForm = component.dynamicForm = <any>new MockDynamicForm();
        dynamicForm.form.value = {
          'a': [
            {
              key: '101',
              value: {
                'f1': '0'
              }
            },
            {
              key: '102',
              value: {
                'f1': '3'
              }
            }
          ]
        };
        component.retrieveAttributeFromForm(<any>schemaVertex, <any>vertex);
        expect(vertex.attrs['a']).toEqual({ 101: { 'f1': '0' }, 102: { 'f1': '3' } });
      });
    });

    it('with UDT type', () => {
      const schemaVertex = {
        attributes: [
          {
            name: 'a',
            type: {
              name: 'UDT',
              tupleName: 'testTuple'
            },
            hasDefaultValue: false
          }
        ]
      };
      const vertex = {
        type: 'INT',
        id: 123,
        attrs: {}
      };
      dynamicForm = component.dynamicForm = <any>new MockDynamicForm();
      dynamicForm.form.value = {
        'a': {
          'f1': 10
        }
      };

      spyOn(compService, 'convertValueType').and.callFake(function (value, type) {
        return type === 'INT' ? Number(value) : value;
      });
      component.udtList = udtList;
      component.retrieveAttributeFromForm(<any>schemaVertex, <any>vertex);
      expect(vertex.attrs['a']).toEqual({ 'f1': 10 });
    });
  });

  describe('should add a new vertex', () => {
    const schemaVertex = {
      primaryId: {
        hasDefaultValue: false
      },
      attributes: [
        {
          name: 'a',
          hasDefaultValue: false
        },
        {
          name: 'b',
          hasDefaultValue: false
        }
      ]
    };

    beforeEach(() => {
      spyOn(logicService, 'getAllVertexTypes').and.returnValue(['vType1', 'vType2']);
      spyOn(logicService, 'getSchemaVertex').and.returnValue(schemaVertex);
      spyOn(component, 'retrieveAttributeFromForm');
      spyOn(dialog, 'open').and.returnValue({
        close: () => { },
        keydownEvents: () => of(),
        afterClosed: () => of()
      });
    });

    it('with success', () => {
      // Prepping for adding vertex.
      component.selectedEvent = 'editAttributes'; // to cover adding a vertex after editing attributes of an element.

      component.onInteraction({ key: 'addVertex' });
      expect(component.vertex.type).toBe('vType1'); // default type for selection.
      expect(component.selectedEvent).toBe('addVertex');
      expect(dialog.open).toHaveBeenCalled();

      // Actual action.
      spyOn(logicService, 'addVertex').and.returnValue({ success: true });
      spyOn(<any>component, 'preCheckForSave').and.returnValue(of(true));
      spyOn(compService, 'upsertVertex').and.returnValue(of({}));
      spyOn(<any>component, 'renderChart');
      spyOn(component, 'highlight');
      spyOn(<any>component, 'focusElementsInNewGraph');
      spyOn(component.loading, 'next');
      spyOn(<any>component, 'initializeVertex').and.callThrough();

      component.addElement();
      expect(component.retrieveAttributeFromForm).toHaveBeenCalled();
      expect(logicService.addVertex).toHaveBeenCalledTimes(1);
      expect((<any>component).renderChart).toHaveBeenCalled();
      // expect(component.highlight).toHaveBeenCalled();
      expect(component.loading.next).toHaveBeenCalledTimes(2);
      expect((<any>component).initializeVertex).toHaveBeenCalled();
    });

    it('with failure from local addition', () => {
      // Prepping for adding vertex.
      component.onInteraction({ key: 'addVertex' });

      // Actual action.
      spyOn(logicService, 'addVertex').and.returnValue({ success: false });
      spyOn(<any>component, 'handleError').and.callThrough();

      component.addElement();
      expect(logicService.addVertex).toHaveBeenCalledTimes(1);
      expect((<any>component).handleError).toHaveBeenCalled();
      expect(component.retrieveAttributeFromForm).toHaveBeenCalled();
    });

    it('with failure from user', () => {
      // Prepping for adding vertex.
      component.onInteraction({ key: 'addVertex' });

      // Actual action.
      spyOn(logicService, 'addVertex').and.returnValue({ success: true });
      spyOn(<any>component, 'preCheckForSave').and.returnValue(of(false));
      spyOn(component.loading, 'next');

      component.addElement();
      expect(logicService.addVertex).toHaveBeenCalledTimes(1);
      expect(component.loading.next).toHaveBeenCalledTimes(2);
      expect(component.retrieveAttributeFromForm).toHaveBeenCalled();
    });

    it('with failure from server addition', () => {
      // Prepping for adding vertex.
      component.onInteraction({ key: 'addVertex' });

      // Actual action.
      spyOn(logicService, 'addVertex').and.returnValue({ success: true });
      spyOn(<any>component, 'preCheckForSave').and.returnValue(of(true));
      spyOn(compService, 'upsertVertex').and.returnValue(throwError({ error: 'error', statusText: 'Error' }));
      spyOn(component.loading, 'next');
      spyOn(<any>component, 'handleError').and.callThrough();
      spyOn(logicService, 'getMatchEdgeTypes').and.returnValue({
        matchedEdgeTypes: ['eType1', 'eType2'],
        shouldReverseEdge: false,
      });

      component.addElement();
      expect(logicService.addVertex).toHaveBeenCalledTimes(1);
      expect(component.retrieveAttributeFromForm).toHaveBeenCalled();
      expect(component.loading.next).toHaveBeenCalledTimes(2);
      expect((<any>component).handleError).toHaveBeenCalled();
    });
  });

  describe('should toggle add edge mode', () => {
    beforeEach(() => {
      component.toolbarConfig = compService.getToolbarConfig();
    });

    it('with on', () => {
      component.onInteraction({ key: 'addEdge' });
      expect((<any>component.toolbarConfig.top[ToolbarButtons.AddEdge]).color).toBe('accent');
      component.toolbarConfig.top.forEach((button, i) => {
        if (i === ToolbarButtons.AddEdge) {
          expect(button.disabled).toBeFalsy();
        } else {
          expect(button.disabled).toBeTruthy();
        }
      });
    });

    it('with off and no source vertex', () => {
      component.onInteraction({ key: 'addEdge' });
      component.onInteraction({ key: 'addEdge' });
      expect((<any>component.toolbarConfig.top[ToolbarButtons.AddEdge]).color).toBe('');
      component.toolbarConfig.top.forEach(button => expect(button.disabled).toBeFalsy());
    });

    it('with off and a source vertex', () => {
      (<any>component).sourceVertex = { others: {} };
      component.onInteraction({ key: 'addEdge' });
      component.onInteraction({ key: 'addEdge' });
      expect((<any>component.toolbarConfig.top[ToolbarButtons.AddEdge]).color).toBe('');
      component.toolbarConfig.top.forEach(button => expect(button.disabled).toBeFalsy());
    });
  });

  describe('should populate new attribute list depend on the selected edge', () => {
    it('for single-edge', () => {
      const schemaEdge = {
        attributes: [
          {
            name: 'a',
            hasDefaultValue: true,
            defaultValue: '123',
            isDiscriminator: false
          },
          {
            name: 'b',
            hasDefaultValue: false,
            isDiscriminator: false
          }
        ],
        discriminatorCount: 0,
        compositeDiscriminator: []
      };
      spyOn(compService, 'buildAttributeQuestions');
      spyOn(logicService, 'getSchemaEdge').and.returnValue(schemaEdge);
      (<any>component).sourceVertex = { exType: 'vType1', exID: 'vId1' };
      (<any>component).targetVertex = { exType: 'vType2', exID: 'vId2' };

      component.onEdgeTypeChange('eType1');
      expect(component.edge.source).toEqual({ type: 'vType1', id: 'vId1' });
      expect(component.edge.target).toEqual({ type: 'vType2', id: 'vId2' });
      expect(component.isDiscriminatorList).toEqual([false, false]);
      expect(compService.buildAttributeQuestions).toHaveBeenCalledWith(schemaEdge.attributes);
    });

    it('for multi-edge', () => {
      const schemaEdge = {
        attributes: [
          {
            name: 'a',
            hasDefaultValue: true,
            defaultValue: '123',
            isDiscriminator: true
          },
          {
            name: 'b',
            hasDefaultValue: false,
            isDiscriminator: true
          },
          {
            name: 'c',
            hasDefaultValue: false,
            isDiscriminator: false
          }
        ],
        discriminatorCount: 2,
        compositeDiscriminator: ['a', 'b']
      };
      spyOn(compService, 'buildAttributeQuestions');
      spyOn(logicService, 'getSchemaEdge').and.returnValue(schemaEdge);
      (<any>component).sourceVertex = { exType: 'vType1', exID: 'vId1' };
      (<any>component).targetVertex = { exType: 'vType2', exID: 'vId2' };

      component.onEdgeTypeChange('eType1');
      expect(component.edge.source).toEqual({ type: 'vType1', id: 'vId1' });
      expect(component.edge.target).toEqual({ type: 'vType2', id: 'vId2' });
      expect(component.isDiscriminatorList).toEqual([true, true, false]);
      expect(compService.buildAttributeQuestions).toHaveBeenCalledWith(schemaEdge.attributes);
    });
  });

  it('should re-initialize edge action when popup window close while adding edge', () => {
    spyOn(<any>component, 'initializeEdgeAction');
    component.selectedEvent = 'addEdge';
    component.closePopup();
    expect((<any>component).initializeEdgeAction).toHaveBeenCalled();
  });

  describe('should add a new edge', () => {
    const schemaEdge = {
      attributes: [
        {
          name: 'a',
          hasDefaultValue: true,
          defaultValue: '123'
        },
        {
          name: 'b',
          hasDefaultValue: false
        }
      ]
    };

    beforeEach(() => {
      spyOn(logicService, 'getSchemaEdge').and.returnValue(schemaEdge);
      spyOn(compService, 'getAddEdgeLabel');
      spyOn(dialog, 'open').and.returnValue({
        close: () => { },
        keydownEvents: () => of(),
        afterClosed: () => of()
      });
    });

    it('with success without reversing the source and target vertex', () => {
      // Prepping for adding edge.
      spyOn(logicService, 'getMatchEdgeTypes').and.returnValue(
        {
          matchedEdgeTypes: ['eType1', 'eType2'],
          shouldReverseEdge: false
        }
      );
      component.selectedEvent = 'editAttributes'; // to cover adding an edge after editing attributes of an element.

      component.onInteraction({ key: 'addEdge' });
      component.selectedVertex = <any>{ exType: 'vType1', exID: 'vId1', others: {} };
      (<any>component).handleAddEdge();
      expect(compService.getAddEdgeLabel).toHaveBeenCalled();
      component.selectedVertex = <any>{ exType: 'vType2', exID: 'vId2', others: {} };
      (<any>component).handleAddEdge();
      expect(dialog.open).toHaveBeenCalled();
      expect(component.selectedEvent).toBe('addEdge');
      expect((<any>component).sourceVertex.exType).toBe('vType1');
      expect((<any>component).targetVertex.exType).toBe('vType2');

      // Actual action.
      spyOn(logicService, 'addEdge').and.returnValue({ success: true });
      spyOn(<any>component, 'preCheckForSave').and.returnValue(of(true));
      spyOn(compService, 'upsertEdge').and.returnValue(of({}));
      spyOn(<any>component, 'renderChart');
      spyOn(<any>component, 'focusElementsInNewGraph');
      spyOn(component.loading, 'next');
      spyOn(<any>component, 'initializeEdge').and.callThrough();
      spyOn(<any>component, 'initializeEdgeAction').and.callThrough();
      spyOn(component, 'retrieveAttributeFromForm');

      component.addElement();
      expect(logicService.addEdge).toHaveBeenCalledTimes(2);
      expect((<any>component).renderChart).toHaveBeenCalled();
      expect(component.loading.next).toHaveBeenCalledTimes(2);
      expect((<any>component).initializeEdge).toHaveBeenCalled();
      expect((<any>component).initializeEdgeAction).toHaveBeenCalled();
      expect(component.retrieveAttributeFromForm).toHaveBeenCalled();
    });

    it('with success with reversing the source and target vertex', () => {
      // Prepping for adding edge.
      spyOn(logicService, 'getMatchEdgeTypes').and.returnValue(
        {
          matchedEdgeTypes: ['eType1'],
          shouldReverseEdge: true
        }
      );
      component.selectedEvent = 'editAttributes'; // to cover adding an edge after editing attributes of an element.

      component.onInteraction({ key: 'addEdge' });
      component.selectedVertex = <any>{ exType: 'vType1', exID: 'vId1', others: {} };
      (<any>component).handleAddEdge();
      expect(compService.getAddEdgeLabel).toHaveBeenCalled();
      component.selectedVertex = <any>{ exType: 'vType2', exID: 'vId2', others: {} };
      (<any>component).handleAddEdge();
      expect(dialog.open).toHaveBeenCalled();
      expect(component.selectedEvent).toBe('addEdge');
      expect((<any>component).sourceVertex.exType).toBe('vType2');
      expect((<any>component).targetVertex.exType).toBe('vType1');

      // Actual action.
      spyOn(logicService, 'addEdge').and.returnValue({ success: true });
      spyOn(<any>component, 'preCheckForSave').and.returnValue(of(true));
      spyOn(compService, 'upsertEdge').and.returnValue(of({}));
      spyOn(<any>component, 'renderChart');
      spyOn(<any>component, 'focusElementsInNewGraph');
      spyOn(component.loading, 'next');
      spyOn(<any>component, 'initializeEdge').and.callThrough();
      spyOn(<any>component, 'initializeEdgeAction').and.callThrough();
      spyOn(component, 'retrieveAttributeFromForm');

      component.addElement();
      expect(logicService.addEdge).toHaveBeenCalledTimes(2);
      expect((<any>component).renderChart).toHaveBeenCalled();
      expect(component.loading.next).toHaveBeenCalledTimes(2);
      expect((<any>component).initializeEdge).toHaveBeenCalled();
      expect((<any>component).initializeEdgeAction).toHaveBeenCalled();
      expect(component.retrieveAttributeFromForm).toHaveBeenCalled();
    });

    it('with failure from no matching edge', () => {
      // Prepping for adding edge.
      spyOn(logicService, 'getMatchEdgeTypes').and.returnValue(
        {
          matchedEdgeTypes: [],
          shouldReverseEdge: false
        }
      );
      spyOn(<any>component, 'initializeEdgeAction');
      spyOn(<any>component, 'handleError').and.callThrough();

      component.onInteraction({ key: 'addEdge' });
      component.selectedVertex = <any>{ exType: 'vType1', exID: 'vId1', others: {} };
      (<any>component).handleAddEdge();
      expect(compService.getAddEdgeLabel).toHaveBeenCalled();
      component.selectedVertex = <any>{ exType: 'vType1', exID: 'vId1', others: {} };
      (<any>component).handleAddEdge();
      expect(dialog.open).toHaveBeenCalled();
      expect((<any>component).initializeEdgeAction).toHaveBeenCalled();
      expect((<any>component).handleError).toHaveBeenCalledWith(
        'There is no edge type between the selected vertices.',
        'Warning'
      );
    });

    it('with failure from local addition', () => {
      // Prepping for adding edge.
      spyOn(logicService, 'getMatchEdgeTypes').and.returnValue(
        {
          matchedEdgeTypes: ['eType1', 'eType2'],
          shouldReverseEdge: false
        }
      );

      component.onInteraction({ key: 'addEdge' });
      component.selectedVertex = <any>{ exType: 'vType1', exID: 'vId1', others: {} };
      (<any>component).handleAddEdge();
      expect(compService.getAddEdgeLabel).toHaveBeenCalled();
      component.selectedVertex = <any>{ exType: 'vType1', exID: 'vId1', others: {} };
      (<any>component).handleAddEdge();
      expect(dialog.open).toHaveBeenCalled();
      expect(component.selectedEvent).toBe('addEdge');

      // Actual action.
      spyOn(logicService, 'addEdge').and.returnValue({ success: false });
      spyOn(component, 'retrieveAttributeFromForm');
      spyOn(<any>component, 'handleError').and.callThrough();

      component.addElement();
      expect(logicService.addEdge).toHaveBeenCalledTimes(1);
      expect(component.retrieveAttributeFromForm).toHaveBeenCalled();
      expect((<any>component).handleError).toHaveBeenCalled();
    });

    it('with failure from user', () => {
      // Prepping for adding edge.
      spyOn(logicService, 'getMatchEdgeTypes').and.returnValue(
        {
          matchedEdgeTypes: ['eType1', 'eType2'],
          shouldReverseEdge: false
        }
      );
      component.selectedEvent = 'editAttributes'; // to cover adding an edge after editing attributes of an element.

      component.onInteraction({ key: 'addEdge' });
      component.selectedVertex = <any>{ exType: 'vType1', exID: 'vId1', others: {} };
      (<any>component).handleAddEdge();
      expect(compService.getAddEdgeLabel).toHaveBeenCalled();
      component.selectedVertex = <any>{ exType: 'vType1', exID: 'vId1', others: {} };
      (<any>component).handleAddEdge();
      expect(dialog.open).toHaveBeenCalled();
      expect(component.selectedEvent).toBe('addEdge');

      // Actual action.
      spyOn(logicService, 'addEdge').and.returnValue({ success: true });
      spyOn(<any>component, 'preCheckForSave').and.returnValue(of(false));
      spyOn(component.loading, 'next');
      spyOn(component, 'retrieveAttributeFromForm');

      component.addElement();
      expect(component.retrieveAttributeFromForm).toHaveBeenCalled();
      expect(logicService.addEdge).toHaveBeenCalledTimes(1);
      expect(component.loading.next).toHaveBeenCalledTimes(2);
    });

    it('with failure from server', () => {
      // Prepping for adding edge.
      spyOn(logicService, 'getMatchEdgeTypes').and.returnValue(
        {
          matchedEdgeTypes: ['eType1', 'eType2'],
          shouldReverseEdge: false
        }
      );
      component.selectedEvent = 'editAttributes'; // to cover adding an edge after editing attributes of an element.

      component.onInteraction({ key: 'addEdge' });
      component.selectedVertex = <any>{ exType: 'vType1', exID: 'vId1', others: {} };
      (<any>component).handleAddEdge();
      expect(compService.getAddEdgeLabel).toHaveBeenCalled();
      component.selectedVertex = <any>{ exType: 'vType1', exID: 'vId1', others: {} };
      (<any>component).handleAddEdge();
      expect(dialog.open).toHaveBeenCalled();
      expect(component.selectedEvent).toBe('addEdge');

      // Actual action.
      spyOn(logicService, 'addEdge').and.returnValue({ success: true });
      spyOn(component, 'retrieveAttributeFromForm');
      spyOn(<any>component, 'preCheckForSave').and.returnValue(of(true));
      spyOn(compService, 'upsertEdge').and.returnValue(throwError({ error: 'error', statusText: 'Error' }));
      spyOn(component.loading, 'next');
      spyOn(<any>component, 'handleError').and.callThrough();

      component.addElement();
      expect(logicService.addEdge).toHaveBeenCalledTimes(1);
      expect(component.retrieveAttributeFromForm).toHaveBeenCalled();
      expect(component.loading.next).toHaveBeenCalledTimes(2);
      expect(dialog.open).toHaveBeenCalled();
      expect((<any>component).handleError).toHaveBeenCalled();
    });
  });

  describe('should pre-check before upsert', () => {
    it('with no vertex', () => {
      spyOn(compService, 'getVertex').and.returnValue(of([]));
      (<any>component).preCheckForSave(undefined, true).subscribe(value => expect(value).toBeTruthy());
    });

    it('with no single-edge', () => {
      logicService.schema.name = 'tempGraph';
      const schemaEdge = {
        type: 'singleEdge',
        discriminatorCount: 0,
        compositeDiscriminator: []
      };
      spyOn(compService, 'getEdge').and.returnValue(throwError('error'));
      spyOn(compService, 'getMultiEdge');
      spyOn(logicService, 'getSchemaEdge').and.returnValue(schemaEdge);
      (<any>component).preCheckForSave({ type: '' }).subscribe(value => {
        expect(compService.getEdge).toHaveBeenCalledWith(logicService.schema.name, { type: '' });
        expect(compService.getMultiEdge).not.toHaveBeenCalled();
        expect(value).toBeTruthy();
      });
    });

    it('with no multi-edge', () => {
      logicService.schema.name = 'tempGraph';
      const schemaEdge = {
        type: 'multiEdge',
        discriminatorCount: 1,
        compositeDiscriminator: ['a']
      };
      const link = {
        type: 'multiEdge',
        attrs: {
          a: '1'
        }
      };
      spyOn(compService, 'getEdge');
      spyOn(compService, 'getMultiEdge').and.returnValue(throwError('error'));
      spyOn(logicService, 'getSchemaEdge').and.returnValue(schemaEdge);
      (<any>component).preCheckForSave(link).subscribe(value => {
        expect(compService.getEdge).not.toHaveBeenCalled();
        expect(compService.getMultiEdge).toHaveBeenCalledWith(logicService.schema.name, link, schemaEdge.compositeDiscriminator);
        expect(value).toBeTruthy();
      });
    });

    it('with existing vertex/edge and user select "Cancel"', () => {
      const dialogRef = {
        keydownEvents: () => of(),
        afterClosed: () => { }
      };
      spyOn(compService, 'getVertex').and.returnValue(of(['abc']));
      spyOn(dialog, 'open').and.returnValue(dialogRef);
      spyOn(dialogRef, 'afterClosed').and.returnValue(of(0));

      (<any>component).preCheckForSave(undefined, true).subscribe(value => expect(value).toBeFalsy());
    });

    it('with existing vertex/edge and user select "Overwrite"', () => {
      const dialogRef = {
        keydownEvents: () => of(),
        afterClosed: () => { }
      };
      spyOn(compService, 'getVertex').and.returnValue(of(['abc']));
      spyOn(dialog, 'open').and.returnValue(dialogRef);
      spyOn(dialogRef, 'afterClosed').and.returnValue(of(1));

      (<any>component).preCheckForSave(undefined, true).subscribe(value => expect(value).toBeTruthy());
    });

    it('with existing multi-edge and user select "Cancel"', () => {
      const dialogRef = {
        keydownEvents: () => of(),
        afterClosed: () => { }
      };
      logicService.schema.name = 'tempGraph';
      const link = {
        type: 'multiEdge',
        attrs: {
          a: '1'
        }
      };
      const schemaEdge = {
        type: 'multiEdge',
        discriminatorCount: 1,
        compositeDiscriminator: ['a']
      };
      spyOn(logicService, 'getSchemaEdge').and.returnValue(schemaEdge);
      spyOn(compService, 'getEdge');
      spyOn(compService, 'getMultiEdge').and.returnValue(of(['abc']));
      spyOn(dialog, 'open').and.returnValue(dialogRef);
      spyOn(dialogRef, 'afterClosed').and.returnValue(of(0));

      (<any>component).preCheckForSave(link, false).subscribe(value => {
        expect(compService.getEdge).not.toHaveBeenCalled();
        expect(compService.getMultiEdge).toHaveBeenCalledWith(logicService.schema.name, link, schemaEdge.compositeDiscriminator);
        expect(value).toBeFalsy();
      });
    });

    it('with existing multi-edge and user select "Overwrite"', () => {
      const dialogRef = {
        keydownEvents: () => of(),
        afterClosed: () => { }
      };
      logicService.schema.name = 'tempGraph';
      const link = {
        type: 'multiEdge',
        attrs: {
          a: '1'
        }
      };
      const schemaEdge = {
        type: 'multiEdge',
        discriminatorCount: 1,
        compositeDiscriminator: ['a']
      };
      spyOn(logicService, 'getSchemaEdge').and.returnValue(schemaEdge);
      spyOn(compService, 'getEdge');
      spyOn(compService, 'getMultiEdge').and.returnValue(of(['abc']));
      spyOn(dialog, 'open').and.returnValue(dialogRef);
      spyOn(dialogRef, 'afterClosed').and.returnValue(of(1));

      (<any>component).preCheckForSave(link, false).subscribe(value => {
        expect(compService.getEdge).not.toHaveBeenCalled();
        expect(compService.getMultiEdge).toHaveBeenCalledWith(logicService.schema.name, link, schemaEdge.compositeDiscriminator);
        expect(value).toBeTruthy();
      });
    });
  });

  describe('should open update window', () => {
    beforeEach(() => {
      spyOn(dialog, 'open').and.returnValue({
        keydownEvents: () => of(),
        afterClosed: () => of()
      });
    });

    it('for vertex', () => {
      const expectedVertex = {
        type: 'type',
        id: 'id',
        attrs: {
          a: 'abc',
          '@b': 0
        }
      };
      spyOn(logicService, 'getVertex').and.returnValue(expectedVertex);
      spyOn(logicService, 'getSchemaVertex').and.returnValue({ attributes: [{ name: 'a' }] });
      component.selectedVertex = <any>{ exType: 'type', exID: 'id' };

      component.onInteraction({ key: 'editAttributes' });
      expect(component.selectedEvent).toBe('editAttributes');
      expect(component.vertex).toBe(expectedVertex);
      expect(component.vertexTypes).toEqual(['type']);
      expect(dialog.open).toHaveBeenCalled();
    });

    it('for single-edge', () => {
      const expectedEdge = {
        type: 'type',
        source: {
          type: 'vType1',
          id: 'vId1'
        },
        target: {
          type: 'vType2',
          id: 'vId2'
        },
        attrs: {
          a: 'abc',
          '@b': 0
        }
      };
      spyOn(logicService, 'getEdge').and.returnValue(expectedEdge);
      spyOn(logicService, 'getSchemaEdge').and.returnValue({ attributes: [{ name: 'a', isDiscriminator: false }] });
      component.selectedEdge = <any>{
        exType: 'type',
        source: {
          exType: 'vType1',
          exID: 'vId1'
        },
        target: {
          exType: 'vType2',
          exID: 'vId2'
        }
      };

      component.onInteraction({ key: 'editAttributes' });
      expect(component.selectedEvent).toBe('editAttributes');
      expect(component.edge).toBe(expectedEdge);
      expect(component.edgeTypes).toEqual(['type']);
      expect(component.isDiscriminatorList).toEqual([false]);
      expect(dialog.open).toHaveBeenCalled();
    });

    it('for multi-edge', () => {
      const expectedEdge = {
        type: 'type',
        source: {
          type: 'vType1',
          id: 'vId1'
        },
        target: {
          type: 'vType2',
          id: 'vId2'
        },
        attrs: {
          a: '123',
          b: 456
        }
      };
      spyOn(logicService, 'getEdge').and.returnValue(expectedEdge);
      spyOn(logicService, 'getSchemaEdge').and.returnValue(
        {
          attributes: [{ name: 'a', isDiscriminator: true }, { name: 'b', isDiscriminator: false }],
          discriminatorCount: 1,
          compositeDiscriminator: ['a']
        }
      );
      component.selectedEdge = <any>{
        exType: 'type',
        source: {
          exType: 'vType1',
          exID: 'vId1'
        },
        target: {
          exType: 'vType2',
          exID: 'vId2'
        },
        attrs: {
          a: '123',
          b: 456
        }
      };

      component.onInteraction({ key: 'editAttributes' });
      expect(component.selectedEvent).toBe('editAttributes');
      expect(component.edge).toBe(expectedEdge);
      expect(component.edgeTypes).toEqual(['type']);
      expect(component.isDiscriminatorList).toEqual([true, false]);
      expect(dialog.open).toHaveBeenCalled();
    });

    it('for no element', () => {
      component.onInteraction({ key: 'editAttributes' });
      expect(component.selectedEvent).toBe('editAttributes');
      expect(dialog.open).not.toHaveBeenCalled();
    });
  });

  describe('should update a vertex', () => {
    const vertex = {
      type: 'type',
      id: 'id',
      attrs: {
        a: 'abc'
      }
    };

    beforeEach(() => {
      spyOn(component, 'retrieveAttributeFromForm');
      component.selectedVertex = <any>{ exType: 'type', exID: 'id' };
      component.vertex = vertex;
    });

    it('with success', () => {
      spyOn(logicService, 'getGraph').and.returnValue({
        graph: {
          nodes: [],
        }
      });
      spyOn(logicService, 'updateVertex').and.returnValue({ success: true });
      spyOn(compService, 'upsertVertex').and.returnValue(of({}));
      spyOn(component.loading, 'next');
      spyOn(<any>component, 'initializeVertex').and.callThrough();
      spyOn(<any>component, 'renderChart');

      component.updateElement();
      expect(logicService.updateVertex).toHaveBeenCalledTimes(2);
      expect(component.loading.next).toHaveBeenCalledTimes(2);
      expect((<any>component).initializeVertex).toHaveBeenCalled();
      expect((<any>component).renderChart).toHaveBeenCalled();
      expect(component.retrieveAttributeFromForm).toHaveBeenCalled();
    });

    it('with failure from local addition', () => {
      spyOn(logicService, 'updateVertex').and.returnValue({ success: false });
      spyOn(<any>component, 'handleError').and.callThrough();

      component.updateElement();
      expect(component.retrieveAttributeFromForm).toHaveBeenCalled();
      expect(logicService.updateVertex).toHaveBeenCalledTimes(1);
      expect((<any>component).handleError).toHaveBeenCalled();
    });

    it('with failure from server addition', () => {
      spyOn(logicService, 'updateVertex').and.returnValue({ success: true });
      spyOn(compService, 'upsertVertex').and.returnValue(throwError({ error: 'error', statusText: 'Error' }));
      spyOn(component.loading, 'next');
      spyOn(<any>component, 'handleError').and.callThrough();

      spyOn(logicService, 'getMatchEdgeTypes').and.returnValue({
        matchedEdgeTypes: ['eType1', 'eType2'],
        shouldReverseEdge: false,
      });

      component.updateElement();
      expect(logicService.updateVertex).toHaveBeenCalledTimes(1);
      expect(component.retrieveAttributeFromForm).toHaveBeenCalled();
      expect(component.loading.next).toHaveBeenCalledTimes(2);
      expect((<any>component).handleError).toHaveBeenCalled();
    });
  });

  describe('should update an edge', () => {
    const edge = {
      type: 'type',
      source: {
        type: 'vType1',
        id: 'vId1'
      },
      target: {
        type: 'vType2',
        id: 'vId2'
      },
      attrs: {
        a: 'abc'
      }
    };

    beforeEach(() => {
      spyOn(component, 'retrieveAttributeFromForm');
      component.selectedEdge = <any>{
        exType: 'type',
        source: {
          exType: 'vType1',
          exID: 'vId1'
        },
        target: {
          exType: 'vType2',
          exID: 'vId2'
        },
        attrs: {}
      };
      component.edge = edge;
    });

    it('with success', () => {
      spyOn(logicService, 'getGraph').and.returnValue({
        graph: {
          nodes: [],
        }
      });
      spyOn(logicService, 'updateEdge').and.returnValue({ success: true });
      spyOn(logicService, 'getSchemaEdge').and.returnValue({
        compositeDiscriminator: []
      });
      spyOn(logicService, 'getMatchEdgeTypes').and.returnValue({
        matchedEdgeTypes: ['eType1', 'eType2'],
        shouldReverseEdge: false,
      });
      spyOn(compService, 'upsertEdge').and.returnValue(of({}));
      spyOn(component.loading, 'next');
      spyOn(<any>component, 'initializeEdge').and.callThrough();
      spyOn(<any>component, 'renderChart');

      component.updateElement();
      expect(logicService.updateEdge).toHaveBeenCalledTimes(2);
      expect(component.retrieveAttributeFromForm).toHaveBeenCalled();
      expect(component.loading.next).toHaveBeenCalledTimes(2);
      expect((<any>component).initializeEdge).toHaveBeenCalled();
      expect((<any>component).renderChart).toHaveBeenCalled();
    });

    it('with failure from local addition', () => {
      spyOn(logicService, 'updateEdge').and.returnValue({ success: false });
      spyOn(<any>component, 'handleError').and.callThrough();
      spyOn(logicService, 'getSchemaEdge').and.returnValue({
        compositeDiscriminator: []
      });

      component.updateElement();
      expect(logicService.updateEdge).toHaveBeenCalledTimes(1);
      expect(component.retrieveAttributeFromForm).toHaveBeenCalled();
      expect((<any>component).handleError).toHaveBeenCalled();
    });

    it('with failure from server addition', () => {
      spyOn(logicService, 'updateEdge').and.returnValue({ success: true });
      spyOn(logicService, 'getSchemaEdge').and.returnValue({
        compositeDiscriminator: []
      });
      spyOn(compService, 'upsertEdge').and.returnValue(throwError({ error: 'error', statusText: 'Error' }));
      spyOn(component.loading, 'next');
      spyOn(<any>component, 'handleError').and.callThrough();
      spyOn(logicService, 'getMatchEdgeTypes').and.returnValue({
        matchedEdgeTypes: ['eType1', 'eType2'],
        shouldReverseEdge: false,
      });

      component.updateElement();
      expect(logicService.updateEdge).toHaveBeenCalledTimes(1);
      expect(component.retrieveAttributeFromForm).toHaveBeenCalled();
      expect(component.loading.next).toHaveBeenCalledTimes(2);
      expect((<any>component).handleError).toHaveBeenCalled();
    });
  });

  describe('should delete selected vetices and edges', () => {
    const selections = {
      nodes: [{
        type: 'vType1',
        id: 'vId1'
      }],
      links: [{
        type: 'eType1',
        source: {
          type: 'vType1',
          id: 'vId1'
        },
        target: {
          type: 'vType1',
          id: 'vId1'
        },
        attrs: {
          a: '123',
          b: 456
        }
      }]
    };
    const dialogRef = {
      keydownEvents: () => of(),
      afterClosed: () => of(1)
    };

    beforeEach(() => {
      spyOn(dialog, 'open').and.returnValue(dialogRef);
      spyOn(logicService, 'getGraph').and.returnValue({
        graph: {
          nodes: [],
        }
      });
    });

    it('with no selected elements', () => {
      spyOn(graphRef.current, 'selectedNodes').and.returnValue([]);
      spyOn(graphRef.current, 'selectedEdges').and.returnValue([]);
      spyOn(<any>component, 'deleteElement');

      component.onInteraction({ key: 'delete' });
      expect(dialog.open).not.toHaveBeenCalled();
      expect((<any>component).deleteElement).not.toHaveBeenCalled();
    });

    it('with user select "Cancel"', () => {
      spyOn(graphRef.current, 'selectedNodes').and.returnValue(selections.nodes);
      spyOn(graphRef.current, 'selectedEdges').and.returnValue(selections.links);
      spyOn(dialogRef, 'afterClosed').and.returnValue(of(0));
      spyOn(<any>component, 'deleteElement');

      component.onInteraction({ key: 'delete' });
      expect((<any>component).deleteElement).not.toHaveBeenCalled();
    });

    it('with user select "Continue" and success (edge is single-edge)', () => {
      spyOn(logicService, 'getSchemaEdge').and.returnValue({ discriminatorCount: 0});
      spyOn(logicService, 'getEdge').and.returnValue({});
      spyOn(graphRef.current, 'selectedNodes').and.returnValue(selections.nodes);
      spyOn(graphRef.current, 'selectedEdges').and.returnValue(selections.links);
      spyOn(compService, 'deleteEdge').and.returnValue(of({}));
      spyOn(compService, 'deleteMultiEdge').and.returnValue(of({}));
      spyOn(compService, 'deleteVertex').and.returnValue(of({}));
      spyOn(component.loading, 'next');
      spyOn(logicService, 'remove');
      spyOn(<any>component, 'renderChart');

      component.onInteraction({ key: 'delete' });
      expect(compService.deleteMultiEdge).not.toHaveBeenCalled();
      expect(compService.deleteEdge).toHaveBeenCalled();
      expect(logicService.remove).toHaveBeenCalled();
      expect((<any>component).renderChart).toHaveBeenCalled();
      expect(component.loading.next).toHaveBeenCalledTimes(2);
    });

    it('with user select "Continue" and success (edge is multi-edge)', () => {
      spyOn(logicService, 'getSchemaEdge').and.returnValue(
        {
          attributes: [{ name: 'a', isDiscriminator: true }, { name: 'b', isDiscriminator: false }],
          discriminatorCount: 1,
          compositeDiscriminator: ['a']
        }
      );
      spyOn(logicService, 'getEdge').and.returnValue(
        {
          exType: 'eType1',
          source: {
            exType: 'vType1',
            exID: 'vId1'
          },
          target: {
            exType: 'vType1',
            exID: 'vId1'
          },
          attrs: {
            a: '123',
            b: 456
          }
        }
      );
      spyOn(graphRef.current, 'selectedNodes').and.returnValue(selections.nodes);
      spyOn(graphRef.current, 'selectedEdges').and.returnValue(selections.links);
      spyOn(compService, 'deleteEdge');
      spyOn(compService, 'deleteMultiEdge').and.returnValue(of({}));
      spyOn(compService, 'deleteVertex').and.returnValue(of({}));
      spyOn(component.loading, 'next');
      spyOn(logicService, 'remove');
      spyOn(<any>component, 'renderChart');

      component.onInteraction({ key: 'delete' });
      expect(compService.deleteEdge).not.toHaveBeenCalled();
      expect(compService.deleteMultiEdge).toHaveBeenCalled();
      expect(logicService.remove).toHaveBeenCalled();
      expect((<any>component).renderChart).toHaveBeenCalled();
      expect(component.loading.next).toHaveBeenCalledTimes(2);
    });

    it('with user select "Continue" and failure', () => {
      spyOn(graphRef.current, 'selectedNodes').and.returnValue(selections.nodes);
      spyOn(graphRef.current, 'selectedEdges').and.returnValue(selections.links);
      spyOn(compService, 'deleteVertex').and.returnValue(throwError({ error: 'error', statusText: 'error' }));
      spyOn(logicService, 'getSchemaEdge').and.returnValue({ discriminatorCount: 0 });
      spyOn(component.loading, 'next');
      spyOn(<any>component, 'handleError').and.callThrough();


      component.onInteraction({ key: 'delete' });
      expect(component.loading.next).toHaveBeenCalledTimes(2);
      expect((<any>component).handleError).toHaveBeenCalled();
    });
  });

  it('should save the current graph result to png', () => {
    spyOn(<any>component, 'getNodes').and.returnValue([
      { exID: 'vId1', attrs: {} },
      { exID: 'vId2', attrs: {} }
    ]);
    spyOn(component, 'saveScreenshot');
    component.onInteraction({ key: 'screenshot' });
    expect(component.saveScreenshot).toHaveBeenCalled();
  });

  it('should save the current graph result to csv', () => {
    spyOn(component, 'exportCSV');
    component.onInteraction({ key: 'exportCSV' });
    expect(component.exportCSV).toHaveBeenCalled();
  });

  describe('should toggle update settings mode', () => {
    beforeEach(() => {
      component.toolbarConfig = compService.getToolbarConfig();
      spyOn(dialog, 'open').and.returnValue({
        close: () => { },
        keydownEvents: () => of(),
        afterClosed: () => of()
      });
    });

    it(`with on and no selected vertex's or edge's name`, () => {
      spyOn(component, 'onCurrentSelectedNameChange');
      spyOn(component, 'setNumberFormattingSample');

      component.onInteraction({ key: 'settings' });
      expect(component.selectedEvent).toBe('settings');
      expect(component.onCurrentSelectedNameChange).toHaveBeenCalled();
      expect(component.setNumberFormattingSample).toHaveBeenCalled();
      expect(dialog.open).toHaveBeenCalled();
      expect((<any>component.toolbarConfig.top[ToolbarButtons.Settings]).color).toBe('accent');
    });

    it(`with on and a selected vertex's or edge's name`, () => {
      spyOn(component, 'onCurrentSelectedNameChange');
      spyOn(component, 'setNumberFormattingSample');
      component.currentSelectedName = 'type';

      component.onInteraction({ key: 'settings' });
      expect(component.selectedEvent).toBe('settings');
      expect(component.onCurrentSelectedNameChange).toHaveBeenCalled();
      expect(component.setNumberFormattingSample).toHaveBeenCalled();
      expect(dialog.open).toHaveBeenCalled();
      expect((<any>component.toolbarConfig.top[ToolbarButtons.Settings]).color).toBe('accent');
    });

    it('with off', () => {
      spyOn(component, 'setNumberFormattingSample');

      component.onInteraction({ key: 'settings' });
      (<any>component).toggleButtonStatus();
      expect((<any>component.toolbarConfig.top[ToolbarButtons.Settings]).color).toBe('');
      expect(component.setNumberFormattingSample).toHaveBeenCalled();
    });
  });

  describe('should update attribute list to be selected', () => {
    it(`with a selected vertex's or edge's name`, () => {
      const attributes = {
        names: ['vType1', 'vType2'],
        data: {
          vType1: {
            name: 'vType1',
            type: 'vertex',
            attributes: [
              {
                name: 'type',
                checked: false
              },
              {
                name: 'id',
                checked: true
              }
            ]
          },
          vType2: {
            name: 'vType2',
            type: 'vertex',
            attributes: [
              {
                name: 'type',
                checked: false
              },
              {
                name: 'id',
                checked: true
              }
            ]
          }
        }
      };
      component.tmpAttributesForSelected = <any>attributes;

      component.onCurrentSelectedNameChange('vType1');
      expect(component.currentSelectedName).toBe('vType1');
      expect(component.shownAttributes).toBe(attributes.data['vType1'].attributes);
    });

    it(`with empty vertex's or edge's name`, () => {
      component.onCurrentSelectedNameChange(undefined);
      expect(component.currentSelectedName).toBeUndefined();
      expect(component.shownAttributes).toBeUndefined();
    });
  });

  describe('should handle graph chart event', () => {
    let forNode: boolean;

    beforeEach(() => {
      forNode = true;
      spyOn(<any>component, 'getCurrentGraphResult');
    });

    it('on pointer up', () => {
      // Call through here so we can cover handling adding edge while not in adding edge mode.
      spyOn(<any>component, 'handleAddEdge').and.callThrough();

      // Handle pointer up on a vertex.
      (<any>component).graphEvents.onClick({
        type: 'vType1',
        id: 'vId1'
      });
      // Handle pointer up on an edge.
      (<any>component).graphEvents.onClick({
        type: 'eType1',
        source: {
          type: 'vType1',
          id: 'vId1'
        },
        target: {
          type: 'vType1',
          id: 'vId1'
        }
      });
      expect((<any>component).handleAddEdge).toHaveBeenCalledTimes(2);
    });

    it('on pointer down', () => {
      spyOn(component.touchedVertex, 'next');

      // Handle pointer down on a vertex.
      (<any>component).graphEvents.onClickDown({
        type: 'vType1',
        id: 'vId1'
      });
      // Handle pointer down on an edge.
      (<any>component).graphEvents.onClickDown({
        type: 'eType1',
        source: {
          type: 'vType1',
          id: 'vId1'
        },
        target: {
          type: 'vType1',
          id: 'vId1'
        }
      });
      expect(component.touchedVertex.next).toHaveBeenCalledTimes(1);
    });

    it('on double click', () => {
      spyOn(<any>component, 'getOneStepNeighbors');

      // Handle double click on a vertex.
      (<any>component).graphEvents.onDoubleClick({
        type: 'vType1',
        id: 'vId1'
      });
      // Handle double click on an edge.
      (<any>component).graphEvents.onDoubleClick({
        type: 'eType1',
        source: {
          type: 'vType1',
          id: 'vId1'
        },
        target: {
          type: 'vType1',
          id: 'vId1'
        }
      });
      expect((<any>component).getOneStepNeighbors).toHaveBeenCalledTimes(1);
    });
  });

  it('should save latest graph result, selection and layout', fakeAsync(() => {
    const node1 = {
      id: 'v1',
      type: 'vt1'
    };
    const node2 = {
      id: 'v2',
      type: 'vt2'
    };
    component.graphLayout = 'tree';
    spyOn(logicService, 'getGraph').and.returnValue({ graph: { nodes: [node1, node2], links: [] } });
    spyOn(graphRef.current, 'graphPositions').and.returnValue([['vt1#v1', 1, 1], ['vt2#v2', 2, 2]]);
    spyOn(graphRef.current, 'selectedNodes').and.returnValue([]);
    spyOn(graphRef.current, 'selectedEdges').and.returnValue([]);

    component.graphEvents.onSelect({ nodes: [], links: [] });
    tick(201);

    discardPeriodicTasks();
  }));

  describe('should get one step neighbors of a vertex', () => {
    beforeEach(() => {
      spyOn(component.loading, 'next');
      spyOn(logicService, 'getAllVertexTypes').and.returnValue([]);
    });

    it('with success', () => {
      spyOn(compService, 'getOneVertexNeighbors').and.returnValue(of({}));
      spyOn(component, 'addData');
      spyOn(component, 'highlight');

      (<any>component).getOneStepNeighbors({ exType: 'type', exID: 'id' });
      expect(component.loading.next).toHaveBeenCalledTimes(2);
      expect(component.addData).toHaveBeenCalled();
      // expect(component.highlight).toHaveBeenCalled();
    });

    it('with success when some edges have reverse edge', () => {
      spyOn(compService, 'getOneVertexNeighbors').and.returnValue(of({}));
      spyOn(component, 'addData');
      spyOn(component, 'highlight');
      logicService.schema = <any>{
        name: 'MyGraph',
        vertexTypes: [],
        edgeTypes: [
          {
            name: 'e1',
            hasReverseEdge: false
          },
          {
            name: 'e2',
            hasReverseEdge: true,
            reverseEdge: 'rev_e2'
          }
        ]
      };

      (<any>component).getOneStepNeighbors({ exType: 'type', exID: 'id' });
      expect(compService.getOneVertexNeighbors)
        .toHaveBeenCalledWith('MyGraph', 'type', 'id', 200, [], ['e1', 'e2', 'rev_e2']);
    });

    it('with failure', () => {
      spyOn(compService, 'getOneVertexNeighbors').and.returnValue(throwError({ error: 'error', statusText: 'Error' }));
      spyOn(<any>component, 'handleError');

      (<any>component).getOneStepNeighbors({ exType: 'type', exID: 'id' });
      expect(component.loading.next).toHaveBeenCalledTimes(2);
      expect((<any>component).handleError).toHaveBeenCalled();
    });
  });
});
