import {
  AfterViewInit, Component, EventEmitter, Input, OnInit,
  OnDestroy, Output, TemplateRef, ViewChild, HostListener,
  ChangeDetectorRef
} from '@angular/core';
import { HttpErrorResponse } from '@angular/common/http';
import { FormGroup } from '@angular/forms';
import { MatDialog, MatDialogConfig, MatDialogRef, MatDialogState } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import {
  ExternalGraph, ExternalLink, ExternalNode,
  GraphChartDataLink, GraphChartDataNode, Color
} from '@tigergraph/tools-models/gvis/insights';
import { cloneDeep, isEqual } from 'lodash';
import {
  concat, fromEvent, forkJoin, merge, timer,
  BehaviorSubject, Observable, Observer, ReplaySubject, Subject
} from 'rxjs';
import { debounceTime, distinctUntilChanged, finalize, takeLast, takeUntil } from 'rxjs/operators';
import * as J<PERSON><PERSON>ip from 'jszip';
import { saveAs } from 'file-saver';

import { DialogData, DialogComponent } from '@app/core/components/dialog';
import { AuthService, CacheService, CopierService, Logger, MessageBus } from '@app/core/services';
import { DynamicFormComponent } from '@app/shared/components/dynamic-form';
import { ExpressionFormConfig } from '@app/shared/components/expression-form';
import { DataGraphWrapperComponent } from '@app/shared/components/data-graph-chart/dataGraphWrapper';
import { ToolbarConfig, ToolbarInteraction } from '@app/shared/components/toolbar-container';
import { ShortcutsMenuComponent } from '@app/shared/components/shortcuts-menu/shortcuts-menu.component';
import { ButtonBase, StrokedIconButton } from '@tigergraph/tools-models/button';
import { HttpSourceErrorResponse } from '@tigergraph/tools-models/error';
import {
  condDict, exprDict, parseExprJson, AtomJson,
  BaseCondition, BaseExpression, ConditionFactory,
  ConditionJson, ExpressionFactory, ExpressionJson,
  FactoryProducer
} from '@tigergraph/tools-models/expression';
import { QuestionBase } from '@tigergraph/tools-models/question';
import { addMissingNode } from '@tigergraph/tools-models/gvis/insights';
import { AppTheme } from '@tigergraph/tools-models/theme';
import {
  Edge, Graph, GSQLAttributeJson,
  GSQLAttributeTypeJson, GSQLGraphJson,
  GSQLEdgeJson, GSQLUdtJson,
  GSQLVertexJson, Vertex
} from '@tigergraph/tools-models/topology';
import { GSQLPrivilege, ValidateResult } from '@tigergraph/tools-models/utils';
import { HelperFunctions } from '@app/shared/utils';
import { OneStepExpansionConfigJSON } from '@app/graph-explorer/graph-explorer.service';
import { GraphEvents } from '@tigergraph/tools-ui/esm/graph/type';
import { GraphRef } from '@tigergraph/tools-ui/esm/graph/type';
import { KIND } from 'baseui/toast';

import { VisualEditorLogicService } from './shared';
import {
  AttributesForSelected, ChartData, ChartSettingConfig,
  ExplorationDBInfo, ExplorationInfo, GraphExplorerConfig,
  NumberFormatting, ToolbarButtons,
  VertexOrEdgeAttributesWithCheckedInfo,
  VisualEditorService, VisualizationConfig
} from './visual-editor.service';
import { getNodeID } from '@tigergraph/tools-models';
import {
  GraphParser,
  createGraphChartDataNode,
  createGraphChartDataLink,
} from '@tigergraph/tools-models/gvis/insights';
import { NodeSingular, EdgeSingular } from 'cytoscape';
import { GRAPH_ICON } from '@tigergraph/tools-ui/esm/graph/popper/styledGraphIcon';
import { Shortcut } from '@tigergraph/app-ui-lib/shortcuts-menu';
import { getElementAttribute } from '@tigergraph/tools-ui/esm/graph/data';

/**
 * Graph explorer info keywords.
 *
 * @export
 * @enum {string}
 */
export enum GraphExplorerLeftPanelConfig {
  AttributeFilter = 'attributeFilter',
  CurrentStatus = 'currentStatus',
  DestinationVertexContent = 'destinationVertexContent',
  EdgeTypeFilter = 'edgeTypeFilter',
  ExpansionStepsConfig = 'expansionStepsConfig',
  NeighborLimit = 'neighborLimit',
  RandomPickVertexNumber = 'randomPickVertexNumber',
  SearchPathLengthLimit = 'searchPathLengthLimit',
  SearchPathMode = 'searchPathMode',
  SearchVertexContent = 'searchVertexContent',
  StartingVertexContent = 'startingVertexContent',
  VertexTypeFilter = 'vertexTypeFilter',
  SelectAllVertexTypes = 'selectAllVertexTypes',
  SelectAllEdgeTypes = 'selectAllEdgeTypes'
}

enum EventKey {
  Open = 'open',
  Save = 'save',
  Screenshot = 'screenshot',
  ExportCSV = 'exportCSV',
  Search = 'search',
  Show = 'show',
  Hide = 'hide',
  Undo = 'undo',
  Redo = 'redo',
  Settings = 'settings',
  VisInfo = 'visInfo',
  AddVertex = 'addVertex',
  AddEdge = 'addEdge',
  EditAttributes = 'editAttributes',
  Delete = 'delete',
  SwitchEdit = 'switchEdit',
  ZoomIn = 'zoomIn',
  ZoomOut = 'zoomOut',
  Refresh = 'refresh'
}

enum NumberFormattingErrorMessage {
  sameError = 'Please choose different symbols for the thousands and decimal separators.',
  formatError = 'Please input a correct number.'
}

interface AttributeRelatedChanges {
  removedAttrs: string[];
  changedAttrs: { [attrName: string]: GSQLAttributeJson };
  newAttrs: { [attrName: string]: GSQLAttributeJson };
}

interface CSVFile {
  [fileName: string]: {
    header: string[],
    row: any[]
  };
}

// Graph limitiation.
const vertexSizeLimit = 5000;
const edgeSizeLimit = 10000;
// One step neighbor.
const oneStepNeighborLimit = 200;
const previewMinWidth = 480;
const previewMinHeight = 320;
// Graph chart settings.
const defaultLabelSize = 18;
const defaultShowAttributesWhenMouseHover = true;

/**
 * Component to render the visual editor interface.
 * Usage example:
 *   <app-visual-editor (selectionChange)="onSelectionChange($event)" (touchedVertex)="onTouchedVertex($event)">
 *   </app-visual-editor>
 *
 * @export
 * @class VisualEditorComponent
 * @implements {AfterViewInit}
 * @implements {OnDestroy}
 */
@Component({
  selector: 'app-visual-editor',
  templateUrl: './visual-editor.component.html',
  styleUrls: ['./visual-editor.component.scss'],
  providers: [
    VisualEditorService,
    VisualEditorLogicService
  ]
})
export class VisualEditorComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('dataGraphContainer', { static: true }) dataGraphContainer: DataGraphWrapperComponent;
  @ViewChild('popupWindow', { static: true }) popupWindow: TemplateRef<any>;
  @ViewChild('configWindow', { static: true }) configWindow: TemplateRef<any>;
  @ViewChild('appDynamicForm') dynamicForm: DynamicFormComponent;
  @ViewChild('shortcutsMenuContainer') shortcutsMenuContainer: ShortcutsMenuComponent;

  @Input() componentName: string;

  @Output()
  selectionChange = new EventEmitter<
    [
      GraphChartDataNode[],
      GraphChartDataLink[]
    ]
  >();
  @Output() touchedVertex = new EventEmitter<GraphChartDataNode>();
  @Output() touchedEdge = new EventEmitter<GraphChartDataLink>();
  @Output() graphExplorerConfigApplied = new EventEmitter<any>();
  @Output() readonly handleEscape = new EventEmitter<void>();
  @Output() readonly createVertex = new EventEmitter<void>();
  @Output() readonly createEdge = new EventEmitter<void>();

  toolbarConfig: ToolbarConfig;
  chartLayout: string[];

  visualizationConfigOfAllTypes = new Map<string, VisualizationConfig>();
  newVisualizationConfigOfAllTypes = new Map<string, VisualizationConfig>();
  selectedVisualizationConfig: VisualizationConfig;
  selectedColorValue: string;
  selectedColorIndex: number;
  isConfigFilterSemanticCorrect = true;
  colorExprFormConfig: ExpressionFormConfig;
  sizeExprFormConfig: ExpressionFormConfig;
  exprModel: BaseCondition | BaseExpression;
  selectedConfigControllerEvent: string;

  loading = new BehaviorSubject<boolean>(false);

  selectedEvent: string;
  newLabelFontSize = defaultLabelSize;
  labelFontSize = defaultLabelSize;

  searchKeyword: string;
  searchByVertexId = true;
  searchByVertexAttrs = false;
  substringMatch = false;

  selectedVertexAttributes: QuestionBase<any>[] = [];
  selectedEdgeAttributes: QuestionBase<any>[] = [];
  vertexTypes: string[] = [];
  edgeTypes: string[] = [];
  vertex: ExternalNode;
  edge: ExternalLink;
  selectedVertex: GraphChartDataNode;
  selectedEdge: GraphChartDataLink;
  udtList: GSQLUdtJson[];
  ready = new ReplaySubject<any>();

  attributesForSelected: AttributesForSelected = undefined;
  tmpAttributesForSelected: AttributesForSelected;
  currentSelectedName: string = undefined;
  shownAttributes: { name: string, checked: boolean }[];
  showAttributesWhenMouseHover = defaultShowAttributesWhenMouseHover;

  saveExplorationForm: FormGroup;
  currentDatetime: number;
  currentUsername: string;
  explorationNameToSave = '';
  explorationDescriptionToSave = '';
  savedExplorationList$ = new BehaviorSubject<{
    file: string,
    username: string,
    timestamp: number,
    previewImage: string,
    description: string
  }[]>([]);
  checkedExploration: string;
  previewImgURL: string;

  warning = new BehaviorSubject<string>(undefined);

  graphParser: GraphParser;
  graphRef: React.MutableRefObject<GraphRef>;

  editButtonChecked: boolean;

  isCopied = false;

  isDiscriminatorList: boolean[];

  // Number formatting
  numberSeparatorList = [',', '.'];
  numberFormatting: NumberFormatting;
  numberFormattingForShowing: NumberFormatting;
  numberFormattingSample: string;
  numberFormattingError: string[];

  private ngUnsubscribe: Subject<any> = new Subject<any>();

  private popupWindowRef: MatDialogRef<TemplateRef<any>>;
  private configWindowRef: MatDialogRef<TemplateRef<any>>;
  private dialogConfig: MatDialogConfig;

  private toolbarDisabledStatus: boolean[] = [];

  private isLoading = false;

  private isViewingExploreHistory = false;
  private isSavingExploration = false;
  private isLocatingVertex = false;
  private isAddingVertex = false;
  private isAddingEdge = false;
  private isUpdatingSettings = false;
  private sourceVertex: GraphChartDataNode;
  private targetVertex: GraphChartDataNode;

  private highlightColor = 'white';

  private _color: Color;

  private graphName: string;

  private cachePrefix = 'VisualEditor';

  /**
   * Map from reverse edge type to forward edge type
   * - key: reverse edge type name
   * - value: forward edge type name
   *
   * @private
   * @type {Map<string, string>}
   * @memberof VisualEditorComponent
   */
  private schemaReverseEdgeMap: Map<string, string> = new Map<string, string>();

  schema: Graph;
  graphData: ExternalGraph;
  graphLayout = 'Force';
  skipAutoLayout = false;
  unlockTimer: NodeJS.Timeout;
  focusGraph = false;
  shortcuts: Shortcut[];
  graphEvents: GraphEvents = {
    onClick: (item: ExternalNode | ExternalLink | undefined) => {
      if (!item) {
        return;
      }
      if ('id' in item) {
        this.handleGraphChartPointerUp(createGraphChartDataNode(item));
      } else if ('source' in item) {
        this.handleGraphChartPointerUp(<GraphChartDataLink><unknown>createGraphChartDataLink(item));
      }
    },
    onClickDown: (item: ExternalNode | ExternalLink | undefined) => {
      if (!item) {
        return;
      }
      if ('id' in item) {
        this.handleGraphChartPointerDown(createGraphChartDataNode(item));
      } else if ('source' in item) {
        this.handleGraphChartPointerDown(<GraphChartDataLink><unknown>createGraphChartDataLink(item));
      }
    },
    onDoubleClick: (item: ExternalNode | ExternalLink | undefined) => {
      if (!item) {
        return;
      }
      if ('id' in item) {
        this.handleGraphChartEnter(createGraphChartDataNode(item));
      }
    },
    onSelect: (graph: ExternalGraph) => {
      const nodes = [], links = [];
      graph.nodes.forEach(item => {
        nodes.push(createGraphChartDataNode(item));
      });
      graph.links.forEach(item => {
        links.push(createGraphChartDataLink(item));
      });
      this.selectionChange.emit([nodes, links]);

      const disable = nodes.length + links.length === 0;
      this.changeButtonDisableStatus(
        'delete',
        disable
      );
    },
    onCreateLink: (source: ExternalNode, target: ExternalNode) => {
      this.toggleAddEdgeMode();
      this.sourceVertex = createGraphChartDataNode(source);
      this.handleGraphChartPointerUp(createGraphChartDataNode(target));
      this.toggleAddEdgeMode();
    },
    showEdgeHandler: () => {
      return this.editButtonChecked && !this.isAddingEdge;
    },
    onMouseOver: (item: ExternalNode | ExternalLink) => {
      if (!this.editButtonChecked) {
        return;
      }
      if ('id' in item) {
        this.graphRef.current?.displayNodeIcon(item, GRAPH_ICON.delete);
      } else if ('source' in item) {
        this.graphRef.current?.displayLinkIcon(item, GRAPH_ICON.delete);
      }
    },
    onMouseOut: (item: ExternalNode | ExternalLink) => {
      if ('id' in item) {
        this.graphRef.current?.removeNodeIcon(item);
      } else if ('source' in item) {
        this.graphRef.current?.removeLinkIcon(item);
      }
    },
    onDelete: (item: ExternalNode | ExternalLink) => {
      this.graphRef.current?.unselectElements();
      if ('id' in item) {
        this.graphRef.current?.selectNodes([item]);
      } else if ('source' in item) {
        this.graphRef.current?.selectEdges([item]);
      }
      this.handleDelete();
    },
    onPositionChange: () => {
      this.updatePositions(this.logicService.graph);
    },
  };

  constructor(
    private dialog: MatDialog,
    private snackBar: MatSnackBar,
    private authService: AuthService,
    private logger: Logger,
    private bus: MessageBus,
    private cacheService: CacheService,
    private cmptService: VisualEditorService,
    private logicService: VisualEditorLogicService,
    private copier: CopierService,
    private cdr: ChangeDetectorRef,
    ) {
    this.dialogConfig = {
      width: '648px', // 600px for width plus the left and right padding which is 24px each.
      ariaDescribedBy: 'dialogDescription',
      autoFocus: false
    };

    this.toolbarConfig = this.cmptService.getToolbarConfig();
    this.shortcuts = this.cmptService.getShortcuts();
    this.chartLayout = this.cmptService.getChartLayout();
    this.colorExprFormConfig = this.cmptService.getExpressionFormConfig(true);
    this.sizeExprFormConfig = this.cmptService.getExpressionFormConfig(false);
    this.currentUsername = this.authService.getUsername();

    this.initializeVertex();
    this.initializeEdge();

    // Init attribute list based on empty schema
    this.getAttributesList();

    this._color = new Color();
  }

  ngOnInit() {
    this.graphName = this.authService.getCurrentGraph();
    this.adjustElementAppearancesBaseOnUserPrivilege();
  }

  ngAfterViewInit() {
    this.graphRef = this.dataGraphContainer.graphRef;
    this.graphParser = new GraphParser();
  }

  ngOnDestroy() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
    this.graphRef.current = null;
    this.graphRef = null;
    this.dataGraphContainer.graphRef = null;
  }

  @HostListener('document:click', ['$event'])
  clickout(event: MouseEvent) {
    // When focusing on the graph, the keyboard events are allowed to trigger
    this.focusGraph = this.dataGraphContainer.containerRef.nativeElement.contains(event.target) ||
      this.shortcutsMenuContainer.containerRef.nativeElement.contains(event.target);
  }

  @HostListener('window:keydown', ['$event'])
  keydownEvent(event: KeyboardEvent) {
    if (
      this.dialog.openDialogs.length > 0 ||
      this.popupWindowRef?.getState() === MatDialogState.OPEN ||
      this.configWindowRef?.getState() === MatDialogState.OPEN ||
      !this.focusGraph
    ) {
      return;
    }
    if ((event.metaKey || event.ctrlKey) && event.key.toLowerCase() === 's') {
      this.onInteraction({ key: EventKey.Save });
      event.preventDefault();
    } else if ((event.metaKey || event.ctrlKey) && event.shiftKey && event.key.toLowerCase() === 'z') {
      this.onInteraction({ key: EventKey.Redo });
    } else if ((event.metaKey || event.ctrlKey) && event.key.toLowerCase() === 'z') {
      this.onInteraction({ key: EventKey.Undo });
    } else if ((event.metaKey || event.ctrlKey) && event.shiftKey && event.key.toLowerCase() === 'r') {
      this.graphRef.current?.centerGraph();
      event.preventDefault();
    } else if (
      (event.key.toLowerCase() === 'backspace' || event.key.toLowerCase() === 'delete') &&
      this.editButtonChecked
    ) {
      this.onInteraction({ key: EventKey.Delete });
    }
  }

  private adjustBottomRightButtons(show: boolean) {
    this.toolbarConfig.top.slice(ToolbarButtons.Separator2)
      .forEach(button => button.show = show);

    if (!this.authService.hasPrivilege(GSQLPrivilege.WriteQuery)) {
      this.toolbarConfig.top[ToolbarButtons.Refresh].show = false;
    }
  }

  /**
   * Update the schema and call set graph style.
   * Adjust appearance accordingly.
   *
   * @param {Graph} schema
   * @memberof VisualEditorComponent
   */
  switchGraph(schema: Graph) {
    this.graphName = this.authService.getCurrentGraph();
    this.getUdtList();

    // TODO: Investigate why the first time rendering the graph it flash to disappear.
    const graphIsNotEmpty = this.logicService.getGraph().graph.nodes.length > 0;
    this.logicService.setSchema(schema);

    // Construct reverse edge map
    schema.edgeTypes.forEach(edgeType => {
      if (edgeType.hasReverseEdge) {
        this.schemaReverseEdgeMap.set(edgeType.reverseEdge, edgeType.name);
      }
    });
    this.getAttributesList();

    if (graphIsNotEmpty) {
      this.renderChart(this.logicService.getGraph());
    }

    this.adjustElementAppearancesBaseOnUserPrivilege();

    // If graph schema is empty, then disable creating vertex and edge types.
    if (schema.vertexTypes.length === 0) {
      this.toolbarConfig.top[ToolbarButtons.AddVertex].disabled = true;
      this.toolbarConfig.top[ToolbarButtons.AddEdge].disabled = true;
    }

    this.currentSelectedName = undefined;
  }

  /**
   * Return ready as an observable.
   *
   * @readonly
   * @memberof VisualEditorComponent
   */
  get isReady() {
    return this.ready.asObservable();
  }

  /**
   * Get udt list.
   *
   * @memberof VisualEditorComponent
   */
  private getUdtList() {
    this.cmptService
      .getUdtList(this.graphName)
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe(udts => {
        this.udtList = udts;
        this.cmptService.udtList = this.udtList;
        this.ready.next();
      });
  }

  /**
   * Update vertices positions
   * @param graph
   * @memberof VisualEditorComponent
   */
  private updatePositions(graph: ExternalGraph) {
    if (!graph) {
      return;
    }
    const positionMap = this.graphRef?.current?.graphPositions()?.reduce((map, p) => {
      if (p?.length === 3) {
        map.set(p[0], [p[1], p[2]]);
      }
      return map;
    }, new Map<string, number[]>());
    graph.nodes.forEach(node => {
      if (positionMap?.has(`${node.type}#${node.id}`)) {
        node.x = positionMap.get(`${node.type}#${node.id}`)[0];
        node.y = positionMap.get(`${node.type}#${node.id}`)[1];
      }
    });
    return graph;
  }

  /**
   * Parse the graph from a given format and add it to logic service.
   * fromVertex can be GraphChartDataNode or geospatial node.
   *
   * @param {*} graph
   * @param {string} format
   * @param {boolean} canonicalizeResult
   * @param {GraphChartDataNode} [fromVertex]
   * @param {boolean} [showPrevious]
   * @param {{ selections: ExternalGraph, layout: string }} [prevSelectionsAndLayout]
   * @param {boolean} [noFocus]
   * @memberof VisualEditorComponent
   */
  addData(
    graph: any,
    format: string,
    canonicalizeResult: boolean,
    fromVertex?: GraphChartDataNode,
    showPrevious?: boolean,
    prevSelectionsAndLayout?: { selections: ExternalGraph, layout: string },
    noFocus?: boolean
  ) {
    const parser = this.graphParser.getParser(format);
    // handle applying exploration result
    if (format === 'static') {
      // Set graph positions
      this.graphLayout = 'Preset';
    }

    let parsedGraph = parser(graph);
    if (!parsedGraph) {
      return;
    }
    parsedGraph = addMissingNode(parsedGraph);

    // Reverse reverse edge.
    if (canonicalizeResult) {
      parsedGraph.links.forEach(link => {
        if (this.schemaReverseEdgeMap.has(link.type)) {
          [link.source, link.target] = [link.target, link.source];
          link.type = this.schemaReverseEdgeMap.get(link.type);
        }
      });
    }

    if (this.exceedGraphSize(parsedGraph)) {
      this.handleError(
        `Unable to render graph since graph size is too large.`,
        'Warning'
      );
    } else {
      if (showPrevious) {
        this.onLayoutSelection(prevSelectionsAndLayout.layout);
      }
      const lockNodes = this.logicService.getGraph().graph.nodes;
      // Lock nodes before adding new data
      this.graphRef?.current?.lockNodes(lockNodes);
      this.logicService.addData(parsedGraph);
      this.renderChart(this.logicService.getGraph(), fromVertex, showPrevious);

      const selections: {
        nodes: GraphChartDataNode[];
        links: GraphChartDataLink[];
      } = {
        nodes: [],
        links: []
      };
      const graphSelections = showPrevious ? prevSelectionsAndLayout.selections : parsedGraph;
      graphSelections.nodes.forEach(vertex => {
        const graphNode = <GraphChartDataNode>createGraphChartDataNode(vertex);
        selections.nodes.push(graphNode);
      });
      graphSelections.links.forEach(edge => {
        const graphLink = <GraphChartDataLink><unknown>createGraphChartDataLink(edge);
        selections.links.push(<GraphChartDataLink>graphLink);
      });
      timer(100)
        .pipe(takeUntil(this.ngUnsubscribe))
        .subscribe(() => {
          // Run force layout
          this.graphRef?.current?.runLayout?.({
            randomize: false,
            fit: false,
            centerGraph: false,
            eles: this.graphRef?.current?.getGraphCollection(parsedGraph)
          }, () => {
            // Execute when force layout stop running
            this.graphRef?.current?.unlockNodes();
          });
          if (prevSelectionsAndLayout?.layout) {
            this.graphLayout = prevSelectionsAndLayout?.layout;
          }
          if ((graphSelections.links.length + graphSelections.nodes.length) <= 100) {
            this.graphRef?.current?.selectElements(graphSelections);
          }
        });
    }
  }

  /**
   * Clear the current data without saving in history.
   *
   * @memberof VisualEditorComponent
   */
  clearData() {
    this.logicService.clearData();
  }

  /**
   * Clear the current data with saving in history.
   *
   * @memberof VisualEditorComponent
   */
  clearDataWithHistory() {
    this.logicService.clearDataWithHistory();
    this.renderChart(this.logicService.getGraph(), undefined, true);
  }

  /**
   * Update currentSelectedName and showAttributes when a vertex or edge's name clicked.
   *
   * @param {string} name
   * @memberof VisualEditorComponent
   */
  onCurrentSelectedNameChange(name: string) {
    if (!name) {
      this.shownAttributes = undefined;
      return;
    }
    this.currentSelectedName = name;
    this.shownAttributes = this.tmpAttributesForSelected.data[name].attributes;
  }

  /**
   * Return the number of vertices and edges.
   *
   * @returns {[number, number]}
   * @memberof VisualEditorComponent
   */
  getGraphSize(): [number, number] {
    const graph = this.logicService.getGraph().graph;
    return [graph.nodes.length, graph.links.length];
  }

  /**
   * Return the currently selected vertices and edges.
   *
   * @returns {{ nodes: GraphChartDataNode[], links: GraphChartDataLink[] }}
   * @memberof VisualEditorComponent
   */
  getCurrentSelections(): { nodes: GraphChartDataNode[], links: GraphChartDataLink[] } {
    const nodes = this.graphRef.current?.selectedNodes().map(i => {
      return createGraphChartDataNode(i) as GraphChartDataNode;
    });
    const links = this.graphRef.current?.selectedEdges().map(i => {
      return createGraphChartDataLink(i) as unknown as GraphChartDataLink;
    });
    return { nodes, links };
  }

  /**
   * Highlight the input vertices by border color.
   *
   * @param {{id: string, type: string}[]} vertices
   * @memberof VisualEditorComponent
   */
  highlight(vertices: { id: string; type: string }[]) {
    const nodesStyle: { node: ExternalNode, style: Record<string, string | number> }[] = [];

    // apply the highlight style to node
    this.getNodes().forEach(node => {
      const nodeStyle: { node: ExternalNode, style: Record<string, string | number> } = {
        node: {
          type: node.exType,
          id: node.exID,
        },
        style: {},
      };
      nodesStyle.push(nodeStyle);
      const found = vertices.find(
        n => n.id === node.exID && n.type === node.exType
      );
      if (found) {
        nodeStyle.style['border-color'] = '#FFF03F';
        nodeStyle.style['border-width'] = '10px';
      } else {
        nodeStyle.style['border-color'] = 'transparent';
        nodeStyle.style['border-width'] = '0';
      }
    });
    // Set style to the graph widget
    timer(100)
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe(() => {
        this.graphRef?.current?.setNodesStyle(nodesStyle);
        this.graphRef?.current?.runLayout({
          randomize: false,
          fit: false,
          centerGraph: false,
        });
      });
  }

  /**
   * Update settings and close popup window.
   *
   * @param {ChartSettingConfig} [configs]
   * @memberof VisualEditorComponent
   */
  updateSettings(configs?: ChartSettingConfig) {
    if (configs) {
      // TODO: temp fix for renaming id to primary id for all vertex types when upgrading to 3.0.
      if (configs.attributesForSelected && configs.attributesForSelected.data) {
        Object.entries(configs.attributesForSelected.data).forEach(([t, conf]) => {
          if (this.logicService.getSchemaVertex(t) !== undefined) {
            conf.attributes[1].name = 'primary id';
          }
        });
      }
      this.tmpAttributesForSelected = configs.attributesForSelected;
      this.newVisualizationConfigOfAllTypes = configs.visualizationConfigOfAllTypes;
      this.numberFormatting = configs.numberFormatting;
      this.showAttributesWhenMouseHover = configs.showAttributesWhenMouseHover;
      this.newLabelFontSize = +configs.labelFontSize;
    } else {
      this.numberFormatting = cloneDeep(this.numberFormattingForShowing);
    }

    // Update which attributes to show for each vertex and edge type
    this.attributesForSelected = cloneDeep(this.tmpAttributesForSelected);

    // Update the visualization config for each vertex and edge type
    this.visualizationConfigOfAllTypes = cloneDeep(this.newVisualizationConfigOfAllTypes);

    // Update label font size.
    if (this.labelFontSize !== this.newLabelFontSize) {
      this.labelFontSize = this.newLabelFontSize;
    }

    // Re-render graph
    if (configs) {
      this.renderChart(this.logicService.getGraph(), undefined, true);
    } else {
      this.renderChart(this.logicService.getGraph());
    }

    // Emit configs update to parent component.
    if (!configs) {
      const updatedConfig = {
        attributesForSelected: this.attributesForSelected,
        visualizationConfigOfAllTypes: this.visualizationConfigOfAllTypes,
        numberFormatting: this.numberFormatting,
        showAttributesWhenMouseHover: this.showAttributesWhenMouseHover,
        labelFontSize: this.labelFontSize
      };

      this.saveSettingsToCacheService(updatedConfig);
      this.closePopupWindow();
    }
  }

  /**
   *  Add a new color config with default color value and empty filter.
   *
   * @memberof VisualEditorComponent
   */
  addColorConfig() {
    const condFactory = FactoryProducer.getFactory(ConditionFactory);
    const exprFactory = FactoryProducer.getFactory(ExpressionFactory);
    const nullExpr = exprFactory.getExpression(exprDict['NullExpression'], []);

    this.newVisualizationConfigOfAllTypes.get(this.currentSelectedName).color.push({
      filter: condFactory.getCondition(
        condDict['NullCondition'],
        [nullExpr, nullExpr]
      ),
      colorValue: '#1f77b4'
    });
  }

  /**
   * Remove one color config.
   *
   * @param {number} index
   * @memberof VisualEditorComponent
   */
  removeColorConfig(index: number) {
    this.newVisualizationConfigOfAllTypes.get(this.currentSelectedName).color.splice(index, 1);
  }

  /**
   * Edit one color config.
   *
   * @param {number} index
   * @memberof VisualEditorComponent
   */
  editColorConfig(index: number) {
    this.selectedConfigControllerEvent = 'editColorConfig';
    this.selectedColorIndex = index;
    this.selectedVisualizationConfig = this.newVisualizationConfigOfAllTypes.get(this.currentSelectedName);
    this.selectedColorValue = this.selectedVisualizationConfig.color[index].colorValue;
    this.exprModel = cloneDeep(<BaseCondition>this.selectedVisualizationConfig.color[index].filter);

    // Get the corresponding vertex or edge and push them in the expression form config.
    const vertexOrEdge = this.logicService.getSchemaVertex(this.currentSelectedName) ||
      this.logicService.getSchemaEdge(this.currentSelectedName);
    this.colorExprFormConfig.entityType = vertexOrEdge.name;

    this.openConfigPopupWindow();
  }

  /**
   * Edit size config.
   *
   * @memberof VisualEditorComponent
   */
  editSizeConfig() {
    this.selectedConfigControllerEvent = 'editSizeConfig';
    this.selectedVisualizationConfig = this.newVisualizationConfigOfAllTypes.get(this.currentSelectedName);
    this.exprModel = cloneDeep(<BaseExpression>this.selectedVisualizationConfig.size);

    // Get the corresponding vertex or edge and push them in the expression form config.
    const vertexOrEdge = this.logicService.getSchemaVertex(this.currentSelectedName) ||
      this.logicService.getSchemaEdge(this.currentSelectedName);
    this.sizeExprFormConfig.entityType = vertexOrEdge.name;

    this.openConfigPopupWindow();
  }

  /**
   * Check if a type is a vertex or edge by its name.
   *
   * @param {string} name
   * @returns {boolean}
   * @memberof VisualEditorComponent
   */
  isVertexType(name: string): boolean {
    if (this.logicService.getSchemaVertex(name)) {
      return true;
    } else {
      return false;
    }
  }

  /**
   * Apply to store the color config.
   *
   * @memberof VisualEditorComponent
   */
  applyColorConfig() {
    if (this.exprModel) {
      this.selectedVisualizationConfig.color[this.selectedColorIndex].filter = <BaseCondition>this.exprModel;
      this.exprModel = undefined;
    }
    this.selectedVisualizationConfig.color[this.selectedColorIndex].colorValue = this.selectedColorValue;
    this.closeConfigPopupWindow();
  }

  /**
   * Apply to store the size config.
   *
   * @memberof VisualEditorComponent
   */
  applySizeConfig() {
    if (this.exprModel) {
      this.selectedVisualizationConfig.size = <BaseExpression>this.exprModel;
      this.exprModel = undefined;
    }
    this.closeConfigPopupWindow();
  }

  clearSizeConfig() {
    const exprFactory = FactoryProducer.getFactory(ExpressionFactory);
    const nullExpr = exprFactory.getExpression(exprDict['NullExpression'], []);

    this.newVisualizationConfigOfAllTypes.get(this.currentSelectedName).size =
      exprFactory.getExpression(exprDict['NullExpression'], [nullExpr, nullExpr]);
  }

  /**
   * Remove the prefix of the attribute variable in expression string.
   *
   * @param {string} exprString
   * @returns {string}
   * @memberof VisualEditorComponent
   */
  removeAlias(exprString: string): string {
    return exprString ?
      exprString.replace(new RegExp(`${this.currentSelectedName}.`, 'g'), '') :
      'None';
  }

  /**
   * Open the visualization config popup page.
   *
   * @memberof VisualEditorComponent
   */
  openConfigPopupWindow() {
    this.configWindowRef = this.dialog.open(this.configWindow, this.dialogConfig);
  }

  /**
   * Close the visualization config popup page.
   *
   * @memberof VisualEditorComponent
   */
  closeConfigPopupWindow() {
    if (this.configWindowRef) {
      this.configWindowRef.close();
    }
  }

  /**
   * Save the screenshot
   *
   * @memberof VisualEditorComponent
   */
  saveScreenshot(scale = 2.0) {
    // Screenshot is too large to export
    if (scale <= 0) {
      this.handleError('Screenshot is too large to export.', 'Error');
      return;
    }
    const pngBlobPromise = this.graphRef.current.exportPng({
      full: true,
      bg: '#fff',
      output: 'blob-promise',
      scale: scale,
    }) as Promise<Blob>;
    pngBlobPromise.then((blob) => {
      // Successfully save the screenshot
      saveAs(blob, 'Screenshot.png');
    }).catch(() => {
      // Retry with a smaller scale
      this.saveScreenshot(scale - 0.2);
    });
  }

  /**
   * Handler for the user's interactions with the toolbar.
   *
   * @param {ToolbarInteraction} event
   * @memberof VisualEditorComponent
   */
  onInteraction(event: ToolbarInteraction) {
    switch (event.key) {
      case EventKey.Search:
        this.selectedEvent = event.key;
        this.openPopupWindow();
        this.toggleLocatingVertexMode();
        break;
      case EventKey.Show:
        this.updatePositions(this.logicService.graph);
        this.handleShow();
        break;
      case EventKey.Hide:
        this.updatePositions(this.logicService.graph);
        this.handleHide();
        break;
      case EventKey.Undo:
        this.updatePositions(this.logicService.graph);
        this.renderChart(this.logicService.undo());
        this.lockNodes();
        timer(100)
          .pipe(takeUntil(this.ngUnsubscribe))
          .subscribe(() => {
            const curSelection = this.getCurrentSelections();
            this.selectionChange.emit([curSelection.nodes, curSelection.links]);
          });
        break;
      case EventKey.Redo:
        this.updatePositions(this.logicService.graph);
        this.renderChart(this.logicService.redo());
        break;
      case EventKey.AddVertex:
        // We want to have fresh vertex object and form everytime the user switch from edit to add.
        if (this.selectedEvent === 'editAttributes') {
          this.initializeVertex();
        }

        this.vertexTypes = this.logicService.getAllVertexTypes();
        this.vertex.type = this.vertexTypes[0]; // set the default vertex type.
        this.onVertexTypeChange(this.vertex.type); // set the attributes.
        this.selectedEvent = event.key;
        this.openPopupWindow();
        this.toggleAddVertexMode();
        break;
      case EventKey.AddEdge:
        // We want to have fresh edge object and form everytime the user switch from edit to add.
        if (this.selectedEvent === 'editAttributes') {
          this.initializeEdge();
        }
        this.toggleAddEdgeMode();
        break;
      case EventKey.EditAttributes:
        this.selectedEvent = event.key;
        this.handleEditAttributes();
        break;
      case EventKey.Delete:
        this.handleDelete();
        break;
      case EventKey.Screenshot:
        const vertices = this.getNodes();
        if (vertices.length === 0) {
          this.handleError('No data to export.', 'Error');
          break;
        }
        if (this.graphRef?.current) {
          this.saveScreenshot();
        }
        break;
      case EventKey.ExportCSV:
        this.exportCSV();
        break;
      case EventKey.Settings:
        this.selectedEvent = event.key;
        this.tmpAttributesForSelected = cloneDeep(this.attributesForSelected);
        if (this.currentSelectedName === undefined) {
          this.onCurrentSelectedNameChange(this.tmpAttributesForSelected.names[0]);
        } else {
          this.onCurrentSelectedNameChange(this.currentSelectedName);
        }
        this.newVisualizationConfigOfAllTypes = cloneDeep(this.visualizationConfigOfAllTypes);
        if (!this.numberFormatting) {
          this.numberFormatting = HelperFunctions.defaultNumberFormatting;
        }
        this.numberFormattingForShowing = cloneDeep(this.numberFormatting);
        this.setNumberFormattingSample();
        this.openPopupWindow();
        this.toggleUpdateSettingsMode();
        break;
      case EventKey.VisInfo:
        this.selectedEvent = event.key;
        this.openPopupWindow();
        break;
      case EventKey.Save:
        this.selectedEvent = event.key;
        this.saveExplorationForm =
          this.cmptService.buildSaveExplorationForm(this.explorationNameToSave, this.explorationDescriptionToSave);
        this.getPreviewImage();
        this.currentDatetime = Date.now();
        this.openPopupWindow();
        this.toggleSaveExplorationMode();
        break;
      case EventKey.Open:
        this.selectedEvent = event.key;
        this.getSavedExplorationList();
        this.openPopupWindow();
        this.toggleOpenExploreHistoryMode();
        break;
      case EventKey.SwitchEdit:
        this.editButtonChecked = !this.editButtonChecked;
        this.enableDrawMode(this.editButtonChecked);
        this.cacheService.setItem(
          `${this.cachePrefix}EditModeOn_${this.graphName}`,
          this.editButtonChecked
        );
        this.toggleEditButtons();
        break;
      case EventKey.ZoomIn:
        this.zoom('in');
        break;
      case EventKey.ZoomOut:
        this.zoom('out');
        break;
      case EventKey.Refresh:
        this.refreshExploration();
        break;
      default:
        if (this.chartLayout.includes(event.key)) {
          this.onLayoutSelection(event.key);
        }
        break;
    }
  }

  getPopupTitle(): string {
    switch (this.selectedEvent) {
      case EventKey.Open:
        return 'Open exploration';
      case EventKey.Save:
        return 'Save exploration';
      case EventKey.Search:
        return 'Locate vertices in result';
      case EventKey.AddVertex:
        return 'Add new vertex';
      case EventKey.AddEdge:
        return 'Add new edge';
      case EventKey.EditAttributes:
        return 'Edit attributes';
      case EventKey.Settings:
        return 'Settings';
      case EventKey.VisInfo:
        return 'Result chart information';
    }
  }

  /**
   * Save Exploration result.
   *
   * @memberof VisualEditorComponent
   */
  saveExploration() {
    this.toggleLoading();
    this.cmptService
      .getSavedExplorationList(this.graphName)
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe(
        list => {
          list = list.map(exploration => exploration.name);
          if (list.includes(this.explorationNameToSave)) {
            const data: DialogData = {
              title: 'Warning',
              messages: [
                `The exploration result "${this.explorationNameToSave}" already exists on the server.`,
                'Do you want to overwrite?'
              ],
              actions: [
                { label: 'CANCEL', value: 0 },
                { label: 'OVERWRITE', value: 1, color: 'warn' }
              ]
            };
            const dialogRef = this.dialog
              .open(DialogComponent, { data: data, autoFocus: false });

            dialogRef
              .keydownEvents()
              .pipe(takeUntil(this.ngUnsubscribe))
              .subscribe(event => {
                if (event.key.toLowerCase() === 'enter') {
                  dialogRef.close(1);
                }
              });

            dialogRef
              .afterClosed()
              .pipe(takeUntil(this.ngUnsubscribe))
              .subscribe(value => {
                if (value === 1) {
                  this.confirmSaveExploration(true);
                } else {
                  this.toggleLoading();
                }
              });
          } else {
            this.confirmSaveExploration(false);
          }
        },
        (err: HttpErrorResponse) => {
          this.toggleLoading();
          this.handleError(err.error, err.statusText);
        }
      );
  }

  /**
   * Delete saved exploration result.
   *
   * @param {string} fileName
   * @memberof VisualEditorComponent
   */
  deleteExploration(fileName: string) {
    const data: DialogData = {
      title: 'Warning',
      messages: [
        'Deletion is irreversible.',
        `Are you sure you want to delete exploration result "${fileName}"?`
      ],
      actions: [
        { label: 'CANCEL', value: 0 },
        { label: 'CONTINUE', value: 1, color: 'warn' }
      ]
    };
    const dialogRef = this.dialog
      .open(DialogComponent, { data: data, autoFocus: false });

    dialogRef
      .keydownEvents()
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe(event => {
        if (event.key.toLowerCase() === 'enter') {
          dialogRef.close(1);
        }
      });

    dialogRef
      .afterClosed()
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe(value => {
        if (value === 1) {
          this.confirmDeleteExploration(fileName);
        }
      });
  }

  /**
   * Apply selected exploration result.
   *
   * @memberof VisualEditorComponent
   */
  applyExploration() {
    const data: DialogData = {
      title: 'Warning',
      messages: [
        'Apply will overwrite your current exploration result (and configurations).',
        'Are you sure you want to apply?'
      ],
      actions: [
        { label: 'CANCEL', value: 0 },
        { label: 'CONTINUE', value: 1, color: 'warn' }
      ]
    };
    const dialogRef = this.dialog
      .open(DialogComponent, { data: data, autoFocus: false });

    dialogRef
      .keydownEvents()
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe(event => {
        if (event.key.toLowerCase() === 'enter') {
          dialogRef.close(1);
        }
      });

    dialogRef
      .afterClosed()
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe(value => {
        if (value === 1) {
          this.toggleLoading();
          this.cmptService
            .getSavedExploration(this.graphName, this.checkedExploration)
            .pipe(
              takeUntil(this.ngUnsubscribe),
              finalize(() => this.toggleLoading())
            )
            .subscribe(
              result => {
                this.confirmApplyExploration(result);
              },
              (err: HttpErrorResponse) => this.handleError(err.error, err.statusText)
            );
        }
      });
  }

  /**
   * Confirm to apply selected exploration to current graph chart.
   *
   * @param {ExplorationInfo} exploration
   * @memberof VisualEditorComponent
   */
  confirmApplyExploration(exploration: ExplorationInfo) {
    if (!exploration.schema) {
      return;
    }
    // If the schema is incompatible, to apply is not allowed.
    const currentSchema = this.logicService.schema;

    // Modify exploration data based on schema compatibility.
    const currentSchemaJson = currentSchema.dumpToGSQLJson();
    const savedSchema = exploration.schema;
    if (!savedSchema) {
      return;
    }
    const schemaNotSame = !isEqual(currentSchemaJson, savedSchema);

    // Map containing all attribute related changes.
    const attrChangesMap = new Map<string, AttributeRelatedChanges>();
    if (schemaNotSame) {
      // Get attribute related changes map.
      const oldTypesMap = new Map<string, { [attrName: string]: GSQLAttributeJson }>();
      (<(GSQLVertexJson | GSQLEdgeJson)[]>savedSchema.VertexTypes).concat(savedSchema.EdgeTypes)
        .forEach(type => {
          const attrs: { [attrName: string]: GSQLAttributeJson } = {};
          type.Attributes.forEach(attr => attrs[attr.AttributeName] = attr);
          oldTypesMap.set(type.Name, attrs);
        });

      (<(GSQLVertexJson | GSQLEdgeJson)[]>currentSchemaJson.VertexTypes)
        .concat(currentSchemaJson.EdgeTypes)
        .forEach(type => {
          const oldAttrs = oldTypesMap.get(type.Name);
          if (!oldAttrs || isEqual(type.Attributes, Object.values(oldAttrs))) {
            return;
          }
          const newAttrNames = type.Attributes.map(attr => attr.AttributeName);
          const removedAttrs: string[] =
            Object.keys(oldAttrs).filter(attrName => !newAttrNames.includes(attrName));
          const changedAttrs: { [name: string]: GSQLAttributeJson } = {};
          const newAttrs: { [name: string]: GSQLAttributeJson } = {};
          type.Attributes.forEach(attr => {
            if (!oldAttrs[attr.AttributeName]) {
              newAttrs[attr.AttributeName] = attr;
            } else if (!isEqual(oldAttrs[attr.AttributeName], attr)) {
              changedAttrs[attr.AttributeName] = attr;
            }
          });
          attrChangesMap.set(type.Name, {
            removedAttrs: removedAttrs,
            changedAttrs: changedAttrs,
            newAttrs: newAttrs
          });
        });
    }

    // Apply exploration result.
    const explorationResult = exploration.data.explorationResult;
    if (explorationResult) {
      this.applyExplorationResult(currentSchemaJson, explorationResult, attrChangesMap, schemaNotSame);
    }

    // Apply graph chart configurations.
    this.applyExplorationChartConfig(
      exploration.data.graphChartConfig,
      currentSchema,
      attrChangesMap,
      schemaNotSame
    );

    // Emit graph explorer configurations.
    let graphExplorerConfig = exploration.data.graphExplorerConfig;
    if (this.componentName === 'GraphExplorer' && graphExplorerConfig) {
      if (schemaNotSame) {
        graphExplorerConfig = this.updateGraphExplorerConfig(
          graphExplorerConfig,
          currentSchemaJson,
          attrChangesMap
        );
      }

      this.graphExplorerConfigApplied.next(graphExplorerConfig);
    }
    this.closePopupWindow();
  }

  /**
   * Save the selected layout.
   *
   * @param {string} layout
   * @memberof VisualEditorComponent
   */
  onLayoutSelection(layout: string) {
    this.skipAutoLayout = this.graphLayout === 'Preset';
    this.graphLayout = layout;
    (<StrokedIconButton>this.toolbarConfig.top[ToolbarButtons.ChangeLayout]).label = this.graphLayout;
  }

  /**
   * Retrieve the URL of the layout example image.
   *
   * @param {string} layout
   * @returns {string}
   * @memberof VisualEditorComponent
   */
  getLayoutImageURL(layout: string): string {
    return `assets/img/layout-icons/${layout}-layout-icon.png`;
  }

  /**
   * Search a vertex in the current visualization.
   *
   * @memberof VisualEditorComponent
   */
  searchVertex() {
    const vertices = this.getNodes();
    const contentToSearch = this.searchKeyword.toLowerCase();

    const found = vertices.filter(vertex => {
      if (this.searchByVertexId) {
        // Check if match ID, early stop if ID is matched.
        const idInLowerCase = vertex.exID.toLowerCase();
        if (
          idInLowerCase === contentToSearch ||
          this.substringMatch && idInLowerCase.includes(contentToSearch)
        ) {
          return true;
        }
      }

      if (this.searchByVertexAttrs) {
        // Check if match attributes, early stop if one attribute is matched.
        for (const key of Object.keys(vertex.attrs)) {
          const attrInLowerCase = `${vertex.attrs[key]}`.toLowerCase();
          if (
            attrInLowerCase === contentToSearch ||
            this.substringMatch && attrInLowerCase.includes(contentToSearch)
          ) {
            return true;
          }
        }
      }
    });

    // If found, set the vertices to be selected and auto center the visualization.
    if (found.length !== 0) {
      this.focusElementsInNewGraph({ nodes: found, links: [] });
    }

    this.searchKeyword = undefined;
    this.closePopupWindow();
  }

  /**
   * Populate the attribute list depends on the vertex type selected.
   *
   * @param {string} type
   * @memberof VisualEditorComponent
   */
  onVertexTypeChange(type: string) {
    const schemaVertex = this.logicService.getSchemaVertex(type);
    this.vertex.attrs = {};
    this.selectedVertexAttributes = [];

    this.selectedVertexAttributes = this.cmptService.buildAttributeQuestions(schemaVertex.attributes);
    // Default value for primary id.
    if (schemaVertex.primaryId.hasDefaultValue) {
      this.vertex.id = schemaVertex.primaryId.defaultValue;
    }
  }

  /**
   * Populate the attribute list depends on the edge type selected.
   *
   * @param {string} type
   * @memberof VisualEditorComponent
   */
  onEdgeTypeChange(type: string) {
    const schemaEdge = this.logicService.getSchemaEdge(type);
    this.edge.attrs = {};
    this.selectedEdgeAttributes = [];
    this.isDiscriminatorList = [];

    this.selectedEdgeAttributes = this.cmptService.buildAttributeQuestions(schemaEdge.attributes);
    this.isDiscriminatorList = schemaEdge.attributes.map(attribute => attribute.isDiscriminator);

    // Set the source and target vertices.
    this.edge.source.type = this.sourceVertex.exType;
    this.edge.source.id = this.sourceVertex.exID;
    this.edge.target.type = this.targetVertex.exType;
    this.edge.target.id = this.targetVertex.exID;
    // Set directed or not.
    this.edge.directed = schemaEdge.directed;
  }

  /**
   * Adjust elements' appearance based on current user's privilege.
   *
   * @memberof VisualEditorComponent
   */
  adjustElementAppearancesBaseOnUserPrivilege() {
    // Exit adding edge mode.
    if (this.isAddingEdge) {
      this.toggleAddEdgeMode();
    }

    // Default toolbar button disable status.
    this.toolbarConfig.top.forEach(button => button.disabled = false);

    if (!this.authService.hasPrivilege(GSQLPrivilege.AppAccessData)) {
      this.changeButtonDisableStatus('export', true);
      this.changeButtonDisableStatus('visInfo', true);
    }
    this.changeButtonDisableStatus('editAttributes', true);
    this.changeButtonDisableStatus('delete', true);

    const currentGraph = this.authService.getCurrentGraph();

    // Enable open/save graph exploration button.
    this.toolbarConfig.top[ToolbarButtons.Open].disabled = false;
    this.toolbarConfig.top[ToolbarButtons.Save].disabled = false;

    if (
      !this.authService.hasPrivilege(GSQLPrivilege.ReadSchema)
    ) {
      // Disable all buttons.
      this.toolbarConfig.top.forEach(button => button.disabled = true);
      // Show hint to user
      this.warning.next(`You don't have privilege to explore ${currentGraph}.`);
    } else {
      this.warning.next(undefined);

      // Edit mode
      this.toolbarConfig.top[ToolbarButtons.Switch].show = true;
      this.editButtonChecked =
        this.cacheService.getItem(`${this.cachePrefix}EditModeOn_${this.graphName}`) ||
        false;
      this.toggleEditButtons();
    }
  }

  /**
   * Close the popup window.
   * In case of add edge, clean source and target vertex.
   *
   * @memberof VisualEditorComponent
   */
  closePopup() {
    if (this.selectedEvent === 'addEdge') {
      this.initializeEdgeAction();
    }
    this.newLabelFontSize = this.labelFontSize;
    this.closePopupWindow();
  }

  /**
   * Add a new vertex or edge to the graph.
   * Close the popup if there is no error; otherwise maintain the popup and show the error to the user.
   *
   * @memberof VisualEditorComponent
   */
  addElement() {
    let res: ValidateResult;

    // Add locally without save.
    if (this.selectedEvent === 'addVertex') {
      this.retrieveAttributeFromForm(this.logicService.getSchemaVertex(this.vertex.type), this.vertex);
      res = this.logicService.addVertex(this.vertex, false);
    } else {
      this.retrieveAttributeFromForm(this.logicService.getSchemaEdge(this.edge.type), this.edge);
      res = this.logicService.addEdge(this.edge, false);
    }

    if (res.success) {
      let obs: Observable<any>;
      let checkObs: Observable<boolean>;

      if (this.selectedEvent === 'addVertex') {
        obs = this.cmptService.upsertVertex(this.logicService.schema.name,
          this.vertex, this.logicService.getSchemaVertex(this.vertex.type));
        checkObs = this.preCheckForSave(this.vertex, true);
      } else {
        const { shouldReverseEdge } = this.logicService.getMatchEdgeTypes(
          this.edge.source.type,
          this.edge.target.type
        );
        const upsertEdge = cloneDeep(this.edge);
        if (shouldReverseEdge) {
          [upsertEdge.source, upsertEdge.target] = [this.edge.target, this.edge.source];
        }
        obs = this.cmptService.upsertEdge(this.logicService.schema.name,
          upsertEdge, this.logicService.getSchemaEdge(this.edge.type));
        checkObs = this.preCheckForSave(this.edge, false);
      }

      this.toggleLoading();

      checkObs
        .pipe(takeUntil(this.ngUnsubscribe))
        .subscribe(canInsert => {
          // Add the vertex/edge on the server and reload graph when it's done.
          if (canInsert) {
            obs
              .pipe(takeUntil(this.ngUnsubscribe))
              .subscribe(
                () => {
                  // Do local addition.
                  if (this.selectedEvent === 'addVertex' && this.vertex.type && this.vertex.id) {
                    this.logicService.addVertex(this.vertex);
                    this.updatePositions(this.logicService.graph);
                    const vertex = cloneDeep(this.vertex);
                    timer(200)
                      .pipe(takeUntil(this.ngUnsubscribe))
                      .subscribe(() => {
                        this.graphRef.current?.selectNodes([vertex]);
                      });
                  } else if (this.selectedEvent === 'addEdge' && this.edge.type) {
                    this.logicService.addEdge(this.edge);
                    const edge = cloneDeep(this.edge);
                    timer(200)
                      .pipe(takeUntil(this.ngUnsubscribe))
                      .subscribe(() => {
                        this.graphRef.current?.selectEdges([edge]);
                      });
                  }

                  this.closePopupWindow();
                  this.renderChart(this.logicService.getGraph());
                  this.toggleLoading();

                  // Clean up.
                  if (this.selectedEvent === 'addVertex') {
                    this.initializeVertex();
                  } else {
                    this.initializeEdge();
                    this.initializeEdgeAction();
                  }
                },
                (err: HttpSourceErrorResponse | string) => {
                  this.toggleLoading();
                  if (typeof err === 'string') {
                    this.handleError(err, 'Error');
                    return;
                  }
                  this.handleError(err.error, err.statusText);
                }
              );
          } else {
            this.toggleLoading();
          }
        });
    } else {
      this.handleError(res.message);
    }
  }

  /**
   * Update a vertex or edge.
   * Close the popup if there is no error; otherwise maintain the popup and show the error to the user.
   *
   * @memberof VisualEditorComponent
   */
  updateElement() {
    let res: ValidateResult;

    // Update locally without save.
    if (this.selectedVertex) {
      this.retrieveAttributeFromForm(this.logicService.getSchemaVertex(this.vertex.type), this.vertex);
      res = this.logicService.updateVertex(this.vertex, false);
    } else {
      this.retrieveAttributeFromForm(this.logicService.getSchemaEdge(this.edge.type), this.edge);
      res = this.logicService.updateEdge(this.edge, false);
    }

    if (res.success) {
      let obs: Observable<any>;

      if (this.selectedVertex) {
        obs = this.cmptService.upsertVertex(this.logicService.schema.name,
          this.vertex, this.logicService.getSchemaVertex(this.vertex.type));
      } else {
        const { shouldReverseEdge } = this.logicService.getMatchEdgeTypes(
          this.edge.source.type,
          this.edge.target.type
        );
        const upsertEdge = cloneDeep(this.edge);
        if (shouldReverseEdge) {
          [upsertEdge.source, upsertEdge.target] = [this.edge.target, this.edge.source];
        }
        obs = this.cmptService.upsertEdge(this.logicService.schema.name,
          upsertEdge, this.logicService.getSchemaEdge(this.edge.type));
      }

      this.closePopupWindow();
      this.toggleLoading();

      // Update the vertex/edge on the server and reload graph when it's done.
      obs
        .pipe(takeUntil(this.ngUnsubscribe))
        .subscribe(
          () => {
            // Do local update.
            if (this.selectedVertex) {
              this.logicService.updateVertex(this.vertex);
              this.initializeVertex();
            } else {
              this.logicService.updateEdge(this.edge);
              this.initializeEdge();
            }

            this.renderChart(this.logicService.getGraph());
            this.lockNodes();
            this.toggleLoading();
          },
          (err: HttpSourceErrorResponse | string) => {
            this.toggleLoading();
            if (typeof err === 'string') {
              this.handleError(err, 'Error');
              return;
            }
            this.handleError(err.error, err.statusText);
          }
        );
    } else {
      this.handleError(res.message);
    }
  }

  /**
   * Refresh the exploration.
   *
   * @memberof VisualEditorComponent
   */
  refreshExploration() {
    const queryCode = this.logicService.getRefreshExplorationQuery();
    this.toggleLoading();
    this.cmptService.runQueryInInterpretedMode(queryCode)
      .pipe(
        takeUntil(this.ngUnsubscribe),
        finalize(() => this.toggleLoading())
      )
      .subscribe(
        response => {
          const duplicateGraph = this.logicService.getGraph();
          const idToNodeMap = new Map<string, ExternalNode>();
          duplicateGraph.graph.nodes.forEach((node) => idToNodeMap.set([node.id, node.type].join(''), node));

          response[0].VertexResult.forEach((vertexResult) => {
            const vertexIdAndType = [vertexResult.v_id, vertexResult.v_type].join('');
            if (idToNodeMap.has(vertexIdAndType)) {
              const node = idToNodeMap.get(vertexIdAndType);
              for (const [key, value] of Object.entries(node.attrs)) {
                if (!vertexResult.attributes.hasOwnProperty?.(key)) {
                  vertexResult.attributes[key] = value;
                }
              }
            }
          });

          this.logicService.clearData();
          this.snackBar.open(
            `Graph just refreshed!`,
            'DISMISS',
            { duration: 3000 }
          );

          // Refresh results.
          this.addData(response, 'gquery', false);
        },
        err => {
          if (typeof err === 'string') {
            this.handleError(err, 'Error');
            return;
          }
          this.handleError(err.error, err.statusText);
        }
      );
  }

  /**
   * Get attribute data from form and convert to correct type.
   *
   * @param {(Vertex | Edge)} schemaVertexOrEdge
   * @param {(ExternalNode | ExternalLink)} vertexOrEdge
   * @memberof VisualEditorComponent
   */
  retrieveAttributeFromForm(schemaVertexOrEdge: Vertex | Edge, vertexOrEdge: ExternalNode | ExternalLink) {
    schemaVertexOrEdge.attributes.forEach(attr => {
      const attrName = attr.isDiscriminator ? this.dynamicForm.form.getRawValue()[attr.name] :
        this.dynamicForm.form.value[attr.name];

      if (attr.type.name === 'LIST') {
        const attrValue = Object.values(attrName).map(value =>
          attr.type.valueTypeName === 'UDT'
            ? value
            : this.cmptService.convertValueType(`${value}`, attr.type.valueTypeName)
        );
        vertexOrEdge.attrs[attr.name] = attrValue;
      } else if (attr.type.name === 'SET') {
        const attrValue = Object.values(attrName)
          .filter((ele, index, self) => self.indexOf(ele) === index)
          .map(value =>
            attr.type.valueTypeName === 'UDT'
              ? value
              : this.cmptService.convertValueType(`${value}`, attr.type.valueTypeName)
          );
        vertexOrEdge.attrs[attr.name] = attrValue;
      } else if (attr.type.name === 'MAP') {
        vertexOrEdge.attrs[attr.name] = {};
        Object.values(attrName).forEach(keyValue => {
          vertexOrEdge.attrs[attr.name][keyValue['key']] =
            attr.type.valueTypeName === 'UDT'
              ? keyValue['value']
              : this.cmptService.convertValueType(`${keyValue['value']}`, attr.type.valueTypeName);
        });
      } else if (attr.type.name === 'UDT') {
        vertexOrEdge.attrs[attr.name] = {};
        const udt = this.udtList.filter(tuple => tuple.name === attr.type.tupleName)[0];
        Object.entries<string>(attrName).forEach(([fieldName, fieldValue], index) => {
          vertexOrEdge.attrs[attr.name][fieldName] =
            this.cmptService.convertValueType(`${fieldValue}`, udt.fields[index].fieldType);
        });
      } else {
        const attrValue = this.cmptService.convertValueType(`${attrName}`, attr.type.name);
        vertexOrEdge.attrs[attr.name] = attrValue;
      }
    });
  }

  /**
   * Beautify the attribute value.
   *
   * @param {*} attrValue
   * @returns {string}
   * @memberof VisualEditorComponent
   */
  beautifyAttribute(attrValue: any): string {
    return JSON.stringify(attrValue);
  }

  /**
   * Set margin left style for label display.
   *
   * @param {number} sizeValue
   * @returns {number}
   * @memberof VisualEditorComponent
   */
  getLabelMarginLeft(sizeValue: number): number {
    if (sizeValue > 30) {
      return -10;
    } else if (sizeValue > 25) {
      return -5;
    }
    return 0;
  }

  toggleEditButtons() {
    this.toolbarConfig.top[ToolbarButtons.Separator1].show = this.editButtonChecked;
    this.toolbarConfig.top[ToolbarButtons.AddVertex].show = this.editButtonChecked;
    this.toolbarConfig.top[ToolbarButtons.AddEdge].show = this.editButtonChecked;
    this.toolbarConfig.top[ToolbarButtons.EditAttributes].show = this.editButtonChecked;
    this.toolbarConfig.top[ToolbarButtons.Delete].show = this.editButtonChecked;
    this.toolbarConfig.top[ToolbarButtons.Switch].checked = this.editButtonChecked;
  }

  handleCopyAllAttributes() {
    let attributesJSon: any;
    if (this.selectedVertex) {
      if (this.vertex) {
        attributesJSon = this.vertex;
      }
    } else if (this.selectedEdge) {
      if (this.edge) {
        attributesJSon = this.edge;
      }
    }

    this.copier.copyText(JSON.stringify(attributesJSon));
    this.isCopied = true;
    timer(2000)
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe(() => {
        this.isCopied = false;
      });
  }

  getSelectedVertexAndEdge(data: {
    selectedVertex: GraphChartDataNode;
    selectedEdge: GraphChartDataLink
  }) {
    this.selectedVertex = data.selectedVertex;
    this.selectedEdge = data.selectedEdge;
  }

  handleGraphChartEnter(item: GraphChartDataNode | GraphChartDataLink) {
    const vertex =
      item && 'isNode' in item ? <GraphChartDataNode>item : undefined;

    if (vertex !== undefined) {
      this.getOneStepNeighbors(vertex);
    }
  }

  handleEscapeEmit() {
    this.handleEscape.emit();
  }

  createVertexEmit() {
    this.createVertex.emit();
  }

  createEdgeEmit() {
    this.createEdge.emit();
  }

  handleGraphChartPointer(item: GraphChartDataNode | GraphChartDataLink) {
    this.handleGraphChartPointerDown(item);
    this.handleGraphChartPointerUp(item);
  }

  /**
   * Export vertices and edges to csv files.
   *
   * @memberof VisualEditorComponent
   */
  exportCSV() {
    const allVertices = <GraphChartDataNode[]>this.getNodes();
    const allEdges = <GraphChartDataLink[]>this.getLinks();

    if (allVertices.length > 0) {
      this.loading.next(true);

      const exportFileName = `${this.authService.getCurrentGraph()}_export_vertices_and_edges`;
      const zip = new JSZip();
      const folder = zip.folder(exportFileName);

      this.makeCSVFilesForVertices(allVertices, folder);
      this.makeCSVFilesForEdges(allEdges, folder);

      zip.generateAsync({ type: 'blob' }).then((content: any) => {
        saveAs(content, `${exportFileName}.zip`);
        this.loading.next(false);
      });
    } else {
      this.handleError('No vertices or edges in the graph!');
    }
  }

  getSeparatorLabel(separator: string): string {
    switch (separator) {
      case ',':
        return ' , (comma)';
      case '.':
        return ' . (point)';
    }
  }

  setNumberFormattingSample() {
    this.numberFormattingError = [];
    const sampleNumber = 1234.56;
    this.numberFormattingSample = String(sampleNumber);

    const { thousandsSeparator, unifyDecimalSpaces, decimalSeparator } =
      this.numberFormattingForShowing;

    if (thousandsSeparator.enabled && thousandsSeparator.value === decimalSeparator.value) {
      this.numberFormattingError.push(NumberFormattingErrorMessage.sameError);
    }

    if (
      !unifyDecimalSpaces.value ||
      !Array.apply(null, { length: 13 }).map(Number.call, Number).includes(Number(unifyDecimalSpaces.value))
    ) {
      this.numberFormattingError.push(NumberFormattingErrorMessage.formatError);
    }

    if (this.numberFormattingError.length > 0) {
      return;
    }

    this.numberFormattingSample =
      HelperFunctions.numberFormatting(
        sampleNumber,
        unifyDecimalSpaces,
        thousandsSeparator,
        decimalSeparator
      );
  }

  canRefresh(): boolean {
    return this.authService.hasPrivilege(GSQLPrivilege.WriteQuery) ||
      // 4.1.0 and later version
      this.authService.hasPrivilege(GSQLPrivilege.CreateQuery);
  }

  /**
   * Make CSV files for vertices.
   *
   * @private
   * @param {GraphChartDataNode[]} verticesData
   * @param {*} folder
   * @memberof VisualEditorComponent
   */
  private makeCSVFilesForVertices(
    verticesData: GraphChartDataNode[],
    folder: any
  ) {
    const csvFiles: CSVFile = {};
    const vertexHeader = ['Vertex ID', 'Vertex Type'];

    verticesData.forEach((vertex: GraphChartDataNode) => {
      const row = {};
      const exType = vertex.exType;

      // Header
      if (!csvFiles[exType]) {
        csvFiles[exType] = {
          header: cloneDeep(vertexHeader),
          row: []
        };

        Object.keys(vertex.attrs).forEach(key => {
          csvFiles[exType].header.push(key);
        });
      }

      // Body
      row[vertexHeader[0]] = vertex.exID;
      row[vertexHeader[1]] = exType;

      Object.entries(vertex.attrs).forEach(([key, value]) => {
        row[key] = HelperFunctions.isPrimitive(value)
          ? value : JSON.stringify(value, null, 4);
      });

      csvFiles[exType].row.push(row);
    });

    Object.entries(csvFiles).forEach(([fileName, fileContent]) => {
      folder.file(
        `${fileName}.csv`,
        HelperFunctions.convertRowsToCSVBlob(fileContent.header, fileContent.row)
      );
    });
  }

  /**
   * Make CSV files for edges.
   *
   * @private
   * @param {GraphChartDataLink[]} edgesData
   * @param {*} folder
   * @memberof VisualEditorComponent
   */
  private makeCSVFilesForEdges(
    edgesData: GraphChartDataLink[],
    folder: any
  ) {
    const csvFiles: CSVFile = {};
    const edgeHeader = [
      'Edge Type',
      'Source Vertex ID',
      'Source Vertex Type',
      'Target Vertex ID',
      'Target Vertex Type'
    ];

    edgesData.forEach((edge: GraphChartDataLink) => {
      const row = {};
      const exType = edge.exType;

      // Header
      if (!csvFiles[exType]) {
        csvFiles[exType] = {
          header: cloneDeep(edgeHeader),
          row: []
        };

        Object.keys(edge.attrs).forEach(key => {
          csvFiles[exType].header.push(key);
        });
      }

      // Body
      row[edgeHeader[0]] = exType;
      row[edgeHeader[1]] = edge.source.exID;
      row[edgeHeader[2]] = edge.source.exType;
      row[edgeHeader[3]] = edge.target.exID;
      row[edgeHeader[4]] = edge.target.exType;
      Object.entries(edge.attrs).forEach(([key, value]) => {
        row[key] = HelperFunctions.isPrimitive(value)
          ? value : JSON.stringify(value, null, 4);
      });

      csvFiles[exType].row.push(row);
    });

    Object.entries(csvFiles).forEach(([fileName, fileContent]) => {
      folder.file(
        `${fileName}.csv`,
        HelperFunctions.convertRowsToCSVBlob(fileContent.header, fileContent.row)
      );
    });
  }

  private handleGraphChartPointerDown(item: GraphChartDataNode | GraphChartDataLink) {
    const touchedVertex =
      item && 'isNode' in item ? <GraphChartDataNode>item : undefined;
    // Emit the selected vertex.
    if (touchedVertex !== undefined) {
      this.touchedVertex.next(touchedVertex);
    }
    const touchedEdge =
      item && 'isLink' in item ? <GraphChartDataLink>item : undefined;
    if (touchedEdge !== undefined) {
      this.touchedEdge.next(touchedEdge);
    }
  }

  private handleGraphChartPointerUp(item: GraphChartDataNode | GraphChartDataLink) {
    this.selectedVertex =
      item && 'isNode' in item ? <GraphChartDataNode>item : undefined;
    this.selectedEdge =
      item && 'isLink' in item ? <GraphChartDataLink>item : undefined;

    this.changeButtonDisableStatus(
      'editAttributes',
      !(this.selectedVertex || this.selectedEdge)
    );
    this.handleAddEdge();
  }

  private saveSettingsToCacheService(value: ChartSettingConfig) {
    const configsToSave = cloneDeep(value);
    if (configsToSave.visualizationConfigOfAllTypes) {
      [...configsToSave.visualizationConfigOfAllTypes].forEach(([key, config]) => {
        config.color = [...config.color].map(colorConfig => ({
          colorValue: colorConfig.colorValue,
          filter: (<BaseCondition>colorConfig.filter).toJson()
        }));
        config.size = (<BaseExpression>config.size).toJson();
      });
      configsToSave.visualizationConfigOfAllTypes =
        HelperFunctions.convertMapToObject(configsToSave.visualizationConfigOfAllTypes);
    }
    this.cacheService.setItem(`${this.cachePrefix}Settings_${this.graphName}`, configsToSave);
  }

  /**
   * Check if the combined vertex and edge counts of new graph and current graph exceeds the limitation.
   *
   * @private
   * @param {ExternalGraph} newGraph
   * @returns {boolean}
   * @memberof VisualEditorComponent
   */
  private exceedGraphSize(newGraph: ExternalGraph): boolean {
    const graph = this.logicService.getGraph().graph;
    const vertexSize = graph.nodes.length + newGraph.nodes.length;
    const edgeSize = graph.links.length + newGraph.links.length;
    return vertexSize >= vertexSizeLimit || edgeSize >= edgeSizeLimit;
  }

  /**
   * Get current graph result.
   *
   * @private
   * @returns {ChartData}
   * @memberof VisualEditorComponent
   */
  private getCurrentGraphResult(): ChartData {
    this.updatePositions(this.logicService.graph);
    const latestGraph = this.logicService.getGraph().graph;
    const currentSelecions = this.getCurrentSelections();
    const selectedNodes = [], selectedEdges = [];
    currentSelecions.nodes?.forEach(node => {
      selectedNodes.push({
        type: node.exType,
        id: node.exID
      });
    });
    currentSelecions.links?.forEach(link => {
      selectedEdges.push({
        type: link.exType,
        source: {
          type: link.source.exType,
          id: link.source.exID
        },
        target: {
          type: link.target.exType,
          id: link.target.exID
        },
        attrs: link.attrs,
        directed: link.directed,
      });
    });
    return {
      latestGraph: latestGraph,
      latestSelections: {
        nodes: selectedNodes,
        links: selectedEdges
      },
      latestLayout: this.graphLayout
    };
  }

  /**
   * Get saved exploration list from server.
   *
   * @private
   * @memberof VisualEditorComponent
   */
  private getSavedExplorationList() {
    this.toggleLoading();
    this.cmptService
      .getSavedExplorationList(this.graphName)
      .pipe(
        takeUntil(this.ngUnsubscribe),
        finalize(() => this.toggleLoading())
      )
      .subscribe(
        savedExplorations => this.savedExplorationList$.next(savedExplorations),
        (err: HttpErrorResponse) => this.handleError(err.error, err.statusText)
      );
  }

  /**
   * Get preview image for current exploration result.
   *
   * @private
   * @memberof VisualEditorComponent
   */
  private getPreviewImage() {
    let width = this.dataGraphContainer.containerRef.nativeElement.clientWidth;
    let height = this.dataGraphContainer.containerRef.nativeElement.clientHeight;
    if (width > previewMinWidth || height > previewMinHeight) {
      const widthRatio = width / previewMinWidth;
      const heightRatio = height / previewMinHeight;
      const fixWidthAreaSize = (width * height) / (widthRatio ** 2);
      const fixHeightAreaSize = (width * height) / (heightRatio ** 2);
      if (fixWidthAreaSize < fixHeightAreaSize) {
        width = previewMinWidth;
        height = height / widthRatio;
      } else {
        height = previewMinHeight;
        width = width / heightRatio;
      }
    }
    // Build screenshot
    this.previewImgURL = this.graphRef?.current?.exportPng({
      maxWidth: width,
      maxHeight: height,
      bg: '#fff',
    }) as string;
  }

  /**
   * Confirm to save exploration to server.
   *
   * @private
   * @memberof VisualEditorComponent
   */
  private confirmSaveExploration(overwrite: boolean) {
    const schema = this.logicService.schema;
    const currentGraphResult = this.getCurrentGraphResult();
    const exploration: ExplorationDBInfo = {
      name: this.explorationNameToSave,
      timestamp: this.currentDatetime,
      username: this.currentUsername,
      schema: schema.dumpToGSQLJson(),
      previewImage: this.previewImgURL,
      data: {
        explorationResult: currentGraphResult,
        graphChartConfig: this.cacheService.getItem(`${this.cachePrefix}Settings_${schema.name}`),
        graphExplorerConfig: this.cacheService.getItem(`${this.componentName}_${schema.name}`)
      },
      description: this.explorationDescriptionToSave
    };
    this.cmptService
      .saveExploration(exploration, overwrite)
      .pipe(
        takeUntil(this.ngUnsubscribe),
        finalize(() => this.toggleLoading())
      )
      .subscribe(
        () => {
          this.closePopupWindow();
          this.snackBar.open(`Exploration result "${this.explorationNameToSave}" is saved`, 'DISMISS', {
            duration: 3000
          });
          this.explorationNameToSave = '';
          this.explorationDescriptionToSave = '';
        },
        (err: HttpErrorResponse) => this.handleError(err.error, err.statusText)
      );
  }

  /**
   * Confirm to delete saved exploration from server.
   *
   * @private
   * @param {string} fileName
   * @memberof VisualEditorComponent
   */
  private confirmDeleteExploration(fileName: string) {
    this.toggleLoading();
    this.cmptService
      .deleteExploration(this.logicService.schema.name, fileName)
      .pipe(
        takeUntil(this.ngUnsubscribe),
        finalize(() => this.toggleLoading())
      )
      .subscribe(
        () => {
          this.checkedExploration = undefined;
          this.getSavedExplorationList();
          this.snackBar.open(`Exploration result "${fileName}" is deleted`, 'DISMISS', {
            duration: 3000
          });
        },
        (err: HttpErrorResponse) => this.handleError(err.error, err.statusText)
      );
  }

  /**
   * Apply chosen exploration graph chart result.
   *
   * @private
   * @param {GSQLGraphJson} currentSchemaJson
   * @param {ChartData} explorationResult
   * @param {Map<string, AttributeRelatedChanges>} attrChangesMap
   * @param {boolean} schemaNotSame
   * @memberof VisualEditorComponent
   */
  private applyExplorationResult(
    currentSchemaJson: GSQLGraphJson,
    explorationResult: ChartData,
    attrChangesMap: Map<string, AttributeRelatedChanges>,
    schemaNotSame: boolean
  ) {
    if (schemaNotSame) {
      const curVertexTypes = currentSchemaJson.VertexTypes.map(vt => vt.Name);
      const curEdgeTypes = currentSchemaJson.EdgeTypes.map(et => et.Name);

      // Modify graph chart data.
      explorationResult.latestGraph.nodes
        = explorationResult.latestGraph.nodes.filter(node => curVertexTypes.includes(node.type));
      explorationResult.latestGraph.links = explorationResult.latestGraph.links.filter(link =>
        curEdgeTypes.includes(link.type) &&
        curVertexTypes.includes(link.source.type) &&
        curVertexTypes.includes(link.target.type)
      );

      // Modify attribute fields.
      (<(ExternalNode | ExternalLink)[]>explorationResult.latestGraph.nodes)
        .concat(explorationResult.latestGraph.links)
        .forEach(nodeOrLink => {
          const attrChangesInfo = attrChangesMap.get(nodeOrLink.type);
          if (attrChangesInfo) {
            const updatedAttrs: { [key: string]: string | number | boolean | Object } = {};
            Object.keys(nodeOrLink.attrs).forEach(attrName => {
              // If attribute doesn't exit anymore, remove it.
              if (attrChangesInfo.removedAttrs.includes(attrName)) {
                return;
              }
              // If attribute changes, remove its value.
              if (attrChangesInfo.changedAttrs[attrName]) {
                updatedAttrs[attrName] =
                  this.assignDefaultValue(attrChangesInfo.changedAttrs[attrName].AttributeType);
                return;
              }
              // If attribute doesn't change, keep it.
              updatedAttrs[attrName] = nodeOrLink.attrs[attrName];
            });
            // If attribute is new, add it.
            Object.entries(attrChangesInfo.newAttrs).forEach(attr =>
              updatedAttrs[attr[0]] = this.assignDefaultValue(attr[1].AttributeType));
            nodeOrLink.attrs = updatedAttrs;
          }
        });

      // Modify graph chart selections
      explorationResult.latestSelections.nodes
        = explorationResult.latestSelections.nodes.filter(node => curVertexTypes.includes(node.type));
      explorationResult.latestSelections.links = explorationResult.latestSelections.links.filter(link =>
        curEdgeTypes.includes(link.type) &&
        curVertexTypes.includes(link.source.type) &&
        curVertexTypes.includes(link.target.type)
      );
    }

    this.clearData();
    // Add data and focus on the entire graph
    this.addData(
      explorationResult.latestGraph,
      'static',
      false,
      undefined,
      true,
      {
        selections: explorationResult.latestSelections,
        layout: explorationResult.latestLayout
      },
      true
    );
  }

  /**
   * Assign default value for changed attributes.
   *
   * @private
   * @param {GSQLAttributeTypeJson} attrType
   * @returns {*}
   * @memberof VisualEditorComponent
   */
  private assignDefaultValue(attrType: GSQLAttributeTypeJson): any {
    switch (attrType.Name) {
      case 'LIST':
      case 'SET':
        return [];
      case 'MAP':
        return {};
      case 'UDT':
        const fields: { [name: string]: any } = {};
        this.udtList.find(udt => udt.name === attrType.TupleName).fields
          .forEach(field => fields[field.fieldName] = '');
        return fields;
      default:
        return '';
    }
  }

  /**
   * Apply exploration graph chart configs based on schema changes.
   *
   * @private
   * @param {ChartSettingConfig} graphChartConfig
   * @param {Graph} schema
   * @param {Map<string, AttributeRelatedChanges>} attrChangesMap
   * @param {boolean} schemaNotSame
   * @memberof VisualEditorComponent
   */
  private applyExplorationChartConfig(
    graphChartConfig: ChartSettingConfig,
    schema: Graph,
    attrChangesMap: Map<string, AttributeRelatedChanges>,
    schemaNotSame: boolean
  ) {
    graphChartConfig =
      graphChartConfig || this.cacheService.getItem(`${this.cachePrefix}Settings_${this.graphName}`);
    if (graphChartConfig) {
      if (schemaNotSame) {
        const updatedAttributesForSelected = cloneDeep(this.attributesForSelected);
        const savedData = graphChartConfig.attributesForSelected.data;
        updatedAttributesForSelected.names.forEach(vertexOrEdge => {
          if (savedData[vertexOrEdge]) {
            const attrInfos = attrChangesMap.get(vertexOrEdge);
            if (attrInfos) {
              updatedAttributesForSelected.data[vertexOrEdge].attributes.forEach(attr => {
                // If expression includes removed or changed attribtues, remove it.
                if (
                  !attrInfos.newAttrs[attr.name] &&
                  !attrInfos.changedAttrs[attr.name]
                ) {
                  attr.checked = savedData[vertexOrEdge].attributes
                    .find(attribute => attribute.name === attr.name).checked;
                }
              });
            }
          }
        });
        graphChartConfig.attributesForSelected = updatedAttributesForSelected;

        const updatedVisualizationConfigOfAllTypes = new Map<string, VisualizationConfig>();
        updatedAttributesForSelected.names.forEach(vertexOrEdge => {
          const visualConfigs: VisualizationConfig = graphChartConfig.visualizationConfigOfAllTypes[vertexOrEdge];
          if (!visualConfigs) {
            updatedVisualizationConfigOfAllTypes.set(
              vertexOrEdge, cloneDeep(this.visualizationConfigOfAllTypes.get(vertexOrEdge)));
          } else {
            const colorConfigs: { colorValue: string, filter: BaseCondition }[] = [];
            const attrInfos = attrChangesMap.get(vertexOrEdge);

            // Modify color configs.
            visualConfigs.color.forEach(colorConfig => {
              const config = <ConditionJson>colorConfig.filter;
              if (
                config.type === 'NullCondition' ||
                !this.hasChangedAttribute(config, attrInfos)
              ) {
                colorConfigs.push({
                  colorValue: colorConfig.colorValue,
                  filter: <BaseCondition>parseExprJson(config, schema)
                });
              }
            });

            // Modify size config.
            let sizeConfig = <ExpressionJson>visualConfigs.size;
            if (sizeConfig.type !== 'NullExpression') {
              if (this.hasChangedAttribute(sizeConfig, attrInfos)) {
                sizeConfig = {
                  type: 'NullExpression',
                  operands: []
                };
              }
            }
            updatedVisualizationConfigOfAllTypes.set(vertexOrEdge, {
              color: colorConfigs,
              size: <BaseExpression>parseExprJson(sizeConfig, schema)
            });
          }
        });
        // Stored infos are in JSON format.
        graphChartConfig.visualizationConfigOfAllTypes = updatedVisualizationConfigOfAllTypes;
      } else {
        // Configs were stored in JSON format, convert them to Map then apply.
        if (graphChartConfig.visualizationConfigOfAllTypes) {
          Object.values(graphChartConfig.visualizationConfigOfAllTypes).forEach(config => {
            config['color'] = config['color'].map(colorConfig => ({
              colorValue: colorConfig.colorValue,
              filter: parseExprJson(colorConfig.filter, schema)
            }));
            config['size'] = parseExprJson(config['size'], schema);
          });
          graphChartConfig.visualizationConfigOfAllTypes =
            HelperFunctions.convertObjectToMap(graphChartConfig.visualizationConfigOfAllTypes);
        }
      }
      this.updateSettings(graphChartConfig);
    } else {
      this.getAttributesList();
      this.showAttributesWhenMouseHover = defaultShowAttributesWhenMouseHover;
      this.labelFontSize = defaultLabelSize;
      this.numberFormatting = cloneDeep(HelperFunctions.defaultNumberFormatting);
      graphChartConfig = {
        attributesForSelected: this.attributesForSelected,
        visualizationConfigOfAllTypes: this.visualizationConfigOfAllTypes,
        numberFormatting: this.numberFormatting,
        showAttributesWhenMouseHover: this.showAttributesWhenMouseHover,
        labelFontSize: this.labelFontSize
      };
    }
    this.saveSettingsToCacheService(graphChartConfig);
  }

  /**
   * Modify Graph Explorer page configurations based on schema changes.
   *
   * @private
   * @param {*} graphExplorerConfig
   * @param {GSQLGraphJson} schema
   * @param {Map<string, AttributeRelatedChanges>} attrChangesMap
   * @returns {GraphExplorerConfig}
   * @memberof VisualEditorComponent
   */
  private updateGraphExplorerConfig(
    graphExplorerConfig: any,
    schema: GSQLGraphJson,
    attrChangesMap: Map<string, AttributeRelatedChanges>
  ): GraphExplorerConfig | OneStepExpansionConfigJSON {
    const updatedGraphExplorerConfig: GraphExplorerConfig | OneStepExpansionConfigJSON = {};
    Object.keys(graphExplorerConfig).forEach(configKey => {
      switch (configKey) {
        case GraphExplorerLeftPanelConfig.VertexTypeFilter:
        case GraphExplorerLeftPanelConfig.EdgeTypeFilter:
          const prevTypeFilter = graphExplorerConfig[configKey];
          const updatedTypeFilter: { [name: string]: boolean } = {};
          const curTypes = configKey === GraphExplorerLeftPanelConfig.VertexTypeFilter
            ? schema.VertexTypes.map(vt => vt.Name)
            : schema.EdgeTypes.map(et => et.Name);
          const prevTypes = Object.keys(prevTypeFilter);

          curTypes.forEach(typeName => {
            // Apply filter for types that are not removed.
            if (prevTypes.includes(typeName)) {
              updatedTypeFilter[typeName] = prevTypeFilter[typeName];
            } else {
              // Extend type filter with newly added types.
              updatedTypeFilter[typeName] = true;
            }
          });
          updatedGraphExplorerConfig[configKey] = updatedTypeFilter;
          break;
        case GraphExplorerLeftPanelConfig.SearchVertexContent:
        case GraphExplorerLeftPanelConfig.StartingVertexContent:
        case GraphExplorerLeftPanelConfig.DestinationVertexContent:
          const vertexType = graphExplorerConfig[configKey].type;
          if (schema.VertexTypes.map(vt => vt.Name).includes(vertexType)) {
            updatedGraphExplorerConfig[configKey] = graphExplorerConfig[configKey];
          }
          break;
        case GraphExplorerLeftPanelConfig.AttributeFilter:
          const curTypeNames =
            schema.VertexTypes.map(vt => vt.Name).concat(schema.EdgeTypes.map(et => et.Name));
          const prevAttrFilters = graphExplorerConfig[configKey];
          const updatedAttrFilter: { [name: string]: ConditionJson } = {};
          Object.keys(prevAttrFilters).filter(typeName => curTypeNames.includes(typeName))
            .forEach(typeName => {
              const attrChangesInfo = attrChangesMap.get(typeName);
              const prevAttrFilter = prevAttrFilters[typeName];
              if (!this.hasChangedAttribute(prevAttrFilter, attrChangesInfo)) {
                updatedAttrFilter[typeName] = prevAttrFilter;
              }
            });
          if (Object.keys(updatedAttrFilter).length > 0) {
            updatedGraphExplorerConfig[configKey] = updatedAttrFilter;
          }
          break;
        case GraphExplorerLeftPanelConfig.ExpansionStepsConfig:
          const updatedExpansionStepsConfig: OneStepExpansionConfigJSON[] = [];
          const expansionConfigs = graphExplorerConfig[configKey];
          expansionConfigs.forEach(oneStepExpansionConfig => {
            const updatedOneStep = <OneStepExpansionConfigJSON>this.updateGraphExplorerConfig(
              oneStepExpansionConfig,
              schema,
              attrChangesMap
            );
            updatedExpansionStepsConfig.push(updatedOneStep);
          });
          updatedGraphExplorerConfig[configKey] = updatedExpansionStepsConfig;
          break;
        default:
          updatedGraphExplorerConfig[configKey] = graphExplorerConfig[configKey];
          break;
      }
    });
    return updatedGraphExplorerConfig;
  }

  /**
   * Check if an expression contains modified attributes.
   *
   * @private
   * @param {string} vertexOrEdgeName
   * @param {string} expression
   * @param {AttributeRelatedChanges} attrInfos
   * @returns {boolean}
   * @memberof VisualEditorComponent
   */
  private hasChangedAttribute(
    expression: ConditionJson | ExpressionJson,
    attrInfos: AttributeRelatedChanges
  ): boolean {
    if (!attrInfos) {
      return false;
    }
    const removedAttrs = attrInfos.removedAttrs;
    const changedAttrs = attrInfos.changedAttrs;
    const queue: (ConditionJson | ExpressionJson | AtomJson)[] = [];
    queue.push(expression);
    while (queue.length > 0) {
      const expr = queue.shift();
      if (expr.type === 'AttrVariable') {
        const attrName = (<AtomJson>expr).value.toString().split('.')[1];
        if (removedAttrs.includes(attrName) || changedAttrs[attrName]) {
          return true;
        }
      } else if (expr['operands']) {
        expr['operands'].forEach(operand => queue.push(operand));
      }
    }
    return false;
  }

  /**
   * Get one step neighbors for a given vertex.
   *
   * @private
   * @param {GraphChartDataNode} vertex
   * @memberof VisualEditorComponent
   */
  private getOneStepNeighbors(vertex: GraphChartDataNode) {
    this.toggleLoading();

    const edgeTypes = [];
    this.logicService.schema.edgeTypes.forEach(edgeType => {
      edgeTypes.push(edgeType.name);
      if (edgeType.hasReverseEdge) {
        edgeTypes.push(edgeType.reverseEdge);
      }
    });

    this.cmptService
      .getOneVertexNeighbors(
        this.logicService.schema.name,
        vertex.exType,
        vertex.exID,
        oneStepNeighborLimit,
        this.logicService.getAllVertexTypes(),
        edgeTypes
      )
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe(
        response => {
          // Add data and focus on the neighbors
          this.addData(response, 'pta', true, vertex, undefined, undefined, false);
          timer(100) // magic number, need more research.
            .pipe(takeUntil(this.ngUnsubscribe))
            .subscribe(() => {
              const graphResult = this.getCurrentGraphResult();
              if ((graphResult.latestSelections.links.length + graphResult.latestSelections.nodes.length) <= 100) {
                this.graphRef?.current?.selectElements(graphResult.latestSelections);
              }
            });
          this.toggleLoading();
        },
        (err: HttpSourceErrorResponse | string) => {
          this.toggleLoading();
          if (typeof err === 'string') {
            this.handleError(err, 'Error');
            return;
          }
          this.handleError(err.error, err.statusText);
        }
      );
  }

  /**
   * Toggle the open saved exploration list mode by setting a flag.
   * Also change the open exploration list button color to accent or default depends on mode.
   *
   * @private
   * @memberof VisualEditorComponent
   */
  private toggleOpenExploreHistoryMode() {
    this.isViewingExploreHistory = !this.isViewingExploreHistory;

    if (this.isViewingExploreHistory) {
      this.toolbarConfig.top[ToolbarButtons.Open].color = 'accent';
    } else {
      this.toolbarConfig.top[ToolbarButtons.Open].color = '';
    }
  }

  /**
   * Toggle the save exploration mode by setting a flag.
   * Also change the save exploration button color to accent or default depends on mode.
   *
   * @private
   * @memberof VisualEditorComponent
   */
  private toggleSaveExplorationMode() {
    this.isSavingExploration = !this.isSavingExploration;

    if (this.isSavingExploration) {
      this.toolbarConfig.top[ToolbarButtons.Save].color = 'accent';
    } else {
      this.toolbarConfig.top[ToolbarButtons.Save].color = '';
    }
  }

  /**
   * Toggle the locating vertex mode by setting a flag.
   * Also change the locating vertex button color to accent or default depends on mode.
   *
   * @private
   * @memberof VisualEditorComponent
   */
  private toggleLocatingVertexMode() {
    this.isLocatingVertex = !this.isLocatingVertex; // turn locating vertex mode on or off.

    // Turn the locating vertex button focus on or off.
    if (this.isLocatingVertex) {
      this.toolbarConfig.top[ToolbarButtons.Search].color = 'accent';
    } else {
      this.toolbarConfig.top[ToolbarButtons.Search].color = '';
    }
  }

  /**
   * Toggle the add vertex mode by setting a flag.
   * Also change the add edge button color to accent or default depends on mode.
   *
   * @private
   * @memberof VisualEditorComponent
   */
  private toggleAddVertexMode() {
    this.isAddingVertex = !this.isAddingVertex; // turn add vertex mode on or off.

    // Turn the vertex button focus on or off.
    if (this.isAddingVertex) {
      this.toolbarConfig.top[ToolbarButtons.AddVertex].color = 'accent';
    } else {
      this.toolbarConfig.top[ToolbarButtons.AddVertex].color = '';
    }
  }

  /**
   * Toggle the add edge mode by setting a flag.
   * Also change the add edge button color to accent or default depends on mode.
   *
   * @private
   * @memberof VisualEditorComponent
   */
  private toggleAddEdgeMode() {
    this.isAddingEdge = !this.isAddingEdge; // turn add edge mode on or off.

    // Turn the edge button focus on or off.
    if (this.isAddingEdge) {
      this.toolbarConfig.top[ToolbarButtons.AddEdge].color = 'accent';

      // Save toolbar disabled status and disable action buttons except adding edge button
      this.saveToolbarDisabledStatus();
      this.toolbarConfig.top.forEach(button => (button.disabled = true));
      this.toolbarConfig.top[ToolbarButtons.AddEdge].disabled = false;
    } else {
      // Load toolbar disabled status
      this.loadToolbarDisabledStatus();
      this.toolbarConfig.top[ToolbarButtons.AddEdge].color = '';
      if (this.sourceVertex) {
        // Remove the add edge label on the source vertex if there is one.
        this.sourceVertex.others.items = [];
        this.graphRef?.current?.removeAllMessages(); // remove the label.
      }
      this.initializeEdgeAction(); // prepare for a new add edge action.
    }
  }

  /**
   * Handler for add edge action.
   * Set the source and target vertex accordingly and open popup if both are set.
   *
   * @private
   * @memberof VisualEditorComponent
   */
  private handleAddEdge() {
    if (this.isAddingEdge && this.selectedVertex) {
      if (!this.sourceVertex) {
        this.sourceVertex = this.selectedVertex;
        this.sourceVertex.others.items = this.cmptService.getAddEdgeLabel(); // set the label.
        if (this.cmptService.getAddEdgeLabel()?.[0]?.text) {
          this.graphRef?.current?.displayNodeMessage({
            node: {
              type: this.sourceVertex.exType,
              id: this.sourceVertex.exID,
            },
            message: this.cmptService.getAddEdgeLabel()[0].text,
            kind: KIND.positive,
          });
        }
      } else {
        this.targetVertex = this.selectedVertex;
        this.sourceVertex.others.items = []; // remove the label.
        this.graphRef?.current?.removeAllMessages(); // remove the label.

        // If the source and target vertex are selected, prepare the form and open the popup window.
        const { matchedEdgeTypes, shouldReverseEdge } = this.logicService.getMatchEdgeTypes(
          this.sourceVertex.exType,
          this.targetVertex.exType
        );
        this.edgeTypes = matchedEdgeTypes;
        if (shouldReverseEdge) {
          [this.sourceVertex, this.targetVertex] = [this.targetVertex, this.sourceVertex];
        }

        if (this.edgeTypes.length !== 0) {
          this.edge.type = this.edgeTypes[0]; // set the default edge type.
          this.onEdgeTypeChange(this.edge.type); // set the attributes.

          this.selectedEvent = 'addEdge';
          this.openPopupWindow();
          setTimeout(() => {
            // trigger change detection to ensure the popup opens with the correct values.
            this.cdr.detectChanges();
          });
        } else {
          this.initializeEdgeAction();
          this.handleError(
            'There is no edge type between the selected vertices.',
            'Warning'
          );
        }
      }
    }
  }

  /**
   * Toggle the updating settings mode by setting a flag.
   * Also change the add edge button color to accent or default depends on mode.
   *
   * @private
   * @memberof VisualEditorComponent
   */
  private toggleUpdateSettingsMode() {
    this.isUpdatingSettings = !this.isUpdatingSettings;

    // Turn the settings button focus on or off.
    if (this.isUpdatingSettings) {
      this.toolbarConfig.top[ToolbarButtons.Settings].color = 'accent';
    } else {
      this.toolbarConfig.top[ToolbarButtons.Settings].color = '';
    }
  }

  /**
   * Check for vertex/edge existence.
   *
   * @private
   * @param {(ExternalNode | ExternalLink)} element
   * @param {boolean} isVertex
   * @returns {Observable<boolean>}
   * @memberof VisualEditorComponent
   */
  private preCheckForSave(element: ExternalNode | ExternalLink, isVertex: boolean): Observable<boolean> {
    let obs: Observable<any>;
    const graphName = this.logicService.schema.name;
    if (isVertex) {
      obs = this.cmptService.getVertex(graphName, <ExternalNode>element);
    } else {
      const schemaEdge = this.logicService.getSchemaEdge(element.type);
      const edge = <ExternalLink>element;
      obs = (schemaEdge.discriminatorCount > 0) ?
        this.cmptService.getMultiEdge(graphName, edge, schemaEdge.compositeDiscriminator) :
        this.cmptService.getEdge(graphName, edge);
    }

    /**
     * FIXME: Fix the logic after server response is consistent.
     * The behavior of non-exist vertex/edge is inconsistent.
     * If a vertex does not exist, server will return an error.
     * If an edge does not exist, server will return empty result.
     */
    return Observable.create((observer: Observer<boolean>) => {
      obs
        .pipe(takeUntil(this.ngUnsubscribe))
        .subscribe(
          result => {
            if (result.length === 0) {
              // Empty result, edge does not exist.
              observer.next(true);
              observer.complete();
            } else {
              const data: DialogData = {
                title: 'Warning',
                messages: [
                  'The vertex/edge already exists.',
                  'Do you want to overwrite?'
                ],
                actions: [
                  { label: 'CANCEL', value: 0 },
                  { label: 'OVERWRITE', value: 1, color: 'warn' }
                ]
              };

              const dialogRef = this.dialog
                .open(DialogComponent, { data: data, autoFocus: false });

              dialogRef
                .keydownEvents()
                .pipe(takeUntil(this.ngUnsubscribe))
                .subscribe(event => {
                  if (event.key.toLowerCase() === 'enter') {
                    dialogRef.close(1);
                  }
                });

              dialogRef
                .afterClosed()
                .pipe(takeUntil(this.ngUnsubscribe))
                .subscribe(value => {
                  value === 1 ? observer.next(true) : observer.next(false);
                  observer.complete();
                });
            }
          },
          () => {
            // Error, vertex does not exist.
            observer.next(true);
            observer.complete();
          }
        );
    });
  }

  /**
   * Handler for edit attributes action.
   * Build a new vertex or edge form, and retrieve the vertex or edge data from the graph
   * to fill the form with its value.
   * The popup is not opened if there is nothing selected.
   *
   * @private
   * @memberof VisualEditorComponent
   */
  private handleEditAttributes() {
    if (this.selectedVertex) {
      this.initializeVertex();
      this.vertex = this.logicService.getVertex(
        this.selectedVertex.exType,
        this.selectedVertex.exID
      );
      if (this.vertex) {
        this.vertexTypes = [this.vertex.type];
        this.selectedVertexAttributes = [];
        const schemaVertex = this.logicService.getSchemaVertex(this.vertex.type);

        this.selectedVertexAttributes = this.cmptService.buildAttributeQuestions(schemaVertex.attributes, this.vertex);
        this.openPopupWindow();
      } else {
        this.handleError(
          'Cannot edit the vertex attributes as the GSQL query didn’t return them. ' +
          'Please fix the GSQL query to print this vertex, not just the edge with this vertex as endpoint.',
          'Warning');
      }
    } else if (this.selectedEdge) {
      this.initializeEdge();
      this.edge = this.logicService.getEdge(
        this.selectedEdge.exType,
        { type: this.selectedEdge.source.exType, id: this.selectedEdge.source.exID },
        { type: this.selectedEdge.target.exType, id: this.selectedEdge.target.exID },
        this.selectedEdge.attrs
      );

      if (this.edge) {
        if (!this.logicService.getSchemaEdge(this.edge.type)) {
          this.handleError('You cannot edit a reverse edge. Please edit its forward edge.');
          return;
        }
        this.edgeTypes = [this.edge.type];
        this.isDiscriminatorList = [];
        this.selectedEdgeAttributes = [];
        const schemaEdge = this.logicService.getSchemaEdge(this.edge.type);

        this.isDiscriminatorList = schemaEdge.attributes.map(attribute => attribute.isDiscriminator);
        this.selectedEdgeAttributes = this.cmptService.buildAttributeQuestions(schemaEdge.attributes, this.edge, true);
        this.openPopupWindow();
      }
    }
  }

  /**
   * Handler for show action.
   * Hide all unselected elements. If nothing is selected, don't do anything.
   *
   * @private
   * @memberof VisualEditorComponent
   */
  private handleShow() {
    const selection = this.getCurrentSelections();

    const vertices: ExternalNode[] = [];
    const edges: ExternalLink[] = [];

    selection.nodes.forEach(node => {
      vertices.push({
        type: node.exType,
        id: node.exID
      });
    });
    selection.links.forEach(link => {
      edges.push({
        type: link.exType,
        source: {
          type: link.source.exType,
          id: link.source.exID
        },
        target: {
          type: link.target.exType,
          id: link.target.exID
        },
        attrs: link.attrs,
        directed: link.directed,
      });
    });

    if (vertices.length + edges.length > 0) {
      // Retain selected elements.
      this.logicService.retain(vertices, edges);
      this.renderChart(this.logicService.getGraph());
    }
  }

  /**
   * Handler for hide action.
   * Hide the selected elements by actually removing them.
   *
   * @private
   * @memberof VisualEditorComponent
   */
  private handleHide() {
    const selection = this.getCurrentSelections();

    const vertices: ExternalNode[] = [];
    const edges: ExternalLink[] = [];

    selection.nodes?.forEach(node => {
      vertices.push({
        type: node.exType,
        id: node.exID
      });
    });
    selection.links?.forEach(link => {
      edges.push({
        type: link.exType,
        source: {
          type: link.source.exType,
          id: link.source.exID
        },
        target: {
          type: link.target.exType,
          id: link.target.exID
        },
        attrs: link.attrs,
        directed: link.directed,
      });
    });

    if (vertices.length + edges.length > 0) {
      // Remove selected elements.
      this.logicService.remove(vertices, edges);
      this.renderChart(this.logicService.getGraph());
      this.lockNodes();
    } else {
      // Remove all.
      this.logicService.removeAll();
      this.renderChart(this.logicService.getGraph());
    }

    // Emit a selection change event.
    this.selectionChange.next([[], []]);
  }

  /**
   * Handler for delete action.
   * Ask the the user for confirmation.
   * If aggre, call removal.
   *
   * @private
   * @memberof VisualEditorComponent
   */
  private handleDelete() {
    const selection = this.getCurrentSelections();

    if (selection.nodes.length + selection.links.length > 0) {
      const data: DialogData = {
        title: 'Warning',
        messages: [
          `${selection.nodes.length} selected vertices and ${selection.links
            .length} selected edges will be deleted from the database.`,
          'Deletion is irreversible.',
          'Are you sure you want to delete the selected vertices and edges from the database?'
        ],
        actions: [
          { label: 'CANCEL', value: 0 },
          { label: 'CONTINUE', value: 1, color: 'warn' }
        ]
      };

      const dialogRef = this.dialog
        .open(DialogComponent, { data: data, autoFocus: false });

      dialogRef
        .keydownEvents()
        .pipe(takeUntil(this.ngUnsubscribe))
        .subscribe(event => {
          if (event.key.toLowerCase() === 'enter') {
            dialogRef.close(1);
          }
        });

      dialogRef
        .afterClosed()
        .pipe(takeUntil(this.ngUnsubscribe))
        .subscribe(value => {
          if (value === 1) {
            this.deleteElement(selection);
          }
        });
    }
  }

  /**
   * Remove the selected elements from the visualization and also from the server.
   *
   * @param {{
   *  nodes: GraphChartDataNode[],
   *  links: GraphChartDataLink[]
   * }} selection
   * @private
   * @memberof VisualEditorComponent
   */
  private deleteElement(selection: { nodes: GraphChartDataNode[], links: GraphChartDataLink[] }) {
    const vertices: { type: string, id: string }[] = [];
    const edges: {
      type: string,
      source: {
        type: string,
        id: string
      },
      target: {
        type: string,
        id: string
      },
      attrs?: {
        [key: string]: string | number | boolean | Object
      }
    }[] = [];

    const vertexObs: Observable<any>[] = [];
    const edgeObs: Observable<any>[] = [];
    const graphName = this.logicService.schema.name;

    selection.nodes.forEach(node => {
      // Build the vertices for local removal.
      vertices.push({
        type: node.exType,
        id: node.exID
      });
      // Build the observables for server removal.
      vertexObs.push(
        this.cmptService.deleteVertex(
          graphName,
          {
            type: node.exType,
            id: node.exID
          }
        )
      );
    });
    selection.links.forEach(link => {
      const edge = this.logicService.getEdge(
        link.exType,
        { type: link.source.exType, id: link.source.exID },
        { type: link.target.exType, id: link.target.exID },
        link.attrs
      );
      // Build the edges for local removal.
      edges.push(edge);
      // Build the observables for server removal.
      const schemaEdge = this.logicService.getSchemaEdge(link.exType, true);
      (schemaEdge.discriminatorCount > 0) ?
        edgeObs.push(this.cmptService.deleteMultiEdge(graphName, edge, schemaEdge.compositeDiscriminator)) :
        edgeObs.push(this.cmptService.deleteEdge(this.logicService.schema.name, edge));
    });

    this.toggleLoading();

    // Delete edges first then delete vertices.
    concat(
      forkJoin(edgeObs),
      forkJoin(vertexObs)
    )
      .pipe(
        takeUntil(this.ngUnsubscribe),
        takeLast(1)
      )
      .subscribe(
        () => {
          // If any edge has a reverse edge, also remove it.
          edges.forEach(edge => {
            if (this.logicService.getSchemaEdge(edge.type)) {
              // Add reverse edge.
              const edgeType = this.logicService.getSchemaEdge(edge.type);
              if (edgeType.hasReverseEdge) {
                const reverseEdge = cloneDeep(edge);
                reverseEdge.type = edgeType.reverseEdge;
                [reverseEdge.source, reverseEdge.target] = [reverseEdge.target, reverseEdge.source];
                edges.push(reverseEdge);
              }
            } else {
              // Add forward edge.
              const forwardEdge = cloneDeep(edge);
              forwardEdge.type = this.schemaReverseEdgeMap.get(edge.type);
              [forwardEdge.source, forwardEdge.target] = [forwardEdge.target, forwardEdge.source];
              edges.push(forwardEdge);
            }
          });
          // Do local removal after server removal.
          this.logicService.remove(vertices, edges);
          this.renderChart(this.logicService.getGraph());
          this.lockNodes();
          this.changeButtonDisableStatus('editAttributes', true);
          this.changeButtonDisableStatus('delete', true);
          this.toggleLoading();
        },
        (err: HttpSourceErrorResponse | string) => {
          this.toggleLoading();
          this.changeButtonDisableStatus('editAttributes', true);
          this.changeButtonDisableStatus('delete', true);
          if (typeof err === 'string') {
            this.handleError(err, 'Error');
            return;
          }
          this.handleError(err.error, err.statusText);
        }
      );
  }

  /**
   * Generate attributesForSelected from schema once.
   *
   * @memberOf VisualEditorComponent
   */
  private getAttributesList() {

    const vertices: VertexOrEdgeAttributesWithCheckedInfo[] = this.logicService.schema.vertexTypes.map(vertex => {
      const tmp: VertexOrEdgeAttributesWithCheckedInfo = {
        name: vertex.name,
        type: 'vertex',
        attributes: [
          { name: 'vertex type', checked: false },
          { name: 'primary id', checked: true }
        ].concat(vertex.attributes.map(attribute => {
          return { name: attribute.name, checked: false };
        })),
      };
      return tmp;
    });

    const edges: VertexOrEdgeAttributesWithCheckedInfo[] = this.logicService.schema.edgeTypes.map(edge => {
      const tmp: VertexOrEdgeAttributesWithCheckedInfo = {
        name: edge.name,
        type: 'edge',
        attributes: [{ name: 'edge type', checked: true }].concat(edge.attributes.map(attribute => {
          return { name: attribute.name, checked: false };
        })),
      };
      return tmp;
    });

    const data: {
      [name: string]: VertexOrEdgeAttributesWithCheckedInfo
    } = {};

    vertices.forEach(vertex => {
      data[vertex.name] = vertex;
    });
    edges.forEach(edge => {
      data[edge.name] = edge;
    });

    this.attributesForSelected = {
      names: vertices.map(vertex => vertex.name).concat(edges.map(edge => edge.name)),
      data: data
    };

    // Initialize the Visualization Config Map.
    const exprFactory = FactoryProducer.getFactory(ExpressionFactory);
    const nullExpr = exprFactory.getExpression(exprDict['NullExpression'], []);

    this.visualizationConfigOfAllTypes = new Map<string, VisualizationConfig>();
    this.attributesForSelected.names.forEach(name => this.visualizationConfigOfAllTypes.set(name, {
      color: [],
      size: exprFactory.getExpression(
        exprDict['NullExpression'],
        [nullExpr, nullExpr]
      )
    }));
  }

  /**
   ** Select and adjust focus for the given elements in the new graph
   *
   * @private
   * @param {{
   *     nodes: GraphChartDataNode[];
   *     links: GraphChartDataLink[];
   *   }} elements
   * @param {boolean} [noFocus]
   * @memberof VisualEditorComponent
   */
  private focusElementsInNewGraph(elements: {
    nodes: GraphChartDataNode[];
    links: GraphChartDataLink[];
  }, noFocus?: boolean) {
    // Need to make the operations asynchronous in order to run after chart render.
    timer(100)
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe(() => {
        const graph: ExternalGraph = {
          nodes: elements.nodes.map(node => ({
            id: node.exID,
            type: node.exType,
          })),
          links: elements.links.map(link => ({
            type: link.exType,
            source: {
              type: link.source.exType,
              id: link.source.exID,
            },
            target: {
              type: link.target.exType,
              id: link.target.exID,
            },
            attrs: link.attrs,
            directed: link.directed,
          })),
        };
        if (!noFocus) {
          this.centerGraph(graph);
        }
        this.graphRef?.current?.unselectElements();
        if ((graph.links.length + graph.nodes.length) <= 100) {
          this.graphRef?.current?.selectElements(graph);
        }
      });
  }

  /**
   * Open the popup window.
   *
   * @private
   * @memberof SchemaDesignerComponent
   */
  private openPopupWindow() {
    this.popupWindowRef = this.dialog.open(this.popupWindow, this.dialogConfig);

    this.popupWindowRef
      .keydownEvents()
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe(event => {
        if (event.key.toLowerCase() === 'enter') {
          switch (this.selectedEvent) {
            case EventKey.Open:
              this.applyExploration();
              break;
            case EventKey.Save:
              this.saveExploration();
              break;
            case EventKey.Search:
              this.searchVertex();
              break;
            case EventKey.AddVertex:
            case EventKey.AddEdge:
              this.addElement();
              break;
            case EventKey.EditAttributes:
              this.updateElement();
              break;
            case EventKey.Settings:
              this.updateSettings();
              break;
            default:
              break;
          }
        }
      });

    this.popupWindowRef
      .afterClosed()
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe(() => this.toggleButtonStatus());
  }

  showingInfo(): boolean {
    return this.selectedEvent === EventKey.VisInfo;
  }

  /**
   * Close the popup window.
   *
   * @private
   * @memberof VisualEditorComponent
   */
  private closePopupWindow() {
    if (this.popupWindowRef) {
      this.popupWindowRef.close();
    }
  }

  toggleButtonStatus() {
    if (this.isLocatingVertex) {
      this.toggleLocatingVertexMode();
    }
    if (this.isAddingVertex) {
      this.toggleAddVertexMode();
    }
    if (this.isAddingEdge) {
      this.toggleAddEdgeMode();
    }
    if (this.isUpdatingSettings) {
      this.toggleUpdateSettingsMode();
    }
    if (this.isViewingExploreHistory) {
      this.toggleOpenExploreHistoryMode();
    }
    if (this.isSavingExploration) {
      this.toggleSaveExplorationMode();
    }
  }

  /**
   * Toggle loading animation.
   * At the same time, toggle disable all currently displayed buttons.
   *
   * @private
   * @memberof VisualEditorComponent
   */
  private toggleLoading() {
    this.isLoading = !this.isLoading;
    this.loading.next(this.isLoading);
  }

  /**
   * Show the user the error through dialog.
   *
   * @private
   * @param {string} message
   * @param {string} [title]
   * @memberof VisualEditorComponent
   */
  private handleError(message: string, title?: string) {
    const data: DialogData = {
      title: title ? title : 'Error',
      messages: [message]
    };
    this.dialog.open(DialogComponent, { data: data });
  }

  /**
   * Render the graph.
   *
   * @private
   * @param {{ graph: ExternalGraph; diff: number }} data
   * @param {GraphChartDataNode} [fromVertex]
   * @param {boolean} [reloadPosition]
   * @memberof VisualEditorComponent
   */
  private renderChart(
    data: { graph: ExternalGraph; diff: number },
    fromVertex?: GraphChartDataNode,
    reloadPosition?: boolean
  ) {
    this.schema = cloneDeep(this.logicService.schema);
    data.graph?.links.forEach(link => {
      link['discriminator'] = this.schema.getEdge(link.type)?.compositeDiscriminator
        ?.map(d => link.attrs[d]).join(':') || '';
    });
    this.graphData = cloneDeep(data.graph);
    this.enableDrawMode(this.editButtonChecked);
    timer(100)
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe(() => {
        this.showStyles();
        this.showLabels();
      });
  }

  /**
   * Show customized styles in graph.
   *
   * @private
   * @memberof VisualEditorComponent
   */
  private showStyles() {
    const nodesStyle: { node: ExternalNode, style: Record<string, string | number> }[] = [];
    const linksStyle: { link: ExternalLink, style: Record<string, string | number> }[] = [];

    // apply the visualization config to node and link
    this.getNodes().forEach(node => {
      const schemaVertex = this.logicService.getSchemaVertex(node.exType);
      const nodeStyle: { node: ExternalNode, style: Record<string, string | number> } = {
        node: {
          type: node.exType,
          id: node.exID,
        },
        style: {
          width: '80px',
          height: '80px',
          'background-color': schemaVertex?.style?.fillColor,
        },
      };
      let nodeFillColor: string;
      let nodeRadius = 0;
      const oneVisualizationConfig = this.visualizationConfigOfAllTypes.get(node.exType);
      if (oneVisualizationConfig) {
        oneVisualizationConfig.color.forEach(oneColor => {
          if (schemaVertex.primaryId) {
            Object.assign(node.attrs, {
              [schemaVertex.primaryId.name]: node.exID
            });
          }
          const colorEvalResult = (<BaseCondition>oneColor.filter).evaluate(node);
          if (colorEvalResult.applicable && colorEvalResult.value) {
            nodeFillColor = oneColor.colorValue;
            nodeStyle.style['background-color'] = nodeFillColor;
          }
        });

        if (oneVisualizationConfig.size !== undefined) {
          const sizeEvalResult = (<BaseExpression>oneVisualizationConfig.size).evaluate(node);
          if (sizeEvalResult.applicable) {
            nodeRadius = Number(sizeEvalResult.value);
            // Clamp the value of node's radius between 20 and 200.
            nodeRadius = Math.max(20, Math.min(200, nodeRadius));
            nodeStyle.style['width'] = `${nodeRadius * 2}px`;
            nodeStyle.style['height'] = `${nodeRadius * 2}px`;
          }
        }
        nodesStyle.push(nodeStyle);
      }
    });

    this.getLinks().forEach(link => {
      const linkStyle: { link: ExternalLink, style: Record<string, string | number> } = {
        link: {
          type: link.exType,
          source: {
            type: link.source.exType,
            id: link.source.exID
          },
          target: {
            type: link.target.exType,
            id: link.target.exID
          },
          attrs: link.attrs,
          directed: link.directed,
        },
        style: {
          width: 2,
        },
      };
      // First, always set edge color based on schema style design.
      // If the edge is a reverse edge, set it as dash line.
      const schemaEdge = this.logicService.getSchemaEdge(link.exType);
      if (schemaEdge) {
        if (schemaEdge.style) {
          linkStyle.style['line-color'] = schemaEdge.style.fillColor;
          linkStyle.style['target-arrow-color'] = schemaEdge.style.fillColor;
        }
      } else {
        const reverseSchemaEdge = this.logicService.getSchemaEdge(
          this.schemaReverseEdgeMap.get(link.exType)
        );
        const links = <GraphChartDataLink[]>this.getLinks();
        const reverseEdge = linksStyle.find(item => item.link.type === reverseSchemaEdge.name);

        if (reverseSchemaEdge.style) {
          // If there is a stored schema style, use it.
          linkStyle.style['line-color'] = reverseSchemaEdge.style.fillColor;
          linkStyle.style['target-arrow-color'] = reverseSchemaEdge.style.fillColor;
        } else if (reverseEdge) {
          // Otherwise, use the reverse edge color.
          linkStyle.style['line-color'] = reverseEdge.style['line-color'];
          linkStyle.style['target-arrow-color'] = reverseEdge.style['target-arrow-color'];
        }
        linkStyle.style['line-style'] = 'dashed';
      }

      let linkFillColor: string;
      let linkThickness: number;
      const oneVisualizationConfig =
        this.visualizationConfigOfAllTypes.get(link.exType) ||
        this.visualizationConfigOfAllTypes.get(this.schemaReverseEdgeMap.get(link.exType));
      if (oneVisualizationConfig) {
        oneVisualizationConfig.color.forEach(oneColor => {
          const evalResult = (<BaseCondition>oneColor.filter).evaluate(link);
          if (evalResult.applicable && evalResult.value) {
            linkFillColor = oneColor.colorValue;
            linkStyle.style['line-color'] = linkFillColor;
            linkStyle.style['target-arrow-color'] = linkFillColor;
          }
        });

        if (oneVisualizationConfig.size) {
          const sizeEvalResult = (<BaseExpression>oneVisualizationConfig.size).evaluate(link);
          if (sizeEvalResult.applicable && sizeEvalResult.value) {
            linkThickness = Number(sizeEvalResult.value);
            // Clamp the value of link's radius between 1 and 30.
            linkThickness = Math.max(1, Math.min(30, linkThickness));
            linkStyle.style['width'] = linkThickness;
          }
        }
        linksStyle.push(linkStyle);
      }
    });

    // Set style to the graph widget
    this.graphRef?.current?.setNodesStyle(nodesStyle);
    this.graphRef?.current?.setLinksStyle(linksStyle);
  }

  /**
   * Get node label style in graph widget.
   *
   * @private
   * @param {string} type
   * @param {string[]} attributes
   * @memberof VisualEditorComponent
   */
  private getNodeLabelStyle(type: string, attributes: string[]) {
    return {
      type,
      style: {
        label: (element: NodeSingular) => {
          if (!attributes || attributes.length === 0) {
            return type;
          }
          let label = '';
          if (attributes.length === 1 && attributes[0] === 'primary id') {
            label = element.data('nodeID');
          } else {
            const graphName = this.cacheService.getItem('GraphName');
            const settings = this.cacheService.getItem(
              `VisualEditorSettings_${graphName}`
            );
            attributes.forEach((attr, index) => {
              if (attr === 'primary id') {
                label += `Vertex id: ${element.data('nodeID')}`;
              } else if (attr === 'vertex type') {
                label += `Vertex type: ${element.data('type')}`;
              } else {
                let value = getElementAttribute(element, attr);
                if (typeof value === 'number') {
                  value = HelperFunctions.numberFormattingForAttrs(value, settings);
                }
                label += `${attr}: ${value}`;
              }
              if (index !== attributes.length - 1) {
                label += '\n';
              }
            });
          }
          return label;
        },
        'text-wrap': 'wrap',
        'font-size': `${this.labelFontSize}px`,
        'text-opacity': !attributes || attributes.length === 0 ? 0 : 1,
      }
    };
  }

  /**
   * Get link label style in graph widget.
   *
   * @private
   * @param {string} type
   * @param {string[]} attributes
   * @memberof VisualEditorComponent
   */
  private getLinkLabelStyle(type: string, attributes: string[]) {
    return {
      type,
      style: {
        label: (element: EdgeSingular) => {
          if (!attributes || attributes.length === 0) {
            return type;
          }
          let label = '';
          if (attributes.length === 1 && attributes[0] === 'edge type') {
            label = element.data('type') ? `\u2060${element.data('type')}\n\n\u2060` : '';
          } else {
            const graphName = this.cacheService.getItem('GraphName');
            const settings = this.cacheService.getItem(
              `VisualEditorSettings_${graphName}`
            );
            attributes.forEach((attr, index) => {
              if (attr === 'edge type') {
                label += `Edge type: ${element.data('type')}`;
              } else {
                let value = getElementAttribute(element, attr);
                if (typeof value === 'number') {
                  value = HelperFunctions.numberFormattingForAttrs(value, settings);
                }
                label += `${attr}: ${value}`;
              }
              if (index !== attributes.length - 1) {
                label += '\n';
              }
            });
          }
          return label;
        },
        'text-wrap': 'wrap',
        'font-size': `${this.labelFontSize}px`,
        'text-opacity': !attributes || attributes.length === 0 ? 0 : 1,
      }
    };
  }

  /**
   * Show labels in graph.
   *
   * @private
   * @memberof VisualEditorComponent
   */
  private showLabels() {
    const nodesLabelStyle: { type: string, style: Record<string, string | number | Function> }[] = [];
    const linksLabelStyle: { type: string, style: Record<string, string | number | Function> }[] = [];

    for (const name of this.attributesForSelected.names) {
      const attributes: string[] = this.attributesForSelected.data[name].attributes.filter(ele => ele.checked).map(ele => ele.name);
      if (this.attributesForSelected.data[name].type === 'vertex') {
          nodesLabelStyle.push(this.getNodeLabelStyle(name, attributes));
      } else {
        // Reverse edge should follow forward edge settings.
        const edgeType = this.logicService.getSchemaEdge(name);
        if (edgeType && edgeType.hasReverseEdge) {
          linksLabelStyle.push(this.getLinkLabelStyle(edgeType.reverseEdge, attributes));
        }
        linksLabelStyle.push(this.getLinkLabelStyle(name, attributes));
      }
    }
    this.graphRef?.current?.setNodesStyleByTypes(nodesLabelStyle);
    this.graphRef?.current?.setLinksStyleByTypes(linksLabelStyle);
  }

  /**
   * Change one button's disabled status.
   * If in addingEdge mode, don't change button disable status. Every button should be disabled.
   *
   * @private
   * @param {string} key
   * @param {boolean} disabled
   * @memberof VisualEditorComponent
   */
  private changeButtonDisableStatus(key: string, disabled: boolean) {
    if (!this.isAddingEdge) {
      (<ButtonBase>this.toolbarConfig.top.find(
        button => button.key === key
      )).disabled = disabled;
    }
  }

  /**
   * Create a new vertex object.
   *
   * @private
   * @memberof VisualEditorComponent
   */
  private initializeVertex() {
    this.vertex = {
      type: '',
      id: '',
      attrs: {}
    };
  }

  /**
   * Create a new edge object.
   *
   * @private
   * @memberof VisualEditorComponent
   */
  private initializeEdge() {
    this.edge = {
      type: '',
      source: {
        type: '',
        id: ''
      },
      target: {
        type: '',
        id: ''
      },
      attrs: {}
    };
  }

  /**
   * Clean the source and target vertex object.
   *
   * @private
   * @memberof VisualEditorComponent
   */
  private initializeEdgeAction() {
    this.sourceVertex = undefined;
    this.targetVertex = undefined;
  }

  /**
   * Save the current toolbar disabled status.
   *
   * @private
   * @memberof VisualEditorComponent
   */
  private saveToolbarDisabledStatus() {
    this.toolbarDisabledStatus = this.toolbarConfig.top.map(
      button => button.disabled
    );
  }

  /**
   * Load the current toolbar disabled status.
   *
   * @private
   * @memberof VisualEditorComponent
   */
  private loadToolbarDisabledStatus() {
    this.toolbarConfig.top.forEach((button, i) => {
      button.disabled = this.toolbarDisabledStatus[i];
    });
  }

  /**
   * Get current graph nodes.
   *
   * @private
   * @returns {GraphChartDataNode[]}
   * @memberof VisualEditorComponent
   */
  private getNodes(): GraphChartDataNode[] {
    return this.logicService.getGraph().graph.nodes?.map(node => {
      return createGraphChartDataNode(node);
    }) || [];
  }

  /**
   * Get current graph links.
   *
   * @private
   * @returns {GraphChartDataLink[]}
   * @memberof VisualEditorComponent
   */
  private getLinks(): GraphChartDataLink[] {
    return this.logicService.getGraph().graph.links?.map(link => {
      return <GraphChartDataLink><unknown>createGraphChartDataLink(link);
    }) || [];
  }

  /**
   * Zoom-in or zoom-out the graph exploration.
   *
   * @param {string} inOrOut
   * @memberof VisualEditorComponent
   */
  zoom(inOrOut: string) {
    switch (inOrOut) {
      case 'in':
        break;
      case 'out':
        break;
    }
  }

  /**
   * Center the entire graph in the new graph.
   *
   * @param {ExternalGraph} graph
   * @memberof VisualEditorComponent
   */
  centerGraph(graph?: ExternalGraph) {
    timer(800)
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe(() => {
        this.graphRef?.current?.centerGraph(graph);
      });
  }

  /**
   * Enable the draw mode in the new graph.
   *
   * @private
   * @memberof VisualEditorComponent
   */
  private enableDrawMode(enable = true) {
    timer(100)
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe(() => {
        this.graphRef.current?.setDrawMode(enable);
      });
  }

  private lockNodes() {
    const lockNodes = this.logicService.getGraph().graph.nodes;
    this.graphRef?.current?.lockNodes(lockNodes);
    timer(100)
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe(() => {
        this.graphRef?.current?.unlockNodes();
      });
  }
}
