import { TestBed, inject } from '@angular/core/testing';
import { HttpParams } from '@angular/common/http';

import { Logger } from '@app/core/services';
import {
  InputQuestion, InputVertexQuestion,
  ListQuestion, SelectQuestion
} from '@tigergraph/tools-models/question';

import { QuestionFormBuilderService } from './question-form-builder.service';

describe('QuestionFormBuilderService', () => {
  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        {
          provide: Logger,
          useFactory: () => ({
            log: () => { }
          })
        },
        QuestionFormBuilderService
      ]
    });
  });

  it('should be created', inject([QuestionFormBuilderService], (service: QuestionFormBuilderService) => {
    expect(service).toBeTruthy();
  }));

  describe('should return new question when requested', () => {
    let queryMeta;
    const vertexTypes = ['vType1', 'vType2'];

    beforeEach(() => {
      queryMeta = {
        params: []
      };
    });

    it('for undefined query meta', inject([QuestionFormBuilderService], (service: QuestionFormBuilderService) => {
      spyOn(<any>service, 'buildOptions');
      service.buildQuestions(undefined, [], true);
      expect((<any>service).buildOptions).not.toHaveBeenCalled();
    }));

    it('for schema not ready', inject([QuestionFormBuilderService], (service: QuestionFormBuilderService) => {
      spyOn(<any>service, 'buildOptions');
      service.buildQuestions(queryMeta, [], false);
      expect((<any>service).buildOptions).not.toHaveBeenCalled();
    }));

    it('for string type question', inject([QuestionFormBuilderService], (service: QuestionFormBuilderService) => {
      queryMeta.params.push({
        paramName: 'a',
        paramType: {
          type: 'STRING'
        }
      });

      const questions = service.buildQuestions(queryMeta, vertexTypes, true);
      expect(questions[0] instanceof InputQuestion).toBeTruthy();
      expect((<InputQuestion>questions[0]).value).toBe('');
      expect((<InputQuestion>questions[0]).variableType).toBe('string');
    }));

    it('for boolean type question', inject([QuestionFormBuilderService], (service: QuestionFormBuilderService) => {
      queryMeta.params.push({
        paramName: 'a',
        paramType: {
          type: 'BOOL'
        }
      });

      const questions = service.buildQuestions(queryMeta, vertexTypes, true);
      expect(questions[0] instanceof SelectQuestion).toBeTruthy();
      expect((<SelectQuestion>questions[0]).options).toEqual([
        { key: 'true', value: 'true' },
        { key: 'false', value: 'false' }
      ]);
      expect((<SelectQuestion>questions[0]).value).toBe('false');
      expect((<SelectQuestion>questions[0]).variableType).toBe('boolean');
    }));

    it('for integer type question', inject([QuestionFormBuilderService], (service: QuestionFormBuilderService) => {
      // Signed integer.
      queryMeta.params.push({
        paramName: 'a',
        paramType: {
          type: 'INT'
        }
      });
      // Unsigned integer.
      queryMeta.params.push({
        paramName: 'b',
        paramType: {
          type: 'UINT'
        }
      });

      const questions = service.buildQuestions(queryMeta, vertexTypes, true);
      expect(questions.length).toBe(2);
      expect(questions[0] instanceof InputQuestion).toBeTruthy();
      expect(questions[1] instanceof InputQuestion).toBeTruthy();

      expect((<InputQuestion>questions[0]).value).toBe(0);
      expect((<InputQuestion>questions[0]).variableType).toBe('int number');
      expect((<InputQuestion>questions[0]).numberType).toBe('integer');

      expect((<InputQuestion>questions[1]).value).toBe(0);
      expect((<InputQuestion>questions[1]).variableType).toBe('uint number');
      expect((<InputQuestion>questions[1]).numberType).toBe('integer');
      expect((<InputQuestion>questions[1]).min).toBe(0);
    }));

    it('for real type question', inject([QuestionFormBuilderService], (service: QuestionFormBuilderService) => {
      queryMeta.params.push({
        paramName: 'a',
        paramType: {
          type: 'FLOAT'
        }
      });

      const questions = service.buildQuestions(queryMeta, vertexTypes, true);
      expect(questions[0] instanceof InputQuestion).toBeTruthy();
      expect((<InputQuestion>questions[0]).value).toBe(0);
      expect((<InputQuestion>questions[0]).variableType).toBe('float number');
      expect((<InputQuestion>questions[0]).numberType).toBe('real');
    }));

    it('for datetime type question',
      inject([QuestionFormBuilderService, Logger], (service: QuestionFormBuilderService, logger: Logger) => {
        spyOn(logger, 'log');
        queryMeta.params.push({
          paramName: 'a',
          paramType: {
            type: 'DATETIME'
          }
        });

        const questions = service.buildQuestions(queryMeta, vertexTypes, true);
        expect(questions.length).toBe(1);
      }));

    it('for vertex type question', inject([QuestionFormBuilderService], (service: QuestionFormBuilderService) => {
      // Non-fixed vertex type.
      queryMeta.params.push({
        paramName: 'a',
        paramType: {
          type: 'VERTEX',
          vertexType: '*'
        }
      });
      // Fixed vertex type.
      queryMeta.params.push({
        paramName: 'b',
        paramType: {
          type: 'VERTEX',
          vertexType: 'vType2'
        }
      });

      const questions = service.buildQuestions(queryMeta, vertexTypes, true);
      expect(questions.length).toBe(2);
      expect(questions[0] instanceof InputVertexQuestion).toBeTruthy();
      expect(questions[1] instanceof InputVertexQuestion).toBeTruthy();

      expect((<InputVertexQuestion>questions[0]).value).toEqual({
        type: 'vType1',
        id: ''
      });
      expect((<InputVertexQuestion>questions[0]).variableType).toBe('vertex');
      expect((<InputVertexQuestion>questions[0]).fixedVertexType).toBeFalsy();

      expect((<InputVertexQuestion>questions[1]).value).toEqual({
        type: 'vType2',
        id: ''
      });
      expect((<InputVertexQuestion>questions[1]).variableType).toBe('vertex');
      expect((<InputVertexQuestion>questions[1]).fixedVertexType).toBeTruthy();
    }));

    it('for list type question', inject([QuestionFormBuilderService], (service: QuestionFormBuilderService) => {
      queryMeta.params.push({
        paramName: 'a',
        paramType: {
          type: 'LIST',
          elementType: {
            type: 'INT'
          }
        }
      });

      const questions = service.buildQuestions(queryMeta, vertexTypes, true);
      expect(questions[0] instanceof ListQuestion).toBeTruthy();
      expect((<ListQuestion<any>>questions[0]).templateOption.value).toBe(0);
      expect((<ListQuestion<any>>questions[0]).templateOption.variableType).toBe('int number');
      expect((<ListQuestion<any>>questions[0]).templateOption.numberType).toBe('integer');
      expect((<ListQuestion<any>>questions[0]).variableType).toBe('multiple int numbers');
    }));
  });

  describe('should return correct params when requested', () => {
    let queryMeta;
    let params;

    beforeEach(() => {
      queryMeta = {
        params: []
      };
      params = {};
    });

    it('for string or boolean type question', inject([QuestionFormBuilderService], (service: QuestionFormBuilderService) => {
      queryMeta.params.push({
        paramName: 'a',
        paramType: {
          type: 'STRING'
        }
      });
      params['a'] = 'abc';

      expect(service.buildParamsForInterpretedMode(queryMeta.params, params, true)).toEqual(new HttpParams().set('a', 'abc'));
      expect(service.buildParamsForInstalledMode(queryMeta.params, params, [])).toEqual({a: 'abc'});
    }));

    it('for number type question', inject([QuestionFormBuilderService], (service: QuestionFormBuilderService) => {
      // With input.
      queryMeta.params.push({
        paramName: 'a',
        paramType: {
          type: 'INT'
        }
      });
      params['a'] = '0';
      // Without input.
      queryMeta.params.push({
        paramName: 'b',
        paramType: {
          type: 'FLOAT'
        }
      });
      params['b'] = '';

      expect(service.buildParamsForInterpretedMode(queryMeta.params, params, true)).toEqual(new HttpParams().set('a', '0'));
      expect(service.buildParamsForInstalledMode(queryMeta.params, params, [])).toEqual({a: 0, b: 0});
    }));

    it('for datetime type question',
      inject([QuestionFormBuilderService, Logger], (service: QuestionFormBuilderService, logger: Logger) => {
        spyOn(logger, 'log');
        queryMeta.params.push({
          paramName: 'a',
          paramType: {
            type: 'DATETIME'
          }
        });
        params['a'] = '2019-01-10';

        expect(service.buildParamsForInterpretedMode(queryMeta.params, params, true)).toEqual(new HttpParams().set('a', '2019-01-10'));
        expect(service.buildParamsForInstalledMode(queryMeta.params, params, [])).toEqual({a: '2019-01-10'});
      }));

    describe('for vertex type question', () => {
      beforeEach(() => {
        // Non-fixed vertex type.
        queryMeta.params.push({
          paramName: 'a',
          paramType: {
            type: 'VERTEX',
            vertexType: '*'
          }
        });
        params['a'] = {
          type: 'vType1',
          id: 'vId1'
        };
        // Fixed vertex type.
        queryMeta.params.push({
          paramName: 'b',
          paramType: {
            type: 'VERTEX',
            vertexType: 'vType2'
          }
        });
        params['b'] = {
          type: 'vType2',
          id: 'vId2'
        };
      });

      it('with query run in compiled mode',
        inject([QuestionFormBuilderService], (service: QuestionFormBuilderService) => {
          const JSONParams = service.buildParamsForInstalledMode(queryMeta.params, params, []);
          expect(JSONParams).toEqual(
            {
              a: {
                type: 'vType1',
                id: 'vId1'
              },
              b: {
                type: 'vType2',
                id: 'vId2'
              }
            }
          );
        }));

      it('with query run in interpreted mode',
        inject([QuestionFormBuilderService], (service: QuestionFormBuilderService) => {
          const urlParams = service.buildParamsForInterpretedMode(queryMeta.params, params, true);
          expect(urlParams).toEqual(
            new HttpParams()
              .set('a[0]', 'vId1').set('a[0].type', 'vType1')
              .set('b[0]', 'vId2').set('b[0].type', 'vType2')
          );
        }));
    });

    describe('for list type question', () => {
      describe('with vertex value', () => {
        beforeEach(() => {
          // Non-fixed vertex type.
          queryMeta.params.push({
            paramName: 'a',
            paramType: {
              type: 'LIST',
              elementType: {
                type: 'VERTEX',
                vertexType: '*'
              }
            }
          });
          params['a'] = {
            '0': { type: 'vType1', id: 'vId1' },
            '1': { type: 'vType1', id: 'vId2' }
          };
          // Fixed vertex type.
          queryMeta.params.push({
            paramName: 'b',
            paramType: {
              type: 'LIST',
              elementType: {
                type: 'VERTEX',
                vertexType: 'vType2'
              }
            }
          });
          params['b'] = {
            '0': { type: 'vType2', id: 'vId1' },
            '1': { type: 'vType2', id: 'vId2' }
          };
        });

        it('and query run in compiled mode',
          inject([QuestionFormBuilderService], (service: QuestionFormBuilderService) => {
            const JSONparams = service.buildParamsForInstalledMode(queryMeta.params, params, []);
            expect(JSONparams).toEqual(
              {
                a: [
                  {
                    type: 'vType1',
                    id: 'vId1'
                  },
                  {
                    type: 'vType1',
                    id: 'vId2'
                  }
                ],
                b: [
                  {
                    type: 'vType2',
                    id: 'vId1'
                  },
                  {
                    type: 'vType2',
                    id: 'vId2'
                  }
                ]
              }
            );
          }));

        it('and query run in interpreted mode',
          inject([QuestionFormBuilderService], (service: QuestionFormBuilderService) => {
            const urlParams = service.buildParamsForInterpretedMode(queryMeta.params, params, true);
            expect(urlParams).toEqual(
              new HttpParams()
                .set('a[0]', 'vId1').set('a[0].type', 'vType1')
                .set('a[1]', 'vId2').set('a[1].type', 'vType1')
                .set('b[0]', 'vId1').set('b[0].type', 'vType2')
                .set('b[1]', 'vId2').set('b[1].type', 'vType2')
            );
          }));
      });

      it('with string or boolean values',
        inject([QuestionFormBuilderService], (service: QuestionFormBuilderService) => {
          queryMeta.params.push({
            paramName: 'a',
            paramType: {
              type: 'LIST',
              elementType: {
                type: 'STRING'
              }
            }
          });
          params['a'] = {
            '0': 'abc',
            '1': 'def'
          };

          const urlParams = service.buildParamsForInterpretedMode(queryMeta.params, params, true);
          expect(urlParams).toEqual(new HttpParams().append('a', 'abc').append('a', 'def'));
          expect(service.buildParamsForInstalledMode(queryMeta.params, params, [])).toEqual({a: ['abc', 'def']});
        }));

      it('with integer values',
        inject([QuestionFormBuilderService], (service: QuestionFormBuilderService) => {
          queryMeta.params.push({
            paramName: 'a',
            paramType: {
              type: 'LIST',
              elementType: {
                type: 'INT'
              }
            }
          });
          params['a'] = {'0': '0', '1': ''};

          const urlParams = service.buildParamsForInterpretedMode(queryMeta.params, params, true);
          expect(urlParams).toEqual(new HttpParams().append('a', '0'));
          expect(service.buildParamsForInstalledMode(queryMeta.params, params, [])).toEqual({a: [0, 0]});
        }));

      it('with datetime values',
        inject([QuestionFormBuilderService, Logger], (service: QuestionFormBuilderService, logger: Logger) => {
          spyOn(logger, 'log');
          queryMeta.params.push({
            paramName: 'a',
            paramType: {
              type: 'LIST',
              elementType: {
                type: 'DATETIME'
              }
            }
          });
          params['a'] = {'0': '2019-01-10'};

          const urlParams = service.buildParamsForInterpretedMode(queryMeta.params, params, true);
          expect(urlParams).toEqual(new HttpParams().append('a', '2019-01-10'));
          expect(service.buildParamsForInstalledMode(queryMeta.params, params, [])).toEqual({a: ['2019-01-10']});
        }));
    });
  });

  describe('should return vertex params input', () => {
    let queryMeta;
    let params;

    beforeEach(() => {
      queryMeta = {
        params: []
      };
      params = {};
    });

    it('for vertex type questions',
      inject([QuestionFormBuilderService], (service: QuestionFormBuilderService) => {
        queryMeta.params.push({
          paramName: 'a',
          paramType: {
            type: 'VERTEX',
            vertexType: 'vType1'
          }
        });
        params['a'] = {
          type: 'vType1',
          id: 'vId1'
        };

        const input = service.getVertexParamInputs(queryMeta, params);
        expect(input).toEqual([{ type: 'vType1', id: 'vId1' }]);
      }));

    it('for list of vertex type questions',
      inject([QuestionFormBuilderService], (service: QuestionFormBuilderService) => {
        queryMeta.params.push({
          paramName: 'a',
          paramType: {
            type: 'LIST',
            elementType: {
              type: 'VERTEX',
              vertexType: 'vType1'
            }
          }
        });
        params['a'] = [
          { type: 'vType1', id: 'vId1' },
          { type: 'vType1', id: 'vId2' }
        ];

        const input = service.getVertexParamInputs(queryMeta, params);
        expect(input).toEqual([
          { type: 'vType1', id: 'vId1' },
          { type: 'vType1', id: 'vId2' }
        ]);
      }));

    it('for list of other types questions',
      inject([QuestionFormBuilderService], (service: QuestionFormBuilderService) => {
        queryMeta.params.push({
          paramName: 'a',
          paramType: {
            type: 'LIST',
            elementType: {
              type: 'INT'
            }
          }
        });
        params['a'] = ['0', '1'];

        const input = service.getVertexParamInputs(queryMeta, params);
        expect(input).toEqual([]);
      }));
  });
});
