import { Injectable } from '@angular/core';
import { HttpParams } from '@angular/common/http';

import { Logger } from '@app/core/services';
import { VertexTypeAndId } from '@app/shared/components/vertex-type-and-id-input';
import {
  QueryMeta, QueryParam,
  QueryParamListType,
  QueryParamVertexType
} from '@tigergraph/tools-models/query';
import {
  InputQuestion, InputVertexQuestion,
  ListQuestion, QuestionBase, SelectQuestion
} from '@tigergraph/tools-models/question';

enum NumberTypes {
  INT = 'INT',
  UINT = 'UINT',
  FLOAT = 'FLOAT',
  DOUBLE = 'DOUBLE',
}

/*
  Types not supported yet: Composite key vertex, Edge, Map, JSON object.
*/
const SupportedQueryParamType = {
  ...NumberTypes,
  BOOL: 'BOOL',
  DATETIME: 'DATETIME',
  STRING: 'STRING',
  VERTEX: 'VERTEX',
  LIST: 'LIST'
};

@Injectable()
export class QuestionFormBuilderService {
  constructor(private logger: Logger) { }

  /**
   * Construct query parameters questions based on query meta.
   *
   * @param {QueryMeta} queryMeta
   * @param {string[]} vertexTypes
   * @returns {QuestionBase<any>[]}
   * @memberof QuestionFormBuilderService
   */
  buildQuestions(
    queryMeta: QueryMeta,
    vertexTypes: string[],
    schemaReady: boolean
  ): QuestionBase<any>[] {
    // Query meta is not ready or schema is not ready
    if (queryMeta === undefined || !schemaReady) {
      return [];
    }

    const questions: QuestionBase<any>[] = [];

    queryMeta.params.forEach(queryParam => {
      const options = this.buildOptions(queryParam, vertexTypes);
      switch (options.controlType) {
        case 'input': {
          questions.push(new InputQuestion(options));
          break;
        }
        case 'select': {
          questions.push(new SelectQuestion(options));
          break;
        }
        case 'vertex': {
          questions.push(new InputVertexQuestion(options));
          break;
        }
        case 'list': {
          questions.push(new ListQuestion(options));
          break;
        }
      }
    });

    return questions;
  }

  /**
   * Build the options for each question.
   *
   * @private
   * @param {QueryParam} queryParam
   * @param {string[]} vertexTypes
   * @returns {*}
   * @memberof QuestionFormBuilderService
   */
  private buildOptions(queryParam: QueryParam, vertexTypes: string[]): any {
    const options: any = {
      key: queryParam.paramName
    };

    switch (queryParam.paramType.type) {
      case 'STRING': {
        options.controlType = 'input';
        options.type = 'text';
        options.value = queryParam.paramDefaultValue ? queryParam.paramDefaultValue : '';
        options.label = 'string';
        options.variableType = 'string';
        break;
      }
      case 'BOOL': {
        options.controlType = 'select';
        options.options = [
          { key: 'true', value: 'true' },
          { key: 'false', value: 'false' }
        ];
        options.value = queryParam.paramDefaultValue ? queryParam.paramDefaultValue : 'false';
        options.label = 'bool';
        options.variableType = 'boolean';
        break;
      }
      case 'INT':
      case 'UINT':
      case 'FLOAT':
      case 'DOUBLE': {
        options.controlType = 'input';
        options.type = 'number';
        options.label = queryParam.paramType.type.toLowerCase() + ' number';
        options.variableType = options.label;
        options.value = queryParam.paramDefaultValue ? queryParam.paramDefaultValue : 0;
        if (
          queryParam.paramType.type === 'FLOAT' ||
          queryParam.paramType.type === 'DOUBLE'
        ) {
          options.numberType = 'real';
        } else {
          options.numberType = 'integer';
        }
        // If it is UINT, set the minimum value for unsigned integer to 0.
        if (queryParam.paramType.type === 'UINT') {
          options.min = 0;
        }
        break;
      }
      case 'DATETIME': {
        options.controlType = 'input';
        options.type = 'datetime';
        options.value = queryParam.paramDefaultValue ?
          new Date(queryParam.paramDefaultValue * 1000)
            .toISOString().replace('T', ' ').slice(0, -5) :
          '';
        options.label = 'datetime';
        options.variableType = 'datetime';
        options.isDateType = true;
        break;
      }
      case 'VERTEX': {
        options.controlType = 'vertex';
        options.label = 'vertex';
        options.variableType = 'vertex';
        // If the vertex type needs user to provide
        if ((<QueryParamVertexType>queryParam.paramType).vertexType === '*') {
          // Construct one empty vertex that use the 1st vertex type as type
          options.value = <VertexTypeAndId>{
            type: vertexTypes[0],
            id: ''
          };
          options.fixedVertexType = false;
        } else {
          // If the vertex type is fixed, use the given vertex type name as input type.
          options.value = <VertexTypeAndId>{
            type: (<QueryParamVertexType>queryParam.paramType).vertexType,
            id: ''
          };
          options.fixedVertexType = true;
        }
        break;
      }
      case 'LIST': {
        // For a list of input, get the option of the inner element, and initially add 0 inner questions
        const tempQueryParam: QueryParam = {
          paramName: queryParam.paramName,
          paramType: (<QueryParamListType>queryParam.paramType).elementType
        };

        options.templateOption = this.buildOptions(tempQueryParam, vertexTypes);
        options.controlType = 'list';
        options.variableType = (
          'multiple ' +
          options.templateOption.variableType +
          's'
        ).replace('vertexs', 'vertices');

        options.questions = [];
        break;
      }
    }

    return options;
  }

  /**
   * Build the json format parameters for installed mode based on the supplied data and query meta.
   *
   * @private
   * @param {QueryParam[]} queryParams Parameter types needed to run the query.
   * @param {*} params Parameter values passed in to run the query.
   * @returns {any} JSON format parameter object.
   * @memberof QuestionFormBuilderService
   */
  buildParamsForInstalledMode(queryParams: QueryParam[], params: any, paramQuestions: QuestionBase<any>[]): any {
    queryParams.forEach(queryParam => {
      const param = params[queryParam.paramName];
      const question = paramQuestions.find(q => q.key === queryParam.paramName);
      if (question && question.usingNullParam) {
        delete params[queryParam.paramName];
        return;
      }
      switch (queryParam.paramType.type) {
        case SupportedQueryParamType.STRING:
        case SupportedQueryParamType.BOOL:
        case SupportedQueryParamType.DATETIME:
        case SupportedQueryParamType.VERTEX: {
          break;
        }
        case SupportedQueryParamType.INT:
        case SupportedQueryParamType.UINT:
        case SupportedQueryParamType.FLOAT:
        case SupportedQueryParamType.DOUBLE: {
          // Convert the data type from string to number.
          params[queryParam.paramName] = +param;
          break;
        }
        case SupportedQueryParamType.LIST: {
          // Get rid of the index key generated from question form.
          const paramList = [];
          for (const paramIndex in param) {
            if (queryParam.paramType.elementType.type in NumberTypes) {
              paramList.push(+param[paramIndex]);
            } else {
              paramList.push(param[paramIndex]);
            }
          }
          params[queryParam.paramName] = paramList;
          break;
        }
        default:
          // Unsupported types: Composite key vertex, Edge, Map, Json object.
      }
    });
    return params;
  }


  /**
   * Build the url parameters for interpret mode query run based on the supplied data and query meta.
   *
   * @private
   * @param {QueryParam[]} queryParams
   * @param {*} params the parameters data.
   * @param {boolean} [testing]
   * @returns {HttpParams}
   * @memberof QuestionFormBuilderService
   */
  buildParamsForInterpretedMode(queryParams: QueryParam[], params: any, testing?: boolean): HttpParams {
    let urlParams = testing
      ? new HttpParams() : new HttpParams({
      encoder: {
        encodeKey(key: string): string {
          return encodeURIComponent(key);
        },

        encodeValue(value: string): string {
          return encodeURIComponent(value);
        },

        decodeKey(key: string): string {
          return decodeURIComponent(key);
        },

        decodeValue(value: string): string {
          return decodeURIComponent(value);
        }
      }
    });

    queryParams.forEach(queryParam => {
      const paramName = queryParam.paramName;
      if (params[paramName] !== null) {
        switch (queryParam.paramType.type) {
          case 'STRING':
          case 'BOOL':
          case 'DATETIME': {
            urlParams = urlParams.set(paramName, params[paramName]);
            break;
          }
          case 'INT':
          case 'UINT':
          case 'FLOAT':
          case 'DOUBLE': {
            // If number is not given, then the parameter will not be passed to run query.
            if (params[paramName].toString().length > 0) {
              urlParams = urlParams.set(paramName, params[paramName]);
            }
            break;
          }
          case 'VERTEX': {
            /**
             * When run query in interpreted mode, "[0]" should be suffix of the parameter name
             * and type. Vertex type should be added.
             * For example:
             *   localhost:14240/gsqlserver/interpreted_query?a[0]=A&a[0].type=Person
            */
            urlParams = urlParams.set(`${paramName}[0]`, params[paramName].id);
            urlParams = urlParams.set(
              paramName + '[0].type',
              params[paramName].type
            );
            break;
          }
          case 'LIST': {
            const elementType = (<QueryParamListType>queryParam.paramType).elementType;
            const paramValue = params[paramName];

            let index = 0;
            Object.keys(paramValue).forEach(key => {
              const param = paramValue[key];
              if (elementType.type === 'VERTEX') {
                // If it is vertex without type, will push paramName[index] and paramName[index].type
                urlParams = urlParams.set(
                  paramName + `[${index}]`,
                  param.id
                );
                urlParams = urlParams.set(
                  paramName + `[${index++}].type`,
                  param.type
                );
              } else {
                // Other type values just append all values, if it's number, skip empty value
                switch (elementType.type) {
                  case 'STRING':
                  case 'BOOL':
                  case 'DATETIME': {
                    urlParams = urlParams.append(paramName, param);
                    break;
                  }
                  case 'INT':
                  case 'UINT':
                  case 'FLOAT':
                  case 'DOUBLE': {
                    // If number is not given, then the parameter will not be passed to run query.
                    if (param.toString().length > 0) {
                      urlParams = urlParams.append(paramName, param);
                    }
                    break;
                  }
                }
              }
            });
            break;
          }
        }
      }
    });

    return urlParams;
  }

  /**
   * Get the vertex type param inputs.
   *
   * @param {QueryMeta} queryMeta
   * @param {*} params
   * @returns {{ type: string, id: string }[]}
   * @memberof QuestionFormBuilderService
   */
  getVertexParamInputs(
    queryMeta: QueryMeta,
    params: any
  ): { type: string; id: string }[] {
    // Collect vertices
    const vertices: { type: string; id: string }[] = [];
    queryMeta.params.forEach(queryParam => {
      const paramName = queryParam.paramName;
      if (params[paramName] !== null) {
        switch (queryParam.paramType.type) {
          case 'VERTEX': {
            vertices.push({
              type: params[paramName].type,
              id: params[paramName].id
            });
            break;
          }
          case 'LIST': {
            const elementType = (<QueryParamListType>queryParam.paramType).elementType;
            const paramValue = params[paramName];

            if (elementType.type === 'VERTEX') {
              Object.keys(paramValue).forEach(key => {
                const param = paramValue[key];
                vertices.push({ type: param.type, id: param.id });
              });
            }
            break;
          }
        }
      }
    });

    return vertices;
  }
}
