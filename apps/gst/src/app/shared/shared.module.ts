import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FlexLayoutModule } from '@angular/flex-layout';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDialogModule } from '@angular/material/dialog';
import { MatGridListModule } from '@angular/material/grid-list';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatListModule } from '@angular/material/list';
import { MatMenuModule } from '@angular/material/menu';
import { MatPaginatorModule, MatPaginatorIntl } from '@angular/material/paginator';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatSliderModule } from '@angular/material/slider';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { MatSortModule } from '@angular/material/sort';
import { MatTableModule } from '@angular/material/table';
import { MatTabsModule } from '@angular/material/tabs';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatTreeModule } from '@angular/material/tree';
import { TranslateModule } from '@ngx-translate/core';
import { ColorPickerModule } from 'ngx-color-picker';
import { ScrollingModule } from '@angular/cdk/scrolling';

import {
  AdjustVerticalComponent, AdjustHorizontalComponent,
  AdjustHorizontalThreeComponent
 } from './components/adjust';
import {
  DynamicFormQuestionComponent,
  DynamicFormQuestionsComponent,
  DynamicFormComponent
} from './components/dynamic-form';
import {
  ExpressionFormQuestionComponent,
  ExpressionFormQuestionsComponent,
  ExpressionFormComponent
} from './components/expression-form';
import { SchemaGraphWrapperComponent } from './components/schema-graph-chart/schemaGraphWrapper';
import { DataGraphWrapperComponent } from './components/data-graph-chart/dataGraphWrapper';
import { LoadingIndicatorComponent } from './components/loading-indicator';
import { SchemaViewerComponent } from './components/schema-viewer';
import { TextEditorComponent } from './components/text-editor';
import {
  ToolbarButtonComponent,
  ToolbarContainerComponent
} from './components/toolbar-container';
import { VertexTypeAndIdInputComponent } from './components/vertex-type-and-id-input';
import { VisualEditorComponent } from './components/visual-editor';
import { LoadingJobUtilityService, QuestionFormBuilderService, SidenavStatusService } from './services';
import { TableViewComponent } from './components/table-view/table-view.component';
import { CustomMatPaginatorIntl } from './components/table-view/CustomMatPaginatorIntl';
import { ListContainerComponent } from './components/list-container/list-container.component';
import { GraphSwitcherComponent } from './components/graph-switcher/graph-switcher.component';
import { ShortcutsMenuComponent } from './components/shortcuts-menu/shortcuts-menu.component';

const MaterialModule = [
  MatAutocompleteModule,
  MatButtonModule,
  MatCheckboxModule,
  MatDialogModule,
  MatGridListModule,
  MatIconModule,
  MatInputModule,
  MatListModule,
  MatMenuModule,
  MatProgressBarModule,
  MatProgressSpinnerModule,
  MatRadioModule,
  MatSelectModule,
  MatSliderModule,
  MatSlideToggleModule,
  MatSnackBarModule,
  MatTabsModule,
  MatTableModule,
  MatToolbarModule,
  MatTooltipModule,
  MatTreeModule,
  MatSortModule,
  MatPaginatorModule,
];

const DynamicForm = [
  DynamicFormQuestionComponent,
  DynamicFormQuestionsComponent,
  DynamicFormComponent
];

const ExpressionForm = [
  ExpressionFormQuestionComponent,
  ExpressionFormQuestionsComponent,
  ExpressionFormComponent
];

const ToolbarContainer = [
  ToolbarButtonComponent,
  ToolbarContainerComponent
];

@NgModule({
  imports: [
    ColorPickerModule,
    CommonModule,
    FlexLayoutModule,
    FormsModule,
    ...MaterialModule,
    ReactiveFormsModule,
    TranslateModule,
    ScrollingModule
  ],
  exports: [
    ColorPickerModule,
    CommonModule,
    FlexLayoutModule,
    FormsModule,
    ...MaterialModule,
    ReactiveFormsModule,
    TranslateModule,

    DynamicFormComponent,
    ExpressionFormComponent,
    SchemaGraphWrapperComponent,
    DataGraphWrapperComponent,
    LoadingIndicatorComponent,
    SchemaViewerComponent,
    TextEditorComponent,
    AdjustVerticalComponent,
    AdjustHorizontalComponent,
    AdjustHorizontalThreeComponent,
    ListContainerComponent,
    ToolbarContainerComponent,
    VertexTypeAndIdInputComponent,
    VisualEditorComponent,
    TableViewComponent,
    GraphSwitcherComponent,
    ShortcutsMenuComponent
  ],
  declarations: [
    ...DynamicForm,
    ...ExpressionForm,
    SchemaGraphWrapperComponent,
    DataGraphWrapperComponent,
    LoadingIndicatorComponent,
    SchemaViewerComponent,
    TextEditorComponent,
    AdjustVerticalComponent,
    AdjustHorizontalComponent,
    AdjustHorizontalThreeComponent,
    ListContainerComponent,
    ...ToolbarContainer,
    VertexTypeAndIdInputComponent,
    VisualEditorComponent,
    TableViewComponent,
    GraphSwitcherComponent,
    ShortcutsMenuComponent
  ],
  providers: [
    LoadingJobUtilityService,
    QuestionFormBuilderService,
    SidenavStatusService,
    {
      provide: MatPaginatorIntl,
      useClass: CustomMatPaginatorIntl,
    },
  ]
})
export class SharedModule { }
