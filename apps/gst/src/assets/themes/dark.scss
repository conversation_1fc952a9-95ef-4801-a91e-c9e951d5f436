@import '../../../node_modules/@angular/material/theming';
@import '../../app-theme';

// Color palette.
$primary: mat-palette($mat-orange, 500);
$accent: mat-palette($mat-teal, 200);
$error: mat-palette($mat-red, 200);

// Define a theme.
$theme: mat-dark-theme($primary, $accent, $error);

// Include all theme styles for the components.
@include angular-material-theme($theme);
@include graph-studio-app-theme($theme);
