@import '../../../node_modules/@angular/material/theming';
@import '../../app-theme';

// Color palette.
$primary: mat-palette($mat-deep-orange, 900);
$accent: mat-palette($mat-teal, 700);
$error: mat-palette($mat-red, A700);

// Change Light-Theme Default Background Color
$mat-light-theme-background: map-merge($mat-light-theme-background, (
  background: map_get($mat-grey, A100),
));

// Define a theme.
$theme: mat-light-theme($primary, $accent, $error);

// Include all theme styles for the components.
@include angular-material-theme($theme);
@include graph-studio-app-theme($theme);
