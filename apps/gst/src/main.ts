import 'hammerjs';

import { enableProdMode } from '@angular/core';
import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';

import { AppModule } from '@app/app.module';
import { environment } from '@environments/environment';
import { setUpCloudEnvAxiosInstance } from '@tigergraph/tools-models';

if (environment.production) {
  enableProdMode();
}

if (environment.cloud) {
  setUpCloudEnvAxiosInstance();
}

platformBrowserDynamic().bootstrapModule(AppModule)
  .catch(err => console.log(err));
