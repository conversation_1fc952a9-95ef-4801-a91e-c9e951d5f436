{"name": "family_of_tools", "version": "0.1.0", "private": true, "dependencies": {"@auth0/auth0-react": "^1.10.0", "@babel/core": "7.23.2", "@fortawesome/fontawesome-free": "^6.5.2", "@fortawesome/fontawesome-svg-core": "^6.5.2", "@fortawesome/free-regular-svg-icons": "^6.5.2", "@fortawesome/free-solid-svg-icons": "^6.5.2", "@fortawesome/react-fontawesome": "^0.2.2", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.3", "@svgr/webpack": "^6.0.0", "@tigergraph/app-ui-lib": "0.3.35", "@tigergraph/tools-models": "1.1.7", "@types/jest": "^27.5.1", "@types/node": "^16.11.35", "@types/react": "^18.0.9", "@types/react-dom": "^18.0.4", "@types/styletron-engine-atomic": "^1.1.1", "@types/styletron-react": "^5.0.3", "@types/styletron-standard": "^2.0.2", "baseui": "^11.0.3", "bfj": "^7.0.2", "browserslist": "^4.18.1", "camelcase": "^6.2.1", "case-sensitive-paths-webpack-plugin": "^2.4.0", "compare-versions": "^6.1.0", "css-loader": "^6.5.1", "dotenv": "^10.0.0", "dotenv-expand": "^5.1.0", "file-loader": "^6.2.0", "fs-extra": "^10.0.0", "html-webpack-plugin": "^5.5.0", "identity-obj-proxy": "^3.0.0", "inline-style-expand-shorthand": "^1.6.0", "mini-css-extract-plugin": "^2.4.5", "particles.js": "^2.0.0", "postcss": "^8.4.33", "postcss-flexbugs-fixes": "^5.0.2", "postcss-loader": "^6.2.1", "postcss-normalize": "^10.0.1", "postcss-preset-env": "^7.0.1", "prompts": "^2.4.2", "react": "^18.1.0", "react-app-polyfill": "^3.0.0", "react-dom": "^18.1.0", "react-icons": "^4.7.1", "react-idle-timer": "^5.7.2", "react-query": "^3.39.0", "react-refresh": "^0.11.0", "react-router-dom": "^6.3.0", "react-to-webcomponent": "^2.0.0-alpha.0", "resolve": "^1.20.0", "resolve-url-loader": "^4.0.0", "sass-loader": "^12.3.0", "semver": "^7.5.2", "source-map-loader": "^3.0.0", "style-loader": "^3.3.1", "styletron-engine-atomic": "^1.4.8", "styletron-react": "^6.0.2", "typescript": "^4.7.4", "web-vitals": "^2.1.4"}, "scripts": {"start": "env-cmd -f .env.prod node scripts/start.js", "build:header": "BUILD_PATH=./src/header/build  node scripts/build.js header", "build:cloudHeader": "BUILD_PATH=./src/cloud-header/build  node scripts/build.js cloudHeader", "prebuild": "npm run build:header && cp ./src/header/build/toolsHead.js ./public/toolsHead.js", "prebuild:testdrive": "cp ./src/jsdk/toolsHead.js ./public/toolsHead.js", "build": "env-cmd -f .env.prod node scripts/build.js", "prebuild:cloud": "bash cloudbuild.sh", "build:cloud": "env-cmd -f .env.cloud node scripts/build.js", "build:testdrive": "env-cmd -f .env.testdrive node scripts/build.js", "test": "node scripts/test.js --coverage  --watchAll=false", "lint": "eslint src/**/*.tsx", "e2e": "npx cypress open --config-file cypress.pro.json", "e2edev": "npx cypress open --config-file cypress.dev.json", "e2elocal": "npx cypress open", "prettier": "prettier . --write", "prettier:check": "prettier . --check"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/traverse": "7.23.2", "@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.2.0", "@testing-library/user-event": "^13.5.0", "babel-jest": "^27.4.2", "babel-loader": "^8.2.3", "babel-plugin-named-asset-import": "^0.3.8", "babel-preset-react-app": "^10.0.1", "css-minimizer-webpack-plugin": "^3.2.0", "cypress": "^9.7.0", "env-cmd": "^10.1.0", "eslint": "^8.15.0", "eslint-config-react-app": "^7.0.1", "eslint-webpack-plugin": "^3.1.1", "express-http-proxy": "^1.6.3", "jest": "^27.4.3", "jest-resolve": "^27.4.2", "jest-watch-typeahead": "^1.0.0", "prettier": "^2.5.1", "react-dev-utils": "^12.0.1", "tailwindcss": "^3.0.2", "terser-webpack-plugin": "^5.2.5", "webpack": "^5.76.0", "webpack-dev-server": "^4.6.0", "webpack-manifest-plugin": "^4.0.2", "workbox-webpack-plugin": "^6.4.1"}, "jest": {"roots": ["<rootDir>/src"], "collectCoverageFrom": ["src/**/*.{js,jsx,ts,tsx}", "!src/**/*.d.ts"], "setupFiles": ["react-app-polyfill/jsdom"], "setupFilesAfterEnv": ["<rootDir>/src/setupTests.ts"], "testMatch": ["<rootDir>/src/**/__tests__/**/*.{js,jsx,ts,tsx}", "<rootDir>/src/**/*.{spec,test}.{js,jsx,ts,tsx}"], "testEnvironment": "jsdom", "transform": {"^.+\\.(js|jsx|mjs|cjs|ts|tsx)$": "<rootDir>/config/jest/babelTransform.js", "^.+\\.css$": "<rootDir>/config/jest/cssTransform.js", "^(?!.*\\.(js|jsx|mjs|cjs|ts|tsx|css|json)$)": "<rootDir>/config/jest/fileTransform.js"}, "transformIgnorePatterns": ["[/\\\\]node_modules[/\\\\].+\\.(js|jsx|mjs|cjs|ts|tsx)$", "^.+\\.module\\.(css|sass|scss)$"], "modulePaths": [], "moduleNameMapper": {"^react-native$": "react-native-web", "^.+\\.module\\.(css|sass|scss)$": "identity-obj-proxy"}, "moduleFileExtensions": ["web.js", "js", "web.ts", "ts", "web.tsx", "tsx", "json", "web.jsx", "jsx", "node"], "watchPlugins": ["jest-watch-typeahead/filename", "jest-watch-typeahead/testname"], "resetMocks": true, "coverageReporters": ["json-summary", "text-summary"]}, "babel": {"presets": ["react-app"]}, "resolutions": {"follow-redirects": "~1.15.5", "@adobe/css-tools": "~4.3.2", "@babel/traverse": "~7.23.7", "postcss": "~8.4.33", "d3-color": "3.1.0"}}