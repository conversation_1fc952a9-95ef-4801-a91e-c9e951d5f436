import { ClusterItem, InstanceAccessOptionEnum } from '.';

export function getMainDomain(url?: string) {
  try {
    let hostname = url ? new URL(url).hostname : window.location.host;
    return hostname
      .replace(/^(?:www\.)?/i, '')
      .split('.')
      .slice(-2)
      .join('.');
  } catch (error) {
    console.error(error);
    return 'tgcloud.io';
  }
}

// Get the cluster domain.
export function getDomain(cluster: ClusterItem): string {
  // If it enables PrivateLink, use the private domain.
  if (cluster.Input.AccessMode === InstanceAccessOptionEnum.Private) {
    return cluster.PrivateDomain;
  } else {
    // For the old solutions, the `Domain` fields are empty string.
    if (!cluster.Domain) {
      return `${cluster.Input.DomainPrefix}.${cluster.Input.DomainSep}.${cluster.Input.Domain}`;
    } else {
      return `${cluster.Domain}`;
    }
  }
}

type UserInfo = {
  email: any;
  firstName: any;
  lastName: any;
  roles: any;
};

let tigerGraphUserInfo: UserInfo | undefined = undefined;

export function getUserInfo() {
  return tigerGraphUserInfo;
}

export function setUserInfo(userInfo: UserInfo) {
  tigerGraphUserInfo = userInfo;
}
