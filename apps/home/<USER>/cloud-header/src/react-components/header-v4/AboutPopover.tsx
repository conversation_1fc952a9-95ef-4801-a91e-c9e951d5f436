import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { profileIconStyle } from '../header/HeaderStyle';
import databasesvg from '../../assets/database.svg';
import toolssvg from '../../assets/tools.svg';

interface AboutPopoverProps {
  dbVersion: string;
  toolsVersion: string;
}

export default function AboutPopover({ dbVersion, toolsVersion }: AboutPopoverProps) {
  const [css] = useStyletron();

  return (
    <div className={css({ padding: '8px', borderRadius: '4px', minWidth: '230px', background: 'white' })}>
      <div className={css({ display: 'flex', marginBottom: '8px', gap: '8px' })}>
        <img src={databasesvg} className={css({ width: '20px' })} alt="" />
        <div className={css({ marginLeft: '8px' })}>
          <div className={css({ fontSize: '14px', fontWeight: '500', color: '#222222' })}>Database Version</div>
          <div className={css({ fontSize: '14px', color: '#115aa0', marginTop: '2px' })}>{dbVersion}</div>
        </div>
      </div>

      <div className={css({ display: 'flex', alignItems: 'center' })}>
        <img src={toolssvg} className={css({ width: '20px' })} alt="" />
        <div className={css({ marginLeft: '8px' })}>
          <div className={css({ fontSize: '14px', fontWeight: '500', color: '#222222' })}>Tools Version</div>
          <div className={css({ fontSize: '14px', color: '#115aa0', marginTop: '2px' })}>{toolsVersion}</div>
        </div>
      </div>
    </div>
  );
}
