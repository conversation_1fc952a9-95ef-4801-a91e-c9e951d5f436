import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import databasesvg from '../../assets/database.svg';
import toolssvg from '../../assets/tools.svg';

interface AboutPopoverItemProps {
  icon: string;
  label: string;
  value: string;
}

function AboutPopoverItem({ icon, label, value }: AboutPopoverItemProps) {
  const [css] = useStyletron();

  return (
    <div
      className={css({
        display: 'flex',
        gap: '8px',
      })}
    >
      <img src={icon} alt={label} />
      <div>
        <div className={css({ fontSize: '16px', fontWeight: '400', lineHeight: '24px', color: '#222' })}>{label}</div>
        <div className={css({ fontSize: '16px', fontWeight: 400, lineHeight: '24px', color: '#115AA0' })}>{value}</div>
      </div>
    </div>
  );
}

interface AboutPopoverProps {
  dbVersion: string;
  toolsVersion: string;
}

export default function AboutPopover({ dbVersion, toolsVersion }: AboutPopoverProps) {
  const [css] = useStyletron();

  return (
    <div
      className={css({
        padding: '8px',
        borderRadius: '4px',
        minWidth: '230px',
        background: 'white',
        display: 'flex',
        flexDirection: 'column',
        gap: '8px',
      })}
    >
      <AboutPopoverItem icon={databasesvg} label="Database Version" value={dbVersion} />
      <AboutPopoverItem icon={toolssvg} label="Tools Version" value={toolsVersion} />
    </div>
  );
}
