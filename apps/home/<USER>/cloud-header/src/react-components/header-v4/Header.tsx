import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { useQuery } from 'react-query';
import {
  headerStyles,
  profileContainer,
  profileIconStyle,
  profileItem,
  profileName,
  profileRole,
} from '../header/HeaderStyle';
import { axiosOrg } from './network';

import logosvg from '../../assets/logo.svg';
import accountsvg from '../../assets/account.svg';
import applicationsvg from '../../assets/application.svg';
import languagesvg from '../../assets/language.svg';
import assignmentsvg from '../../assets/assignment.svg';
import helpoutlinesvg from '../../assets/help-outline.svg';
import signoutsvg from '../../assets/sign-out.svg';
import letterlogopng from '../../assets/letter-logo-v4.png';
import infosvg from '../../assets/info.svg';
import databasesvg from '../../assets/database.svg';
import toolssvg from '../../assets/tools.svg';
import { PLACEMENT, Popover, StatefulPopover } from 'baseui/popover';
import { HelpPopover } from '../header/Popovers';
import { Language2Label, LanguageType, ToolSupportSwitchLanguage, ToolType } from '../common';
import { useEffect, useState } from 'react';
import { useAuth0 } from '@auth0/auth0-react';
import { getBaseURL, getLogoutReturnURL, getToolLanguage } from './util';
import axios from 'axios';
import { getToolLogo } from './ToolsLogo';
import ToolsPopover from './ToolsPopover';
import LanguagePopover from './LanguagePopover';

async function getUserProfile(): Promise<any> {
  const response = await axiosOrg.get('/users/me');
  return response.data;
}
interface UserProfile {
  firstName: string;
  lastName: string;
  email: string;
  roles: { displayName: string }[];
}

export default function CloudV4Header(props: any) {
  const { clusterId, onChangeTool, currentTool, onHandleLogout, handleChangeLanguageCallback } = props;
  const [css] = useStyletron();

  const [user, setUser] = useState<UserProfile>({
    firstName: '',
    lastName: '',
    email: '',
    roles: [{ displayName: '' }],
  });
  useQuery(['user-profile'], getUserProfile, {
    onSuccess: (data) => {
      setUser({
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email,
        roles: data.roles,
      });
    },
    onError: (error) => {
      console.error(error);
    },
  });

  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const [isToolsOpen, setIsToolsOpen] = useState(false);
  const [isHelpOpen, setIsHelpOpen] = useState(false);
  const [isLanguageOpen, setIsLanguageOpen] = useState(false);
  const [isAboutOpen, setIsAboutOpen] = useState(false);
  const [currentLanguage, setCurrentLanguage] = useState<LanguageType>();
  const [dbVersion, setDbVersion] = useState('');
  const [toolsVersion, setToolsVersion] = useState('');

  const { logout } = useAuth0();

  useEffect(() => {
    const initLanguage = () => {
      const language = getToolLanguage() || LanguageType.English;
      setCurrentLanguage(language);
    };

    const fetchDbVersion = async () => {
      try {
        const response = await axios.get(`${getBaseURL()}/api/version`, { withCredentials: true });
        const version = response.data.results?.tigergraph_version || '';
        setDbVersion(version);
      } catch (error) {
        console.error('Failed to fetch database version:', error);
        setDbVersion('Unknown');
      }
    };

    const getToolsVersionInfo = () => {
      // @ts-ignore
      const releaseVersion = typeof $TOOLS_RELEASE_VERSION !== 'undefined' ? $TOOLS_RELEASE_VERSION : '1.0';
      // @ts-ignore
      const buildNumber = typeof $BUILD_NUMBER !== 'undefined' ? $BUILD_NUMBER : 'dev';
      setToolsVersion(`${releaseVersion}.${buildNumber}`);
    };

    initLanguage();
    fetchDbVersion();
    getToolsVersionInfo();
  }, []);

  const handleLogout = async () => {
    const clusterLogoutRep = async () => {
      await axios.post(`${getBaseURL()}/api/auth/logout`, {}, { withCredentials: true });
    };

    onHandleLogout && onHandleLogout();
    try {
      await clusterLogoutRep();
    } catch (error) {
      console.error(error);
    }
    setIsProfileOpen(false);
    logout({ returnTo: getLogoutReturnURL() });
  };

  const handleGotoTerms = () => {
    window.open('https://www.tigergraph.com/tigergraph-cloud-subscription-terms/', '_blank');
  };

  const ProfilePopover = () => (
    <div className={css(profileContainer)}>
      <div className={css({ padding: '16px 16px', borderBottom: 'solid 1px #D4DADF' })}>
        <div className={css(profileName)}>{user.firstName + ' ' + user.lastName}</div>
        <div className={css({ color: '#656565', marginTop: '4px', lineHeight: '16px' })}>{user.email}</div>
        <div className={css(profileRole)}>{user.roles[0].displayName}</div>
      </div>
      <div>
        {clusterId && ToolSupportSwitchLanguage.includes(currentTool) && (
          <StatefulPopover
            overrides={{
              Body: {
                style: () => ({
                  zIndex: 9999,
                  borderTopRightRadius: '5px',
                  borderTopLeftRadius: '5px',
                  borderBottomLeftRadius: '5px',
                  borderBottomRightRadius: '5px',
                }),
              },
              Inner: {
                style: () => ({
                  borderTopRightRadius: '5px',
                  borderTopLeftRadius: '5px',
                  borderBottomLeftRadius: '5px',
                  borderBottomRightRadius: '5px',
                }),
              },
            }}
            triggerType={'hover'}
            onOpen={() => setIsLanguageOpen(true)}
            onClose={() => setIsLanguageOpen(false)}
            content={({ close }) => (
              <LanguagePopover
                close={() => {
                  close();
                  setIsProfileOpen(false);
                }}
                setCurrentLanguage={setCurrentLanguage}
                currentTool={currentTool}
                handleChangeLanguageCallback={handleChangeLanguageCallback}
              />
            )}
          >
            <div className={css(profileItem)}>
              <img src={languagesvg} className={css(profileIconStyle)} alt="" />
              <span>{Language2Label[currentLanguage]}</span>
              <svg
                className={css({ marginLeft: '100px' })}
                width="8"
                height="12"
                viewBox="0 0 8 12"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M1.29006 9.87998L5.17006 5.99998L1.29006 2.11998C0.900059 1.72998 0.900059 1.09998 1.29006 0.70998C1.68006 0.31998 2.31006 0.31998 2.70006 0.70998L7.29006 5.29998C7.68006 5.68998 7.68006 6.31998 7.29006 6.70998L2.70006 11.3C2.31006 11.69 1.68006 11.69 1.29006 11.3C0.910059 10.91 0.900059 10.27 1.29006 9.87998Z"
                  fill="#6A7D8F"
                />
              </svg>
            </div>
          </StatefulPopover>
        )}

        <div className={css(profileItem)} onClick={() => handleGotoTerms()}>
          <img src={assignmentsvg} className={css(profileIconStyle)} alt="" />
          <span>Terms and conditions</span>
        </div>

        <div className={css(profileItem)} onClick={() => handleLogout()}>
          <img src={signoutsvg} className={css(profileIconStyle)} alt="" />
          <span>Sign out</span>
        </div>
      </div>
    </div>
  );

  const AboutPopover = () => (
    <div className={css(profileContainer)}>
      <div className={css({ padding: '16px 16px' })}>
        <div className={css({ display: 'flex', alignItems: 'center', marginBottom: '16px' })}>
          <img src={databasesvg} className={css(profileIconStyle)} alt="" />
          <div className={css({ marginLeft: '8px' })}>
            <div className={css({ fontSize: '14px', fontWeight: '500', color: '#222222' })}>Database Version</div>
            <div className={css({ fontSize: '14px', color: '#115aa0', marginTop: '2px' })}>{dbVersion}</div>
          </div>
        </div>
        <div className={css({ display: 'flex', alignItems: 'center' })}>
          <img src={toolssvg} className={css(profileIconStyle)} alt="" />
          <div className={css({ marginLeft: '8px' })}>
            <div className={css({ fontSize: '14px', fontWeight: '500', color: '#222222' })}>Tools Version</div>
            <div className={css({ fontSize: '14px', color: '#115aa0', marginTop: '2px' })}>{toolsVersion}</div>
          </div>
        </div>
      </div>
    </div>
  );

  const handleChangeTool = (tool: ToolType) => {
    onChangeTool && onChangeTool(tool);
  };

  return (
    <div className={css(headerStyles)}>
      <div
        className={css({
          display: 'flex',
          alignItems: 'center',
        })}
      >
        {!currentTool && <img aria-label="Cloud_header" src={logosvg} alt="" />}
        {currentTool && getToolLogo(currentTool)}
        {currentTool && (
          <>
            <span
              className={css({
                borderRight: '1px solid #D4DADF',
                height: '25px',
                marginRight: '8px',
              })}
            ></span>
            <div
              className={css({
                minWidth: '76px',
                display: 'flex',
                alignItems: 'baseline',
              })}
            >
              <span
                className={css({
                  marginRight: '2px',
                  fontSize: '10px',
                  lineHeight: '16px',
                  fontWeight: '300',
                  color: '#546A80',
                  verticalAlign: 'sub',
                })}
              >
                on
              </span>
              <img
                alt=""
                className={css({
                  verticalAlign: 'sub',
                  width: '64px',
                })}
                src={letterlogopng}
              />
            </div>
          </>
        )}
      </div>

      <div
        className={css({
          display: 'flex',
          alignItems: 'center',
        })}
      >
        {clusterId && (
          <div
            className={css({
              display: 'flex',
              alignItems: 'center',
            })}
          >
            <Popover
              onClick={() => setIsToolsOpen(!isToolsOpen)}
              onClickOutside={(e) => setIsToolsOpen(false)}
              content={() => <ToolsPopover onChangeTool={handleChangeTool} setIsToolsOpen={setIsToolsOpen} />}
              isOpen={isToolsOpen}
              placement={PLACEMENT.bottomRight}
              overrides={{
                Body: {
                  style: ({ $theme }) => ({
                    zIndex: 999,
                  }),
                },
              }}
            >
              <img
                alt=""
                src={applicationsvg}
                className={css({
                  cursor: 'pointer',
                  width: '16px',
                  height: '16px',
                })}
              />
            </Popover>
          </div>
        )}
        <Popover
          onClick={() => setIsHelpOpen(!isHelpOpen)}
          onClickOutside={() => {
            setIsHelpOpen(false);
          }}
          placement={PLACEMENT.bottomRight}
          content={() => <HelpPopover currentTool={currentTool} />}
          isOpen={isHelpOpen}
          overrides={{
            Body: {
              style: ({ $theme }) => ({
                zIndex: 999,
              }),
            },
          }}
        >
          <img
            alt=""
            src={helpoutlinesvg}
            className={css({
              marginLeft: '10px',
              marginRight: '-6px',
              width: '20px',
              height: '20px',
              cursor: 'pointer',
              padding: '6px',
              boxSizing: 'content-box',
              ':active': {
                backgroundColor: '#D4DADF',
                borderRadius: '5px',
              },
            })}
          />
        </Popover>
        <Popover
          onClick={() => setIsAboutOpen(!isAboutOpen)}
          onClickOutside={() => setIsAboutOpen(false)}
          isOpen={isAboutOpen}
          placement={PLACEMENT.bottomRight}
          overrides={{
            Body: {
              style: ({ $theme }) => ({
                zIndex: 999,
              }),
            },
          }}
          content={() => <AboutPopover />}
        >
          <img
            alt=""
            src={infosvg}
            className={css({
              marginLeft: '10px',
              marginRight: '-6px',
              width: '20px',
              height: '20px',
              cursor: 'pointer',
              padding: '6px',
              boxSizing: 'content-box',
              ':active': {
                backgroundColor: '#D4DADF',
                borderRadius: '5px',
              },
            })}
          />
        </Popover>
        <Popover
          onClick={() => setIsProfileOpen(!isProfileOpen)}
          onClickOutside={(e) => {
            if (!isLanguageOpen) {
              setIsProfileOpen(false);
            }
          }}
          isOpen={isProfileOpen}
          placement={PLACEMENT.bottomRight}
          overrides={{
            Body: {
              style: ({ $theme }) => ({
                zIndex: 999,
              }),
            },
          }}
          content={() => <ProfilePopover />}
        >
          <img
            alt=""
            src={accountsvg}
            className={css({ margin: '8px 16px', cursor: 'pointer', width: '32px', height: '32px' })}
          />
        </Popover>
      </div>
    </div>
  );
}
