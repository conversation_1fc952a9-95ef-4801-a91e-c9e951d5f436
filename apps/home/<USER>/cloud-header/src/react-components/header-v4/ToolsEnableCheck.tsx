import { <PERSON><PERSON>, <PERSON>dal<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Mo<PERSON>Header } from '@tigergraph/app-ui-lib/modal';
import { getPortalURL } from './util';
import { useLayoutEffect, useMemo } from 'react';
import { AddonName, useAddonContext } from './useAddonContext';

type ToolDesc = {
  name: string;
  path: string;
  addonName?: AddonName;
};

const toolsDesc: ToolDesc[] = [
  {
    name: 'Graph Studio',
    path: '/studio',
    addonName: 'TigerGraph GraphStudio',
  },
  {
    name: 'Insights',
    path: '/insights',
    addonName: 'TigerGraph Insights',
  },
  {
    name: 'Admin Portal',
    path: '/admin',
    addonName: 'TigerGraph AdminPortal',
  },
  {
    name: 'GraphQL',
    path: '/graphql',
    addonName: 'TigerGraph GraphQL',
  },
  {
    name: 'GSQL Shell',
    path: '/gsql',
  },
];

function getToolDesc() {
  const appPath = window.location.pathname;
  return toolsDesc.find((desc) => appPath.startsWith(desc.path));
}

const toolDesc = getToolDesc();

export default function ToolsEnableCheck() {
  const { installedAddonNames } = useAddonContext();

  const isToolEnable = useMemo(() => {
    if (installedAddonNames) {
      return !toolDesc || installedAddonNames.includes(toolDesc.addonName);
    }
    return true;
  }, [installedAddonNames]);

  const isShowError = !isToolEnable;

  useLayoutEffect(() => {
    if (!isShowError) {
      return;
    }
    // see apps/insights/src/components/toolsHeader.tsx
    // set insights tool header zIndex to 1 so the license check ui show in header will overlay the app ui correctly
    const node = document.getElementsByTagName('tools-head')[0];
    if (node && node.parentElement) {
      node.parentElement.style.zIndex = '1';
    }
  }, [isShowError]);

  if (!isShowError) {
    return null;
  }

  const modalTitle = 'Access Denied';
  const modalMessages = [
    `You do not have access to ${toolDesc.name}.`,
    'Please install the addon in TigerGraph Savanna Portal.',
  ];

  return (
    <Modal
      closeable={false}
      role="dialog"
      isOpen={true}
      autoFocus={false}
      animate={false}
      overrides={{
        Root: {
          style: {
            // 1 the same level as cloud left nav bar
            // 2 and less than tool's header
            zIndex: 5,
          },
        },
      }}
    >
      <ModalHeader>{modalTitle}</ModalHeader>
      <ModalBody>
        {modalMessages.map((message) => (
          <>
            {message}
            <br />
          </>
        ))}
        <br />
      </ModalBody>
      <ModalFooter>
        <ModalButton
          onClick={() => {
            window.location.href = `${getPortalURL()}/marketplace/addons`;
          }}
        >
          Go to Install
        </ModalButton>
      </ModalFooter>
    </Modal>
  );
}
