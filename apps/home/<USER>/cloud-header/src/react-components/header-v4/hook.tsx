import { useQuery } from 'react-query';
import { Result, axiosController } from './network';

export type AddOnItemType = {
  ID: string;
  Background: string;
  Avatar: string;
  Title: string;
  Description: string;
  Amount?: string;
  IsOfficial?: boolean;
  Categories?: string[];
  Announcement?: string[];
  BannerImageList?: string[];
};

export type UseAddonsMetaReturnType = Result<{
  AddonsList: AddOnItemType[];
  RequestAddons: AddOnItemType[];
}>;

export const useAddonsMeta = () => {
  return useQuery({
    queryKey: ['addons', 'meta'],
    queryFn: async () => {
      const res = await axiosController.get<UseAddonsMetaReturnType>('/v2/add-ons/meta');
      return res.data.Result;
    },
  });
};

export type EnabledAddonsType = {
  ID: string;
  Enabled: boolean;
  AddonsID: string;
};

export type ListEnabledAddonsReturnType = Result<EnabledAddonsType[]>;

export const useListEnabledAddons = () => {
  return useQuery({
    queryKey: ['list', 'enabled', 'addons'],
    queryFn: async () => {
      const res = await axiosController.get<ListEnabledAddonsReturnType>('/v2/add-ons/installed');
      return res.data.Result;
    },
  });
};

export const useWorkspaceDetail = ({ workspaceID, workgroupID }: { workgroupID: string; workspaceID: string }) => {
  return useQuery({
    queryKey: ['workspace', 'detail', workspaceID],
    queryFn: async () => {
      const response = await axiosController.get<{
        Result: { addons: [{ addons_id: string; enable: boolean }] | null } | null;
      }>(`v2/workgroups/${workgroupID}/workspaces/${workspaceID}`);
      return response.data.Result;
    },
    enabled: Boolean(workgroupID && workspaceID),
  });
};
