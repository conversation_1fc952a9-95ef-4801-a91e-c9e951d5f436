import axios from 'axios';
import { get<PERSON><PERSON>rollerUR<PERSON>, getC<PERSON>ie, getLogoutReturnURL, getOrgURL } from './util';

const axiosController = axios.create({
  baseURL: getControllerURL(),
});
axiosController.interceptors.response.use(
  (res) => {
    return res;
  },
  (err) => {
    let response = err.response;
    if (response.status === 401) {
      window.location.replace(getLogoutReturnURL());
    }
    return Promise.reject(err);
  }
);

const axiosOrg = axios.create({
  baseURL: getOrgURL(),
});
axiosOrg.interceptors.response.use(
  (res) => {
    return res;
  },
  (err) => {
    let response = err.response;
    if (response.status === 401) {
      window.location.replace(getLogoutReturnURL());
    }
    return Promise.reject(err);
  }
);

const token = getCookie('idTokenV4');
axiosController.defaults.headers.common['Authorization'] = 'Bearer ' + token;
axiosOrg.defaults.headers.common['Authorization'] = 'Bearer ' + token;

export { axiosController, axiosOrg };

export type Result<T = unknown> = {
  Error: boolean;
  ErrorDetails?: any;
  Message?: string;
  Result?: T;
};
