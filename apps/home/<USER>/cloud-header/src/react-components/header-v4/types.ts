export type WorkspaceAddons = {
  enable: boolean;
  addons_id: string;
  // workspace_id: string;
  enable_configuration: Record<string, string>;
};

export type WorkspaceT = {
  name: string;
  status: WorkspaceStatus;
  err_message: string;
  workspace_id: string;
  workgroup_id: string;
  database_id: string;
  tg_version: string;
  version_number?: number;
  created_at: string;
  creator: string;
  last_modified_time: string;
  is_rw: boolean;
  nginx_host: string;
  enable_auto_start: boolean;
  enable_ha: boolean;
  enable_copilot: boolean;
  copilot_llm_provider_config_id: string;
  auto_stop_minutes: number;
  snapshot_time: string;
  refresh_message: string;
  addons?: WorkspaceAddons[];
  graph_topology_size_bytes?: number;
  vertex_count?: number;
  edge_count?: number;
  solution_catalog_id: string;

  maintenance_window_time_zone: string;
  // weekday 0-6, 0 means Sunday
  maintenance_window_day_of_week: number;
  // a number between 0-23
  maintenance_window_hour_of_day: number;
  // update_strategy_always
  update_strategy: 'update_strategy_never' | 'update_strategy_window';

  // calculated fields, two place to calculate this props
  // 1. in group detail
  // 2. workspace context
  canAccess: boolean;

  // set up in
  // 1 workspaceContext.tsx
  // 2 workgroup/api.ts
  workgroup_name: string; // from workgroup
  cloud_provider_id: string; // from workgroup
  region: string; // from workgroup
  platform: string; // from workgroup
  database_name: string; // from tg_database

  // this org_id is only available in admin dashboard
  org_id?: string;

  // memory percent
  tigergraph_memory_usage_percent?: number;
  tigergraph_data_memory_usage_percent?: number;
};
