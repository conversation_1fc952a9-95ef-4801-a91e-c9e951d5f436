import { FC, PropsWithChildren, createContext, useContext, useMemo } from 'react';
import { useAddonsMeta, useWorkspaceDetail } from './hook';
import { getCurrentClusterId, getCurrentGroupId } from './util';

export type AddonName =
  | 'TigerGraph GraphStudio'
  | 'TigerGraph Insights'
  | 'TigerGraph AdminPortal'
  | 'TigerGraph GraphQL';

interface AddonContextValue {
  installedAddonNames: string[] | null;
  isAddonMetaError: boolean;
  isAddonError: boolean;
}

const addonContext = createContext<AddonContextValue>({} as AddonContextValue);

const useAddonContext = () => {
  const context = useContext(addonContext);
  return {
    ...context,
    isAddonEnable: (addonName: AddonName) => {
      return !!context.installedAddonNames?.includes(addonName);
    },
  };
};

const AddonProvider: FC<PropsWithChildren> = (props) => {
  const { data: addonsMeta, isError: isAddonMetaError } = useAddonsMeta();
  const { data: workspace, isError: isAddonError } = useWorkspaceDetail({
    workgroupID: getCurrentGroupId(),
    workspaceID: getCurrentClusterId(),
  });

  const installedAddonNames = useMemo(() => {
    if (!addonsMeta || !workspace) return null;

    let addonNames =
      workspace.addons
        ?.filter((ad) => ad.enable)
        .map((addon) => {
          const addonMeta = addonsMeta.AddonsList.find((meta) => meta.ID === addon.addons_id);
          return addonMeta?.Title as string;
        }) || [];
    if (!addonNames.includes('TigerGraph AdminPortal')) {
      addonNames.push('TigerGraph AdminPortal');
    }
    return addonNames;
  }, [addonsMeta, workspace]);

  const value = {
    installedAddonNames,
    isAddonMetaError,
    isAddonError,
  };

  return <addonContext.Provider value={value} {...props} />;
};

export { AddonProvider, useAddonContext };
