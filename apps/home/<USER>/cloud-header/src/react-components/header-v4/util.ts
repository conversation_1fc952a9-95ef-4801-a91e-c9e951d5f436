import { LanguageType, ToolType } from '../common';

export function getCurrentClusterId(): string {
  return sessionStorage.getItem('CURRENTCLUSTERID') || '';
}

export function getCurrentGroupId() {
  return sessionStorage.getItem('CURRENTGROUPID') || '';
}

export function getCloudVersion(): string {
  return sessionStorage.getItem('CLOUDVERSION') || '';
}

export function isProd() {
  return window.location.origin.includes('tgcloud.io');
}

export function getCloudEnv(): string {
  return sessionStorage.getItem('CLOUDENV') || '';
}

export function getBaseURL(): string {
  return sessionStorage.getItem('BASEURL') || '';
}

export function getOrgName(): string {
  return sessionStorage.getItem('ORGNAME') || '';
}

export function getMainDomain(): string {
  return window.location.host.split('.').slice(-2).join('.');
}

export function getDBDomain(): string {
  const match = sessionStorage
    .getItem('BASEURL')
    ?.match(
      /tg-[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}\.[a-zA-Z0-9.-]+?\.(privatelink|i)/i
    );
  return match ? match[0] : '';
}

const localStoragePrefix = 'TG_Cloud';

export enum LocalStorageKeys {
  toolLanguage = 'toolLanguage',
}

export const Type2Route = {
  [ToolType.GraphStudio]: 'studio',
  [ToolType.Insights]: 'insights',
  [ToolType.GsqlShell]: 'gsql',
  [ToolType.GraphQL]: 'graphql',
  [ToolType.AdminPortal]: 'admin',
};

export function getLocalstorageKey(key: string): string {
  return localStoragePrefix + '.' + key;
}

export function getToolLanguage(): LanguageType {
  return localStorage.getItem(getLocalstorageKey(LocalStorageKeys.toolLanguage)) as LanguageType;
}

export function getControllerURL(): string {
  const env = getCloudEnv();
  if (window.location.origin.includes('tgcloud.io')) {
    return 'https://api.tgcloud.io/controller/v4';
  }
  if (env === 'dev') {
    return 'https://api-v4.tgcloud-dev.com/controller';
  }
  if (env === 'staging') {
    return 'https://api-staging.tgcloud.io/controller/v4';
  }
  if (env === 'uat') {
    return 'https://api-v4-uat.tgcloud-dev.com/controller';
  }
  return '';
}

export function getOrgURL(): string {
  const env = getCloudEnv();
  if (window.location.origin.includes('tgcloud.io')) {
    return 'https://api.tgcloud.io';
  }
  if (env === 'dev') {
    return 'https://api-v4.tgcloud-dev.com';
  }
  if (env === 'staging') {
    return 'https://api.tgcloud.io';
  }
  if (env === 'uat') {
    return 'https://api-v4-uat.tgcloud-dev.com';
  }
  return '';
}

export function getCookie(cname: string): string {
  let name = cname + '=';
  const ca = document.cookie.split(';');
  for (let i = 0; i < ca.length; i++) {
    let c = ca[i].trim();
    if (c.indexOf(name) === 0) return c.substring(name.length, c.length);
  }
  return '';
}

export function getPortalURL(): string {
  const env = getCloudEnv();
  let subdomain = '';

  switch (env) {
    case 'dev':
      subdomain = '';
      break;
    case 'staging':
      subdomain = 'portal';
      break;
    case 'uat':
      subdomain = 'portal';
      break;
    default:
      // production
      subdomain = '';
      break;
  }

  return `${window.location.protocol}//${subdomain ? subdomain + '.' : ''}${getMainDomain()}`;
}

export function getLogoutReturnURL() {
  return `${getPortalURL()}/?logout=true'`;
}
