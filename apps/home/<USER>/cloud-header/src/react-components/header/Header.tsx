import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { PLACEMENT, Popover, StatefulPopover } from 'baseui/popover';
import { compare } from 'compare-versions';
import * as React from 'react';
import { useEffect } from 'react';

import { headerStyles, profileContainer, profileIconStyle, profileItem, profileName, profileRole } from './HeaderStyle';

import { useAuth0 } from '@auth0/auth0-react';
import { Select } from '@tigergraph/app-ui-lib/select';
import axios from 'axios';
import { ImperativeMethods, Option, Value } from 'baseui/select';

import {
  ClusterItem,
  Language2Label,
  LanguageType,
  PlatformEnum,
  ToolSupportSwitchLanguage,
  ToolType,
} from '../common/index';
import { IdleModal } from './IdleModal';
import { HelpPopover, LanguagePopover, ToolsPopover } from './Popovers';

import { getDomain, getMainDomain, getUserInfo, setUserInfo } from '../common/util';

import { useLicense } from '../../../../license/license';
import accountsvg from '../../assets/account.svg';
import adminportalhorizontalsvg from '../../assets/admin-portal-horizontal.svg';
import applicationsvg from '../../assets/application.svg';
import assignmentsvg from '../../assets/assignment.svg';
import graphstudiohorizontalsvg from '../../assets/graph-studio-horizontal.svg';
import graphqlhorizontalsvg from '../../assets/graphql-horizontal.svg';
import gsqlshellhorizontalsvg from '../../assets/gsql-shell-horizontal.svg';
import helpoutlinesvg from '../../assets/help-outline.svg';
import insightshorizontalsvg from '../../assets/insights-horizontal.svg';
import languagesvg from '../../assets/language.svg';
import letterlogopng from '../../assets/letter-logo.png';
import logosvg from '../../assets/logo.svg';
import machinelearninghorizontalsvg from '../../assets/machine-learning-horizontal.svg';
import menusvg from '../../assets/menu.svg';
import signoutsvg from '../../assets/sign-out.svg';
import { getControllerDomain } from './util';

const localStoragePrefix = 'TG_Cloud';

export enum LocalStorageKeys {
  orgName = 'orgName',
  toolLanguage = 'toolLanguage',
  pci = 'pci',
}

export const Type2Route = {
  [ToolType.GraphStudio]: 'studio',
  [ToolType.Insights]: 'insights',
  [ToolType.GsqlShell]: 'gsql',
  [ToolType.GraphQL]: 'graphql',
  [ToolType.AdminPortal]: 'admin',
};

export function getLocalstorageKey(key: string): string {
  return localStoragePrefix + '.' + key;
}

export function setOrgSettings(orgSettings: any) {
  orgSettings?.pci && localStorage.setItem(getLocalstorageKey(LocalStorageKeys.pci), orgSettings?.pci);
}

export function getAppURL(cluster: ClusterItem, toolType: ToolType): string {
  switch (toolType) {
    // Get url for GraphStudio or Admin Portal version lower than 3.7.
    case ToolType.GraphStudio:
    case ToolType.AdminPortal: {
      const appMeta = cluster.Meta.Applications.find((app) => app.Description === 'GraphStudio');
      let appURL = `https://${getDomain(cluster)}${appMeta.URLPrefix}`;
      if (toolType === ToolType.AdminPortal) appURL += 'admin/';
      return appURL;
    }
    case ToolType.MLWorkbench: {
      const mlwbMeta = cluster.Meta.Applications.find((app) => app.Description === 'Machine Learning Workbench');
      return `https://${getDomain(cluster)}${mlwbMeta.URLPrefix}`;
    }
    default:
      return '';
  }
}

export function getIsPCIOn() {
  return localStorage.getItem(getLocalstorageKey(LocalStorageKeys.pci));
}

function getToolLanguage(): LanguageType {
  return localStorage.getItem(getLocalstorageKey(LocalStorageKeys.toolLanguage)) as LanguageType;
}
// for 3.7.0, GsqlShell is not available on Azure platform
export function isDisableTool(tool: ToolType, cluster: ClusterItem) {
  if (tool && cluster) {
    if (tool !== ToolType.MLWorkbench) {
      return tool === ToolType.GsqlShell && cluster.Platform === PlatformEnum.Azure;
    } else {
      return cluster.Meta.Applications.filter((app) => app.Description === 'Machine Learning Workbench').length === 0;
    }
  } else {
    return false;
  }
}

export const goto = (url: string) => {
  window.open(url, '_blank');
};

export enum ClusterStatus {
  Uninitialized = 'uninitialized',
  Initialized = 'initialized',
  Processing = 'processing',
  Configuring = 'configuring',
  Ready = 'ready',
  Stopping = 'stopping',
  Stopped = 'stopped',
  Starting = 'starting',
  Terminating = 'terminating',
  Terminated = 'terminated',
  Resizing = 'resizing',
  Error = 'error',
}
export function isClusterSupportSwitch(cluster: ClusterItem) {
  return cluster.State === ClusterStatus.Ready && cluster.HasGraphStudioAccess;
}
export function lowerVersion(version: string): boolean {
  const MinRequiredVersion = '3.7.0';
  return compare(version, MinRequiredVersion, '<');
}

export function isClusterSupportTool(cluster: ClusterItem, toolType: ToolType) {
  // for mlwb, we only show clusters with Meta.Applications contains MLWB.
  if (toolType === ToolType.MLWorkbench) {
    return (
      isClusterSupportSwitch(cluster) &&
      cluster.Meta.Applications.find((app) => app.Description === 'Machine Learning Workbench')
    );
  } else if (toolType === ToolType.AdminPortal || toolType === ToolType.GraphStudio) {
    return isClusterSupportSwitch(cluster);
  } else {
    return isClusterSupportSwitch(cluster) && !lowerVersion(cluster.Version);
  }
}

function getCookie(cname: string): string {
  let name = cname + '=';
  const ca = document.cookie.split(';');
  for (let i = 0; i < ca.length; i++) {
    let c = ca[i].trim();
    if (c.indexOf(name) === 0) return c.substring(name.length, c.length);
  }
  return '';
}
axios.interceptors.response.use(
  (res) => {
    return res;
  },
  (err) => {
    let response = err.response;
    const localEnv = window.location.hostname.includes('localhost');
    const protocol = localEnv ? 'https:' : window.location.protocol;
    if (response.status === 401) {
      let returnUrl = `${protocol}//${getControllerDomain()}/?returnTo=${encodeURIComponent(window.location.href)}`;
      if (window.self === window.top) {
        window.location.replace(returnUrl);
      }
      return;
    }
    return Promise.reject(err);
  }
);

axios.interceptors.request.use(
  (config) => {
    // For following request, we do not append headers.
    if (config.url?.includes('/api/auth/logout')) {
      return config;
    }

    const token = getCookie('idToken');
    if (token) {
      config.headers['Authorization'] = 'Bearer ' + token;
    }
    if (!config.headers['Content-Type']) {
      config.headers['Content-Type'] = 'application/json';
    }

    return config;
  },
  (error) => {
    Promise.reject(error);
  }
);

export const Header = (props: any) => {
  const {
    cloudSliderId,
    clusterId,
    onChangeCluster,
    onChangeTool,
    onRedirectToToolHome,
    onRedirectToSelectCluster,
    currentTool,
    onHandleLogout,
    menuClickCallBack,
    handleChangeLanguageCallback,
  } = props;
  const [clusters, setClusters] = React.useState<ClusterItem[]>([]);
  const [selectedCluster, setSelectedCluster] = React.useState<Value>([]);
  const [isProfileOpen, setIsProfileOpen] = React.useState(false);
  const [isToolsOpen, setIsToolsOpen] = React.useState(false);
  const [isHelpOpen, setIsHelpOpen] = React.useState(false);
  const [currentLanguage, setCurrentLanguage] = React.useState<LanguageType>();
  const controlRef = React.useRef<ImperativeMethods>(null);
  const [loadingCluster, setLoadingCluster] = React.useState(true);

  const { logout } = useAuth0();
  const [css] = useStyletron();
  const user = getUserInfo();
  const localEnv = window.location.hostname.includes('localhost');
  const protocol = localEnv ? 'https:' : window.location.protocol;
  const mainDomain = localEnv ? 'tgcloud-dev.com' : window.location.host.split('.').slice(-2).join('.');
  const toolOrigin = `${protocol}//tools.${mainDomain}`;
  const testToolOrigin = `${protocol}//tools-dev.${mainDomain}`;
  const toolOrigin_380 = `${protocol}//tools-v380.${mainDomain}`;
  const testToolOrigin_380 = `${protocol}//tools-dev-v380.${mainDomain}`;
  const DEV_CLUSTER_ID = '84da0312-9cd8-4a0f-909c-c54c4b2f2279';
  const DEV_CLUSTER_ID_3_9_0 = '470094d8-976f-49bd-b7bd-2bdf57b5f9ce';

  async function getMe() {
    try {
      const response = await axios.get(`https://api.${getMainDomain()}/users/me`);
      // console.log(response);
      const userInfo = {
        email: response.data.email,
        firstName: response.data.firstName,
        lastName: response.data.lastName,
        roles: response.data.roles,
      };
      setUserInfo(userInfo);
      // return response.data;
    } catch (err) {
      console.log(err);
      // return Promise.reject(err.response.data);
    }
  }

  async function getOrgInfo() {
    try {
      const response = await axios.get(`https://api.${getMainDomain()}/orgs/self`);
      setOrgSettings(response.data?.org_settings);
      // return response.data;
    } catch (err) {
      console.log(err);
      // return Promise.reject(err.response.data);
    }
  }

  async function fetchCluster() {
    try {
      const response = await axios.get(`https://${getControllerDomain()}/api/solution`, {
        withCredentials: true,
      });
      setLoadingCluster(false);
      // for azure, gshell is not supported
      const clusters = response.data.Result.filter((cluster: ClusterItem) => !isDisableTool(currentTool, cluster));
      const supportSwitchClusters = clusters.filter((cluster: ClusterItem) =>
        isClusterSupportTool(cluster, currentTool)
      );
      setClusters(supportSwitchClusters);
    } catch (error) {
      console.log(error);
    }
  }

  useEffect(() => {
    getMe();
    getOrgInfo();
    fetchCluster();
    // eslint-disable-next-line
  }, []);

  const { license, refetchLicense } = useLicense();
  useEffect(() => {
    if (isToolsOpen) {
      refetchLicense();
    }
  }, [isToolsOpen, refetchLicense]);

  // judge if the language popover is open, if open, profile popover won't close automatically
  let isLanguageOpen = false;

  const closeAllPopovers = () => {
    controlRef.current?.setDropdownOpen(false);
    setIsProfileOpen(false);
    setIsToolsOpen(false);
    setIsHelpOpen(false);
  };

  const initIframeClickListener = () => {
    setInterval(function () {
      const elem = document.activeElement;
      if (elem && elem.tagName === 'IFRAME') {
        closeAllPopovers();
      }
    }, 200);
  };

  const initLanguage = () => {
    const language = getToolLanguage() || LanguageType.English;
    language && setCurrentLanguage(language);
  };

  useEffect(() => {
    initIframeClickListener();
    initLanguage();
    // eslint-disable-next-line
  }, []);

  useEffect(() => {
    const currentCluster = clusters.filter((cluster: ClusterItem) => cluster.ID === clusterId)[0];
    currentCluster && onChangeCluster && onChangeCluster(currentCluster);
    currentCluster && setSelectedCluster([{ ...currentCluster, label: currentCluster.Name }]);
    // eslint-disable-next-line
  }, [clusters, clusterId]);

  const gotoSolutionSelectPage = () => {
    if (onRedirectToSelectCluster) {
      onRedirectToSelectCluster();
    } else {
      let returnUrl = '';
      if (window.location.origin.includes('tgcloud-dev.com')) {
        returnUrl = 'https://classic.tgcloud-dev.com';
      } else if (window.location.origin.includes('tgcloud.io')) {
        returnUrl = 'https://classic.tgcloud.io';
      }
      window.location.replace(`${returnUrl}/app/tools/${currentTool}`);
    }
  };

  const handleLogout = (needReturnBack = false) => {
    onHandleLogout && onHandleLogout();
    Promise.all(
      clusters.map((cluster: ClusterItem) => {
        return axios.post(
          `https://${cluster.Domain}/api/auth/logout`,
          {},
          {
            withCredentials: true,
          }
        );
      })
    ).finally(() => {
      setIsProfileOpen(false);
      let returnUrl = '';
      const querystring = new URLSearchParams({ logOut: 'true' });
      querystring.append('returnTo', encodeURIComponent(window.location.href));

      if (window.location.origin.includes('tgcloud-dev.com')) {
        returnUrl = `https://classic.tgcloud-dev.com/?${querystring}`;
      } else if (window.location.origin.includes('tgcloud.io')) {
        returnUrl = `https://classic.tgcloud.io/?${querystring}`;
      }
      logout({ returnTo: returnUrl }); // logout from auth0
    });
  };

  function isClusterVersionHigherThan(cluster: ClusterItem, version: string) {
    return compare(cluster.Version, version, '>=');
  }

  function getHighVersionURL(cluster: ClusterItem, toolType: ToolType) {
    const domain = getDomain(cluster);
    if (isClusterVersionHigherThan(cluster, '3.9.0')) {
      if (cluster.ID === DEV_CLUSTER_ID_3_9_0) {
        return `${testToolOrigin}/${Type2Route[toolType]}/?clusterid=${cluster.ID}&domain=${
          domain.match(/[A-Za-z0-9-]{1,63}\.(privatelink|i)/i)[0]
        }&orgName=${getLocalstorageKey(LocalStorageKeys.orgName)}`;
      } else {
        return `${toolOrigin}/${Type2Route[toolType]}/?clusterid=${cluster.ID}&domain=${
          domain.match(/[A-Za-z0-9-]{1,63}\.(privatelink|i)/i)[0]
        }&orgName=${getLocalstorageKey(LocalStorageKeys.orgName)}`;
      }
    } else if (isClusterVersionHigherThan(cluster, '3.8.0')) {
      if (cluster.ID === DEV_CLUSTER_ID) {
        return `${testToolOrigin_380}/${Type2Route[toolType]}/?clusterid=${cluster.ID}&domain=${
          domain.match(/[A-Za-z0-9-]{1,63}\.(privatelink|i)/i)[0]
        }&orgName=${getLocalstorageKey(LocalStorageKeys.orgName)}`;
      } else {
        return `${toolOrigin_380}/${Type2Route[toolType]}/?clusterid=${cluster.ID}&domain=${
          domain.match(/[A-Za-z0-9-]{1,63}\.(privatelink|i)/i)[0]
        }&orgName=${getLocalstorageKey(LocalStorageKeys.orgName)}`;
      }
    } else {
      if (window.location.origin.includes('tgcloud-dev.com')) {
        return `https://classic.tgcloud-dev.com/app/tools/${toolType}/${cluster.ID}`;
      } else if (window.location.origin.includes('tgcloud.io')) {
        return `https://classic.tgcloud.io/app/tools/${toolType}/${cluster.ID}`;
      }
    }
  }

  function navigateToTool(toolType: ToolType, cluster: ClusterItem) {
    if (lowerVersion(cluster.Version)) {
      window.open(getAppURL(cluster, toolType), '_blank');
    } else if (toolType === ToolType.MLWorkbench) {
      window.open(getAppURL(cluster, toolType), '_blank');
    } else {
      window.location.replace(getHighVersionURL(cluster, toolType));
    }
  }

  const tool2Img = {
    [ToolType.GraphStudio]: (
      <img className={css({ height: '30px', width: '131px' })} src={graphstudiohorizontalsvg} alt="graphstudio" />
    ),
    [ToolType.AdminPortal]: (
      <img className={css({ height: '30px', width: '134px' })} src={adminportalhorizontalsvg} alt="adminportal" />
    ),
    [ToolType.GsqlShell]: (
      <img className={css({ height: '30px', width: '117px' })} src={gsqlshellhorizontalsvg} alt="gsqlshell" />
    ),
    [ToolType.GraphQL]: (
      <img className={css({ height: '30px', width: '110px' })} src={graphqlhorizontalsvg} alt="graphql" />
    ),
    [ToolType.Insights]: (
      <img className={css({ height: '30px', width: '175px' })} src={insightshorizontalsvg} alt="insights" />
    ),
    [ToolType.MLWorkbench]: (
      <img className={css({ height: '30px' })} src={machinelearninghorizontalsvg} alt="mlworkbench" />
    ),
  };

  const getValueLabel = ({ option }: { option: Option & ClusterItem }) => {
    return (
      <div
        className={css({
          lineHeight: '18px',
        })}
      >
        <span
          className={css({
            marginRight: '8px',
            fontSize: '14px',
            color: '#294560',
          })}
        >
          {option.label}
        </span>
        <span
          className={css({
            fontSize: '12px',
            color: '#656565',
          })}
        >
          (version: {option.Version})
        </span>
      </div>
    );
  };

  const getOptionLabel = ({ option }: any) => {
    const { isAll } = option;
    return isAll ? (
      // eslint-disable-next-line
      <a
        className={css({
          color: '#1564B1',
          display: 'block',
          borderBottom: '1px solid #D4DADF',
          padding: '8px',
        })}
        onClick={() => gotoSolutionSelectPage()}
      >
        View all cluster details
      </a>
    ) : (
      <div
        className={css({
          display: 'flex',
          justifyContent: 'space-between',
          padding: '8px',
        })}
      >
        <span
          className={css({
            color: '#222222',
          })}
        >
          {option.Name}
        </span>
        <span
          className={css({
            color: '#767676',
          })}
        >
          (version: {option.Version})
        </span>
      </div>
    );
  };

  const handleGotoTerms = () => {
    goto('https://www.tigergraph.com/tigergraph-cloud-subscription-terms/');
  };

  const ProfilePopover = () => (
    <div className={css(profileContainer)}>
      <div className={css({ padding: '16px 16px', borderBottom: 'solid 1px #D4DADF' })}>
        <div className={css(profileName)}>{user?.firstName + ' ' + user?.lastName}</div>
        <div className={css({ color: '#656565', marginTop: '4px', lineHeight: '16px' })}>{user?.email}</div>
        <div className={css(profileRole)}>{user?.roles[0]?.displayName}</div>
      </div>
      <div>
        {clusterId && ToolSupportSwitchLanguage.includes(currentTool) && (
          <StatefulPopover
            overrides={{
              Body: {
                style: () => ({
                  zIndex: 9999,
                  borderTopRightRadius: '5px',
                  borderTopLeftRadius: '5px',
                  borderBottomLeftRadius: '5px',
                  borderBottomRightRadius: '5px',
                }),
              },
              Inner: {
                style: () => ({
                  borderTopRightRadius: '5px',
                  borderTopLeftRadius: '5px',
                  borderBottomLeftRadius: '5px',
                  borderBottomRightRadius: '5px',
                }),
              },
            }}
            onOpen={() => (isLanguageOpen = true)}
            onClose={() => (isLanguageOpen = false)}
            triggerType={'hover'}
            content={({ close }) => (
              <LanguagePopover
                setIsProfileOpen={setIsProfileOpen}
                setCurrentLanguage={setCurrentLanguage}
                currentTool={currentTool}
                handleChangeLanguageCallback={handleChangeLanguageCallback}
              />
            )}
          >
            <div className={css(profileItem)}>
              <img src={languagesvg} className={css(profileIconStyle)} alt="" />
              <span>{Language2Label[currentLanguage]}</span>
              <svg
                className={css({ marginLeft: '100px' })}
                width="8"
                height="12"
                viewBox="0 0 8 12"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M1.29006 9.87998L5.17006 5.99998L1.29006 2.11998C0.900059 1.72998 0.900059 1.09998 1.29006 0.70998C1.68006 0.31998 2.31006 0.31998 2.70006 0.70998L7.29006 5.29998C7.68006 5.68998 7.68006 6.31998 7.29006 6.70998L2.70006 11.3C2.31006 11.69 1.68006 11.69 1.29006 11.3C0.910059 10.91 0.900059 10.27 1.29006 9.87998Z"
                  fill="#6A7D8F"
                />
              </svg>
            </div>
          </StatefulPopover>
        )}

        <div className={css(profileItem)} onClick={() => handleGotoTerms()}>
          <img src={assignmentsvg} className={css(profileIconStyle)} alt="" />
          <span>Terms and conditions</span>
        </div>

        <div className={css(profileItem)} onClick={() => handleLogout()}>
          <img src={signoutsvg} className={css(profileIconStyle)} alt="" />
          <span>Sign out</span>
        </div>
      </div>
    </div>
  );

  const handleClusterChange = (value: Value) => {
    if (value[0].isAll) {
      return;
    }
    setSelectedCluster((prevState) => {
      if (prevState.length > 0 && prevState[0].ID === value[0].ID) {
        return prevState;
      } else {
        if (value.length > 0) {
          if (onChangeCluster) {
            onChangeCluster(value[0] as ClusterItem);
          } else {
            navigateToTool(currentTool, value[0] as ClusterItem);
          }
        }
        return value;
      }
    });
  };

  const handleMenuCollapse = () => {
    const cloudSliderDom = document.getElementById(cloudSliderId);
    if (cloudSliderDom) {
      const sliderToDelete = cloudSliderDom;
      const parentNode = sliderToDelete.parentNode;
      const elementCopy = sliderToDelete.cloneNode(true);
      elementCopy.open = !sliderToDelete.open;
      parentNode.removeChild(sliderToDelete);
      parentNode.appendChild(elementCopy);
    }
    menuClickCallBack && menuClickCallBack();
  };

  const handleChangeTool = (tool: ToolType) => {
    if (onChangeTool) {
      onChangeTool(tool);
    }
  };

  const handleRedirectToToolHome = () => {
    if (onRedirectToToolHome) {
      onRedirectToToolHome();
    } else {
      if (window.location.origin.includes('tgcloud-dev.com')) {
        window.location.replace(`https://classic.tgcloud-dev.com/app/tools`);
      } else if (window.location.origin.includes('tgcloud.io')) {
        window.location.replace(`https://tgcloud.io/app/tools`);
      }
    }
  };

  return (
    <div className={css(headerStyles)}>
      {!currentTool && <IdleModal onLogout={() => handleLogout(true)} />}
      <div
        className={css({
          display: 'flex',
          alignItems: 'center',
        })}
      >
        <img
          src={menusvg}
          onClick={handleMenuCollapse}
          aria-label="Cloud_header"
          className={css({ margin: '18px 19px', cursor: 'pointer', width: '18px', height: '12px' })}
          alt=""
        />
        {!currentTool && <img src={logosvg} alt="" />}
        {currentTool && tool2Img[currentTool]}
        {currentTool && (
          <>
            <span
              className={css({
                borderRight: '1px solid #D4DADF',
                height: '25px',
                marginRight: '8px',
              })}
            ></span>
            <div
              className={css({
                minWidth: '76px',
                display: 'flex',
                alignItems: 'baseline',
              })}
            >
              <span
                className={css({
                  fontSize: '10px',
                  lineHeight: '16px',
                  fontWeight: '300',
                  color: '#546A80',
                  verticalAlign: 'sub',
                })}
              >
                on
              </span>
              <img
                alt=""
                className={css({
                  verticalAlign: 'sub',
                  width: '64px',
                })}
                src={letterlogopng}
              />
            </div>
          </>
        )}
      </div>

      <div
        className={css({
          display: 'flex',
          alignItems: 'center',
        })}
      >
        {clusterId && (
          <div
            className={css({
              width: '340px',
              display: 'flex',
              alignItems: 'center',
            })}
          >
            <Select
              isLoading={loadingCluster}
              controlRef={controlRef}
              autoFocus={true}
              options={[{ isAll: true, label: 'View all cluster details' }].concat(
                clusters.map((cluster) => ({
                  ...cluster,
                  label: cluster.Name,
                  isAll: false,
                }))
              )}
              placeholder={'Search cluster'}
              onChange={({ value }) => handleClusterChange(value)}
              value={selectedCluster}
              valueKey={'ID'}
              clearable={false}
              searchable={false}
              getValueLabel={getValueLabel}
              getOptionLabel={getOptionLabel}
              overrides={{
                Root: {
                  style: ({ $theme }) => ({
                    marginRight: '24px',
                    maxWidth: '300px',
                  }),
                },
                ValueContainer: {
                  style: ({ $theme }) => ({
                    height: '32px',
                  }),
                },
                Popover: {
                  props: {
                    overrides: {
                      Body: {
                        style: ({ $theme }) => ({
                          zIndex: 999,
                        }),
                      },
                    },
                  },
                },
                DropdownListItem: {
                  style: ({ $theme }) => ({
                    paddingLeft: '0',
                    paddingRight: '0',
                    paddingBottom: '0',
                    paddingTop: '0',
                  }),
                },
              }}
            />
            <Popover
              onClick={() => setIsToolsOpen(!isToolsOpen)}
              onClickOutside={(e) => setIsToolsOpen(false)}
              content={() => (
                <ToolsPopover
                  onChangeTool={handleChangeTool}
                  setIsToolsOpen={setIsToolsOpen}
                  onRedirectToToolHome={handleRedirectToToolHome}
                  selectedCluster={selectedCluster}
                  license={license}
                />
              )}
              isOpen={isToolsOpen}
              placement={PLACEMENT.bottomRight}
              overrides={{
                Body: {
                  style: ({ $theme }) => ({
                    zIndex: 999,
                  }),
                },
              }}
            >
              <img
                alt=""
                src={applicationsvg}
                className={css({
                  cursor: 'pointer',
                  width: '16px',
                  height: '16px',
                })}
              />
            </Popover>
          </div>
        )}
        <Popover
          onClick={() => setIsHelpOpen(!isHelpOpen)}
          onClickOutside={() => {
            setIsHelpOpen(false);
          }}
          placement={PLACEMENT.bottomRight}
          content={() => <HelpPopover currentTool={currentTool} />}
          isOpen={isHelpOpen}
          overrides={{
            Body: {
              style: ({ $theme }) => ({
                zIndex: 999,
              }),
            },
          }}
        >
          <img
            alt=""
            src={helpoutlinesvg}
            className={css({
              marginLeft: '10px',
              marginRight: '-6px',
              width: '20px',
              height: '20px',
              cursor: 'pointer',
              padding: '6px',
              boxSizing: 'content-box',
              ':active': {
                backgroundColor: '#D4DADF',
                borderRadius: '5px',
              },
            })}
          />
        </Popover>
        <Popover
          onClick={() => setIsProfileOpen(!isProfileOpen)}
          onClickOutside={(e) => {
            !isLanguageOpen && setIsProfileOpen(false);
          }}
          isOpen={isProfileOpen}
          placement={PLACEMENT.bottomRight}
          overrides={{
            Body: {
              style: ({ $theme }) => ({
                zIndex: 999,
              }),
            },
          }}
          content={() => <ProfilePopover />}
        >
          <img
            alt=""
            src={accountsvg}
            className={css({ margin: '8px 16px', cursor: 'pointer', width: '32px', height: '32px' })}
          />
        </Popover>
      </div>
    </div>
  );
};
