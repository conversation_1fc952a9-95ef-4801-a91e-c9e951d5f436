import { StyleObject } from 'styletron-standard';

export const profileContainer = {
  background: 'white',
  borderRadius: '4px',
  padding: '8px 0',
  minWidth: '230px',
} as StyleObject;

export const profileItem = {
  padding: '10px 16px',
  fontSize: '16px',
  ':hover': {
    background: '#EAECEF',
  },
  cursor: 'pointer',
  display: 'flex',
  alignItems: 'center',
  marginBottom: '4px',
  ':last-child': {
    marginBottom: '0px',
  },
} as StyleObject;

export const profileName = {
  fontFamily: 'Urbanist',
  fontWeight: 600,
  fontSize: '24px',
  color: '#294560',
  lineHeight: '40px',
} as StyleObject;

export const profileRole = {
  background: '#F1F2F4',
  borderRadius: '20px',
  padding: '4px 12px',
  display: 'inline-block',
  marginTop: '16px',
  color: '#3F5870',
} as StyleObject;

export const headerStyles = {
  position: 'relative',
  height: '47px', // because of the 1px height border
  lineHeight: '48px',
  background: '#FFFFFF',
  boxShadow: '0px 4px 10px rgb(25 118 210 / 5%)',
  borderBottom: '1px solid rgb(212, 218, 223)',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
};

export const profileIconStyle = {
  verticalAlign: 'text-bottom',
  marginRight: '16px',
  width: '20px',
} as StyleObject;
