const template = document.createElement('template');
template.innerHTML = `<style>
    .tools{
        height: 48px;
        padding: 0 16px;
        position: relative;
        background: #fff;
        border-bottom: 1px solid rgb(212, 218, 223);
        box-sizing: border-box;
        display: flex;
        align-item: center;
        justify-content: space-between;
    }
    .tools * {
      box-sizing: border-box;
    }
    .tools_left {
        width: auto;
        height: 48px;
        display: flex;
        align-items: center;
        font-size: 24px;
        color: #1976D2;
        font-family: 'Urbanist';
        font-style: normal;
        font-weight: 500;
        font-size: 24px;
        text-transform: capitalize;
    }
    .tools_box {
      display: flex;
    }
    .tools_version {
      color: #656565;
      font-size: 12px;
      line-height: 47px;
      margin-right: 20px;
    }
    .head-icon {
        height: 32px;
        border-radius: 16px;
        margin-right: 16px;
    }
    .tools_apps {
        margin-top: 11px;
        cursor: pointer;
        display: none;
        float: right;
        margin-bottom: 10px;
        margin-right: 28px;
    }
    .tools_apps-container {
        position: relative;
        color: #000;
    }
    .tools_apps-list-container {
        position: absolute;
        top: 4px;
        right: 0;
        z-index: 999;
        margin: 0 auto;
        background: #FFFFFE;
        box-shadow: -2px -2px 6px rgba(0, 0, 0, 0.1), 2px 2px 6px rgba(0, 0, 0, 0.1);
        border-radius: 4px;
        width: 376px;
    }
    .tools_apps-list {
        background: white;
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        box-sizing: border-box;
        padding: 16px;
        gap: 18px;
    }
    .tools_right {
        height: 48px;
        display: flex;
        align-items: center;
        color: #000;
        align-self: flex-end;
        margin-right: 4px;
    }
    .tools_right-menu {
        position: absolute;
        width: 216px;
        border-radius: 5px;
        background: #FFFFFF;
        box-shadow: -2px -2px 6px rgba(0, 0, 0, 0.1), 2px 2px 6px rgba(0, 0, 0, 0.1);
        top: 45px;
        right: 0px;
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 400;
        font-size: 16px;
        line-height: 24px;
        color: #222222;
    }
    .tools_right-item {
        outline: 0;
        border: none;
        -webkit-tap-highlight-color: transparent;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        line-height: 48px;
        height: 48px;
        padding: 0 16px;
        text-align: left;
        text-decoration: none;
        max-width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    .tools_right-item .icon {
      width: 20px;
      height: 20px;
      margin-right: 10px;
    }
    .tools_right-item .desc {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .tools_right-item .left {
      flex: 1;
      display: flex;
      align-items: center;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .tools_right-item .toggle-btn {
      width: 10px;
      height: 10px;
      flex-shrink: 0;
    }
    .tools_right-item .theme-label {
      flex-shrink: 0;
    }
    .clickable:hover {
        cursor: pointer;
        background: #E8F1FB;
    }
    .tools_right-icon {
        width: 32px;
        height: 32px;
        border-radius: 16px;
        overflow: hidden;
        cursor: pointer;
    }
    .tools_apps-item {
        flex-direction: column;
        width: 104px;
        height: 80px;
        user-select: none;
        border: 1px solid #D4DADF;
        border-radius: 10px;
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 16px;
        display: flex;
        align-items: center;
        text-align: center;
        justify-content: center;
        color: #222222;
        cursor: pointer;
    }
    .tools_apps-item img {
      margin-bottom:5px;
      width: 96px;
      height: 64px;
    }
    .tools-icon {
        width: 32px;
        height: 32px;
        border-radius: 16px;
    }
    .tools_apps-list a {
        text-decoration: none;
        color: #000;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        overflow: hidden;
    }
    .tools_apps-bottom {
      width: 100%;
      height: 55px;
      border-top: 2px solid rgba(212, 218, 223, 0.4);
      text-align: center;
      line-height: 55px;
      display: block;
      font-family: 'Roboto';
      font-style: normal;
      font-weight: 600;
      font-size: 16px;
      letter-spacing: 0.02em;
      text-transform: capitalize;
      color: #115AA0;
      text-decoration: none;
    }
    .tools-head-icon {
        width: 32px;
        height: 32px;
    }
    .language-btn:hover .language-drop-down {
        display: block;
    }
    .language-drop-down, .license-drop-down {
        position: absolute;
        z-index: 999;
        box-shadow: 0px 0px 8px rgb(0 0 0 / 30%);
        transform: translate(-100%, 0px);
        margin-left: -16px;
        border-radius: 5px;
        background: white;
        display: none;
    }
    .language-drop-down .language-drop-down-btn {
        cursor: pointer;
        text-align: left;
        padding: 0 8px;
        display: block;
        text-decoration: none;
        z-index: 999;
    }
    .language-drop-down-btn:hover {
        background: #E8F1FB;
    }
    .license-btn:hover .license-drop-down {
      display: block;
    }
    .license-drop-down-item {
      cursor: initial;
      margin: 8px 16px;
      display: flex;
      align-items: flex-start;
      line-height: 20px;
    }
    .license-drop-down-item>svg {
      width: 20px;
      margin-right: 8px;
    }
    .info-label {
      color: #115AA0;
    }
    .theme-label {
      position: absolute;
      right: 11px;
    }
    .preview {
        background: #294560;
        font-size: 10px;
        color: #FFFFFE;
        position: absolute;
        transform: rotate(45deg);
        height: 18px;
        width: 145px;
        text-align: center;
        line-height: 18px;
        right: -54px;
        top: 12px;
    }
    #banner {
      position: fixed;
      top: 48px;
      left: 0;
      right: 0;
      display: flex;
      justify-content: center;
      padding: 5px 0;
      color: #fff;
      background-color: rgba(236, 74, 10, 0.6);
      z-index: 9999;
      transition: opacity 0.5s ease-in-out;
      display: none;
      font-size: 13px;
      font-family: 'Roboto';
    }
    #banner p {
      margin: 0;
    }
    #banner #rec-list {
      margin: 5px 0 0 0;
    }
    #banner #close-btn {
      position: absolute;
      top: 2px;
      right: 6px;
      border: none;
      background: transparent; 
      font-size: 1.5em;
      cursor: pointer;
    }
    #close-btn::before,
    #close-btn::after {
      content: "";
      position: absolute;
      width: 2px;
      height: 16px;
      background-color: #000;
    }

    #close-btn::before {
      transform: rotate(45deg);
    }

    #close-btn::after {
      transform: rotate(-45deg);
    }
    @media screen and (max-width: 396px) {
      .tools_right-menu {
        width: 164px;
        right: -14px;
      }
      .tools_apps-item {
        width: 88px;
        height: 88px;
      }
      .tools_apps-list-container {
        width: 320px; 
      }
      .tools_apps-list {
        gap: 14px;
      }
    }
    </style>
    <div class="tools">
        <div class="tools_left">
            <img class="head-icon" src="/favicon.ico"/>
        </div>
        <div class="tools_box">
        <div class="tools_version"></div>
        <div class="tools_apps">
            <svg class="tools_apps-btn" style="width: 24px; height: 24px; color: #5f6368; opacity: 0.6;" focusable="false" viewBox="0 0 24 24">
              <path class="tools_apps-btn-path" d="M6,8c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM12,20c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM6,20c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM6,14c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM12,14c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM16,6c0,1.1 0.9,2 2,2s2,-0.9 2,-2 -0.9,-2 -2,-2 -2,0.9 -2,2zM12,8c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM18,14c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM18,20c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2z"></path>
              </svg>
            <div class="tools_apps-container">
            </div>
        </div>
        <div class="tools_right">
            <div class="tools_right-icon">
                <img class="tools-head-icon" src="/user.svg"/>
            </div>
            <div class="tools_right-container">
            </div>
        </div>
        </div>
        <div id="banner">
          <button id="close-btn" aria-label="close-banner-button"></button>
          <div>
            <p>Please take the following steps to improve your TG system security:
            </p>
            <ul id="rec-list">
            </ul>
          </div>
        </div>
    </div>`;
class ToolsHead extends HTMLElement {
  appListT = `
    <div class="tools_apps-list-container">
      <div class="tools_apps-list">
        <a href="/studio/">
          <div class="tools_apps-item">
            <img src="/gst.svg">
          </div>
        </a>
        <a href="/insights/">
          <div class="tools_apps-item">
          <img src="/insights.svg">
          </div>
        </a>
        <a href="/gsql/">
          <div class="tools_apps-item">
            <img src="/shell.svg">
          </div>
        </a>
        <a href="/admin/">
          <div class="tools_apps-item">
            <img src="/admin.svg">
          </div>
        </a>
        <a href="/graphql/">
          <div class="tools_apps-item">
            <img src="/QL.svg">
          </div>
        </a>
      </div>
      <a href="/" class="tools_apps-bottom">
        Go to TigerGraph Suite
      </a>
    </div>`;

  menuListT = ``;

  appListDisplay = false;
  menuListDisplay = false;
  shadowRoot = null;

  constructor() {
    super();
    this._license = {
      Audience: '',
      EndTime: '',
      Status: '',
    };
    this._appName = '';
    this._userName = '';
    this._isDark = false;
    this._appIcon = '';
    this._innerApp = '';
    this._language = 'en-US';
  }

  get appName() {
    return this._appName;
  }

  set appName(appName) {
    this._appName = appName;
    this.setMenuList();
  }

  get userName() {
    return this._userName;
  }

  set userName(userName) {
    this._userName = userName;
    this.setMenuList();
  }

  get appIcon() {
    return this._appIcon;
  }

  set appIcon(appIcon) {
    this.shadowRoot.querySelector('.head-icon').setAttribute('src', appIcon);
    this._appIcon = appIcon;
  }

  get innerApp() {
    return this._innerApp;
  }

  set innerApp(app) {
    this._innerApp = app;
    const $appBtn = this.shadowRoot.querySelector('.tools_apps');
    if (app !== 'home') {
      $appBtn.style.display = 'block';
    } else {
      $appBtn.style.display = 'none';
    }
  }

  get license() {
    return this._license;
  }

  set license(license) {
    this._license = license ? { ...license } : null;
    this.setMenuList();
  }

  get isDark() {
    return this._isDark;
  }

  set isDark(isDark) {
    this._isDark = isDark;
    this.setMenuList();
  }

  get language() {
    return this._language;
  }

  set language(language) {
    this._language = language;
    this.setMenuList();
  }

  showSecurityBanner() {
    const storageKey = 'TigerGraphSecurityRecommendations';
    const banner = this.shadowRoot.getElementById('banner');
    const closeBtn = this.shadowRoot.getElementById('close-btn');

    const showBanner = () => {
      closeBtn.addEventListener('click', () => {
        banner.style.opacity = 0;
        localStorage.removeItem(storageKey);
        setTimeout(() => {
          banner.style.display = 'none';
        }, 500);
      });

      banner.style.display = 'flex';
      banner.style.opacity = 1;
    };

    let securityRecs;
    try {
      securityRecs = JSON.parse(localStorage.getItem(storageKey));
    } catch (error) {
      //
    }
    if (Array.isArray(securityRecs)) {
      securityRecs.forEach((rec) => {
        const container = this.shadowRoot.querySelector('#banner #rec-list');
        const recItem = document.createElement('li');
        recItem.innerText = rec;
        container.appendChild(recItem);
      });
      showBanner();
    }
  }

  setAppListPos() {
    const $applistContainer = this.shadowRoot.querySelector('.tools_apps-container');
    const $applist = this.shadowRoot.querySelector('.tools_apps-list-container');
    const applistWidth = $applist.clientWidth;
    $applist.style.right = Math.min($applistContainer.getBoundingClientRect().right - applistWidth, 0) + 'px';
  }

  connectedCallback() {
    this.shadowRoot = this.attachShadow({ mode: 'open' });
    this.shadowRoot.appendChild(template.content.cloneNode(true));

    this.setMenuList();

    const $appbutton = this.shadowRoot.querySelector('.tools_apps');
    const $applist = this.shadowRoot.querySelector('.tools_apps-container');
    this.$applist = $applist;
    window.addEventListener('resize', () => {
      if (this.appListDisplay) {
        this.setAppListPos();
      }
    });
    $appbutton.addEventListener(
      'click',
      () => {
        if (!this.appListDisplay) {
          $applist.innerHTML = this.appListT;
          this.setAppListPos();
        } else {
          $applist.innerHTML = '';
        }
        this.appListDisplay = !this.appListDisplay;
      },
      false
    );
    const $iconbutton = this.shadowRoot.querySelector('.tools_right-icon');
    const $menulist = this.shadowRoot.querySelector('.tools_right-container');
    this.$menulist = $menulist;
    $iconbutton.addEventListener('click', (e) => {
      if (!this.menuListDisplay) {
        $menulist.innerHTML = this.menuListT;
        this.initMenuEvent(this.shadowRoot);
        this.initLanguageEvent(this.shadowRoot);
      } else {
        $menulist.innerHTML = '';
      }
      this.menuListDisplay = !this.menuListDisplay;
    });
    const $tools = this.shadowRoot.querySelector('.tools');
    $tools.addEventListener(
      'click',
      (e) => {
        const toolsApp = this.shadowRoot.querySelector('.tools_apps');
        if (this.appListDisplay && !toolsApp.contains(e.target)) {
          this.clearList();
        }
        const toolsMenu = this.shadowRoot.querySelector('.tools_right');
        if (this.menuListDisplay && !toolsMenu.contains(e.target)) {
          this.clearList();
        }
      },
      true
    );

    document.addEventListener('click', (e) => {
      if (this.appListDisplay && e.target.tagName.toLowerCase() !== 'tools-head') {
        this.clearList();
      }
      if (this.menuListDisplay && e.target.tagName.toLowerCase() !== 'tools-head') {
        this.clearList();
      }
    });
    this.injectFont();
    this.getTigerGraphVersion();

    this.showSecurityBanner();
  }

  clearList() {
    this.$applist.innerHTML = '';
    this.$menulist.innerHTML = '';
    this.menuListDisplay = false;
    this.appListDisplay = false;
  }

  initMenuEvent(shadowRoot) {
    const $logoutbutton = shadowRoot.querySelector('.logout');
    const languageBtn = shadowRoot.querySelector('.language-btn');
    const languageDropdown = shadowRoot.querySelector('.language-drop-down');
    const licenseBtn = shadowRoot.querySelector('.license-btn');
    const licenseDropdown = shadowRoot.querySelector('.license-drop-down');
    const clearDropdown = () => {
      languageDropdown && (languageDropdown.style.display = 'none');
      licenseDropdown && (licenseDropdown.style.display = 'none');
    };
    shadowRoot.querySelector('.userName').innerText = this.userName;
    $logoutbutton.addEventListener('click', () => {
      clearDropdown();
      window.location.href = `/#/login?returnURL=${encodeURIComponent('/' + this.innerApp + '/')}&loggedOut=true`;
      document.dispatchEvent(new CustomEvent('toolsevent', { detail: 'logout' }));
    });

    if (languageBtn) {
      languageBtn.addEventListener('click', () => {
        languageDropdown.style.display = 'block';
        licenseDropdown && (licenseDropdown.style.display = 'none');
      });
      languageBtn.addEventListener('mouseenter', () => {
        languageDropdown.style.display = 'block';
        licenseDropdown && (licenseDropdown.style.display = 'none');
      });
      languageBtn.addEventListener('mouseleave', () => {
        languageDropdown.style.display = 'none';
      });
    }
    if (licenseBtn) {
      licenseBtn.addEventListener('click', () => {
        licenseDropdown.style.display = 'block';
        languageDropdown && (languageDropdown.style.display = 'none');
      });
      licenseBtn.addEventListener('mouseenter', () => {
        licenseDropdown.style.display = 'block';
        languageDropdown && (languageDropdown.style.display = 'none');
      });
      licenseBtn.addEventListener('mouseleave', () => {
        licenseDropdown.style.display = 'none';
      });
    }
  }
  initLanguageEvent(shadowRoot) {
    const $changeToEnglish = shadowRoot.querySelector('.english');
    const $changeToChinese = shadowRoot.querySelector('.chinese');
    const $changeToJapanese = shadowRoot.querySelector('.japanese');

    $changeToEnglish?.addEventListener('click', () => {
      this.chooseLanguage('en-US');
      this.clearList();
    });

    $changeToChinese?.addEventListener('click', () => {
      this.chooseLanguage('zh-Hans');
      this.clearList();
    });

    $changeToJapanese?.addEventListener('click', () => {
      this.chooseLanguage('ja-Jpan');
      this.clearList();
    });
  }
  chooseLanguage(language) {
    this.dispatchEvent(
      new CustomEvent('changelanguage', {
        bubbles: true,
        detail: {
          language,
        },
      })
    );
    this._language = language;
    this.setMenuList();
  }
  parseDate(timeStamp) {
    return new Date(timeStamp).toISOString().split('T')[0];
  }
  setMenuList() {
    const languageMapText = {
      'en-US': 'English',
      'zh-Hans': '简体中文',
      'ja-Jpan': '日本語',
    };
    const helpLinkMapApp = {
      GraphStudio: 'https://docs.tigergraph.com/gui/current/graphstudio/overview',
      AdminPortal: 'https://docs.tigergraph.com/gui/current/admin-portal/overview',
      'GSQL Shell': 'https://docs.tigergraph.com/tigergraph-server/current/gsql-shell/web',
      GraphQL: 'https://docs.tigergraph.com/graphql/current/',
      Insights: 'https://docs.tigergraph.com/insights/current',
    };
    this.menuListT = `<div class="tools_right-menu">
      <div class="tools_right-item">
        <div class="left" title=${this.userName}>
          <svg class="icon" width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M10 0C4.48 0 0 4.48 0 10C0 15.52 4.48 20 10 20C15.52 20 20 15.52 20 10C20 4.48 15.52 0 10 0ZM10 3C11.66 3 13 4.34 13 6C13 7.66 11.66 9 10 9C8.34 9 7 7.66 7 6C7 4.34 8.34 3 10 3ZM10 17.2C7.5 17.2 5.29 15.92 4 13.98C4.03 11.99 8 10.9 10 10.9C11.99 10.9 15.97 11.99 16 13.98C14.71 15.92 12.5 17.2 10 17.2Z" fill="#3F5870"/>
          </svg>
          <span class="desc userName"></span>
        </div>
      </div>
      <div class="tools_right-item logout clickable">
        <div class="left">
          <svg class="icon" width="20" height="18" viewBox="0 0 20 18" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M15 4L13.59 5.41L16.17 8H6V10H16.17L13.59 12.58L15 14L20 9L15 4ZM2 2H10V0H2C0.9 0 0 0.9 0 2V16C0 17.1 0.9 18 2 18H10V16H2V2Z" fill="#3F5870"/>
          </svg>      
          <span class="desc">Log out</span>
        </div>
      </div>
      ${
        this.appName === 'GraphStudio' || this.appName === 'AdminPortal'
          ? `
      <div class="tools_right-item clickable language-btn">
        <div class="left">
          <svg class="icon" width="22" height="20" viewBox="0 0 22 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M11.65 13.67C11.79 13.31 11.7 12.9 11.42 12.62L9.33 10.56L9.36 10.53C11.1 8.59 12.34 6.36 13.07 4H15.01C15.55 4 16 3.55 16 3.01V2.99C16 2.45 15.55 2 15.01 2H9V1C9 0.45 8.55 0 8 0C7.45 0 7 0.45 7 1V2H0.99C0.45 2 0 2.45 0 2.99C0 3.54 0.45 3.98 0.99 3.98H11.17C10.5 5.92 9.44 7.75 8 9.35C7.19 8.46 6.51 7.49 5.94 6.47C5.78 6.18 5.49 6 5.16 6C4.47 6 4.03 6.75 4.37 7.35C5 8.48 5.77 9.56 6.67 10.56L2.3 14.87C1.9 15.26 1.9 15.9 2.3 16.29C2.69 16.68 3.32 16.68 3.72 16.29L8 12L10.02 14.02C10.53 14.53 11.4 14.34 11.65 13.67ZM16.5 8C15.9 8 15.36 8.37 15.15 8.94L11.48 18.74C11.24 19.35 11.7 20 12.35 20C12.74 20 13.09 19.76 13.23 19.39L14.12 17H18.87L19.77 19.39C19.91 19.75 20.26 20 20.65 20C21.3 20 21.76 19.35 21.53 18.74L17.86 8.94C17.64 8.37 17.1 8 16.5 8ZM14.88 15L16.5 10.67L18.12 15H14.88Z" fill="#3F5870"/>
          </svg>
          <span class="desc">${languageMapText[this._language]}</span>
        </div>
        <svg class="toggle-btn" viewBox="0 0 8 12" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M1.28859 9.88047L5.16859 6.00047L1.28859 2.12047C0.898594 1.73047 0.898594 1.10047 1.28859 0.710469C1.67859 0.320469 2.30859 0.320469 2.69859 0.710469L7.28859 5.30047C7.67859 5.69047 7.67859 6.32047 7.28859 6.71047L2.69859 11.3005C2.30859 11.6905 1.67859 11.6905 1.28859 11.3005C0.908594 10.9105 0.898594 10.2705 1.28859 9.88047Z" fill="#6A7D8F"/>
        </svg>
        <div class="language-drop-down">
          <div class="language-drop-down-btn english">🇺🇸 English</div>
          <div class="language-drop-down-btn chinese">🇨🇳 简体中文</div>
          <div class="language-drop-down-btn japanese">🇯🇵 日本語</div>
        </div>
      </div>
      ${
        this.appName === 'GraphStudio'
          ? `<div class="tools_right-item clickable license-btn">
        <div class="left">
          <svg class="icon" width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M22 10.6067L19.56 7.81669L19.9 4.12669L16.29 3.30669L14.4 0.106689L11 1.56669L7.6 0.106689L5.71 3.29669L2.1 4.10669L2.44 7.80669L0 10.6067L2.44 13.3967L2.1 17.0967L5.71 17.9167L7.6 21.1067L11 19.6367L14.4 21.0967L16.29 17.9067L19.9 17.0867L19.56 13.3967L22 10.6067ZM8.38 14.6167L6 12.2167C5.61 11.8267 5.61 11.1967 6 10.8067L6.07 10.7367C6.46 10.3467 7.1 10.3467 7.49 10.7367L9.1 12.3567L14.25 7.19669C14.64 6.80669 15.28 6.80669 15.67 7.19669L15.74 7.26669C16.13 7.65669 16.13 8.28669 15.74 8.67669L9.82 14.6167C9.41 15.0067 8.78 15.0067 8.38 14.6167Z" fill="#3F5870"/>
          </svg>
          <span class="desc">License</span>
        </div>
        <svg class="toggle-btn" viewBox="0 0 8 12" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M1.28859 9.88047L5.16859 6.00047L1.28859 2.12047C0.898594 1.73047 0.898594 1.10047 1.28859 0.710469C1.67859 0.320469 2.30859 0.320469 2.69859 0.710469L7.28859 5.30047C7.67859 5.69047 7.67859 6.32047 7.28859 6.71047L2.69859 11.3005C2.30859 11.6905 1.67859 11.6905 1.28859 11.3005C0.908594 10.9105 0.898594 10.2705 1.28859 9.88047Z" fill="#6A7D8F"/>
        </svg>
      <div class="license-drop-down">
      ${
        this.license
          ? `<div class="license-drop-down-item">
      <svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M22 10.6067L19.56 7.81669L19.9 4.12669L16.29 3.30669L14.4 0.106689L11 1.56669L7.6 0.106689L5.71 3.29669L2.1 4.10669L2.44 7.80669L0 10.6067L2.44 13.3967L2.1 17.0967L5.71 17.9167L7.6 21.1067L11 19.6367L14.4 21.0967L16.29 17.9067L19.9 17.0867L19.56 13.3967L22 10.6067ZM8.38 14.6167L6 12.2167C5.61 11.8267 5.61 11.1967 6 10.8067L6.07 10.7367C6.46 10.3467 7.1 10.3467 7.49 10.7367L9.1 12.3567L14.25 7.19669C14.64 6.80669 15.28 6.80669 15.67 7.19669L15.74 7.26669C16.13 7.65669 16.13 8.28669 15.74 8.67669L9.82 14.6167C9.41 15.0067 8.78 15.0067 8.38 14.6167Z" fill="#3F5870"/>
      </svg>
      <div>           
        <span style="display:block;">Licensed to</span>
        <span style="display:block;" class="info-label">${this.license.Audience}</span>
      </div>
    </div>
    <div class="license-drop-down-item">
      <svg width="18" height="21" viewBox="0 0 18 21" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M16 2.60669H15V1.60669C15 1.05669 14.55 0.606689 14 0.606689C13.45 0.606689 13 1.05669 13 1.60669V2.60669H5V1.60669C5 1.05669 4.55 0.606689 4 0.606689C3.45 0.606689 3 1.05669 3 1.60669V2.60669H2C0.89 2.60669 0.00999999 3.50669 0.00999999 4.60669L0 18.6067C0 19.7067 0.89 20.6067 2 20.6067H16C17.1 20.6067 18 19.7067 18 18.6067V4.60669C18 3.50669 17.1 2.60669 16 2.60669ZM16 17.6067C16 18.1567 15.55 18.6067 15 18.6067H3C2.45 18.6067 2 18.1567 2 17.6067V7.60669H16V17.6067ZM4 9.60669H6V11.6067H4V9.60669ZM8 9.60669H10V11.6067H8V9.60669ZM12 9.60669H14V11.6067H12V9.60669Z" fill="#3F5870"/>
      </svg>
      <div>           
        <span style="display:block;">Expiration date</span>
        <span style="display:block;" class="info-label">${this.parseDate(this.license.EndTime * 1000)}</span>
      </div>
    </div>`
          : ``
      }
        <div class="license-drop-down-item">
          <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M10 0.606689C4.48 0.606689 0 5.08669 0 10.6067C0 16.1267 4.48 20.6067 10 20.6067C15.52 20.6067 20 16.1267 20 10.6067C20 5.08669 15.52 0.606689 10 0.606689ZM10 15.6067C9.45 15.6067 9 15.1567 9 14.6067V10.6067C9 10.0567 9.45 9.60669 10 9.60669C10.55 9.60669 11 10.0567 11 10.6067V14.6067C11 15.1567 10.55 15.6067 10 15.6067ZM11 7.60669H9V5.60669H11V7.60669Z" fill="#3F5870"/>
          </svg>
          <div>           
            <span style="display:block;">Status</span>
            ${
              this.license
                ? `<span style="display:block;" class="info-label">${this.license.Status}</span>`
                : `<span style="display:block;color: red;" class="info-label">Invalid</span>`
            }
          </div>
        </div>
      </div>
    </div>`
          : ''
      }
      `
          : ''
      }
      ${
        this.appName !== 'TigerGraph Suite'
          ? `<a style="text-decoration: none; color: #000" target="_blank" href="${helpLinkMapApp[this.appName]}">
        <div class="tools_right-item clickable">
          <div class="left">
            <svg class="icon" width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M10 0C4.48 0 0 4.48 0 10C0 15.52 4.48 20 10 20C15.52 20 20 15.52 20 10C20 4.48 15.52 0 10 0ZM11 17H9V15H11V17ZM13.07 9.25L12.17 10.17C11.45 10.9 11 11.5 11 13H9V12.5C9 11.4 9.45 10.4 10.17 9.67L11.41 8.41C11.78 8.05 12 7.55 12 7C12 5.9 11.1 5 10 5C8.9 5 8 5.9 8 7H6C6 4.79 7.79 3 10 3C12.21 3 14 4.79 14 7C14 7.88 13.64 8.68 13.07 9.25Z" fill="#3F5870"/>
            </svg>
            <span class="desc">Help</span>
          </div>
        </div>
      </a>`
          : ''
      }
      <a style="text-decoration: none; color: #000" target="_blank" href="https://docs.tigergraph.com/gui/current/graphstudio/patent-and-third-party-notice">
        <div class="tools_right-item third-party-notice clickable">
          <div class="left">
            <svg class="icon" width="18" height="20" viewBox="0 0 18 20" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M16 2H11.82C11.4 0.84 10.3 0 9 0C7.7 0 6.6 0.84 6.18 2H2C0.9 2 0 2.9 0 4V18C0 19.1 0.9 20 2 20H16C17.1 20 18 19.1 18 18V4C18 2.9 17.1 2 16 2ZM9 2C9.55 2 10 2.45 10 3C10 3.55 9.55 4 9 4C8.45 4 8 3.55 8 3C8 2.45 8.45 2 9 2ZM10 16H5C4.45 16 4 15.55 4 15C4 14.45 4.45 14 5 14H10C10.55 14 11 14.45 11 15C11 15.55 10.55 16 10 16ZM13 12H5C4.45 12 4 11.55 4 11C4 10.45 4.45 10 5 10H13C13.55 10 14 10.45 14 11C14 11.55 13.55 12 13 12ZM13 8H5C4.45 8 4 7.55 4 7C4 6.45 4.45 6 5 6H13C13.55 6 14 6.45 14 7C14 7.55 13.55 8 13 8Z" fill="#3F5870"/>
            </svg>
            <span class="desc">Third-party notice</span>
          </div>
        </div>
      </a>
    </div>`;
  }
  injectFont() {
    const css = `@font-face {
      font-family: Urbanist;
      font-display: block;
      src: url("/Urbanist.ttf")
    }
    @font-face {
      font-family: 'Roboto';
      font-display: block;
      src: url('/Roboto-Regular.ttf');
    }`;
    //if our style is already injected we do not need to inject it a second time
    if (!document.getElementById('myCustomFontStyle')) {
      const head = document.head || document.getElementsByTagName('head')[0],
        style = document.createElement('style');
      style.id = 'myCustomFontStyle';
      style.type = 'text/css';
      style.innerText = css;
      head.appendChild(style);
    }
  }
  getTigerGraphVersion() {
    const $version = this.shadowRoot.querySelector('.tools_version');
    fetch('/api/version')
      .then((response) => response.json())
      .then((data) => {
        $version.innerHTML = `Version: ${data.results && data.results.tigergraph_version}`;
      });
  }
}

customElements.define('tools-head', ToolsHead);

console.log('===============================================================');
console.log(' _______                 ______                 __');
console.log(' /_  __(_)___ ____  _____/ ____/________ _____  / /_');
console.log('  / / / / __ `/ _ \\/ ___/ / __/ ___/ __ `/ __ \\/ __ \\');
console.log(' / / / / /_/ /  __/ /  / /_/ / /  / /_/ / /_/ / / / /');
console.log('/_/ /_/\\__, /\\___/_/   \\____/_/   \\__,_/ .___/_/ /_/');
console.log('     /____/                          /_/');
console.log('===============================================================');
// eslint-disable-next-line no-undef
console.log('TigerGraph Suite ' + $TOOLS_RELEASE_VERSION + '.' + $BUILD_NUMBER);
console.log('Powered by TigerGraph');
