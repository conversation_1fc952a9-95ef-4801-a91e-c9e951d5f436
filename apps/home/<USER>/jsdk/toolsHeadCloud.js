function getHashSearchParam(key) {
  const url = window.location.href;
  const hash = url.substring(url.indexOf('#') + 1);
  const searchIndex = hash.indexOf('?');
  const search = searchIndex !== -1 ? hash.substring(searchIndex + 1) : '';
  const usp = new URLSearchParams(search);
  console.log('getHashSearchParam', url, hash, search);
  return usp.get(key) || '';
}

function checkURLSecurity(domain) {
  if (!domain) return false;
  const idRegRule = /^[A-Za-z0-9-]{1,63}\.i$/;
  const idRegRulePrivate = /^[A-Za-z0-9-]{1,63}\.privatelink$/;
  const idRegRuleV4 =
    /^tg-[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}\.[a-zA-Z0-9.-]+\.(privatelink|i|byoc)$/;
  const checkPass = idRegRule.test(domain) || idRegRulePrivate.test(domain) || idRegRuleV4.test(domain);
  let baseUrl = '';
  if (checkPass && window.location.origin.includes('tgcloud-dev.com')) {
    baseUrl = `https://${domain}.tgcloud-dev.com`;
  } else if (checkPass && window.location.origin.includes('tgcloud.io')) {
    baseUrl = `https://${domain}.tgcloud.io`;
  } else {
    baseUrl = false;
  }
  return baseUrl;
}

function setPageBaseUrl() {
  let $baseUrl = checkURLSecurity(getHashSearchParam('domain'));
  const $sessionBaseUrl = sessionStorage.getItem('BASEURL');
  $baseUrl = $baseUrl ? decodeURIComponent($baseUrl) : $sessionBaseUrl || window.location.origin;
  sessionStorage.setItem('BASEURL', $baseUrl);
}

function initCloudParams(param, key) {
  let paramValue = getHashSearchParam(param);
  const $sessionValue = sessionStorage.getItem(key);
  paramValue = paramValue ? decodeURIComponent(paramValue) : $sessionValue;
  if (paramValue) {
    sessionStorage.setItem(key, paramValue);
  }
}

// function loadCloudHeaderComponent() {
//   // Get the origin of the current page, e.g. 'https://tools.tgcloud.io'
//   const url = window.location.origin;
//   // Extract the main domain from the origin, e.g. 'tgcloud.io'
//   const match = url.match(/\.([^./]+\.[^./]+)$/);
//   const currentDomain = match ? match[1] : "tgcloud.io"; // Use default 'tgcloud.io' if unable to extract
//   const scriptElement = document.createElement("script");
//   const cloudHeaderUrl = `https://${currentDomain}/assets/cloudHeader.min.js?v=${getCurrentVersion()}`;
//   scriptElement.src = cloudHeaderUrl;
//   document.body.appendChild(scriptElement);
// }

setPageBaseUrl();
initCloudParams('orgName', 'ORGNAME');
initCloudParams('clusterid', 'CURRENTCLUSTERID');
initCloudParams('groupid', 'CURRENTGROUPID');
initCloudParams('cloudEnv', 'CLOUDENV');
initCloudParams('cloudVersion', 'CLOUDVERSION');

// window.onload = function () {
//   loadCloudHeaderComponent();
// };

function checkIfNeedLogin() {
  if (new URL(window.location.href).pathname === '/') {
    return;
  }
  const baseURL = sessionStorage.getItem('BASEURL');
  const orgName = sessionStorage.getItem('ORGNAME');
  const clusterid = sessionStorage.getItem('CURRENTCLUSTERID');

  if (!baseURL || !orgName || !clusterid || orgName === 'null' || clusterid === 'null') {
    // get cloud domain from 'tools-v390.tgcloud.io' or 'tools-v390.tgcloud-dev.io'
    const cloudDomain = window.location.host.split('.').slice(-2).join('.');
    window.location.href = `https://${cloudDomain}`;
  }
}

checkIfNeedLogin();

console.log('===============================================================');
console.log(' _______                 ______                 __');
console.log(' /_  __(_)___ ____  _____/ ____/________ _____  / /_');
console.log('  / / / / __ `/ _ \\/ ___/ / __/ ___/ __ `/ __ \\/ __ \\');
console.log(' / / / / /_/ /  __/ /  / /_/ / /  / /_/ / /_/ / / / /');
console.log('/_/ /_/\\__, /\\___/_/   \\____/_/   \\__,_/ .___/_/ /_/');
console.log('     /____/                          /_/');
console.log('===============================================================');
// eslint-disable-next-line no-undef
console.log('TigerGraph Suite ' + $TOOLS_RELEASE_VERSION + '.' + $BUILD_NUMBER);
console.log('Powered by TigerGraph');
