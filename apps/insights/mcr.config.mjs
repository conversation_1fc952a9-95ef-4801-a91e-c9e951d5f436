const config = {
  // logging: 'debug',

  // provider: 'istanbul',

  name: 'App Insights Test Report',

  all: './src',

  sourceFilter: (sourcePath) => {
    // test files
    if (/\.test\.(js|ts)x?$/.test(sourcePath)) {
      return false;
    }
    // typings
    if (/\.d\.ts$/.test(sourcePath)) {
      return false;
    }

    if (/\.(js|ts)x?$/.test(sourcePath)) {
      return true;
    }

    // other static files like svg, png.
    return false;
  },
  sourcePath: (filePath, info) => {
    return `apps/insights/${filePath}`;
  },

  reports: ['raw', 'console-details', 'v8', 'v8-json'],
  lcov: true,

  outputDir: 'coverage',

  onEnd: (results) => {
    console.log(`coverage report generated: ${results.reportPath}`);
  },
};

export default config;
