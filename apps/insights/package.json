{"name": "insights", "version": "0.1.3", "private": true, "dependencies": {"@babel/traverse": "7.23.2", "@elfalem/leaflet-curve": "^0.9.1", "@floating-ui/react-dom": "^1.0.1", "@floating-ui/react-dom-interactions": "^0.13.1", "@fortawesome/fontawesome-free": "^6.1.1", "@fortawesome/fontawesome-svg-core": "^1.3.0", "@fortawesome/free-regular-svg-icons": "^6.0.0", "@fortawesome/free-solid-svg-icons": "^6.0.0", "@fortawesome/react-fontawesome": "^0.1.17", "@loadable/component": "^5.15.0", "@reactour/mask": "^1.0.0", "@reactour/popover": "^1.0.0", "@reactour/tour": "^3.0.0", "@rehooks/component-size": "^1.0.3", "@shopify/react-web-worker": "^5.0.2", "@tigergraph/tools-models": "1.1.18", "@types/codemirror": "^5.60.5", "@types/cytoscape": "3.19.11", "@types/cytoscape-dagre": "^2.3.0", "@types/d3-color": "^3.1.0", "@types/d3-scale": "^4.0.2", "@types/d3-scale-chromatic": "^3.0.0", "@types/mixpanel": "^2.14.4", "@types/mixpanel-browser": "^2.38.0", "@types/node": "^12.20.43", "@types/react": "18.2.22", "@types/react-dom": "18.2.22", "@types/react-grid-layout": "^1.3.1", "@types/styletron-engine-atomic": "^1.1.1", "@types/styletron-react": "^5.0.3", "@types/styletron-standard": "^2.0.2", "@uiw/react-codemirror": "3.2.7", "@uiw/react-md-editor": "^3.23.5", "ahooks": "^3.3.0", "baseui": "^11.2.0", "body-scroll-lock": "^4.0.0-beta.0", "clsx": "^1.1.1", "codemirror": "^5.65.2", "cytoscape": "3.21.3", "d3-color": "^3.1.0", "d3-scale": "^4.0.2", "d3-scale-chromatic": "^3.0.0", "dom-to-image": "^2.6.0", "echarts": "^5.3.1", "echarts-for-react": "^3.0.2", "eventemitter3": "^4.0.7", "file-saver": "^2.0.5", "final-form": "^4.20.6", "graphql": "^16.5.0", "graphql-request": "^4.2.0", "history": "^5.3.0", "html-to-image": "^1.9.0", "inline-style-expand-shorthand": "^1.4.0", "jssha": "^3.2.0", "leaflet": "^1.8.0", "leaflet.markercluster": "^1.5.3", "lodash-es": "^4.17.21", "lucide-static": "^0.454.0", "mixpanel-browser": "^2.45.0", "papaparse": "^5.3.1", "react": "^18.0.2", "react-beforeunload": "^2.5.3", "react-colorful": "^5.5.1", "react-dom": "^18.0.2", "react-error-boundary": "^3.1.4", "react-final-form": "^6.5.9", "react-grid-layout": "https://github.com/linyutg/react-grid-layout#de82c8ff6278e3b1a68f2784f0fa6ab41773370a", "react-hook-form": "^7.54.2", "react-icon-tint": "^2.3.0", "react-icons": "^4.9.0", "react-merge-refs": "^2.0.1", "react-movable": "^3.0.4", "react-query": "^3.34.19", "react-router-dom": "^6.2.1", "react-svg": "^16.1.34", "react-use-clipboard": "^1.0.8", "rehype-sanitize": "^6.0.0", "short-uuid": "^4.2.0", "styletron-engine-atomic": "^1.4.8", "styletron-react": "^6.0.2", "typescript": "~4.8.4", "web-worker": "^1.2.0", "websandbox": "^0.5.3", "zod": "^3.22.4"}, "scripts": {"start": "GENERATE_SOURCEMAP=false react-app-rewired start", "start:https": "GENERATE_SOURCEMAP=false HTTPS=true SSL_CRT_FILE=./cert/cert.pem SSL_KEY_FILE=./cert/key.pem react-app-rewired start", "start:cloud": "GENERATE_SOURCEMAP=false BROWSER=none WDS_SOCKET_HOST=localhost env-cmd -f .env.cloud react-app-rewired start", "start:win": "set \"GENERATE_SOURCEMAP=false\" && react-app-rewired start", "build:production": "env-cmd -f .env.production react-app-rewired --max_old_space_size=4096 build", "build:cloud": "env-cmd -f .env.cloud react-app-rewired --max_old_space_size=4096 build", "prebuild:cloud": "cp ./public/index-cloud.html ./public/index.html", "eject": "react-scripts eject", "proposal-lint": "eslint --no-error-on-unmatched-pattern --ext .js,.jsx,.ts,.tsx -- src", "proposal-lint:fix": "eslint --no-error-on-unmatched-pattern --ext .js,.jsx,.ts,.tsx --fix -- src", "lint": "eslint --ext .js,.jsx,.ts,.tsx --max-warnings=0 -- src & tsc", "lint:fix": "eslint --ext .js,.jsx,.ts,.tsx --max-warnings=0 --fix -- src", "format": "prettier --write './**/*.{js,jsx,ts,tsx,css,md,json}' --config ./.prettierrc", "analyze": "source-map-explorer 'insights/static/js/*.js'", "test": "vitest --config vitest.config.ts run --coverage", "prettier": "prettier . --write", "prettier:check": "prettier . --check", "prepare": "cd ../../ && husky install"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/preset-typescript": "^7.16.7", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.0.1", "@testing-library/user-event": "^14.4.3", "@types/jsoneditor": "^9.5.1", "@types/lodash-es": "^4.17.12", "@types/resize-observer-browser": "^0.1.7", "@typescript-eslint/eslint-plugin": "^5.15.0", "@typescript-eslint/parser": "^5.15.0", "@vitest/coverage-v8": "2.1.5", "env-cmd": "^10.1.0", "eslint": "^8.11.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-baseui": "^11.2.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-react": "^7.29.4", "express-http-proxy": "^1.6.3", "happy-dom": "^15.11.6", "html-prefetch-webpack-plugin": "^1.1.2", "http-proxy-middleware": "^2.0.6", "husky": ">=6", "identity-obj-proxy": "^3.0.0", "jsdom-worker": "^0.3.0", "lint-staged": ">=10", "loader-utils": "^3.2.1", "msw": "^2.6.5", "prettier": "^2.5.1", "react-app-rewired": "^2.2.1", "react-scripts": "5.0.1", "react-test-renderer": "^18.1.0", "resize-observer-polyfill": "^1.5.1", "source-map-explorer": "^2.5.2", "vitest": "^2.1.5", "vitest-monocart-coverage": "^2.1.2"}, "lint-staged": {"*.js": "eslint --cache --fix"}, "msw": {"workerDirectory": "public"}, "jest": {"transform": {"^.+\\.tsx?$": "ts-jest"}, "watchPathIgnorePatterns": ["cypress"], "moduleNameMapper": {"^d3-(.*)$": "d3-$1/dist/d3-$1.min", "^axios$": "axios/dist/node/axios.cjs", "\\.(css|less)$": "identity-obj-proxy", "\\.(svg)$": "<rootDir>/src/test/mocks/svgMock.js"}, "coverageReporters": ["json-summary", "text-summary", "html"]}, "resolutions": {"follow-redirects": "~1.15.5", "d3-color": "3.1.0", "strip-ansi": "6.0.1", "@types/cytoscape": "3.19.10", "@types/react": "18.2.22"}}