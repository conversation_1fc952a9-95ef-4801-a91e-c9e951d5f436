import { render } from '@testing-library/react';
import { Provider as StyletronProvider } from 'styletron-react';
import { Client as Styletron } from 'styletron-engine-atomic';
import { QueryClient, QueryClientProvider } from 'react-query';
import App from './App';

const engine = new Styletron();
const queryClient = new QueryClient();

vi.mock('./pages/application', () => ({
  default: () => <div>application</div>,
}));
vi.mock('./pages/dashboard', () => ({
  default: () => <div>dashboard</div>,
}));

test('render app', () => {
  const { container } = render(<App />, {
    wrapper: ({ children }) => (
      <StyletronProvider value={engine}>
        <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
      </StyletronProvider>
    ),
  });

  const toolsHead = container.querySelector('div');

  expect(toolsHead).toBeInTheDocument();
});
