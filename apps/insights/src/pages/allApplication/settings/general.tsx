import { useEffect } from 'react';
import { Form, Field } from 'react-final-form';
import { KIND } from 'baseui/toast';
import { ModalButton } from '@tigergraph/app-ui-lib/modal';
import { FormControl } from '@tigergraph/app-ui-lib/form-control';
import { Spinner } from '@tigergraph/app-ui-lib/spinner';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';

import { ApplicationState } from '@tigergraph/tools-ui/esm/insights/pages/application/applicationSlice';
import { AppT } from '../hook';
import { useMutationUpsertApp, useQueryApp } from '../../application/hook';
import { showToast } from '@tigergraph/app-ui-lib/styledToasterContainer';
import StyledInput from '@tigergraph/tools-ui/esm/insights/components/styledInput';
import StyledSelect from '@tigergraph/tools-ui/esm/insights/components/styledSelect';
import { getErrorMessage } from '@tigergraph/tools-ui/esm/insights/components/error';
import { useGraphList } from '@tigergraph/tools-ui/esm/insights/pages/chart/useMetaHook';
import { SelectLucideIconContainer } from '@tigergraph/tools-ui/esm/insights/components/selectLucideIcon';
import { getBaseURL } from '@tigergraph/tools-models';

type Props = {
  application: Omit<AppT, 'username' | 'userRole'>;
};

export default function General({ application }: Props) {
  const [css, theme] = useStyletron();
  const graphs = useGraphList(getBaseURL());
  const mutation = useMutationUpsertApp();
  const { data, isLoading, isError, error } = useQueryApp({ id: application.id });

  useEffect(() => {
    if (isError && error) {
      showToast({
        kind: KIND.negative,
        message: `Load the application failed. \n${getErrorMessage(error)}`,
      });
    }
  }, [isError, error]);

  return (
    <div>
      {isLoading ? (
        <div
          className={css({
            display: 'flex',
            justifyContent: 'center',
            paddingBottom: '16px',
          })}
        >
          <Spinner />
        </div>
      ) : null}
      {data && !isLoading ? (
        <Form
          initialValues={{
            title: data.title,
            iconURL: data.iconURL,
            defaultGraph: data.defaultGraph,
          }}
          onSubmit={(value) => {
            if (mutation.isLoading) {
              return;
            }
            let updateApplication: ApplicationState = {
              ...data,
              title: value.title,
              iconURL: value.iconURL,
              defaultGraph: value.defaultGraph,
              version: data.version,
            };
            mutation.mutate(
              {
                id: updateApplication.id,
                appConfig: updateApplication,
                version: updateApplication.version,
              },
              {
                onSuccess: () => {
                  showToast({
                    kind: KIND.positive,
                    message: 'Updated the application.',
                  });
                },
                onError: (err) => {
                  showToast({
                    kind: KIND.negative,
                    message: `Update the application failed. \n${getErrorMessage(err)}`,
                  });
                },
              }
            );
          }}
          validate={(values) => {
            let errors: {
              title?: string;
              iconURL?: string;
              defaultGraph?: string;
            } = {};
            if (!values.title) {
              errors.title = 'Required';
            }
            if (!values.iconURL) {
              errors.iconURL = 'Required';
            }
            if (!values.defaultGraph) {
              errors.defaultGraph = 'Required';
            }
            return errors;
          }}
          render={({ handleSubmit }) => (
            <form onSubmit={handleSubmit}>
              <Field name="title">
                {({ input, meta }) => (
                  <FormControl label="Application name" error={meta.touched ? meta.error : null}>
                    <StyledInput {...input} />
                  </FormControl>
                )}
              </Field>
              <Field name="iconURL">
                {({ input, meta }) => (
                  <FormControl label="Application icon" error={meta.touched ? meta.error : null}>
                    <SelectLucideIconContainer
                      iconURL={input.value}
                      onIconSelected={(iconURL) => {
                        input.onChange(iconURL);
                      }}
                      isCloud={process.env.REACT_APP_ENV === 'cloud'}
                    />
                  </FormControl>
                )}
              </Field>
              <Field name="defaultGraph">
                {({ input, meta }) => (
                  <FormControl
                    label="Default graph"
                    overrides={{
                      Caption: {
                        style: {
                          ...theme.typography.Body2,
                          color: theme.colors.black03,
                          fontSize: '14px',
                          lineHeight: '16px',
                          fontWeight: 400,
                        },
                      },
                    }}
                    caption={graphs && graphs.length > 0 ? 'You can configure it in each widget individually.' : ''}
                    error={meta.touched ? meta.error : null}
                  >
                    {graphs && graphs.length > 0 ? (
                      <StyledSelect
                        value={input.value ? [{ id: input.value }] : undefined}
                        onChange={(params) => {
                          input.onChange(params.value[0].id);
                        }}
                        // @ts-ignore
                        onBlur={input.onBlur}
                        onFocus={input.onFocus}
                        options={graphs.map((item) => ({ id: item }))}
                        labelKey="id"
                        maxDropdownHeight="230px"
                        clearable={false}
                      />
                    ) : (
                      <span>
                        No graph is available, please go to <a href="../studio/">GraphStudio</a> to create your first
                        graph or contact the administrator.
                      </span>
                    )}
                  </FormControl>
                )}
              </Field>
              <div
                className={css({
                  display: 'flex',
                  justifyContent: 'flex-end',
                  marginBottom: '16px',
                })}
              >
                <ModalButton type="submit" isLoading={mutation.isLoading}>
                  OK
                </ModalButton>
              </div>
            </form>
          )}
        />
      ) : null}
    </div>
  );
}
