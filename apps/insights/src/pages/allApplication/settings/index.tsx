import { useState } from 'react';
import { ModalHeader, ModalBody } from '@tigergraph/app-ui-lib/modal';
import { Tab, Tabs } from '@tigergraph/app-ui-lib/tab';
import StyledModal from '@tigergraph/tools-ui/esm/insights/components/styledModal';
import { AppT } from '../hook';

import General from './general';
import Security from './security';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { supportAccessToken } from '../../../config/feature';

type Props = {
  isOpen: boolean;
  onClose: () => void;
  application: Omit<AppT, 'username' | 'userRole'>;
};

export default function Setting({ isOpen, onClose, application }: Props) {
  const [activeKey, setActiveKey] = useState('general');
  const [css] = useStyletron();

  const showSecurityTab = supportAccessToken();

  return (
    <StyledModal
      onClose={onClose}
      isOpen={isOpen}
      autoFocus
      returnFocus
      overrides={{
        Dialog: {
          style: {
            width: '50vw',
            display: 'flex',
            flexDirection: 'column',
          },
        },
      }}
    >
      <ModalHeader>Edit Application</ModalHeader>
      <ModalBody>
        <Tabs
          activeKey={activeKey}
          onChange={({ activeKey }) => {
            setActiveKey(activeKey as string);
          }}
          activateOnFocus
        >
          <Tab title="General" key="general">
            <General application={application} />
          </Tab>
          {showSecurityTab ? (
            <Tab title="Security" key="security">
              <div
                className={css({
                  minHeight: '472px',
                })}
              >
                <Security application={application} />
              </div>
            </Tab>
          ) : null}
        </Tabs>
      </ModalBody>
    </StyledModal>
  );
}
