import { useEffect, useState } from 'react';
import { Form, Field } from 'react-final-form';
import { ModalButton } from '@tigergraph/app-ui-lib/modal';
import { FormControl } from '@tigergraph/app-ui-lib/form-control';
import { Spinner } from '@tigergraph/app-ui-lib/spinner';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { Button } from '@tigergraph/app-ui-lib/button';
import { Input } from '@tigergraph/app-ui-lib/input';
import { GoPlus } from 'react-icons/go';
import { MdContentCopy } from 'react-icons/md';

import { AppT } from '../hook';
import { StyledToast, showToast } from '@tigergraph/app-ui-lib/styledToasterContainer';
import StyledInput from '@tigergraph/tools-ui/esm/insights/components/styledInput';
import { ErrorDisplay, getErrorMessage } from '@tigergraph/tools-ui/esm/insights/components/error';
import { useAccessToken, useMutationCreateToken, useMutationDeleteToken } from './api';
import app from './app.png';
import useCopyClipboard from 'react-use-clipboard';
import IconButton from '@tigergraph/tools-ui/esm/insights/components/iconButton';

type Props = {
  application: Omit<AppT, 'username' | 'userRole'>;
};

export default function Security({ application }: Props) {
  const [css, theme] = useStyletron();
  const [showForm, setShowForm] = useState(false);

  let { data, isLoading, isError, error } = useAccessToken(application.id);

  const deleteToken = useMutationDeleteToken();
  const createToken = useMutationCreateToken();

  if (isError && error) {
    return <ErrorDisplay error={error} label="Failed to fetch token" />;
  }

  if (isLoading) {
    return (
      <div
        className={css({
          display: 'flex',
          justifyContent: 'center',
          padding: '16px',
        })}
      >
        <Spinner />
      </div>
    );
  }

  const showNewButton = !showForm && !(data && data.token);

  return (
    <div>
      <div
        className={css({
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'flex-end',
        })}
      >
        <div>
          <div
            className={css({
              ...theme.typography.Subtitle,
            })}
          >
            Access tokens
          </div>
          <div
            className={css({
              ...theme.typography.Body2,
            })}
          >
            Access tokens can be used to share Insights widgets with other users, without requiring them to log in to
            Insights.
          </div>
        </div>
        <div
          className={css({
            flexShrink: 0,
            marginLeft: '10px',
          })}
        >
          {showNewButton ? (
            <Button onClick={() => setShowForm(true)} size="large">
              <GoPlus />
              Generate new token
            </Button>
          ) : null}
        </div>
      </div>
      {data && data.token ? (
        <div
          className={css({
            marginTop: '16px',
            padding: '24px',
            border: '1px solid rgb(230, 235, 242)',
            backgroundColor: '#F9FAFB',
          })}
        >
          <div
            className={css({
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              marginBottom: '24px',
            })}
          >
            <div
              className={css({
                flex: 1,
                display: 'flex',
                alignItems: 'center',
                marginRight: '24px',
              })}
            >
              <span
                className={css({
                  flex: 1,
                })}
              >
                <StyledInput
                  size="large"
                  readOnly
                  value={data.token.slice(0, 4) + '*'.repeat(14) + data.token.slice(-4)}
                />
              </span>
              <span
                className={css({
                  fontSize: '12px',
                  marginLeft: '12px',
                })}
              >
                Account ({data.username})
              </span>
            </div>
            <div
              className={css({
                flex: 0,
              })}
            >
              <Button
                onClick={() => {
                  deleteToken.mutate(
                    { appID: application.id },
                    {
                      onSuccess: () => {
                        showToast({
                          kind: 'positive',
                          message: 'Deleted the token.',
                        });
                      },
                      onError: (err) => {
                        showToast({
                          kind: 'negative',
                          message: `Delete the token failed. \n${getErrorMessage(err)}`,
                        });
                      },
                    }
                  );
                }}
                kind="destructive"
                isLoading={deleteToken.isLoading}
              >
                Delete
              </Button>
            </div>
          </div>
        </div>
      ) : null}
      {showForm ? (
        <div
          className={css({
            marginTop: '16px',
            padding: '12px 24px',
            border: '1px solid rgb(230, 235, 242)',
            backgroundColor: '#F9FAFB',
          })}
        >
          <CreateAccessTokenTips />
          <Form
            onSubmit={(value) => {
              if (createToken.isLoading) {
                return;
              }
              createToken.mutate(
                {
                  appID: application.id,
                  username: value.username,
                  password: value.password,
                },
                {
                  onSuccess: () => {
                    showToast({
                      kind: 'positive',
                      message: 'Created the token.',
                    });
                    setShowForm(false);
                  },
                  onError: (err) => {
                    showToast({
                      kind: 'negative',
                      message: `Failed to create token: \n${getErrorMessage(err)}`,
                    });
                  },
                }
              );
            }}
            validate={(values) => {
              let errors: {
                username?: string;
                password?: string;
              } = {};
              if (!values.username) {
                errors.username = 'Required';
              }
              if (!values.password) {
                errors.password = 'Required';
              }
              return errors;
            }}
            render={({ handleSubmit }) => (
              <form onSubmit={handleSubmit}>
                <Field name="username">
                  {({ input, meta }) => (
                    <FormControl label="Database Account" error={meta.touched ? meta.error : null}>
                      <StyledInput {...input} />
                    </FormControl>
                  )}
                </Field>
                <Field name="password">
                  {({ input, meta }) => (
                    <FormControl label="Database Password" error={meta.touched ? meta.error : null}>
                      <StyledInput {...input} type="password" />
                    </FormControl>
                  )}
                </Field>
                <div
                  className={css({
                    display: 'flex',
                    justifyContent: 'flex-end',
                    marginBottom: '16px',
                  })}
                >
                  <ModalButton type="submit" isLoading={createToken.isLoading}>
                    Generate token
                  </ModalButton>
                </div>
              </form>
            )}
          />
        </div>
      ) : null}
    </div>
  );
}

function CreateAccessTokenTips() {
  const [css, theme] = useStyletron();
  return (
    <>
      <div
        className={css({
          ...theme.typography.Body2,
          backgroundColor: '#FEF9C3',
          padding: '12px 12px 12px 24px',
          color: '#CA8A04',
          marginBottom: '16px',
          lineHeight: '1.5',
          marginTop: '8px',
        })}
      >
        Please note that it is recommended to create a new TigerGraph user account with minimal permissions to generate
        an access token for Insights. By setting minimal permissions on the user, you can limit potential security risks
        and ensure that only necessary data is accessible via Insights. Creating a new user account also allows you to
        better manage permissions and access for specific use cases, without compromising the security of other
        TigerGraph resources.
        {/* You can refer to the{' '}
      <a href="#x">documentation</a> to configure the database account. */}
      </div>
    </>
  );
}

export function ShareTokenBasedURL({ applicationID, shareLink }: { applicationID: string; shareLink: string }) {
  const [css] = useStyletron();

  const { data, isLoading, isError, error, isSuccess } = useAccessToken(applicationID);

  return (
    <>
      {isError && error ? <ErrorDisplay error={error} label="Failed to fetch token" /> : null}
      {isLoading ? (
        <div
          className={css({
            display: 'flex',
            justifyContent: 'center',
            padding: '16px',
          })}
        >
          <Spinner />
        </div>
      ) : null}
      {isSuccess ? (
        data && data.token ? (
          <CopyShareURL shareLink={shareLink} token={data.token} />
        ) : (
          <NoAccessToken />
        )
      ) : null}
    </>
  );
}

function NoAccessToken() {
  const [css] = useStyletron();
  return (
    <div role="alert" className={css({ padding: '12px' })}>
      <StyledToast
        message={
          <>
            <div>To share the widget, you need to create an access token first.</div>
            <div>
              You can go to{' '}
              <strong>
                application (click
                <img
                  src={app}
                  alt="application"
                  className={css({
                    height: '24px',
                    position: 'relative',
                    top: '6px',
                    padding: '0 3px',
                  })}
                />
                ) &gt; settings &gt; security
              </strong>{' '}
              to create the access token.
            </div>
          </>
        }
        kind="warning"
        closeable={false}
      />
    </div>
  );
}

function CopyShareURL({ shareLink, token }: { shareLink: string; token: string }) {
  const [, theme] = useStyletron();

  shareLink = generateAccessTokenURL(shareLink, token);
  const [linkCopied, setLinkCopied] = useCopyClipboard(shareLink, {
    successDuration: 1000,
  });

  useEffect(() => {
    if (linkCopied) {
      showToast({
        kind: 'positive',
        message: 'Link copied successfully.',
      });
    }
  }, [linkCopied]);

  return (
    <>
      <Input
        readOnly
        ref={(ele) => {
          if (ele) {
            setTimeout(() => {
              ele.scrollLeft = 0;
            }, 100);
          }
        }}
        value={shareLink}
        endEnhancer={
          <IconButton onClick={setLinkCopied}>
            <MdContentCopy size={24} color={theme.colors.bodyText} />
          </IconButton>
        }
      />
    </>
  );
}

function generateAccessTokenURL(link: string, token: string) {
  const url = new URL(link);
  url.searchParams.append('TigerGraphToken', token);
  return url.toString();
}
