import { useStyletron } from 'baseui';
import { Layer } from 'baseui/layer';
import { StatefulPopover } from '@tigergraph/app-ui-lib/popover';
import { TRIGGER_TYPE, PLACEMENT } from 'baseui/popover';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { IconProp } from '@fortawesome/fontawesome-svg-core';
import { faChevronLeft, faChevronRight } from '@fortawesome/free-solid-svg-icons';

import { useParams, useSearchParams, Outlet, useOutletContext, useNavigate } from 'react-router-dom';
import short from 'short-uuid';
import { useBeforeunload } from 'react-beforeunload';
import { KIND } from 'baseui/toast';

import guidebg from '../../assets/images/guidebg.png';
import { useMutationUpsertPage } from '../application/hook';
import { ApplicationState } from '@tigergraph/tools-ui/esm/insights/pages/application/applicationSlice';
import ApplicationHeader from './applicationHeader';
import { DashboardState } from '@tigergraph/tools-ui/esm/insights/pages/dashboard/dashboardSlice';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { encodeURLSearchParams } from '../../utils';
import { NewWidgetButton } from './NewWidgetButton';
import WidgetSelectionPopover from '../../components/widgetSelection';
import Pages from './pageList';
import PageForm from './pageForm';
import useApplication, { getPageVersion } from './useApplication';
import { hideToolsHeader, showToolsHeader } from '../../components/toolsHeader';
import { NavBarHeight } from '../../components/navbar';
import { SingleStepTourProvider } from '@tigergraph/tools-ui/esm/insights/components/onboard/TourProvider';
import NewPageButton from './NewPageButton';
import trackUtil from '../../utils/analytics-service';
import { GlobalParams, GlobalParam, globalParamsFromURLSearchParams } from '@tigergraph/tools-ui/esm/insights/chart';
import { showToast } from '@tigergraph/app-ui-lib/styledToasterContainer';
import { appendBaseURLToURIIfNeeded } from '../../config/config-interface';
import { getErrorMessage } from '@tigergraph/tools-ui/esm/insights/components/error';
import NoPermission from './noPermission';
import { useIsFetching } from 'react-query';

type ContextType = {
  application: ApplicationState;
  page: DashboardState;
  onPageChanged: (page: DashboardState) => void;
  effectGlobalParameters: GlobalParams;
  batchSetRunTimeGlobalParameter: (pageID: string, params: GlobalParams) => void;
  setRunTimeGlobalParameter: (pageID: string, name: string, value: GlobalParam) => void;
  deleteRunTimeGlobalParameter: (name: string) => void;
  resetScreenshotTimer: () => void;
  setIsSaving: (value: boolean) => void;

  isReadOnly: boolean;
  isSaving: boolean;
  onEnterFullScreen: () => void;
  onExistFullScreen: () => void;
};

export function useDashboard() {
  return useOutletContext<ContextType>();
}

/*
each chart(or widget) will have several status
  a. preview
  b. edit (we have inApplication flag to distinguish if it's a inline edit or separate edit)
  c. normal

There are two level of global parameter

1. config level
   in edit mode, will change config level parameter

2. runtime level (will override config level parameter)
   a. URL params will merged to runtime level to take effect
   b. in preview and normal mode, will change runtime level parameter

chart will received a merged global parameters
*/
export default function ApplicationPage() {
  const [css] = useStyletron();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  const { isFetching, isError, application, resetScreenshotTimer, error } = useApplication();
  const canEdit = application?.userRoleForApp === 'editor' || application?.userRoleForApp === 'owner';
  const isReadOnly = searchParams.get('edit') !== 'true' || !canEdit;

  const [noPermission, setNoPermission] = useState(false);

  useEffect(() => {
    if (isError) {
      const { errors } = error.response;
      if (errors.length && errors[0].message.includes('no permission')) {
        setNoPermission(true);
        return;
      }
      navigate('/404');
    }
  }, [isError, navigate, error]);

  useEffect(() => {
    if (isFetching && isReadOnly) {
      hideToolsHeader();
    }
    return () => showToolsHeader();
  }, [isFetching, isReadOnly]);

  if (isFetching) {
    return (
      <div
        className={css({
          display: 'flex',
          justifyContent: 'center',
          paddingTop: '112px',
        })}
      >
        <div
          className={css({
            position: 'fixed',
            top: '46%',
            left: '46%',
          })}
        >
          <svg
            className={css({
              position: 'relative',
              top: '0',
              left: '0',
            })}
            width="120px"
            height="120px"
            viewBox="-14 -14 228 228"
            color="#f68019"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <defs>
              <linearGradient id="spinner-secondHalf">
                <stop offset="0%" stopOpacity="0" stopColor="currentColor" />
                <stop offset="100%" stopOpacity="0.5" stopColor="currentColor" />
              </linearGradient>
              <linearGradient id="spinner-firstHalf">
                <stop offset="0%" stopOpacity="1" stopColor="currentColor" />
                <stop offset="100%" stopOpacity="0.5" stopColor="currentColor" />
              </linearGradient>
            </defs>

            <g strokeWidth="32">
              <path stroke="url(#spinner-secondHalf)" d="M 4 100 A 96 96 0 0 1 196 100" />
              <path stroke="url(#spinner-firstHalf)" d="M 196 100 A 96 96 0 0 1 4 100" />

              <path stroke="currentColor" strokeLinecap="round" d="M 4 100 A 96 96 0 0 1 4 98" />
            </g>
            <animateTransform
              from="0 0 0"
              to="360 0 0"
              attributeName="transform"
              type="rotate"
              repeatCount="indefinite"
              dur="1300ms"
            />
          </svg>
          <svg
            className={css({
              position: 'relative',
              top: '-30px',
              left: '-90px',
            })}
            width="57"
            height="55"
            viewBox="0 0 57 55"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M1.54872 20.6845C0.786368 19.9172 -0.58206 18.5398 0.270232 17.6819C6.66269 13.3925 5.38419 9.53202 12.2028 5.67157C19.0214 1.81111 21.5478 2.69975 21.5784 2.66896C24.9877 -0.762605 33.511 0.0953075 33.511 0.0953075C33.511 2.66896 31.3802 5.67157 31.3802 5.67157C43.938 12.5346 40.3296 28.4055 40.3296 28.4055C46.2959 20.2556 43.7389 11.6768 43.7389 11.6768C57.3761 22.4003 39.9034 40.8449 39.9034 40.8449C54.8192 32.266 54.393 21.5424 54.393 21.5424C66.7517 41.2738 31.8063 53.1412 19.8738 55C36.4941 33.9818 30.1017 20.2556 26.2662 17.253C30.1017 24.974 27.1185 30.0355 27.1185 29.6924C24.9877 27.5477 22.4307 26.2608 18.1691 26.6898C13.9075 27.1187 10.4982 31.4081 9.21966 32.695C8.79352 33.1239 5.8119 30.6131 4.95803 28.4055C3.79664 25.4029 4.29078 25.273 4.53187 24.545C4.95805 23.2582 3.2534 22.4003 1.54876 20.6845L1.54872 20.6845Z"
              fill="#EE9240"
            />
          </svg>
        </div>
      </div>
    );
  }

  if (noPermission) {
    return <NoPermission />;
  }

  if (!application) {
    return null;
  }

  return <Application application={application} resetScreenshotTimer={resetScreenshotTimer} />;
}

function Application({
  application,
  resetScreenshotTimer,
}: {
  application: ApplicationState;
  resetScreenshotTimer: () => void;
}) {
  const [css] = useStyletron();
  const params = useParams<{ appID: string; pageID?: string }>();
  const [showNewPageForm, setShowNewPageForm] = useState(false);
  const [showPopover, setShowPopover] = useState(false);

  const [isPageListCollapsed, setIsPageListCollapsed] = useState(false);
  const [showCollapse, setShowCollapse] = useState(false);
  const [isDashboardFullScreen, setDashboardFullScreen] = useState(false);

  const onEnterFullScreen = useCallback(() => {
    setDashboardFullScreen(true);
  }, []);

  const onExistFullScreen = useCallback(() => {
    setDashboardFullScreen(false);
  }, []);

  useEffect(() => {
    // trigger dashboard grid re-layout
    window.dispatchEvent(new CustomEvent('resize'));
  }, [isPageListCollapsed]);

  const [searchParams] = useSearchParams();
  const canEdit = application?.userRoleForApp === 'editor' || application?.userRoleForApp === 'owner';
  const isReadOnly = searchParams.get('edit') !== 'true' || !canEdit;
  const navigate = useNavigate();
  const [unsavedPages, setUnsavedPages] = useState<Record<string, DashboardState>>({});
  const [isSaving, setIsSaving] = useState(false);

  const isFetchingPage = useIsFetching(['pageAndWidgets', params.appID, params.pageID]);

  const changePage = useCallback((page: DashboardState & { isDelete?: boolean }) => {
    setUnsavedPages((unsavedPages) => ({ ...unsavedPages, [page.id]: { ...page, chartMap: {} } }));
  }, []);

  useEffect(() => {
    if (isReadOnly) {
      hideToolsHeader();
    }
  }, [isReadOnly]);

  const upsertPageResult = useMutationUpsertPage();

  useBeforeunload((event) => {
    // prevent user from refreshing page until all page changing have been saved
    if (Object.keys(unsavedPages).length || upsertPageResult.isLoading) {
      event.preventDefault();
    }
  });

  useEffect(() => {
    for (let pageId of Object.keys(unsavedPages)) {
      if (isSaving || upsertPageResult.isLoading) {
        // to avoid upsert page multiple times in a period
        return;
      }

      let newUnsavedPage = { ...unsavedPages[pageId] };
      let isNew = unsavedPages[pageId].isNew;
      let newUnsavedPages = { ...unsavedPages };
      delete newUnsavedPages[pageId];
      setUnsavedPages(newUnsavedPages);

      upsertPageResult.mutate(
        {
          appId: params.appID,
          pageConfig: { ...newUnsavedPage, version: getPageVersion({ pages: application.pages, pageId }) },
        },
        {
          onSuccess: () => {
            if (isNew) {
              navigate(`/app/${params.appID}/page/${pageId}${isReadOnly ? '' : '?edit=true'}`, { replace: true });
            }
            showToast({
              kind: 'positive',
              message: 'Page ' + (isNew ? 'created' : 'changed') + ' successfully.',
            });

            resetScreenshotTimer();
          },
          onError: (err) => {
            showToast({
              kind: KIND.negative,
              message: getErrorMessage(err),
            });
          },
        }
      );
      return;
    }
  }, [
    unsavedPages,
    navigate,
    isReadOnly,
    isSaving,
    upsertPageResult,
    application.pages,
    resetScreenshotTimer,
    params.appID,
  ]);

  const [pagesRunTimeGlobalParameters, setPagesRunTimeGlobalParameters] = useState<Record<string, GlobalParams>>({});

  const batchSetRunTimeGlobalParameter = useCallback((pageID: string, params: GlobalParams) => {
    setPagesRunTimeGlobalParameters((pagesRunTimeGlobalParameters) => {
      let updatePageGlobalParameters = {
        ...pagesRunTimeGlobalParameters,
        [pageID]: params,
      };
      return updatePageGlobalParameters;
    });
  }, []);

  const setRunTimeGlobalParameter = useCallback((pageID: string, name: string, value: GlobalParam) => {
    setPagesRunTimeGlobalParameters((pagesRunTimeGlobalParameters) => {
      const updatePageGlobalParameters = {
        ...pagesRunTimeGlobalParameters,
        [pageID]: {
          ...(pagesRunTimeGlobalParameters[pageID] || {}),
          [name]: value,
        },
      };
      return updatePageGlobalParameters;
    });
  }, []);

  const deleteRunTimeGlobalParameter = useCallback(
    (pageID: string, name: string) => {
      if (pagesRunTimeGlobalParameters[pageID]?.[name] !== undefined) {
        setPagesRunTimeGlobalParameters((pagesRunTimeGlobalParameters) => {
          const tempPageRunTimeGlobalParameters = { ...(pagesRunTimeGlobalParameters[pageID] || {}) };
          delete tempPageRunTimeGlobalParameters[name];
          const updatePageGlobalParameters = {
            ...pagesRunTimeGlobalParameters,
            [pageID]: {
              ...tempPageRunTimeGlobalParameters,
            },
          };
          return updatePageGlobalParameters;
        });
      }
    },
    [pagesRunTimeGlobalParameters]
  );

  const getNewDashboardState = (): DashboardState => {
    var title = 'New Page';
    if (application.pages.find((q) => q.title === 'New Page')) {
      for (let i = 2; i <= +process.env.REACT_APP_MAXIMUM_PAGES; i++) {
        if (!application.pages.find((q) => q.title === 'New Page (' + i + ')')) {
          title = 'New Page (' + i + ')';
          break;
        }
      }
    }

    return {
      id: '',
      globalParameters: {},
      isNew: true,
      title: title,
      version: '',
    };
  };

  // sync with router
  useEffect(() => {
    if (!application) {
      return;
    }
    let pages = application.pages;

    // no pages
    if (pages.length === 0) {
      navigate(`/app/${params.appID}${searchParams.toString().length > 0 ? `?${searchParams.toString()}` : ''}`, {
        replace: true,
      });
      return;
    }

    for (let page of pages) {
      if (page.id === params.pageID) {
        return;
      }
    }

    let firstPage = pages[0];
    navigate(
      `/app/${params.appID}/page/${firstPage.id}${
        searchParams.toString().length > 0 ? `?${searchParams.toString()}` : ''
      }`,
      { replace: true }
    );
  }, [application, navigate, searchParams, params.appID, params.pageID, isReadOnly]);

  const { title, iconURL, pages } = application;

  let currentPage: DashboardState | undefined = undefined;
  for (let page of pages) {
    if (page.id === params.pageID && (page.isDetail || page.isNew)) {
      currentPage = page;
      break;
    }
  }

  const effectGlobalParameters = useMemo(() => {
    if (!currentPage) {
      return {};
    }

    let effectGlobalParameters: GlobalParams = {};
    if (currentPage && currentPage.globalParameters) {
      let globalParamsInURL = globalParamsFromURLSearchParams(currentPage.globalParameters, searchParams);

      // 1 params for config
      // 2 params from runtime change(but not save to server)
      // 3 params from URL
      effectGlobalParameters = {
        ...currentPage.globalParameters,
        ...(pagesRunTimeGlobalParameters[currentPage.id] || {}),
        ...globalParamsInURL,
      };
    }
    return effectGlobalParameters;
  }, [currentPage, pagesRunTimeGlobalParameters, searchParams]);

  const top = NavBarHeight;

  const createPageSteps = [
    {
      selector: '[data-tour="create-page"]',
      content: <div>Click here to create a new page.</div>,
    },
  ];

  const createWidgetSteps = [
    {
      selector: '[data-tour="create-widget"]',
      content: <div>Click here to add a new widget.</div>,
    },
  ];

  return (
    <div className={css({ display: 'flex' })}>
      <Layer>
        <div
          className={css({
            position: 'absolute',
            left: 0,
            width: isPageListCollapsed ? '64px' : '240px',
            top: `${top}px`,
            height: `calc(100vh - ${top}px)`,
            backgroundColor: 'white',
            zIndex: isDashboardFullScreen ? '-1' : 'auto',
            '@media print': {
              display: 'none',
              top: 0,
            },
          })}
          onMouseEnter={() => setShowCollapse(true)}
          onMouseLeave={() => setShowCollapse(false)}
        >
          {!isReadOnly && (
            <div
              className={css({
                display: 'flex',
                justifyContent: 'center',
                padding: '16px 0',
              })}
            >
              <SingleStepTourProvider steps={createPageSteps}>
                <NewPageButton
                  onClick={() => setShowNewPageForm(true)}
                  noPage={pages.length === 0}
                  isPageListCollapsed={isPageListCollapsed}
                />
              </SingleStepTourProvider>
            </div>
          )}
          <div className={css({ height: `calc(100% - ${isReadOnly ? 0 : 72}px)`, overflowY: 'auto' })}>
            <Pages
              changePage={(page) => changePage(page)}
              isReadOnly={isReadOnly}
              pages={pages}
              pageListCollapsed={isPageListCollapsed}
            />
          </div>
          <div
            className={css({
              width: '24px',
              position: 'absolute',
              right: '-24px',
              top: 0,
              bottom: 0,
            })}
          >
            {/* right gray line */}
            <div className={css({ height: '100%', width: '1px', background: 'rgb(221, 221, 221)' })}></div>
          </div>
          {(showCollapse || isPageListCollapsed) && (
            <div
              className={css({
                width: '24px',
                height: '24px',
                borderRadius: '50%',
                border: '1px solid #D4DADF',
                color: '#D4DADF',
                position: 'absolute',
                boxSizing: 'border-box',
                right: '-12px',
                top: '60px',
                background: '#fff',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                cursor: 'pointer',
                zIndex: 1,
                ':hover': {
                  background: '#FFF5EC',
                  color: '#FB9A44',
                  boxShadow: 'inset 0px -1px 0px #D4DADF',
                  border: 'none',
                },
                ':active': {
                  background: '#FDD7B4',
                  color: '#FB9A44',
                  border: 'none',
                  boxShadow: 'inset 0px -1px 0px #D4DADF',
                },
              })}
              onClick={() => setIsPageListCollapsed(!isPageListCollapsed)}
            >
              {isPageListCollapsed ? (
                <FontAwesomeIcon icon={faChevronRight as IconProp} size="sm" />
              ) : (
                <FontAwesomeIcon icon={faChevronLeft as IconProp} size="sm" />
              )}
            </div>
          )}
        </div>
      </Layer>
      <ApplicationHeader
        isSaving={isSaving}
        isReadOnly={isReadOnly}
        isDashboardFullScreen={isDashboardFullScreen}
        title={title}
        iconURL={iconURL}
        application={application}
        onPreview={() => {
          let previewParams = new URLSearchParams(searchParams.toString());
          previewParams.delete('edit');
          if (previewParams.toString().length === 0) {
            window.open(appendBaseURLToURIIfNeeded(`app/${params.appID}/page/${params.pageID}`), '_blank');
          } else {
            window.open(
              appendBaseURLToURIIfNeeded(
                `app/${params.appID}/page/${params.pageID}?${encodeURLSearchParams(previewParams)}`
              ),
              '_blank'
            );
          }
        }}
      />
      <div
        className={css({
          flexBasis: 0,
          flexGrow: 1,
          marginLeft: isPageListCollapsed ? '64px' : '240px',
          marginTop: `${top}px`,
          '@media print': {
            marginLeft: 0,
            marginTop: 0,
          },
        })}
      >
        {currentPage && (
          <Outlet
            context={
              {
                application,
                page: currentPage,
                onPageChanged: changePage,
                isSaving: isSaving,
                setIsSaving,
                effectGlobalParameters,
                batchSetRunTimeGlobalParameter,
                setRunTimeGlobalParameter,
                deleteRunTimeGlobalParameter,
                isReadOnly,
                resetScreenshotTimer,
                onEnterFullScreen,
                onExistFullScreen,
              } as ContextType
            }
          />
        )}
        {(!currentPage || (currentPage?.chartMap && Object.keys(currentPage?.chartMap).length === 0)) &&
          !isReadOnly &&
          !isFetchingPage && (
            <div
              className={css({
                width: '100%',
                height: '100%',
                display: 'flex',
                justifyContent: 'center',
                paddingTop: 'calc( (100vh - 88px) * 0.05 )',
              })}
            >
              <img
                src={guidebg}
                alt="Application Building Guide"
                className={css({
                  display: 'block',
                  opacity: '0.6',
                  maxWidth: 'calc( (100vw - 240px) * 0.9 )',
                  position: 'absolute',
                })}
              ></img>
            </div>
          )}
      </div>
      {!isReadOnly && currentPage ? (
        <SingleStepTourProvider steps={createWidgetSteps} position="left">
          <div>
            <StatefulPopover triggerType={TRIGGER_TYPE.hover} placement={PLACEMENT.top} content="New widget">
              <NewWidgetButton
                noWidget={currentPage && Object.keys(currentPage?.chartMap).length === 0}
                onClick={() => setShowPopover(true)}
                showPopover={showPopover}
              />
            </StatefulPopover>
            <WidgetSelectionPopover
              enableCreation={!isReadOnly}
              currentPageWidgets={currentPage.chartMap}
              onCreateWidget={(type: string) => {
                navigate(`/app/${params.appID}/page/${params.pageID}?edit=true`, {
                  replace: true,
                });
                setTimeout(() => {
                  const newID = short.generate();
                  navigate(`page/${currentPage.id}/widgetNew/${newID}`, {
                    state: {
                      widgetType: type,
                    },
                  });
                  trackUtil.trackNewChart(type);
                });
              }}
              isOpen={showPopover}
              onClose={() => {
                setTimeout(() => {
                  setShowPopover(false);
                }, 200);
              }}
            >
              <span
                className={css({
                  position: 'fixed',
                  right: '90px',
                  bottom: '80px',
                  width: 0,
                  height: 0,
                })}
              />
            </WidgetSelectionPopover>
          </div>
        </SingleStepTourProvider>
      ) : null}
      <PageForm
        pageNum={application.pages.length}
        isOpen={showNewPageForm}
        onClose={() => setShowNewPageForm(false)}
        page={getNewDashboardState()}
        onPageCreated={(newPage) => {
          // init page props
          newPage.isNew = true;
          newPage.layouts = { md: [] };

          // add page
          if (application.pages && application.pages.length) {
            let pages = [...application.pages];
            pages.sort((a, b) => {
              return a.weight - b.weight;
            });
            newPage.weight = pages[pages.length - 1].weight + 10;
          } else {
            newPage.weight = 10;
          }

          // upsert page config
          changePage(newPage);
        }}
        onPageUpdate={(updatePage) => {
          changePage(updatePage);
        }}
      />
    </div>
  );
}
