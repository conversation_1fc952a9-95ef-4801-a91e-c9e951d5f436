import { useQuery, useMutation, useQueryClient } from 'react-query';
import { gql, ClientError } from 'graphql-request';
import { ApplicationState } from '@tigergraph/tools-ui/esm/insights/pages/application/applicationSlice';
import { globalParametersMigration } from '@tigergraph/tools-ui/esm/insights/chart';
import { DashboardState } from '@tigergraph/tools-ui/esm/insights/pages/dashboard/dashboardSlice';
import { ChartState } from '@tigergraph/tools-ui/esm/insights/charts';
import {
  updateQueryAppAndPagesCache,
  updateQueryAppCache,
  updateQueryPageAndWidgetCache,
  updateQueryPageAndWidgetsCache,
} from './queryCache';
import { Role } from '../allApplication/shareApplication/types';
import graphQLClient from '../../utils/graphQLClient';
import { parseJSON, stringifyJSON } from '@tigergraph/tools-models';

interface AppT {
  id: string;
  title: string;
  screenshot?: string;
  icon?: string;
  version: string;
}

export interface WidgetT {
  id: string;
  appId?: string;
  pageId?: string;
  config?: string;
  version: string;
}

interface PageT {
  pageId: string;
  config?: string;
  version: string;
}

interface queryPageAndWidgetArgs {
  appId: string;
  pageId: string;
  widgetId: string;
}

export interface queryPageAndWidgetResult {
  page: DashboardState;
  widget: ChartState;
}

export function useQueryPageAndWidget({ appId, pageId, widgetId }: queryPageAndWidgetArgs) {
  return useQuery<queryPageAndWidgetResult, ClientError>(
    ['pageAndWidget', appId, pageId, widgetId],
    async () => {
      const query = gql`
        query Insights_GetPageAndWidget($appId: String!, $pageId: String!, $widgetId: String!) {
          Insights_GetPage(appId: $appId, pageId: $pageId) {
            pageId
            config
            version
          }
          Insights_GetWidget(appId: $appId, pageId: $pageId, id: $widgetId) {
            appId
            pageId
            id
            config
            version
          }
        }
      `;

      const variables = {
        appId,
        pageId,
        widgetId,
      };

      const { Insights_GetPage, Insights_GetWidget } = await graphQLClient.request<{
        Insights_GetPage: PageT;
        Insights_GetWidget: WidgetT;
      }>(query, variables);

      let page: DashboardState = parseJSON(Insights_GetPage.config);
      if (page.globalParameters) {
        page.globalParameters = globalParametersMigration(page.globalParameters);
      }
      // when create widget, the widget config could be null
      let widget: ChartState = Insights_GetWidget.config ? parseJSON(Insights_GetWidget.config) : null;
      return { page, widget };
    },
    {
      // ensure only query api when pageId is not null
      enabled: !!pageId,
    }
  );
}

interface queryPageAndWidgetsArgs {
  appId: string;
  pageId: string;
}

export function useQueryPageAndWidgets({ appId, pageId }: queryPageAndWidgetsArgs) {
  return useQuery<DashboardState, ClientError>(
    ['pageAndWidgets', appId, pageId],
    async () => {
      const query = gql`
        query Insights_GetPageAndWidgets($appId: String!, $pageId: String!) {
          Insights_GetPage(appId: $appId, pageId: $pageId) {
            pageId
            config
            version
          }
          Insights_GetWidgets(appId: $appId, pageId: $pageId) {
            appId
            pageId
            id
            config
            version
          }
        }
      `;

      const variables = {
        appId,
        pageId,
      };

      const { Insights_GetPage, Insights_GetWidgets } = await graphQLClient.request<{
        Insights_GetPage: PageT;
        Insights_GetWidgets: Array<WidgetT>;
      }>(query, variables);

      let chartMap: Record<string, ChartState> = {};
      for (let chart of Insights_GetWidgets) {
        chartMap[chart.id] = parseJSON(chart.config);
      }
      let page: DashboardState = parseJSON(Insights_GetPage.config);
      page.chartMap = chartMap;
      if (page.globalParameters) {
        page.globalParameters = globalParametersMigration(page.globalParameters);
      }
      page.isDetail = true;
      return page;
    },
    {
      // ensure only query api when pageId is not null
      enabled: !!pageId,
    }
  );
}

interface queryAppAndPagesArgs {
  id: string;
  disabled?: boolean;
}

export function useQueryAppAndPages({ id, disabled }: queryAppAndPagesArgs) {
  return useQuery<ApplicationState, ClientError>(
    ['appAndPages', id],
    async () => {
      const query = gql`
        query Insights_GetAppAndPages($id: String!) {
          Insights_GetApp(id: $id) {
            username
            config
            version
            userRoleForApp
          }
          Insights_GetPages(appId: $id) {
            appId
            pageId
            config
            version
          }
        }
      `;

      const variables = {
        id,
      };

      const {
        Insights_GetApp: { config, username, version, userRoleForApp },
        Insights_GetPages,
      } = await graphQLClient.request<{
        Insights_GetApp: { username: string; config: string; version: string; userRoleForApp: Role };
        Insights_GetPages: Array<PageT>;
      }>(query, variables);

      let pageList: Array<DashboardState> = [];

      for (let page of Insights_GetPages) {
        let pageConfig: DashboardState = parseJSON(page.config);
        pageConfig.globalParameters = globalParametersMigration(pageConfig.globalParameters);
        pageConfig.isDetail = false;
        pageConfig.weight = pageConfig.weight || 0;
        pageList.push(pageConfig);
      }

      pageList.sort((a, b) => {
        return a.weight - b.weight;
      });

      let app: ApplicationState = parseJSON(config);
      app.owner = username;
      app.version = version;
      app.userRoleForApp = userRoleForApp;
      if (pageList && pageList.length) {
        app.pages = pageList;
      } else if (!app.pages) {
        app.pages = [];
      }
      return app;
    },
    {
      enabled: !disabled,
    }
  );
}

interface queryAppArgs {
  id: string;
  disabled?: boolean;
}

export function useQueryApp({ id, disabled }: queryAppArgs) {
  return useQuery<ApplicationState, ClientError>(
    ['app', id],
    async () => {
      const query = gql`
        query Insights_GetApp($id: String!) {
          Insights_GetApp(id: $id) {
            username
            config
            version
            userRoleForApp
          }
        }
      `;

      const variables = {
        id,
      };

      const {
        Insights_GetApp: { username, config, version, userRoleForApp },
      } = await graphQLClient.request<{
        Insights_GetApp: { username: string; config: string; version: string; userRoleForApp: Role };
      }>(query, variables);

      let app: ApplicationState = parseJSON(config);

      app.owner = username;
      app.version = version;
      app.userRoleForApp = userRoleForApp;
      return app;
    },
    {
      enabled: !disabled,
    }
  );
}

interface upsertPageAndWidgetArgs {
  appId: string;
  pageId: string;
  pageConfig: DashboardState;
  widgetConfig: ChartState;
  changeGlobalParams?: {
    widgetId: string;
  };
}

interface upsertPageAndWidgetResult {
  Insights_UpsertPage: string;
  Insights_UpsertWidget: string;
}

async function upsertPageAndWidget({ appId, pageId, pageConfig, widgetConfig }: upsertPageAndWidgetArgs) {
  const mutation = gql`
    mutation Insights_UpsertPageAndWidget(
      $appId: String!
      $pageId: String!
      $pageConfig: String!
      $widgetConfig: String!
    ) {
      Insights_UpsertPage(appId: $appId, pageConfig: $pageConfig)
      Insights_UpsertWidget(pageId: $pageId, appId: $appId, widgetConfig: $widgetConfig)
    }
  `;
  let variables = {
    appId: appId,
    pageId: pageId,
    widgetConfig: stringifyJSON(widgetConfig),
    pageConfig: stringifyJSON(pageConfig),
  };

  return await graphQLClient.request<upsertPageAndWidgetResult>(mutation, variables);
}

export function useMutationUpsertPageAndWidget() {
  const queryClient = useQueryClient();

  return useMutation<upsertPageAndWidgetResult, ClientError, upsertPageAndWidgetArgs>(upsertPageAndWidget, {
    mutationKey: ['upsertApp', 'pageAndWidget'],
    onSuccess: (res, { appId, pageId, pageConfig, widgetConfig, changeGlobalParams }) => {
      updateQueryPageAndWidgetsCache({
        queryClient,
        appId,
        pageId,
        upsertPageAndWidget: {
          pageConfig,
          widgetConfig,
          pageVersion: res.Insights_UpsertPage,
          widgetVersion: res.Insights_UpsertWidget,
        },
      });

      if (changeGlobalParams) {
        updateQueryPageAndWidgetCache({
          queryClient,
          appId,
          pageId,
          widgetId: widgetConfig.id,
          upsertPageAndWidget: {
            pageConfig,
            widgetConfig,
            pageVersion: res.Insights_UpsertPage,
            widgetVersion: res.Insights_UpsertWidget,
          },
        });
      }
    },
  });
}

interface upsertPageArgs {
  appId: string;
  pageConfig: DashboardState;
  changeGlobalParams?: {
    widgetId: string;
  };
}

interface upsertPageResult {
  Insights_UpsertPage: string;
}

async function upsertPage({ appId, pageConfig }: upsertPageArgs) {
  const mutation = gql`
    mutation Insights_UpsertPage($appId: String!, $pageConfig: String!) {
      Insights_UpsertPage(appId: $appId, pageConfig: $pageConfig)
    }
  `;

  pageConfig.isNew = false;
  let variables = {
    appId: appId,
    pageConfig: stringifyJSON(pageConfig),
  };

  return await graphQLClient.request<upsertPageResult>(mutation, variables);
}

export function useMutationUpsertPage() {
  const queryClient = useQueryClient();

  return useMutation<upsertPageResult, ClientError, upsertPageArgs>(upsertPage, {
    mutationKey: ['upsertApp', 'page'],
    onSuccess: (res, { pageConfig, appId, changeGlobalParams }) => {
      // when edit global params
      if (changeGlobalParams) {
        updateQueryPageAndWidgetsCache({
          queryClient,
          appId,
          pageId: pageConfig.id,
          upsertPage: {
            pageConfig,
            pageVersion: res.Insights_UpsertPage,
          },
        });

        updateQueryPageAndWidgetCache({
          queryClient,
          appId,
          pageId: pageConfig.id,
          widgetId: changeGlobalParams.widgetId,
          upsertPage: {
            pageConfig,
            pageVersion: res.Insights_UpsertPage,
          },
        });
      } else {
        // when add page/ change layout
        updateQueryAppAndPagesCache({
          queryClient,
          appId,
          upsertPage: {
            pageConfig,
            pageVersion: res.Insights_UpsertPage,
          },
        });

        updateQueryPageAndWidgetsCache({
          queryClient,
          appId,
          pageId: pageConfig.id,
          upsertPage: {
            pageConfig,
            pageVersion: res.Insights_UpsertPage,
          },
        });
      }
    },
  });
}

interface deletePageArgs {
  appId: string;
  pageId: string;
  version: string;
}

interface deletePageResult {
  Insights_DeletePage: string;
}

async function deletePage({ appId, pageId, version }: deletePageArgs) {
  const mutation = gql`
    mutation Insights_DeletePage($pageId: String!, $appId: String!, $version: String!) {
      Insights_DeletePage(pageId: $pageId, appId: $appId, version: $version)
    }
  `;
  let variables = {
    appId: appId,
    pageId: pageId,
    version: version,
  };
  return await graphQLClient.request<deletePageResult>(mutation, variables);
}

export function useMutationDeletePage() {
  const queryClient = useQueryClient();

  return useMutation<deletePageResult, ClientError, deletePageArgs>(deletePage, {
    onSuccess: (_, { appId, pageId }) => {
      updateQueryAppAndPagesCache({
        queryClient,
        appId,
        deletePage: { id: pageId },
      });
    },
  });
}

interface upsertWidgetArgs {
  pageId: string;
  appId: string;
  widgetConfig: ChartState;
}

interface upsertWidgetResult {
  Insights_UpsertWidget: string;
}

async function upsertWidget({ appId, pageId, widgetConfig }: upsertWidgetArgs) {
  const mutation = gql`
    mutation Insights_UpsertWidget($pageId: String!, $appId: String!, $widgetConfig: String!) {
      Insights_UpsertWidget(pageId: $pageId, appId: $appId, widgetConfig: $widgetConfig)
    }
  `;
  let variables = {
    appId: appId,
    pageId: pageId,
    widgetConfig: stringifyJSON(widgetConfig),
  };

  return await graphQLClient.request<upsertWidgetResult>(mutation, variables);
}

export function useMutationUpsertWidget() {
  const queryClient = useQueryClient();

  return useMutation<upsertWidgetResult, ClientError, upsertWidgetArgs>(upsertWidget, {
    mutationKey: ['upsertApp', 'widget'],
    onSuccess: (res, { appId, pageId, widgetConfig }) => {
      updateQueryPageAndWidgetsCache({
        queryClient,
        appId,
        pageId,
        upsertWidget: {
          widgetConfig,
          widgetVersion: res.Insights_UpsertWidget,
        },
      });

      updateQueryPageAndWidgetCache({
        queryClient,
        appId,
        pageId,
        widgetId: widgetConfig.id,
        upsertWidget: {
          widgetConfig,
          widgetVersion: res.Insights_UpsertWidget,
        },
      });
    },
  });
}

interface deleteWidgetArgs {
  appId: string;
  pageId: string;
  pageConfig: string;
  id: string;
  version: string;
}

interface deleteWidgetResult {
  Insights_DeleteWidget: string;
  Insights_UpsertPage: string;
}

async function deleteWidget({ appId, pageId, id, version, pageConfig }: deleteWidgetArgs) {
  const mutation = gql`
    mutation Insights_DeleteWidget(
      $pageId: String!
      $appId: String!
      $id: String!
      $version: String!
      $pageConfig: String!
    ) {
      Insights_DeleteWidget(pageId: $pageId, appId: $appId, id: $id, version: $version)
      Insights_UpsertPage(appId: $appId, pageConfig: $pageConfig)
    }
  `;
  let variables = {
    appId,
    pageId,
    id,
    version,
    pageConfig,
  };
  return await graphQLClient.request<deleteWidgetResult>(mutation, variables);
}

export function useMutationDeleteWidget() {
  const queryClient = useQueryClient();

  return useMutation<
    deleteWidgetResult,
    ClientError,
    { appId: string; page: DashboardState; id: string; version: string }
  >(
    ({ appId, page, id, version }) => {
      return deleteWidget({
        appId,
        pageId: page.id,
        pageConfig: stringifyJSON(page),
        id,
        version,
      });
    },
    {
      onSuccess: (res, { appId, page, id }) => {
        updateQueryPageAndWidgetsCache({
          queryClient,
          appId,
          pageId: page.id,
          deleteWidget: { id },
          upsertPage: {
            pageConfig: page,
            pageVersion: res.Insights_UpsertPage,
          },
        });
      },
    }
  );
}

interface upsertAppArgs {
  id: string;
  screenshot?: string;
  appConfig: ApplicationState;
  version: string;
}

interface upsertAppResult {
  Insights_UpsertApp: string;
}

export async function upsertApp({ screenshot, appConfig, version }: upsertAppArgs) {
  const mutation = gql`
    mutation Insights_UpsertApp($appConfig: String!, $screenshot: String) {
      Insights_UpsertApp(appConfig: $appConfig, screenshot: $screenshot)
    }
  `;

  // support screenshot from application config
  if (!screenshot && appConfig.screenshot) {
    screenshot = appConfig.screenshot;
    appConfig = {
      ...appConfig,
      version,
    };
    delete appConfig.screenshot;
  }

  let variables = {
    appConfig: stringifyJSON(appConfig),
  };

  if (screenshot) {
    variables = {
      ...variables,
      // @ts-ignore
      screenshot: screenshot,
    };
  }

  return await graphQLClient.request<upsertAppResult>(mutation, variables);
}

export function useMutationUpsertApp() {
  const queryClient = useQueryClient();

  return useMutation<upsertAppResult, ClientError, upsertAppArgs>(upsertApp, {
    onSuccess: (upsertAppResult, { id, appConfig }) => {
      // need to update version in appConfig
      appConfig = {
        ...appConfig,
        version: upsertAppResult.Insights_UpsertApp,
      };
      updateQueryAppCache({ queryClient, id, upsertApp: { appConfig } });
      // also need to update application detail cache
      updateQueryAppAndPagesCache({ queryClient, appId: id, upsertApp: { appConfig } });
    },
  });
}

interface duplicateAppArgs {
  duplicateAppId: string;
  newAppId: string;
  newTitle: string;
  screenshot: string;
}

interface duplicateAppResult {
  Insights_DuplicateApp: {
    appVersion: string;
    pagesVersion: Array<Array<string>>;
  };
}

export async function duplicateApp({ duplicateAppId, newAppId, newTitle, screenshot }: duplicateAppArgs) {
  const mutation = gql`
    mutation Insights_DuplicateApp(
      $duplicateAppId: String!
      $newAppId: String!
      $newTitle: String!
      $screenshot: String
    ) {
      Insights_DuplicateApp(
        duplicateAppId: $duplicateAppId
        newAppId: $newAppId
        newTitle: $newTitle
        screenshot: $screenshot
      ) {
        appVersion
        pagesVersion
      }
    }
  `;

  let variables = {
    duplicateAppId,
    newAppId,
    newTitle,
    screenshot,
  };

  return await graphQLClient.request<duplicateAppResult>(mutation, variables);
}

export function useMutationDuplicateApp() {
  const queryClient = useQueryClient();

  return useMutation<duplicateAppResult, ClientError, duplicateAppArgs>(duplicateApp, {
    onSuccess: () => {
      queryClient.invalidateQueries<Array<AppT>>(['apps']);
    },
  });
}

interface exportDataArgs {
  appId: string;
  disabled: boolean;
}

export interface ImportExportData {
  appConfig: string;
  pageConfigs: Array<string>;
  widgetConfigs: Array<string>;
}

// covert backend store format to app config
function convertStoreFormat2AppConfig(Insights_ExportData: ImportExportData): ApplicationState {
  let res: ApplicationState = parseJSON(Insights_ExportData.appConfig);
  res.pages = [];

  for (let page of Insights_ExportData.pageConfigs) {
    let pageConfig = parseJSON(page);
    pageConfig.chartMap = {};
    for (let widgetStr of Insights_ExportData.widgetConfigs) {
      let widget = parseJSON(widgetStr);
      if (widget.PageId === pageConfig.id) {
        let widgetConfig = parseJSON(widget.Config);
        pageConfig.chartMap[widgetConfig.id] = widgetConfig;
      }
    }
    res.pages.push(pageConfig);
  }
  return res;
}

export function useExportData({ appId, disabled }: exportDataArgs) {
  return useQuery<ApplicationState, ClientError>(
    ['exportApp', appId],
    async () => {
      const query = gql`
        query Insights_ExportData($appId: String!) {
          Insights_ExportData(appId: $appId) {
            appConfig
            pageConfigs
            widgetConfigs
          }
        }
      `;

      const variables = {
        appId,
      };

      const { Insights_ExportData } = await graphQLClient.request<{ Insights_ExportData: ImportExportData }>(
        query,
        variables
      );

      return convertStoreFormat2AppConfig(Insights_ExportData);
    },
    {
      enabled: !disabled,
    }
  );
}

interface importDataResult {
  Insights_ImportData: {
    appVersion: string;
    pagesVersion: Array<Array<string>>;
  };
}

interface importDataArgs {
  importData: ApplicationState;
  appId: string;
  version: string;
}

// covert app config to backend store format
function convertAppConfig2StoreFormat(uploadConfig: ApplicationState): ImportExportData {
  let res: ImportExportData = {
    appConfig: '',
    pageConfigs: [],
    widgetConfigs: [],
  };
  let appConfig = { ...uploadConfig, pageConfigSeparated: true };
  let pages = uploadConfig.pages;
  delete appConfig['pages'];
  res.appConfig = stringifyJSON(appConfig);
  for (let page of pages) {
    let tempPage = { ...page };
    if (tempPage.chartMap) {
      for (let widget of Object.values(tempPage.chartMap)) {
        res.widgetConfigs.push(
          stringifyJSON({
            PageId: tempPage.id,
            Config: stringifyJSON(widget),
          })
        );
      }
      delete tempPage['chartMap'];
    }
    res.pageConfigs.push(stringifyJSON(tempPage));
  }
  return res;
}

export async function importData({
  importData,
  appId,
  version,
}: {
  importData: ApplicationState;
  appId: string;
  version: string;
}) {
  const mutation = gql`
    mutation Insights_ImportData(
      $appId: String!
      $version: String!
      $appConfig: String!
      $pageConfigs: [String!]!
      $widgetConfigs: [String!]!
    ) {
      Insights_ImportData(
        appId: $appId
        version: $version
        appConfig: $appConfig
        pageConfigs: $pageConfigs
        widgetConfigs: $widgetConfigs
      ) {
        appVersion
        pagesVersion
      }
    }
  `;

  let data = convertAppConfig2StoreFormat(importData);

  let variables = {
    ...data,
    version,
    appId,
  };

  return await graphQLClient.request<importDataResult>(mutation, variables);
}

export function useMutationImportData() {
  const queryClient = useQueryClient();

  return useMutation<importDataResult, ClientError, importDataArgs>(importData, {
    onSuccess: (_, { appId }) => {
      updateQueryAppAndPagesCache({
        queryClient,
        appId,
        importApp: true,
      });
    },
  });
}
