import { QueryClient } from 'react-query';
import { ChartState } from '@tigergraph/tools-ui/esm/insights/charts';
import { DashboardState } from '@tigergraph/tools-ui/esm/insights/pages/dashboard/dashboardSlice';
import { ApplicationState } from '@tigergraph/tools-ui/esm/insights/pages/application/applicationSlice';
import { getUserName } from '../../utils/username';
import { AppT } from '../allApplication/hook';
import { queryPageAndWidgetResult } from './hook';

interface updateQueryAppCacheArgs {
  id: string;
  queryClient: QueryClient;
  upsertApp: {
    appConfig: ApplicationState;
  };
}

export function updateQueryAppCache({ queryClient, upsertApp, id }: updateQueryAppCacheArgs) {
  // 1 make sure screenshot/icon is correct
  let originalApp = queryClient.getQueryData<ApplicationState>(['app', id]);
  let newConfig = {
    ...originalApp,
    ...upsertApp.appConfig,
  };
  // 2 make sure owner is correct
  if (!newConfig.owner) {
    newConfig.owner = getUserName();
  }
  queryClient.setQueryData<ApplicationState>(['app', id], newConfig);
  queryClient.invalidateQueries<Array<AppT>>(['apps']);
}

interface updateQueryAppAndPagesCacheArgs {
  queryClient: QueryClient;
  appId: string;
  deletePage?: { id: string };
  upsertPage?: {
    pageVersion: string;
    pageConfig: DashboardState;
  };
  importApp?: boolean;
  upsertApp?: {
    appConfig: ApplicationState;
  };
}

// only update page will need to call this function now.
export function updateQueryAppAndPagesCache({
  queryClient,
  appId,
  deletePage,
  upsertPage,
  importApp,
  upsertApp,
}: updateQueryAppAndPagesCacheArgs) {
  if (importApp) {
    queryClient.invalidateQueries<ApplicationState>(['appAndPages', appId]);
    queryClient.invalidateQueries<Array<AppT>>(['apps']);
    return;
  }
  if (upsertApp) {
    let originalAppAndPages = queryClient.getQueryData<ApplicationState>(['appAndPages', appId]);
    if (!originalAppAndPages) {
      // if there is no `['appAndPages', appId]` cache, we just return
      return;
    }
    let newAppAndPages = {
      ...originalAppAndPages,
      // we should not `...upsertApp.appConfig` as it will make `newAppAndPages` state invalid as `upsertApp.appConfig` not have valid `pages` value
      title: upsertApp.appConfig.title,
      iconURL: upsertApp.appConfig.iconURL,
      defaultGraph: upsertApp.appConfig.defaultGraph,
      version: upsertApp.appConfig.version,
    };
    queryClient.setQueryData<ApplicationState>(['appAndPages', appId], newAppAndPages);
    return;
  }

  // page related operation
  if (deletePage || upsertPage) {
    let originalAppAndPages = queryClient.getQueryData<ApplicationState>(['appAndPages', appId]);

    let newPages = [...originalAppAndPages.pages];

    if (deletePage) {
      for (let index in newPages) {
        if (newPages[index].id === deletePage.id) {
          newPages.splice(parseInt(index, 10), 1);
          break;
        }
      }
    }

    if (upsertPage) {
      let newPageConfig = { ...upsertPage.pageConfig, version: upsertPage.pageVersion, isNew: false, isDetail: false };
      let createPage = true;
      for (let index in newPages) {
        if (newPages[index].id === newPageConfig.id) {
          newPages[index] = newPageConfig;
          createPage = false;
          break;
        }
      }
      if (createPage) {
        newPages.push(newPageConfig);
      }
    }

    queryClient.setQueryData<ApplicationState>(['appAndPages', appId], { ...originalAppAndPages, pages: newPages });
  }
}

interface updateQueryPageAndWidgetsCacheArgs {
  queryClient: QueryClient;
  appId: string;
  pageId: string;
  upsertPageAndWidget?: {
    pageConfig: DashboardState;
    widgetConfig: ChartState;
    pageVersion: string;
    widgetVersion: string;
  };
  upsertPage?: {
    pageConfig: DashboardState;
    pageVersion: string;
  };
  upsertWidget?: {
    widgetConfig: ChartState;
    widgetVersion: string;
  };
  deleteWidget?: {
    id: string;
  };
}
// current, will only change one page config and widget config at the same time.
export function updateQueryPageAndWidgetsCache({
  queryClient,
  appId,
  pageId,
  deleteWidget,
  upsertPageAndWidget,
  upsertPage,
  upsertWidget,
}: updateQueryPageAndWidgetsCacheArgs) {
  // change cache in queryPageAndWidgets
  let originalPageAndWidgets = queryClient.getQueryData<DashboardState>(['pageAndWidgets', appId, pageId]);
  let newPageConfig: DashboardState;
  if (originalPageAndWidgets) {
    newPageConfig = {
      ...originalPageAndWidgets,
      chartMap: {
        ...originalPageAndWidgets.chartMap,
      },
    };
  }

  if (deleteWidget) {
    delete newPageConfig.chartMap[deleteWidget.id];
  }

  if (upsertPageAndWidget) {
    newPageConfig = {
      ...newPageConfig,
      globalParameters: upsertPageAndWidget.pageConfig.globalParameters,
      layouts: upsertPageAndWidget.pageConfig.layouts,
      version: upsertPageAndWidget.pageVersion,
    };
    newPageConfig.chartMap[upsertPageAndWidget.widgetConfig.id] = {
      ...upsertPageAndWidget.widgetConfig,
      version: upsertPageAndWidget.widgetVersion,
    };
  }

  if (upsertPage) {
    newPageConfig = {
      ...newPageConfig,
      id: upsertPage.pageConfig.id,
      title: upsertPage.pageConfig.title,
      weight: upsertPage.pageConfig.weight,
      iconURL: upsertPage.pageConfig.iconURL,
      globalParameters: upsertPage.pageConfig.globalParameters,
      layouts: upsertPage.pageConfig.layouts,
      version: upsertPage.pageVersion,
    };
  }

  if (upsertWidget) {
    newPageConfig.chartMap[upsertWidget.widgetConfig.id] = {
      ...upsertWidget.widgetConfig,
      version: upsertWidget.widgetVersion,
    };
  }

  newPageConfig.isDetail = true;
  if (!newPageConfig.chartMap) {
    newPageConfig.chartMap = {};
  }

  queryClient.setQueryData<DashboardState>(['pageAndWidgets', appId, pageId], newPageConfig);
}

interface updateQueryPageAndWidgetCacheArgs {
  queryClient: QueryClient;
  appId: string;
  pageId: string;
  widgetId: string;
  upsertWidget?: {
    widgetConfig: ChartState;
    widgetVersion: string;
  };
  upsertPage?: {
    pageConfig: DashboardState;
    pageVersion: string;
  };
  upsertPageAndWidget?: {
    pageConfig: DashboardState;
    widgetConfig: ChartState;
    pageVersion: string;
    widgetVersion: string;
  };
}
export function updateQueryPageAndWidgetCache({
  queryClient,
  appId,
  pageId,
  widgetId,
  upsertPageAndWidget,
  upsertPage,
  upsertWidget,
}: updateQueryPageAndWidgetCacheArgs) {
  // change cache in queryPageAndWidgets
  let originalPageAndWidget = queryClient.getQueryData<queryPageAndWidgetResult>([
    'pageAndWidget',
    appId,
    pageId,
    widgetId,
  ]);
  let { page, widget } = originalPageAndWidget;

  if (upsertWidget) {
    widget = {
      ...upsertWidget.widgetConfig,
      version: upsertWidget.widgetVersion,
    };
  }

  if (upsertPageAndWidget) {
    page = {
      ...upsertPageAndWidget.pageConfig,
      version: upsertPageAndWidget.pageVersion,
    };
    widget = {
      ...upsertPageAndWidget.widgetConfig,
      version: upsertPageAndWidget.widgetVersion,
    };
  }

  if (upsertPage) {
    page = {
      ...upsertPage.pageConfig,
      version: upsertPage.pageVersion,
    };
  }

  queryClient.setQueryData<queryPageAndWidgetResult>(['pageAndWidget', appId, pageId, widgetId], { page, widget });
}
