import { useState, useCallback, useReducer } from 'react';
import Chart from '@tigergraph/tools-ui/esm/insights/charts/chart';
import { useGraphList, useQueries, useSchema } from '@tigergraph/tools-ui/esm/insights/pages/chart/useMetaHook';
import ChartLoad, { ChartLoadProps } from './chartLoad';
import { useSearchParams } from 'react-router-dom';
import {
  GlobalParam,
  globalParamsFromURLSearchParams,
  globalParamsToURLSearchParams,
  isGlobalParamInSearchParams,
} from '@tigergraph/tools-ui/esm/insights/chart';
import { chartReducer } from '@tigergraph/tools-ui/esm/insights/charts';
import { getBaseURL } from '@tigergraph/tools-models';

export default function ChartShare() {
  return <ChartLoad chartStatus="embed">{(props) => <ChartShareContainer {...props} />}</ChartLoad>;
}

function ChartShareContainer({ page, chart: initChart }: ChartLoadProps) {
  const [chart, dispatch] = useReducer(chartReducer, initChart);

  const graphs = useGraphList(getBaseURL());
  const {
    schema,
    isLoading: isLoadingSchema,
    isError: isSchemaError,
  } = useSchema(getBaseURL(), (chart as any).graphName);
  const {
    queries,
    isLoading: isLoadingQueries,
    isError: isQueriesError,
  } = useQueries(getBaseURL(), (chart as any).graphName);

  // we might to add extra customization for embed mode, so keep the following code.
  // const to = `/app/${params.appID}/page/${params.pageID}/widgetEmbed/${params.chartID}`;
  // const resolved = useResolvedPath(to);
  // const isEmbed = useMatch({ path: resolved.pathname, end: true });

  const [searchParams, setSearchParams] = useSearchParams();
  const [globalParamsForEmbed, setGlobalParamsForEmbed] = useState(() => {
    let urlParams = globalParamsFromURLSearchParams(page.globalParameters, searchParams);
    return {
      ...page.globalParameters,
      ...urlParams,
    };
  });

  const setGlobalParameter = useCallback(
    (name: string, value: GlobalParam) => {
      setGlobalParamsForEmbed((globalParams) => {
        let params = {
          ...globalParams,
          [name]: value,
        };

        // also update search params.
        if (isGlobalParamInSearchParams(searchParams, value)) {
          let newSearchParams = globalParamsToURLSearchParams(params, searchParams);
          setSearchParams(newSearchParams, {
            replace: true,
          });
        }

        return params;
      });
    },
    [searchParams, setSearchParams]
  );

  const deleteGlobalParameter = useCallback((name: string) => {
    setGlobalParamsForEmbed((globalParams) => {
      let params = {
        ...globalParams,
      };

      if (name in globalParams) {
        delete globalParams[name];
      }

      return params;
    });
  }, []);

  return (
    <Chart
      baseURL={getBaseURL()}
      chart={chart}
      dispatch={dispatch}
      isCloud={process.env.REACT_APP_ENV === 'cloud'}
      schema={schema}
      queries={queries}
      graphs={graphs}
      isLoading={isLoadingSchema || isLoadingQueries}
      isError={isSchemaError || isQueriesError}
      onGraphChanged={(graph) =>
        dispatch({
          graphName: graph,
        })
      }
      chartStatus="embed"
      globalParameters={globalParamsForEmbed}
      setGlobalParameter={setGlobalParameter}
      deleteGlobalParameter={deleteGlobalParameter}
      // temporary disable link for embed mode
      links={[]}
      getLink={() => ''}
      onChartDiscard={() => {}}
      onChartSave={() => {}}
    />
  );
}
