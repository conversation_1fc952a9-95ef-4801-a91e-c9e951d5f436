import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import clsx from 'clsx';
import { useParams } from 'react-router-dom';

import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ModalBody, ModalButton, ModalFooter } from '@tigergraph/app-ui-lib/modal';
import { StatefulPopover, Popover } from '@tigergraph/app-ui-lib/popover';
import { TRIGGER_TYPE, PLACEMENT } from 'baseui/popover';
import { MdFullscreen, MdFullscreenExit, MdShare } from 'react-icons/md';

import { Ellipsis } from '@tigergraph/tools-ui/esm/insights/components/icons';
import { ChartStatus } from './type';
import { useSandbox } from './sandbox';
import { EditIcon, DuplicateIcon, DeleteIcon } from '@tigergraph/tools-ui/esm/insights/components/icons';
import IconButton from '@tigergraph/tools-ui/esm/insights/components/iconButton';

import StyledModal from '@tigergraph/tools-ui/esm/insights/components/styledModal';
import { GlobalParams } from '@tigergraph/tools-ui/esm/insights/chart';
import { expand } from 'inline-style-expand-shorthand';
import { ApplicationState } from '@tigergraph/tools-ui/esm/insights/pages/application/applicationSlice';

import ShareDialog from './shareDialog';
import TokenShareDialog from './tokenShareDialog';
import { supportAccessToken } from '../../config/feature';
import { ChartState } from '@tigergraph/tools-ui/esm/insights/charts';
import ChartTitleMenu, { MenuItemType } from './chartTitleMenu';
import { convertRowsToCSVBlob, saveData } from '../../utils/download';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faDownload } from '@fortawesome/free-solid-svg-icons';
import { IconProp } from '@fortawesome/fontawesome-svg-core';
import { tableChartEmitter } from '@tigergraph/tools-ui/esm/insights/charts/table/table';

type Props = {
  application: ApplicationState;
  title: string;
  shareLink: string;
  chartStatus: ChartStatus;
  onChartDelete: () => void;
  onChartDuplicate: (pageID: string, isCurrentPage?: boolean) => void;
  onChartEdit: () => void;
  isFullScreen: boolean;
  setIsFullScreen: (boolean) => void;
  globalParams: GlobalParams;
  hideWidgetName: boolean;
  chart: ChartState;
  chartData: Record<string, any>;
};

export default function ChartTitle({
  application,
  title,
  shareLink,
  chartStatus,
  onChartDelete,
  onChartDuplicate,
  onChartEdit,
  isFullScreen,
  setIsFullScreen,
  globalParams,
  hideWidgetName,
  chart,
  chartData,
}: Props) {
  const [css, theme] = useStyletron();
  const [showWarnDialog, setShowWarnDialog] = useState(false);

  const supportTokenShare = supportAccessToken();
  const [showShareDialog, setShowShareDialog] = useState(false);
  const [showTokenShareDialog, setShowTokenShareDialog] = useState(false);
  const [showMenu, setShowMenu] = useState(false);
  const columns = useRef<string[]>([]);

  const { errMsg, evaluatedTitle } = useSandbox(title, globalParams);

  let finalTitle = title.indexOf('${') >= 0 ? evaluatedTitle : title;

  const params = useParams<{ appID: string; pageID: string }>();

  const onCloseMenu = useCallback(() => {
    setShowMenu(false);
  }, []);

  const withCloseMenu = useCallback(
    (f: (item: MenuItemType) => void) => (item: MenuItemType) => {
      f(item);
      onCloseMenu();
    },
    [onCloseMenu]
  );

  const pageListSubItems = useMemo(() => {
    const onSelect = withCloseMenu(({ id, params }: MenuItemType) => {
      onChartDuplicate(id, params?.isCurrentPage as boolean);
    });
    let ret: MenuItemType[] = [];

    for (let page of application.pages) {
      if (page.id === params.pageID) {
        ret.push({
          label: 'Current Page',
          id: page.id,
          params: {
            isCurrentPage: true,
          },
          onSelect,
        });
        break;
      }
    }

    for (let page of application.pages) {
      if (page.id !== params.pageID) {
        ret.push({
          label: page.title,
          id: page.id,
          onSelect,
        });
      }
    }

    return ret;
  }, [params, application, onChartDuplicate, withCloseMenu]);

  useEffect(() => {
    const updateColumns = (params) => {
      if (params?.id && params?.columns && params.id === chart.id) {
        columns.current = params.columns;
      }
    };
    tableChartEmitter.on('updateColumns', updateColumns);
    return () => {
      tableChartEmitter.off('updateColumns', updateColumns);
    };
  }, [chart.id]);

  const exportData = (type: 'json' | 'csv') => () => {
    let data;

    switch (chart.type) {
      case 'table':
        // parseJSON stringify for deepClone
        const tableData: Record<string, string | number | boolean>[] | undefined = chartData.chartStates.tableData;
        data = tableData?.map((d) => {
          return columns.current.reduce((res, col) => {
            const isObjectData = d[col] && typeof d[col] === 'object';
            if (type === 'csv' && isObjectData) {
              res[col] = JSON.stringify(d[col]);
            } else {
              res[col] = d[col];
            }
            return res;
          }, {});
        });

        break;
      default:
        data = chartData.chartData.results;
        break;
    }
    if (type === 'json') {
      saveData(new Blob([JSON.stringify(data)], { type: 'octet/stream' }), `${finalTitle}.json`);
    } else {
      const csvData = convertRowsToCSVBlob(columns.current, data);
      saveData(csvData, `${finalTitle}.csv`);
    }
  };

  const exportMenuSubItems: MenuItemType[] = [
    chart.type === 'table' && { label: 'CSV', id: 'CSV', onSelect: withCloseMenu(exportData('csv')) },
    { label: 'JSON', id: 'JSON', onSelect: withCloseMenu(exportData('json')) },
  ].filter(Boolean);

  const exportMenuItem: MenuItemType = {
    label: MenuItem({ icon: <FontAwesomeIcon icon={faDownload as IconProp} />, label: 'Export' }),
    id: 'Export',
    subMenu: exportMenuSubItems,
  };

  const menuItems: MenuItemType[] = [
    {
      label: MenuItem({ icon: <EditIcon />, label: 'Edit' }),
      id: 'Edit',
      onSelect: withCloseMenu(onChartEdit),
    },
    {
      label: MenuItem({ icon: <MdShare />, label: 'Share' }),
      id: 'Share',
      onSelect: withCloseMenu(() => {
        if (supportTokenShare) {
          setShowTokenShareDialog(true);
        } else {
          setShowShareDialog(true);
        }
      }),
    },
    {
      label: MenuItem({ icon: <DuplicateIcon />, label: 'Duplicate to' }),
      id: 'Duplicate',
      subMenu: pageListSubItems,
    },
    !['Inputs', 'markdown'].includes(chart.type) && exportMenuItem,
    {
      label: MenuItem({ icon: <DeleteIcon />, label: 'Delete' }),
      id: 'Delete',
      onSelect: withCloseMenu(() => setShowWarnDialog(true)),
    },
  ].filter(Boolean);
  const menuItemsForPreview = [exportMenuItem];

  const showScreenFull = chart.type !== 'value' && chart.type !== 'Inputs' && !hideWidgetName;

  const showChartDropdownMenu = useMemo(() => {
    if (isFullScreen) {
      return false;
    }
    if (!['Inputs', 'markdown'].includes(chart.type)) {
      return true;
    }
    if (chartStatus === 'preview') {
      return false;
    }
    return true;
  }, [chart.type, chartStatus, isFullScreen]);

  return (
    <div
      className={
        css(
          hideWidgetName
            ? {
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'flex-end',
                position: 'absolute',
                top: '0px',
                left: '0px',
                height: '20px',
                width: '100%',
                zIndex: '2',
              }
            : {
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                margin: '8px 8px 0',
              }
        ) + ` widget-item-header`
      }
    >
      <div
        className={clsx(
          'draggable',
          css({
            cursor: chartStatus === 'preview' ? 'auto' : 'move',
            fontSize: '16px',
            lineHeight: '24px',
            overflow: 'hidden',
            whiteSpace: 'nowrap',
            textOverflow: 'ellipsis',
            color: errMsg ? theme.colors['text.danger'] : theme.colors.black03,
            flexGrow: 1,
            minWidth: '14px',
            minHeight: '14px', // take space for empty title
          })
        )}
      >
        {errMsg || (hideWidgetName ? '' : finalTitle)}
      </div>

      <div className={css({ display: 'flex', alignItems: 'center' }) + ` widget-item-header-actions`}>
        {showScreenFull && (
          <StatefulPopover
            triggerType={TRIGGER_TYPE.hover}
            placement={PLACEMENT.bottom}
            content={isFullScreen ? 'Exit fullscreen' : 'Enter fullscreen'}
          >
            <IconButton onClick={() => setIsFullScreen(!isFullScreen)}>
              {isFullScreen ? <MdFullscreenExit size={24} /> : <MdFullscreen size={24} />}
            </IconButton>
          </StatefulPopover>
        )}
        {showChartDropdownMenu && (
          <div
            className={css(
              hideWidgetName
                ? { position: 'absolute', right: '8px', top: '8px' }
                : {
                    position: 'relative',
                  }
            )}
          >
            <StatefulPopover
              triggerType={TRIGGER_TYPE.hover}
              placement={PLACEMENT.bottom}
              content="Widget configuration"
            >
              <IconButton
                $style={{
                  marginLeft: '4px',
                }}
                onClick={() => setShowMenu(true)}
              >
                <Ellipsis />
              </IconButton>
            </StatefulPopover>
            <Popover
              showArrow={false}
              isOpen={showMenu}
              onEsc={onCloseMenu}
              onClickOutside={onCloseMenu}
              popperOptions={{
                modifiers: {
                  preventOverflow: {
                    enabled: true,
                  },
                  hide: {
                    enabled: true,
                  },
                },
              }}
              content={<ChartTitleMenu menuItems={chartStatus === 'preview' ? menuItemsForPreview : menuItems} />}
              overrides={{
                Body: {
                  style: {
                    backgroundColor: '#fff',
                    marginTop: '8px',
                  },
                },
                Inner: {
                  style: {
                    backgroundColor: '#fff',
                    ...expand({
                      padding: 0,
                    }),
                  },
                },
              }}
              placement={PLACEMENT.bottomLeft}
            >
              <span
                className={css({
                  position: 'absolute',
                  left: 0,
                  right: 0,
                  top: 0,
                  bottom: 0,
                  zIndex: -1,
                })}
              />
            </Popover>
          </div>
        )}
      </div>
      <StyledModal onClose={() => setShowWarnDialog(false)} isOpen={showWarnDialog}>
        <ModalHeader>Delete Widget</ModalHeader>
        <ModalBody>
          Are you sure you want to <strong>{title}</strong>
        </ModalBody>
        <ModalFooter>
          <ModalButton kind="secondary" onClick={() => setShowWarnDialog(false)}>
            Cancel
          </ModalButton>
          <ModalButton
            onClick={() => {
              onChartDelete();
              setShowWarnDialog(false);
            }}
          >
            OK
          </ModalButton>
        </ModalFooter>
      </StyledModal>
      <ShareDialog isOpen={showShareDialog} onClose={() => setShowShareDialog(false)} shareLink={shareLink} />
      <TokenShareDialog
        isOpen={showTokenShareDialog}
        onClose={() => setShowTokenShareDialog(false)}
        shareLink={shareLink}
        application={application}
      />
    </div>
  );
}

function MenuItem({ icon, label }: { icon: React.ReactNode; label: React.ReactNode }) {
  const [css, theme] = useStyletron();
  return (
    <div
      className={css({
        display: 'flex',
        alignItems: 'center',
        color: theme.colors.gray900,
      })}
    >
      <div
        className={css({
          width: '32px',
          display: 'flex',
          textAlign: 'center',
          alignItems: 'center',
        })}
      >
        {icon}
      </div>
      <span>{label}</span>
    </div>
  );
}
