import { useEffect, useCallback, useState, useRef, useMemo, useReducer } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import isEqual from 'lodash/isEqual';
import { useBeforeunload } from 'react-beforeunload';

import Chart from '@tigergraph/tools-ui/esm/insights/charts/chart';
import { chartReducer, ChartState } from '@tigergraph/tools-ui/esm/insights/charts';
import ChartTitle from './chartTitle';
import { ApplicationState } from '@tigergraph/tools-ui/esm/insights/pages/application/applicationSlice';
import { ChartStatus } from './type';
import { generateLink, getLinks } from './links';
import ChartLoad, { ChartLoadProps } from './chartLoad';
import { useGraphList, useQueries, useSchema } from '@tigergraph/tools-ui/esm/insights/pages/chart/useMetaHook';
import { GlobalParam, GlobalParams } from '@tigergraph/tools-ui/esm/insights/chart';
import { appendBaseURLToURIIfNeeded } from '../../config/config-interface';
import {
  useMutationDeleteWidget,
  useMutationUpsertPage,
  useMutationUpsertWidget,
  useMutationUpsertPageAndWidget,
} from '../application/hook';
import { showToast } from '@tigergraph/app-ui-lib/styledToasterContainer';
import { KIND } from 'baseui/toast';
import { getErrorMessage } from '@tigergraph/tools-ui/esm/insights/components/error';
import { DashboardState } from '@tigergraph/tools-ui/esm/insights/pages/dashboard/dashboardSlice';
import { getBaseURL } from '@tigergraph/tools-models';
import { cloneDeep } from 'lodash-es';

export default function ChartPage() {
  return <ChartLoad>{(props) => <ChartPageContainer {...props} />}</ChartLoad>;
}

function ChartPageContainer({ application, page, chart }: ChartLoadProps) {
  const [css] = useStyletron();

  const params = useParams<{ appID: string; pageID: string; chartID: string }>();

  const upsertWidget = useMutationUpsertWidget();
  const upsertPage = useMutationUpsertPage();
  const upsertPageAndWidget = useMutationUpsertPageAndWidget();

  const navigate = useNavigate();

  const savedWidget = useMemo(() => cloneDeep(chart), [chart]);

  useBeforeunload((event) => {
    const isInIframe = window.self !== window.top;
    // 1 change widget
    // 2 or change global params.
    if ((!isEqual(chart, savedWidget) || !isEqual(globalParamsForEdit, page.globalParameters)) && !isInIframe) {
      // we can not do any thing here, include
      // 1. window.confirm
      // 2. setTimeout to show dialog later(for ctr/cmd + R to refresh, it's indistinguishable for both cancel/confirm case)
      event.preventDefault();
    }
  });

  const [globalParamsForEdit, setGlobalParamsForEdit] = useState(page.globalParameters);

  const setGlobalParameter = useCallback((name: string, value: GlobalParam, prevName?: string) => {
    setGlobalParamsForEdit((globalParamsForEdit) => {
      let params = {
        ...globalParamsForEdit,
        [name]: value,
      };

      if (prevName) {
        delete params[prevName];
      }
      return params;
    });
  }, []);

  const deleteGlobalParameter = useCallback((name: string) => {
    setGlobalParamsForEdit((globalParamsForEdit) => {
      const params = {
        ...globalParamsForEdit,
      };
      if (name in globalParamsForEdit) {
        delete params[name];
      }
      return params;
    });
  }, []);

  const onChartSave = (chart: ChartState) => {
    if (isEqual(chart, savedWidget) && isEqual(globalParamsForEdit, page.globalParameters)) {
      navigate(`/app/${params.appID}/page/${params.pageID}?edit=true`, { replace: true });
    }

    if (!isEqual(globalParamsForEdit, page.globalParameters) && !isEqual(savedWidget, chart)) {
      // globalParams and widget changed together
      upsertPageAndWidget.mutate(
        {
          appId: params.appID,
          pageId: params.pageID,
          pageConfig: { ...page, globalParameters: globalParamsForEdit },
          widgetConfig: { ...chart, id: chart.id },
          changeGlobalParams: {
            widgetId: chart.id,
          },
        },
        {
          onSuccess: (res) => {
            showToast({
              kind: 'positive',
              message: 'Widget saved successfully.',
            });
            navigate(`/app/${params.appID}/page/${params.pageID}?edit=true`, { replace: true });
          },
          onError: (err) => {
            showToast({
              kind: KIND.negative,
              message: `Save widget failed. \n${getErrorMessage(err)}`,
            });
          },
        }
      );
    } else if (!isEqual(globalParamsForEdit, page.globalParameters)) {
      // only globalParams changed
      upsertPage.mutate(
        {
          appId: params.appID,
          pageConfig: { ...page, globalParameters: globalParamsForEdit },
          changeGlobalParams: {
            widgetId: chart.id,
          },
        },
        {
          onSuccess: () => {
            showToast({
              kind: 'positive',
              message: 'Widget saved successfully.',
            });
            navigate(`/app/${params.appID}/page/${params.pageID}?edit=true`, { replace: true });
          },
          onError: (err) => {
            showToast({
              kind: KIND.negative,
              message: `Save Widget failed. \n${getErrorMessage(err)}`,
            });
          },
        }
      );
    } else if (!isEqual(savedWidget, chart)) {
      // only widget changed
      upsertWidget.mutate(
        {
          appId: params.appID,
          pageId: params.pageID,
          widgetConfig: { ...chart, id: chart.id },
        },
        {
          onSuccess: (res) => {
            showToast({
              kind: 'positive',
              message: 'Widget saved successfully.',
            });
            navigate(`/app/${params.appID}/page/${params.pageID}?edit=true`, { replace: true });
          },
          onError: (err) => {
            showToast({
              kind: KIND.negative,
              message: `Save Widget failed. \n${getErrorMessage(err)}`,
            });
          },
        }
      );
    }
  };

  const onChartDiscard = () => {
    navigate(`/app/${params.appID}/page/${params.pageID}?edit=true`, { replace: true });
  };

  return (
    <div
      className={css({
        height: '100%',
        boxSizing: 'border-box',
      })}
    >
      <ChartContainer
        application={application}
        page={page}
        initChart={chart}
        chartStatus="edit"
        globalParams={globalParamsForEdit}
        setGlobalParameter={setGlobalParameter}
        deleteGlobalParameter={deleteGlobalParameter}
        onChartSave={onChartSave}
        onChartDiscard={onChartDiscard}
      />
    </div>
  );
}

export function ChartContainer({
  application,
  page,
  initChart,
  chartStatus,
  globalParams,
  setGlobalParameter,
  deleteGlobalParameter,
  onChartSave,
  onChartDiscard,
  isFullScreen = false,
  setIsFullScreen,
  onChartDuplicate,
}: {
  application: ApplicationState;
  page: DashboardState;
  initChart: ChartState;
  chartStatus: ChartStatus;
  globalParams: GlobalParams;
  setGlobalParameter: (name: string, value: GlobalParam) => void;
  deleteGlobalParameter: (name: string) => void;
  onChartSave: (chart: ChartState) => void;
  onChartDiscard: () => void;
  isFullScreen?: boolean;
  setIsFullScreen?: (boolean: boolean) => void;
  onChartDuplicate?: (pageID: string, isCurrentPage?: boolean) => void;
}) {
  const [css] = useStyletron();
  const baseURL = getBaseURL();

  const [chart, dispatch] = useReducer(chartReducer, initChart);
  const [chartData, setChartData] = useState<Record<string, any>>({});

  // todo(lin)
  // some widget need schema/queries to run, so we need
  // 1 provider loading status
  // 2 handle error
  // 3 not delay widget data loading that no need for schema/queries
  const graphs = useGraphList(baseURL);
  const {
    schema,
    isLoading: isLoadingSchema,
    isError: isSchemaError,
    error: schemaError,
  } = useSchema(baseURL, chart.graphName);
  const {
    queries,
    isLoading: isLoadingQueries,
    isError: isQueriesError,
    error: queriesError,
  } = useQueries(baseURL, chart.graphName);

  const navigate = useNavigate();
  const { mutate: deleteWidget } = useMutationDeleteWidget();

  const params = useParams<{ appID: string; pageID: string }>();

  const chartRef = useRef<HTMLDivElement>(null);

  const shareLink = appendBaseURLToURIIfNeeded(
    `${window.location.origin}${process.env.PUBLIC_URL}/app/${params.appID}/page/${params.pageID}/widgetShare/${chart.id}`
  );

  // setup default graph name in edit mode
  useEffect(() => {
    if (chartStatus === 'edit' && graphs.length > 0 && !chart.graphName) {
      dispatch({
        graphName: graphs[0],
      });
    }
  }, [chart, dispatch, graphs, chartStatus]);

  const links = useMemo(() => {
    return getLinks(application);
  }, [application]);

  // use pageID to generate link, so we can support page rename
  // but for params, but do not want support params rename as it will make our api surface more complicated
  //  1 global params in url
  //  2 global params to match query params
  const getLink = useCallback(
    (pageID: string, params: Record<string, string>) => {
      return generateLink(application, chartStatus, pageID, params);
    },
    [application, chartStatus]
  );

  // delete chart:
  // 1. if the chart is an Inputs widget and it contains global parameters, it removes all the parameters from page;
  // 2. otherwise, we remove the chart directly;
  const onChartDelete = () => {
    let globalParameters = page.globalParameters;
    if (chart.type === 'Inputs') {
      globalParameters = { ...globalParams };
      if (chart.chartSettings.inputStates) {
        for (const inputState of chart.chartSettings.inputStates) {
          delete globalParameters[inputState.name];
        }
      }
    }

    deleteWidget(
      {
        appId: params.appID,
        page: {
          ...page,
          globalParameters,
        },
        id: chart.id,
        version: chart.version,
      },
      {
        onSuccess: () => {
          showToast({
            kind: 'positive',
            message: 'Widget deleted successfully.',
          });
        },
        onError: (err) => {
          showToast({
            kind: KIND.negative,
            message: `Delete widget failed. \n${getErrorMessage(err)}`,
          });
        },
      }
    );
  };

  const onChartEdit = () => {
    navigate(`/app/${params.appID}/page/${params.pageID}?edit=true`, {
      replace: true,
    });
    setTimeout(() => {
      navigate(`widgetEdit/${chart.id}`);
    });
  };
  const { hideWidgetName } = chart;
  return (
    <div
      className={css({
        position: 'relative',
        height: '100%',
        display: 'flex',
        // refer: https://stackoverflow.com/questions/33605552/how-to-prevent-a-flex-item-height-to-overflow-due-to-its-content
        minHeight: 0,
        flexDirection: 'column',
        backgroundColor: 'white',
      })}
    >
      {chartStatus !== 'edit' && (
        <ChartTitle
          application={application}
          title={chart.title}
          shareLink={shareLink}
          chartStatus={chartStatus}
          onChartDelete={onChartDelete}
          onChartDuplicate={onChartDuplicate}
          onChartEdit={onChartEdit}
          isFullScreen={isFullScreen}
          setIsFullScreen={setIsFullScreen}
          globalParams={globalParams}
          hideWidgetName={hideWidgetName}
          chart={chart}
          chartData={chartData}
        />
      )}
      <div
        className={css({
          display: 'flex',
          flexDirection: 'column',
          flexBasis: 0,
          flexGrow: 1,
          minHeight: 0,
        })}
        ref={chartRef}
      >
        <Chart
          baseURL={baseURL}
          chart={chart}
          dispatch={dispatch}
          isCloud={process.env.REACT_APP_ENV === 'cloud'}
          schema={schema}
          queries={queries}
          graphs={graphs}
          isLoading={isLoadingSchema || isLoadingQueries}
          isError={isSchemaError || isQueriesError}
          schemaError={schemaError}
          queriesError={queriesError}
          onGraphChanged={(graph) =>
            dispatch({
              graphName: graph,
            })
          }
          chartStatus={chartStatus}
          globalParameters={globalParams}
          setGlobalParameter={setGlobalParameter}
          deleteGlobalParameter={deleteGlobalParameter}
          links={links}
          getLink={getLink}
          onChartDiscard={onChartDiscard}
          onChartSave={() => onChartSave(chart)}
          onChartDataUpdate={setChartData}
        />
      </div>
    </div>
  );
}
