import { ApplicationState } from '@tigergraph/tools-ui/esm/insights/pages/application/applicationSlice';
import { ChartStatus } from './type';
import { encodeURLSearchParams } from '../../utils';
import { GlobalParams } from '@tigergraph/tools-ui/esm/insights/chart';
import { appendBaseURLToURIIfNeeded } from '../../config/config-interface';

export function getLinks(application: ApplicationState): { pageName: string; pageID: string; params: GlobalParams }[] {
  let links: { pageName: string; pageID: string; params: GlobalParams }[] = [];

  let pages = application.pages;
  for (let page of pages) {
    links.push({
      pageName: page.title,
      pageID: page.id,
      params: page.globalParameters,
    });
  }

  return links;
}

export function generateLink(
  application: ApplicationState,
  chartStatus: ChartStatus,
  pageID: string,
  params: Record<string, string>
) {
  for (let page of application.pages) {
    if (page.id === pageID) {
      pageID = page.id;

      if (chartStatus !== 'preview' && chartStatus !== 'embed') {
        params['edit'] = 'true';
      }

      let searchParams = new URLSearchParams(params);

      // in edit page, this link will open in new tab, so we add baseURL
      if (chartStatus === 'edit') {
        return appendBaseURLToURIIfNeeded(
          `${window.location.origin}${process.env.PUBLIC_URL}/app/${
            application.id
          }/page/${pageID}?${encodeURLSearchParams(searchParams)}`
        );
      } else {
        return `/app/${application.id}/page/${pageID}?${encodeURLSearchParams(searchParams)}`;
      }
    }
  }

  return '';
}
