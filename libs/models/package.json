{"name": "@tigergraph/tools-models", "version": "1.1.50-APPS-4062-4", "description": "TigerGraph data models shared with all applications", "main": "index.js", "types": "index.d.ts", "publishConfig": {"registry": "https://npm.pkg.github.com/tigergraph"}, "scripts": {"build:readme": "shx cp README.md dist/README.md", "build:license": "shx cp LICENSE dist/LICENSE", "build:package-json": "shx cp package.json dist/package.json", "build:npmrc": "shx cp .npmrc dist/.npmrc", "build:pkg": "NODE_ENV=production BABEL_ENV=production rollup -c --no-treeshake", "build:package": "rimraf dist/ && yarn build:pkg && yarn typescript:generate && yarn build:readme && yarn build:license && yarn build:package-json && yarn build:npmrc", "build": "yarn build:package", "prettier": "prettier . --write", "prerelease": "yarn build", "release": "cd dist/ && npm publish", "test": "nyc ts-node node_modules/jasmine/bin/jasmine", "test:local": "source spec/support/test.sh", "typescript:check": "tsc -p ./tsconfig.json --noEmit", "typescript:generate": "tsc -p ./tsconfig.json --emitDeclarationOnly"}, "author": "TigerGraph", "license": "SEE LICENSE IN LICENSE", "devDependencies": {"@babel/core": "^7.19.1", "@babel/plugin-transform-runtime": "^7.19.1", "@babel/preset-env": "^7.19.1", "@babel/preset-typescript": "^7.18.6", "@istanbuljs/nyc-config-typescript": "^1.0.2", "@rollup/plugin-babel": "^5.3.1", "@rollup/plugin-commonjs": "^22.0.2", "@rollup/plugin-node-resolve": "^14.1.0", "@rollup/plugin-typescript": "^8.5.0", "@types/jasmine": "^4.3.0", "@types/lodash": "4.14.182", "@types/semver": "^7.5.8", "eslint": "^8.23.1", "jasmine": "^4.4.0", "nyc": "^15.1.0", "rollup": "^2.79.1", "shx": "^0.3.4", "ts-node": "^10.9.1", "typescript": "^4.8.3"}, "dependencies": {"@babel/runtime": "^7.17.2", "@babel/runtime-corejs3": "^7.17.2", "@babel/traverse": "7.23.2", "@types/codemirror": "5.60.5", "axios": "~1.7.4", "bignumber.js": "^9.1.1", "codemirror": "5.65.2", "compare-versions": "^6.0.0-rc.1", "crypto-js": "^4.2.0", "generate-schema": "^2.6.0", "json5": "^2.2.2", "lodash": "^4.17.21", "papaparse": "^5.3.2", "rxjs": "~6.6.7"}, "peerDependencies": {"axios": ">=1.6.5"}}