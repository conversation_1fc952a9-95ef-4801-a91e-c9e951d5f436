import { isEqual, isObject } from 'lodash';
import { unparse } from 'papaparse';
import JSONBigInt from '../bigint';

import { GSQLEdgeJson, GSQLUdtJson, TypeCheckedStatus } from '../topology';
import {
  BaseCondition, BaseExpression, ConditionJson, ExpressionJson,
} from '../expression';
import { ExternalGraph } from '../gvis';

export interface VertexOrEdgeAttributesWithCheckedInfo {
  name: string;
  type: 'vertex' | 'edge';
  attributes: {
    name: string,
    checked: boolean
  }[];
}

export interface AttributesForSelected {
  names: string[];
  data: {
    [name: string]: VertexOrEdgeAttributesWithCheckedInfo
  };
}

export interface VisualizationConfig {
  color: {
    filter: BaseCondition | ConditionJson,
    colorValue: string
  }[];
  size: BaseExpression | ExpressionJson;
}

/**
 * Graph chart infos to save.
 * - latestGraph: current result in graph chart.
 * - latestSelections: current selections in graph chart.
 * - latestLayout: current graph chart layout.
 *
 * @export
 * @interface ChartData
 */
export interface ChartData {
  latestGraph: ExternalGraph;
  latestSelections: ExternalGraph;
  latestLayout: string;
}

export enum NumberFormatSettings {
  ThousandsSeparator = 'thousandsSeparator',
  UnifyDecimalSpaces = 'unifyDecimalSpaces',
  DecimalSeparator = 'decimalSeparator'
}

export type NumberFormatting = {
  [lable in NumberFormatSettings]: {
    enabled: boolean;
    value: string;
  };
};

export interface ChartSettingConfig {
  attributesForSelected: AttributesForSelected;
  visualizationConfigOfAllTypes: Map<string, VisualizationConfig>;
  numberFormatting: NumberFormatting;
  showAttributesWhenMouseHover: boolean;
  labelFontSize: number;
}

export namespace HelperFunctions {
  export const defaultNumberFormatting = {
    thousandsSeparator: {
      enabled: false,
      value: ','
    },
    unifyDecimalSpaces: {
      enabled: false,
      value: '0'
    },
    decimalSeparator: {
      enabled: true,
      value: '.'
    }
  };

  export const dataFileName = 'MyDataSource';

  /**
   * Replace graph name placeholder into graph name.
   *
   * @export
   * @param {string} graphName
   * @param {string} url
   * @returns {string}
   */
  export function applyGraphNameToURL(graphName: string, url: string): string {
    return url.replace('[GRAPH_NAME]', graphName);
  }

  /**
   * Replace placeholder with value in url
   *
   * @export
   * @param {string} url
   * @param {object} replaceObject
   *                 key is the placeholder string
   *                 value is the value to replace the url placeholder
   *
   * @return {string} replaced url
   */
  export function replacePlaceholderInUrl(url: string, replaceObject: object): string {
    let result = url;

    Object.keys(replaceObject).forEach(placeholder => {
      if (result.includes(`${placeholder}`)) {
        result = result.replace(`${placeholder}`, replaceObject[placeholder]);
      } else {
        console.error(`'${placeholder}' doesn't exist in '${url}'`);
      }
    });

    return result;
  }

  /**
   * Make the 1st character capitalized
   *
   * @export
   * @param {string} word
   * @returns {string}
   */
  export function capitalizeFirstCharacter(word: string): string {
    if (!word) {
      return '';
    }
    return word.charAt(0).toUpperCase() + word.slice(1);
  }

  /**
   * Convert a list of indexes to a list of ordinal strings.
   *
   * @export
   * @param {number[]} indexes
   * @returns {string[]}
   */
  export function convertIdxToOrdinalStr(indexes: number[]): string[] {
    if (!indexes) {
      return [];
    }

    const ordinalStrs = indexes
      .map(i => i + 1)
      .map(o => {
        switch (o) {
          case 1:
            return '1st';
          case 2:
            return '2nd';
          case 3:
            return '3rd';
          default:
            return `${o}th`;
        }
      });

    return ordinalStrs;
  }

  /**
   * Escape html tags in string.
   *
   * @export
   * @param {string} str
   * @returns {string}
   */
  export function escapeHtml(str: string): string {
    if (!str) {
      return '';
    }
    return str.replace(/[&<>"']/g, (m) => {
      return {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        '\'': '&#039;'
      }[m];
    });
  }

  /**
   * Escape single and double quotes.
   *
   * @export
   * @param {string} str
   * @returns {string}
   */
  export function escapeQuote(str: string): string {
    if (!str) {
      return '';
    }
    return str.replace(/[\\"']/g, (m) => {
      return {
        '\\': '\\\\',
        '"': '\\"',
        '\'': '\\\''
      }[m];
    });
  }

  /**
   * Conver file name to id.
   *
   * @export
   * @param {string} fileName
   * @returns {string}
   */
  export function fileNameToId(fileName: string): string {
    return fileName.replace(/[^a-zA-Z0-9]/g, '_');
  }

  /**
   * The unique id is generated from Math.random()
   * and is converted to alphanumeric to retain all of the randomness.
   * Since JavaScript is single thread there will be no collision.
   *
   * @export
   * @returns {string} Unique id.
   */
  export function idGenerator(): string {
    return Math.random().toString(36).substr(2);
  }

  /**
   * Join an array of strings with comma separated and an and before the last member.
   *
   * @export
   * @param {string[]} strs
   * @returns {string}
   */
  export function joinStrAsPara(strs: string[]): string {
    const len = strs.length;
    const arr = strs.slice();
    let str = arr.slice(0, len - 1).join(', ');

    if (len > 1) {
      str += ` and ${arr[len - 1]}`;
    } else {
      str = arr[0];
    }

    return str || '';
  }

  /**
   * Remove concat chars ' ', '-', '_', '\t', '/' and convert to lowercase
   *
   * @export
   * @param {string} fileName
   * @returns {string}
   */
  export function removeConcatCharsAndToLowerCase(fileName: string): string {
    return fileName.replace(/[ _/\-\t\r]/g, '').toLowerCase();
  }

  /**
   * Retrieve the file name out of file path.
   *
   * @export
   * @param {string} path
   * @returns {string}
   */
  export function retrieveFileNameFromPath(path: string): string {
    return path.lastIndexOf('/') > -1 ? path.substring(path.lastIndexOf('/') + 1) : path;
  }

  /**
   * Truncate a UTF-8 string if it is too long. Retain a string that at most has a visual width
   * of {maxWidth} character spaces (e.g., 'a' takes 1 character space, '中' takes 2
   * character spaces). If truncated, the result will append '...'.
   *
   * @export
   * @param {string} value
   * @param {number} maxWidth
   * @returns {string}
   */
  export function truncateUTF8String(value: string, maxWidth: number): string {
    let len = 0;
    let cutEllipsis = -1;
    let cutLength = -1;
    for (let i = 0; i < value.length; i++) {
      if (value.charCodeAt(i) >= 0 && value.charCodeAt(i) < 256) {
        len++;
      } else {
        len += 2;
      }
      if (len > maxWidth - 3 && cutEllipsis === -1) {
        cutEllipsis = i;
      }
      if (len > maxWidth) {
        cutLength = i - 1;
        break;
      }
    }
    if (cutLength > -1) {
      return value.substring(0, cutEllipsis) + '...';
    } else {
      return value;
    }
  }

  /**
   * Get the UTF-8 string length.
   *
   * @export
   * @param {string} value
   * @returns {number}
   */
  export function strLenUTF8(value: string): number {
    let len = 0;
    for (let i = 0; i < value.length; i++) {
      if (value.charCodeAt(i) >= 0 && value.charCodeAt(i) < 256) {
        len++;
      } else {
        len += 2;
      }
    }
    return len;
  }

  /**
   * Get the UTF-8 string width in pixel in mono-width font.
   *
   * @export
   * @param {string} value
   * @param {number} fontSize
   * @returns {number}
   */
  export function strWidthUTF8(value: string, fontSize: number): number {
    let narrow = 0;
    for (let i = 0; i < value.length; i++) {
      if ('…' === `${value[i]}`) {
        narrow++;
      }
    }
    return (HelperFunctions.strLenUTF8(value) + value.length * 0.26) * fontSize / 2.26
      - narrow * 0.44 * fontSize;
  }

  /**
   * Judge if two points horizontal and vertical distance both less than dist.
   *
   * @export
   * @param {number} x1
   * @param {number} y1
   * @param {number} x2
   * @param {number} y2
   * @param {number} dist
   * @returns {boolean}
   */
  export function twoPointsNearEachOther(
    x1: number,
    y1: number,
    x2: number,
    y2: number,
    dist: number
  ): boolean {
    return Math.abs(x1 - x2) < dist && Math.abs(y1 - y2) < dist;
  }

  /**
   * Retrieve attribute or accumulator value from attributes.
   *
   * @export
   * @param {Object} attrs
   * @param {string} name
   * @returns {*}
   */
  export function retrieveAttributeAndAccumValue(attrs: Object, name: string): any {
    let attrValue = attrs[name];
    // If the attribute is printed as vSet.attrName, it should also be applied.
    if (attrValue === undefined) {
      Object.keys(attrs).forEach(attrName => {
        if (attrName.endsWith(`.${name}`)) {
          attrValue = attrs[attrName];
        }
      });
    }
    return attrValue;
  }

  /**
   * Convert a hex represented color to RGBA format with given opacity.
   *
   * @export
   * @param {string} colorHex
   * @param {number} opacity
   * @returns {string}
   */
  export function changeColorOpacity(colorHex: string, opacity: number): string {
    if (colorHex.length !== 7) {
      return undefined;
    }
    const red = parseInt(colorHex.substring(1, 3), 16);
    const green = parseInt(colorHex.substring(3, 5), 16);
    const blue = parseInt(colorHex.substring(5, 7), 16);
    return `rgba(${red}, ${green}, ${blue}, ${opacity})`;
  }

  /**
   * Exclude elements in the right set from the left set.
   *
   * @export
   * @template T
   * @param {T[]} leftSet
   * @param {T[]} rightSet
   * @returns {T[]}
   */
  export function exclude<T>(leftSet: T[], rightSet: T[]): T[] {
    return leftSet.filter(lhs => rightSet.filter(rhs => isEqual(lhs, rhs)).length === 0);
  }

  /**
   * Convert epoch time to yyyy-MM-dd hh:ii:ss format.
   * Convert epoch time(in milliseconds) to yyyy-MM-dd hh:ii:ss format.
   *
   * @export
   * @param {number} epoch
   * @param {number} [timeZone]
   * @returns {string}
   */
  export function epochToDatetime(epoch: number, timeZone?: number): string {
    const tzOffset = timeZone !== undefined ? timeZone : new Date().getTimezoneOffset() * 60000;
    const date = new Date(epoch - tzOffset);
    return date.toISOString().replace('T', ' ').slice(0, -5);
  }

  /**
   * Convert seconds to {...}d{...}h{...}m{...}s formet.
   *
   * @export
   * @param {number} sec
   * @returns {string}
   */
  export function beautifySeconds(sec: number): string {
    if (sec < 1) {
      return `${sec}s`;
    }
    let result = '';
    let remain = Math.round(sec);
    // seconds
    result = `${remain % 60}s` + result;
    // minutes
    remain = Math.floor(remain / 60);
    if (remain > 0) {
      result = `${remain % 60}m ` + result;
    }
    // hours
    remain = Math.floor(remain / 60);
    if (remain > 0) {
      result = `${remain % 24}h ` + result;
    }
    // days
    remain = Math.floor(remain / 24);
    if (remain > 0) {
      result = `${remain}d ` + result;
    }
    return result;
  }

  /**
   * Convert a long bytes to its shorter format.
   *
   * @export
   * @param {number} bytes
   * @param {number} [decimals=0]
   * @returns {string} Shorter format of the bytes.
   */
  export function abbreviateByte(bytes: number, decimals: number = 0): string {
    if (bytes === null || bytes === undefined || Number.isNaN(bytes)) {
      return 'NaN';
    }

    if (bytes === 0) {
      return '0Bytes';
    }

    const suffix = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
    const k = 1024;
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    decimals = bytes > k ? decimals : 0; // prevent small number to have fraction.

    return (bytes / Math.pow(k, i)).toFixed(decimals) + suffix[i];
  }

  /**
   * Convert a long number to its shorter format.
   *
   * @export
   * @param {number} number
   * @param {number} [decimals=0]
   * @returns {string} Shorter format of the number.
   */
  export function abbreviateNumber(number: number, decimals: number = 0): string {
    if (number === null || number === undefined || Number.isNaN(number)) {
      return 'NaN';
    }

    if (number === 0) {
      return '0';
    }

    const suffix = ['', 'K', 'M', 'B', 'T', 'KT', 'MT', 'BT', 'TT'];
    const k = 1000;
    const i = Math.floor(Math.log(number) / Math.log(k));
    decimals = number > k ? decimals : 0; // prevent small number to have fraction.

    return (number / Math.pow(k, i)).toFixed(decimals) + suffix[i];
  }

  /**
   * Beautify number.
   * e.g.: 12345 => 12,345; 1000000000 => 1,000,000,000
   *
   * @export
   * @param {number} num
   * @returns {string}
   */
  export function beautifyNumber(num: number): string {
    if (num === 0) {
      return '0';
    }
    let result = '';
    const sig = num < 0 ? '-' : '';
    num = Math.abs(num);
    for (let cnt = 0; num > 0;) {
      result = (num % 10) + result;
      num = Math.floor(num / 10);
      if (++cnt % 3 === 0 && num > 0) {
        result = ',' + result;
      }
    }
    return sig + result;
  }

  /**
   * Convert map to JSON object.
   *
   * @export
   * @param {Map<any, any>} map
   * @returns {*}
   */
  export function convertMapToObject(map: Map<any, any>): any {
    if (!map) {
      return;
    }
    const obj = {};
    for (const [key, value] of [...map]) {
      obj[key] = value;
    }
    return obj;
  }

  /**
   * Convert JSON object to map.
   *
   * @export
   * @param {Object} obj
   * @returns {Map<any, any>}
   */
  export function convertObjectToMap(obj: Object): Map<any, any> {
    if (!obj) {
      return;
    }
    const map = new Map<any, any>();
    Object.entries(obj).forEach(([key, value]) => {
      map.set(key, value);
    });
    return map;
  }

  /**
   * Replace the alias in the expression.
   *
   * @export
   * @param {string} exprString
   * @param {string} oldAlias
   * @param {string} newAlias
   * @returns {string}
   */
  export function replaceAlias(exprString: string, oldAlias: string, newAlias: string): string {
    return exprString ?
      exprString.replace(
        new RegExp(`(\\W|^)(${oldAlias}\\.)`, 'g'), (_, p1) => p1 + (newAlias ? `${newAlias}.` : '')
      ) : exprString;
  }

  /**
   * Determine whether a udt has vertex or edge type field.
   *
   * @export
   * @param {GSQLUdtJson} udt
   * @returns {boolean}
   */
  export function hasVertexOrEdgeType(udt: GSQLUdtJson): boolean {
    for (let i = 0; i < udt.fields.length; i++) {
      const type = udt.fields[i].fieldType;
      if (type === 'VERTEX' || type === 'EDGE') {
        return true;
      }
    }
    return false;
  }

  /**
   * Check if a UDT has variant length string type field.
   *
   * @export
   * @param {GSQLUdtJson} udt
   * @returns {boolean}
   */
  export function hasVarLengthString(udt: GSQLUdtJson): boolean {
    for (const field of udt.fields) {
      if (field.fieldType === 'STRING' && field.length === undefined) {
        return true;
      }
    }
    return false;
  }

  /*
   * Check or uncheck for following two conditions:
   *  - 1. When uncheck a vertex type,
   *       uncheck all edge types with it as source or target vertex type as well.
   *  - 2. When check an edge type, check its source and target vertex type as well.
   *
   * @param elements Base elements.
   * @param typeList List of elements that might need to change status.
   * @param isVertex
   */
  export function toggleCheckStatus(
    elements: TypeCheckedStatus[],
    typesToChange: TypeCheckedStatus[],
    isVertex = false
  ) {
    if (isVertex) {
      const unselectedVertices =
        elements.filter(element => !element.selected).map(element => element.vertexOrEdgeType.Name);
      typesToChange.forEach(vertexType => {
        const edge = <GSQLEdgeJson>vertexType.vertexOrEdgeType;
        if (
          unselectedVertices.includes(edge.FromVertexTypeName) ||
          unselectedVertices.includes(edge.ToVertexTypeName)
        ) {
          vertexType.selected = false;
        } else if (edge.EdgePairs) {
          for (const edgePair of edge.EdgePairs) {
            if (
              unselectedVertices.includes(edgePair.From) ||
              unselectedVertices.includes(edgePair.To)
            ) {
              vertexType.selected = false;
              break;
            }
          }
        }
      });
    } else {
      const verticesToSelect = new Set<String>();
      elements.filter(element => element.selected).forEach(element => {
        const edge = <GSQLEdgeJson>element.vertexOrEdgeType;
        verticesToSelect.add(edge.FromVertexTypeName);
        verticesToSelect.add(edge.ToVertexTypeName);
        const edgePairs = edge.EdgePairs;
        if (edgePairs) {
          edgePairs.forEach(edgePair => {
            verticesToSelect.add(edgePair.From);
            verticesToSelect.add(edgePair.To);
          });
        }
      });
      typesToChange.forEach(type => {
        if (verticesToSelect.has(type.vertexOrEdgeType.Name)) {
          type.selected = true;
        }
      });
    }
  }

  /**
   *
   *
   * @export
   * @param {string} str
   * @param {number} start: length of characters showing at the beginning.
   * @param {number} end:   length of characters showing at the end.
   * @returns {string}
   */
  export function limitStringDisplayLength(str: string, start: number, end: number): string {
    return str.replace(new RegExp(`(.{${start}})..+(.{${end}})`, 'g'), '$1...$2');
  }


  /**
   * Convert rows to csv blob.
   *
   * @export
   * @param {string[]} header
   * @param {any[]} rows
   * @returns {Blob}
   */
  export function convertRowsToCSVBlob(header: string[], rows: any[]): Blob {
    const csv = unparse(rows, {
      header: true,
      columns: header,
    });
    const blob = new Blob([csv], {
      type: 'text/plain;charset=utf-8',
    });
    return blob;
  }


  /**
   * Check if the value is primitive type.
   *
   * @param value
   * @returns {boolean}
   */
  export function isPrimitive(value: any): boolean {
    const valueType = typeof value;
    return valueType === 'number' ||
      valueType === 'string' ||
      valueType === 'boolean';
  }

  /**
   * Check if the value is an object (not primitive, null, undefined, or array).
   *
   * @param {any} value
   * @returns {booelan}
   */
  export function isObject(value: any): boolean {
    return Object.prototype.toString.call(value) === '[object Object]' && typeof value !== 'bigint';
  }


  /**
   * Format number with thousands separator and decimal separator.
   *
   * @export
   * @param {number} number
   * @param {{enabled: boolean, value: string}} unifyDecimalSpaces
   * @param {{enabled: boolean, value: string}} thousandsSeparator
   * @param {{enabled: boolean, value: string}} decimalSeparator
   * @return {*}  {string}
   */
  export function numberFormatting(
    number: number,
    unifyDecimalSpaces: { enabled: boolean, value: string },
    thousandsSeparator: { enabled: boolean, value: string },
    decimalSeparator: { enabled: boolean, value: string },
  ): string {
    let res: string;

    // Unify decimal spaces
    if (unifyDecimalSpaces.enabled) {
      res = number.toFixed(
        Number(unifyDecimalSpaces.value)
      );
    } else {
      res = String(number);
    }

    // Decimal separator
    res =
      res.replace('.', decimalSeparator.value);

    // Thousands separator
    if (thousandsSeparator.enabled) {
      const [intPart, decimalPart] = res.split(
        decimalSeparator.value
      );

      res = intPart.replace(
        /\B(?=(\d{3})+(?!\d))/g,
        thousandsSeparator.value
      );

      if (decimalPart) {
        res = `${res}${decimalSeparator.value}${decimalPart}`;
      }
    }
    return res;
  }


  /**
   * Format attribute with thousands separator and decimal separator.
   *
   * @export
   * @param {number} attr
   * @return {*}  {string}
   */
  export function numberFormattingForAttrs(attrValue: number, settings: ChartSettingConfig): string {
    let numberFormattingData = defaultNumberFormatting;
    if (settings && settings.numberFormatting) {
      numberFormattingData = settings.numberFormatting;
    }

    const { thousandsSeparator, unifyDecimalSpaces, decimalSeparator } = numberFormattingData;

    return numberFormatting(
      attrValue,
      unifyDecimalSpaces,
      thousandsSeparator,
      decimalSeparator
    );
  }

  /**
   * Use state machine to get to the end of parameter part and insert graph name.
   *
   * @export
   * @param {string} gsqlQuery
   * @param {string} graphName
   * @return {*}  {string}
   */
  export function insertGraphNameForInterpretMode(gsqlQuery: string, graphName: string): string {
    enum Status {
      START = 0,
      IN_PARAMETER,
      IN_STRING,
      IN_STRING_SKIP,
      STOP
    }

    let parenthesisNestingLevel = 0;
    let curState = Status.START;
    for (let index = 0; index < gsqlQuery.length; index++) {
      const curChar = gsqlQuery.charAt(index);
      switch (curState) {
        case Status.START:
          // before encounter any (
          if (curChar === '(') {
            curState = Status.IN_PARAMETER;
            parenthesisNestingLevel += 1;
          }
          break;
        case Status.IN_PARAMETER:
          // after encounter the ( inside the parameter
          if (curChar === '(') {
            parenthesisNestingLevel += 1;
          } else if (curChar === '"') {
            curState = Status.IN_STRING;
          } else if (curChar === ')') {
            parenthesisNestingLevel -= 1;
            if (parenthesisNestingLevel === 0) {
              curState = Status.STOP;
            }
          }
          break;
        case Status.IN_STRING:
          // encounter the "
          if (curChar === '\\') {
            // if there's a \ skip the character after it
            curState = Status.IN_STRING_SKIP;
          } else if (curChar === '"') {
            // if there's another "
            curState = Status.IN_PARAMETER;
          }
          break;
        case Status.IN_STRING_SKIP:
          // encounter the \ inside ", skip this character
          curState = Status.IN_STRING;
          break;
      }
      if (curState === Status.STOP) {
        // arrive at the position of the right bracket of parameter
        const findGraphName = new RegExp(/^\)\s*for\s+graph/i);
        if (gsqlQuery.substring(index, gsqlQuery.length).match(findGraphName)) {
          // Right after the parameter, there's a FOR GRAPH keyword following
          break;
        }
        return gsqlQuery.substring(0, index + 1) + ` FOR GRAPH ${graphName} ` + gsqlQuery.substring(index + 1, gsqlQuery.length);
      }
    }
    return gsqlQuery;
  }

  // Have to remove HttpParams for shared models
  // export function concatURLAndSearchParams(
  //   url: string,
  //   init?: string | URLSearchParams | string[][] | Record<string, string> | HttpParams  
  // ) {
  //   let params: string | URLSearchParams;
  //   if (init instanceof HttpParams) {
  //     params = init.toString();
  //   } else {
  //     params = new URLSearchParams(init);
  //   }
  //   return `${url}?${params}`;
  // }
  export function concatURLAndSearchParams(
    url: string,
    init?: string | URLSearchParams | string[][] | Record<string, string>
  ) {
    const params: string | URLSearchParams = new URLSearchParams(init);
    return `${url}?${params}`;
  }
}

/**
 * Use state machine to get to the end of parameter part and insert graph name.
 *
 * @export
 * @param {string} gsqlQuery
 * @param {string} graphName
 * @return {*}  {string}
 */
export function insertGraphNameForInterpretMode(gsqlQuery: string, graphName: string): string {
  enum Status {
    START = 0,
    IN_PARAMETER,
    IN_STRING,
    IN_STRING_SKIP,
    STOP,
  }

  let parenthesisNestingLevel = 0;
  let curState = Status.START;
  for (let index = 0; index < gsqlQuery.length; index++) {
    const curChar = gsqlQuery.charAt(index);
    switch (curState) {
      case Status.START:
        // before encounter any (
        if (curChar === '(') {
          curState = Status.IN_PARAMETER;
          parenthesisNestingLevel += 1;
        }
        break;
      case Status.IN_PARAMETER:
        // after encounter the ( inside the parameter
        if (curChar === '(') {
          parenthesisNestingLevel += 1;
        } else if (curChar === '"') {
          curState = Status.IN_STRING;
        } else if (curChar === ')') {
          parenthesisNestingLevel -= 1;
          if (parenthesisNestingLevel === 0) {
            curState = Status.STOP;
          }
        }
        break;
      case Status.IN_STRING:
        // encounter the "
        if (curChar === '\\') {
          // if there's a \ skip the character after it
          curState = Status.IN_STRING_SKIP;
        } else if (curChar === '"') {
          // if there's another "
          curState = Status.IN_PARAMETER;
        }
        break;
      case Status.IN_STRING_SKIP:
        // encounter the \ inside ", skip this character
        curState = Status.IN_STRING;
        break;
    }
    if (curState === Status.STOP) {
      // arrive at the position of the right bracket of parameter
      const findGraphName = new RegExp(/^\)\s*for\s+graph/i);
      if (gsqlQuery.substring(index, gsqlQuery.length).match(findGraphName)) {
        // Right after the parameter, there's a FOR GRAPH keyword following
        break;
      }
      return (
        gsqlQuery.substring(0, index + 1) +
        ` FOR GRAPH ${graphName} ` +
        gsqlQuery.substring(index + 1, gsqlQuery.length)
      );
    }
  }
  return gsqlQuery;
}

// implement toJSON function for BigInt, so that it can be stringified by native JSON.stringify
// @ts-ignore
BigInt.prototype.toJSON = function () {
  return this.toString();
}
export function parseJSON(response: string) {
  let parsedData: any;
  try {
    parsedData = JSONBigInt({ useNativeBigInt: true }).parse(response);
  } catch (e) {
    console.error(e);
    parsedData = JSON.parse(response);
  }
  return parsedData; 
}

export function stringifyJSON(...args: Parameters<typeof JSON.stringify>) {
  let str: string;
  try {
    str = JSONBigInt({ useNativeBigInt: true }).stringify(...args);
  } catch (e) {
    str = JSON.stringify(...args); 
  }
  return str;
}

// On cloud 4, the app should use this function to get param string(carrys workspace info)
// when generate the redirection URL to other app or itself
export function getToolsRedirectionURLParamStr() {
  const params = new URLSearchParams();

  const baseURL = sessionStorage.getItem('BASEURL') || '';
  const domain = baseURL.match(/tg-[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}\.[a-zA-Z0-9.-]+?\.(privatelink|i)/i);
  if (!domain) {
    console.error('Invalid base url', baseURL);
    return ''
  }

  params.set('domain', domain[0]);
  params.set('clusterid', sessionStorage.getItem('CURRENTCLUSTERID'));
  params.set('orgName', sessionStorage.getItem('ORGNAME'));
  params.set('groupid', sessionStorage.getItem('CURRENTGROUPID'))
  params.set('cloudVersion', sessionStorage.getItem('CLOUDVERSION'));
  if (sessionStorage.getItem('CLOUDENV')) {
    params.set('cloudEnv', sessionStorage.getItem('CLOUDENV'));
  }

  return params.toString();
}