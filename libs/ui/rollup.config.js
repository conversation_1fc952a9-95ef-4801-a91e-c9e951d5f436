import { DEFAULT_EXTENSIONS } from '@babel/core';
import babel from '@rollup/plugin-babel';
import resolve from '@rollup/plugin-node-resolve';
import commonjs from '@rollup/plugin-commonjs';
import url from '@rollup/plugin-url';
import svgr from '@svgr/rollup';
import postcss from 'rollup-plugin-postcss';
// import css from 'rollup-plugin-css-only';
import json from '@rollup/plugin-json';
import packageMeta from './package.json';
import path from 'path';

const extensions = [...DEFAULT_EXTENSIONS, '.ts', '.tsx'];

module.exports = [
  {
    input: 'src/index.ts',
    output: [
      {
        format: 'cjs',
        dir: path.resolve('dist', path.dirname(packageMeta.main)),
        preserveModules: true,
        // refer
        // 1. https://rollupjs.org/configuration-options/#output-preservemodulesroot
        // 2. https://github.com/rollup/plugins/issues/321#issuecomment-858541214
        preserveModulesRoot: 'src',
        exports: 'auto',
        sourcemap: true,
      },
      {
        format: 'esm',
        dir: path.resolve('dist', path.dirname(packageMeta.module)),
        preserveModules: true,
        preserveModulesRoot: 'src',
        exports: 'auto',
      },
    ],
    plugins: [
      resolve({
        extensions,
      }),
      commonjs(),
      babel({
        extensions,
        babelHelpers: 'runtime',
      }),
      json(),
      url(),
      svgr(),
      postcss({
        extract: true,
      }),
      // css({
      //   output: (styles, styleNodes) => {
      //     const fs = require('fs'); const outputPath = path.resolve('dist', 'style.css');
      //     fs.writeFileSync(outputPath, styles);
      //   }
      // }),
    ],
    external: [/node_modules/, '@tigergraph/tools-models', '*.stories.mdx', '*.stories.tsx', 'src/stories'],
  },
];
