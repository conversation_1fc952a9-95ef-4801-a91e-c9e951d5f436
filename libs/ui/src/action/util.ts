import { encodeURLSearchParams } from '../utils';
import { Action, ActionParam } from './type';

export const buildLinkFromAction = (
  action: Action,
  getLink: (pageID: string, params: Record<string, string>) => string,
  getParam: (param: ActionParam) => Record<string, string>,
  isEditMode?: boolean
) => {
  let href = action.url;
  if (action.pageID) {
    href = getLink(
      action.pageID,
      action.params.reduce(
        (prev, param) => {
          const parameters = getParam(param) || {};
          for (let key of Object.keys(parameters)) {
            if (parameters[key]) {
              prev[key] = parameters[key];
            }
          }
          return prev;
        },
        isEditMode ? ({ edit: 'true' } as Record<string, any>) : {}
      )
    );
  } else if (action.url) {
    const params = new URLSearchParams();
    action.params.forEach((param) => {
      const parameters = getParam(param) || {};
      for (let key of Object.keys(parameters)) {
        if (parameters[key]) {
          params.append(key, parameters[key]);
        }
      }
    });
    if (isEditMode) {
      params.append('edit', 'true');
    }
    if (params.toString().length > 0) {
      href += '?' + encodeURLSearchParams(params);
    }
    if (!href.startsWith('http')) {
      href = 'http://' + href;
    }
  }
  return href;
};
