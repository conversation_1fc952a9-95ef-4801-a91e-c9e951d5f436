import { ReactNode, CSSProperties } from 'react';
import { useStyletron } from 'baseui';
import HelpText from './helpText';
import IconPopover from '../insights/components/iconPopover';

type Props = {
  children?: ReactNode;
  label?: string;
  style?: CSSProperties;
  icon?: ReactNode;
  helpText?: ReactNode;
  popover?: ReactNode | ((args: { close: () => void }) => ReactNode);
  onConfirm?: () => void;
  onCancel?: () => void;
  right?: ReactNode;
  onOpen?: () => void;
};

export default function ChartSettingItemContainer(props: Props) {
  const [css] = useStyletron();
  const { label, children, style, icon, popover, onConfirm, onCancel, onOpen, right, helpText } = props;

  return (
    <div
      className={css({
        display: 'flex',
        flexDirection: 'column',
        // @ts-ignore
        minHeight: '32px',
        justifyContent: 'center',
        // @ts-ignore
        marginBottom: '6px',
        ...style,
      })}
    >
      {(label || popover) && (
        <div
          className={css({
            display: 'flex',
            justifyContent: 'space-between',
            fontSize: '14px',
            lineHeight: '16px',
            marginBottom: children ? '4px' : '0',
          })}
        >
          {label && <HelpText content={helpText}>{label}</HelpText>}
          {popover && (
            <IconPopover label={icon} popover={popover} onConfirm={onConfirm} onCancel={onCancel} onOpen={onOpen} />
          )}
          {right}
        </div>
      )}
      {children}
    </div>
  );
}
