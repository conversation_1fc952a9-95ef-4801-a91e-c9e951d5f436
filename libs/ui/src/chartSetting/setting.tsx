import React from 'react';
import { Button } from 'baseui/button';
import { StatefulPopover } from 'baseui/popover';
import { useState } from 'react';
import { HexColorPicker } from 'react-colorful';
import { useStyletron } from 'styletron-react';

import { SETTING_TYPE } from './type';

import ChartSettingItemContainer from './chartSettingItemContainer';

import HelpText from './helpText';
import { StyledInput, StyledSelect, StyledSlider } from '../insights/components';
import StyledToggle from '../insights/components/styledToggle';

type Props = {
  field: string;
  value: any;
  settingType: SETTING_TYPE;
  onChange: (field: string, value: any) => void;
};

const Setting = ({ field, value, settingType, onChange }: Props) => {
  const [css] = useStyletron();
  const label = `${settingType.label}`;
  const { helpText } = settingType;
  const [internalValue, setInternalValue] = useState(value || settingType.default);
  const [sliderValue, setSliderValue] = useState([value || settingType.default]);

  const updateValueChange = () => {
    if (internalValue === value) {
      return;
    }
    onChange(field, internalValue);
  };

  switch (settingType.type) {
    case 'toggle':
      return (
        <ChartSettingItemContainer>
          <StyledToggle
            checked={value !== null && value !== undefined ? value : settingType.default}
            onChange={(e) => onChange(field, e.currentTarget.checked)}
          >
            <HelpText content={helpText}>{label}</HelpText>
          </StyledToggle>
        </ChartSettingItemContainer>
      );
    case 'number':
      return (
        <ChartSettingItemContainer label={label} helpText={helpText}>
          <StyledInput
            value={value ? value : settingType.default}
            onChange={(e) => onChange(field, e.currentTarget.value)}
            type="number"
          />
        </ChartSettingItemContainer>
      );
    case 'slider':
      return (
        <ChartSettingItemContainer label={label} helpText={helpText}>
          <StyledSlider
            value={sliderValue}
            min={settingType.min}
            max={settingType.max}
            step={settingType.step}
            onChange={({ value }) => {
              setSliderValue(value);
            }}
            // for now, we use onFinalChange
            onFinalChange={({ value }) => {
              onChange(field, value[0]);
            }}
          />
        </ChartSettingItemContainer>
      );
    case 'name':
      return (
        <ChartSettingItemContainer label={label} helpText={helpText}>
          <StyledInput
            value={internalValue as string}
            onChange={(e) => setInternalValue(e.currentTarget.value)}
            onBlur={(e) => updateValueChange()}
            onKeyPress={(e) => {
              if (e.key === 'Enter') {
                updateValueChange();
              }
            }}
          />
        </ChartSettingItemContainer>
      );
    case 'text':
      return (
        <ChartSettingItemContainer label={label} helpText={helpText}>
          <StyledInput
            value={value ? value : settingType.default}
            onChange={(e) => onChange(field, e.currentTarget.value)}
          />
        </ChartSettingItemContainer>
      );
    case 'color':
      return (
        <ChartSettingItemContainer label={label} helpText={helpText}>
          <StatefulPopover
            content={() => (
              <HexColorPicker
                color={value ? value : settingType.default}
                onChange={(color) => onChange(field, color)}
              />
            )}
            returnFocus
            autoFocus
          >
            <div className={css({ paddingTop: '10px' })}>
              <Button
                overrides={{
                  BaseButton: {
                    style: {
                      backgroundColor: value || settingType.default,
                    },
                  },
                }}
              />
            </div>
          </StatefulPopover>
        </ChartSettingItemContainer>
      );
    case 'list':
      return (
        <ChartSettingItemContainer label={label} helpText={helpText}>
          <StyledSelect
            clearable={false}
            value={[value ? { id: value } : { id: settingType.default }]}
            options={settingType.values.map((value) => ({
              id: typeof value === 'boolean' ? (value ? 'on' : 'off') : value,
            }))}
            onChange={({ value }) => onChange(field, value[0].id)}
            labelKey="id"
          />
        </ChartSettingItemContainer>
      );
  }
};

export default Setting;
