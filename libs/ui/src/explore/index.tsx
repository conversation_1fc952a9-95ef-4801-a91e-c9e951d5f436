import React, { use<PERSON><PERSON>back, useEffect, useMemo, useRef, useState } from 'react';
import { ErrorBoundary } from 'react-error-boundary';

import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { ExternalGraph, getLinkID, getNodeID, QueryMeta } from '@tigergraph/tools-models';
import { Modal, ModalBody, ModalButton, ModalFooter, ModalHeader } from '@tigergraph/app-ui-lib/modal';

import { useQueries, useSchema } from '../insights/pages/chart/useMetaHook';
import { SearchItem } from '../insights/charts';
import { isShowSchema } from '../insights/charts/chartSearch';
import { useGSQLQuery } from '../insights/charts/query/useGSQLQuery';
import { ErrorDisplay } from '../insights/components/error';
import ChartInput from '../insights/charts/query/chartInput';
import { ChartProps } from '../insights/chart';
import { addGraph, convertGraphToCytoscape, convertSchemaToGraph } from '../graph/data';
import Graph from '../graph';
import { CytoscapeExtensions, GraphRef, NodePositions, SettingType } from '../graph/type';
import { Core, Position } from 'cytoscape';
import { filterGraphData, GraphFilter, GraphFilters } from '../insights/charts/graphNew/graphFilter';
import { getUserUploadedIconPathPrefix } from '../insights/utils/path';
import { Spinner } from '@tigergraph/app-ui-lib/spinner';
import { useCallbackRef } from '../lib/useCallbackRef';
import { ReactComponent as EmptyIcon } from './empty.svg';
import { Body2 } from '@tigergraph/app-ui-lib/typography';
import { Button } from '@tigergraph/app-ui-lib/button';
import { Plus } from 'baseui/icon';

type Props = {
  baseURL: string;
  graphName: string;
  graphs: string[];

  placeHolder?: string;
  sendIcon?: React.ReactNode;

  themeType?: 'light' | 'dark';
  isClusterMode?: boolean;
  isCloud?: boolean;
  isEditMode?: boolean;

  initialGraphData?: ExternalGraph;
  disableCache?: boolean;
};

// 1. support edit mode
// 2. keep previous result when run query

// We can not reuse chart.tsx from insights as
//	1. state is managed by useQuery so it can not support our local first state management
//  2. code is generic to all widget, so it's hard to add graph specific feature

const graphLimit = 5000;

export function Explore({
  baseURL,
  graphName,
  graphs,
  placeHolder,
  sendIcon,
  isClusterMode,
  themeType,
  isCloud,
  isEditMode,
  initialGraphData,
  disableCache,
}: Props) {
  const [css] = useStyletron();
  const [showWarning, setShowWarning] = useState(false);

  const userUploadedIconPathPrefix = useMemo(() => {
    return getUserUploadedIconPathPrefix(isCloud);
  }, [isCloud]);

  const cyRef = useRef<(Core & CytoscapeExtensions) | null>(null);
  const graphRef = useRef<GraphRef>();
  const [graph, setGraph] = useState<ExternalGraph>(() => {
    if (initialGraphData) {
      return initialGraphData;
    }
    return {
      nodes: [],
      links: [],
    };
  });

  const [settings, setSettings] = useState<Record<string, any>>({
    showUndoRedo: true,
  } as SettingType);

  const onSettingUpdate = useCallback((key: string, value: any) => {
    setSettings((settings) => ({
      ...settings,
      [key]: value,
    }));
  }, []);

  const [searchPattern, setSearchPattern] = useState<SearchItem[]>([]);
  const [cachedSearchPattern, setCachedSearchPattern] = useState<SearchItem[]>([]);
  const [patternLimit, setPatternLimit] = useState(5);
  const [hopLimit, setHopLimit] = useState(5);
  const [timeLimit, setTimeLimit] = useState(0);
  const [memoryLimit, setMemoryLimit] = useState(0);

  // need to skip initial reset as we may have initial graph data
  const isFirstRenderRef = useRef(true);
  // when graph name change, reset graph as our schema is graph scoped
  useEffect(() => {
    if (isFirstRenderRef.current === true) {
      isFirstRenderRef.current = false;
      return;
    }

    setGraph({
      nodes: [],
      links: [],
    });
    setCachedSearchPattern([]);
    setSearchPattern([]);
  }, [graphName, setGraph]);

  const showSchema = isShowSchema(searchPattern?.[0]);

  const {
    schema,
    isLoading: isLoadingSchema,
    isError: isSchemaError,
    error: schemaError,
  } = useSchema(baseURL, graphName);

  const {
    queries,
    isLoading: isLoadingQueries,
    isError: isQueriesError,
    error: queriesError,
  } = useQueries(baseURL, graphName);

  const filteredQueries = useMemo(() => {
    const filteredQueries: { [key: string]: QueryMeta } = {};
    for (const [key, value] of Object.entries(queries)) {
      // for cluster mode, only allowed installed queries
      if (isClusterMode && value.installed && value.originalCode) {
        filteredQueries[key] = value;
      } else if (value.originalCode) {
        filteredQueries[key] = value;
      }
    }
    return filteredQueries;
  }, [isClusterMode, queries]);

  const { isError, error, refetch, isFetching } = useGSQLQuery({
    baseURL,
    cache: false,
    force: false,
    queryType: 'pattern',
    searchPattern: cachedSearchPattern,
    query: '',
    staticData: '{}',
    patternLimit: patternLimit,
    globalParameters: {},
    queries,
    queriesError,
    schema,
    schemaError,
    graphName,
    refreshRate: 0,
    isClusterMode,
    memoryLimit,
    timeLimit,
    manual: true,
    onSuccess: (chartData) => {
      const { graph: newData } = chartData;
      const { deltaGraph } = addGraph(graph, newData);

      // calculate the previous cola lock status
      const previousGraph = graph;
      const colaLockStatus = new Map<string, boolean>();
      for (let node of previousGraph.nodes) {
        const target = cyRef.current.getElementById(getNodeID(node));
        const isColaLocked = target.scratch('__cola_locked');
        if (isColaLocked) {
          colaLockStatus.set(getNodeID(node), true);
        }
        // lock before run layout
        target.scratch('__cola_locked', true);
      }

      if (graph.nodes.length + deltaGraph.nodes.length + graph.links.length + deltaGraph.links.length > graphLimit) {
        setShowWarning(true);
        return;
      }

      // add new graph result by call undo.do so we can undo it by use toolbar
      graphRef.current.undoRedoRef.current.do('add', {
        deltaGraph,
      });

      setTimeout(() => {
        if (graphRef.current) {
          if (graph.nodes.length === 0) {
            // note: we need to run with fit: false here, or else the edge will not show correctly!!!
            graphRef.current.runLayout({
              randomize: true,
              fit: false,
              centerGraph: true,
            });
          } else {
            // keep consistent with node expansion
            const collection = cyRef.current.collection();
            for (let node of newData.nodes) {
              collection.merge(cyRef.current.getElementById(getNodeID(node)));
            }
            for (let link of newData.links) {
              collection.merge(cyRef.current.getElementById(getLinkID(link)));
            }

            graphRef.current.runLayout(
              {
                eles: collection,
                randomize: true,
                fit: false,
                centerGraph: false,
              },
              () => {
                for (let node of previousGraph.nodes) {
                  const target = cyRef.current.getElementById(getNodeID(node));
                  const isColaLocked = colaLockStatus.get(getNodeID(node));

                  // only unlock if previous is unlocked
                  if (!isColaLocked) {
                    target.removeScratch('__cola_locked');
                  }
                }
              }
            );

            // perf optimization: only select when the collection is small
            if (collection.length <= 100) {
              collection.select();
            }
          }
        }
      }, 50);
    },
  });

  useEffect(() => {
    if (graphRef.current) {
      graphRef.current.setDrawMode(!showSchema && isEditMode);
    }
  }, [showSchema, isEditMode]);

  const unifiedChartData = useMemo(() => {
    const chartData = !showSchema
      ? { graph, results: {}, tables: [] }
      : { graph: convertSchemaToGraph(schema.results), results: {}, tables: [] };

    return chartData;
  }, [schema, graph, showSchema]);

  // this is only to render ChartInput.
  const chartProps: ChartProps = {
    baseURL,
    id: 'explore-graph',
    graphName: graphName,
    schema: schema,
    chartData: unifiedChartData,
    isEditMode,
    settings,
    onSettingUpdate,
    chartStates: {},
    onChartStateUpdate: () => {},
    setGlobalParameter: () => {},
    // @ts-ignore
    getGlobalParameter: () => {},
    emptyNodesLinksView: null,
    globalParameters: {},
    links: [],
    getLink: () => '',
    isCloud,
    isClusterMode,
    themeType,
  };

  // graph filtering
  useEffect(() => {
    if (!cyRef.current) {
      return;
    }
    const graphFilters: GraphFilters = settings.graphFilters ?? {
      nodes: {},
      links: {},
    };
    const activeElements = convertGraphToCytoscape(filterGraphData(graph, graphFilters)[0]),
      inactiveElements = convertGraphToCytoscape(filterGraphData(graph, graphFilters)[1]);

    for (let ele of activeElements) {
      cyRef.current.getElementById(ele.data.id).removeClass('filterInactive');
    }
    for (let ele of inactiveElements) {
      cyRef.current.getElementById(ele.data.id).addClass('filterInactive');
    }
  }, [graph, settings.graphFilters]);

  // save
  const save = () => {
    if (!cyRef.current) {
      return;
    }

    const { nodes } = graph;

    // do not overwrite the cache
    if (nodes.length === 0) {
      return;
    }

    const positions: NodePositions = {};

    for (let node of nodes) {
      let eleNode = cyRef.current.getElementById(getNodeID(node));
      positions[getNodeID(node)] = eleNode.position();
    }

    const cacheResult: CacheData = {
      baseURL,
      graphName,

      graph,
      positions,

      settings,
      viewport: {
        zoom: cyRef.current.zoom(),
        pan: cyRef.current.pan(),
      },
    };
    sessionStorage.setItem('explore_cache', JSON.stringify(cacheResult));
  };

  const saveCallbackRef = useCallbackRef(save);

  // only save when component is destroyed to get the correct position etc
  useEffect(() => {
    return () => {
      if (disableCache) {
        return;
      }
      saveCallbackRef();
    };
  }, [saveCallbackRef, disableCache]);

  // restore
  const restore = () => {
    if (disableCache) {
      return;
    }
    if (!cyRef.current) {
      return;
    }

    if (initialGraphData) {
      return;
    }

    const cacheResult = sessionStorage.getItem('explore_cache');
    if (cacheResult) {
      const cache = JSON.parse(cacheResult) as CacheData;
      // clean cache
      sessionStorage.removeItem('explore_cache');

      // check if the context is changed
      if (cache.baseURL !== baseURL || cache.graphName !== graphName) {
        return;
      }

      const {
        graph,
        positions,
        settings,
        viewport: { pan, zoom },
      } = cache;

      setSettings(settings);
      setGraph(graph);
      setTimeout(() => {
        for (let [id, pos] of Object.entries(positions)) {
          cyRef.current.getElementById(id).position(pos);
        }
        cyRef.current.zoom(zoom);
        cyRef.current.pan(pan);
      }, 100);
    }
  };

  const [haveShowGraph, setHaveShowGraph] = useState(false);
  useEffect(() => {
    if (!haveShowGraph) {
      if (graph.nodes.length > 0) {
        setHaveShowGraph(true);
      } else if (haveCacheResult(baseURL, graphName)) {
        // have cache result
        setHaveShowGraph(true);
      } else if (searchPattern.length > 0) {
        setHaveShowGraph(true);
      }
    }
  }, [searchPattern, haveShowGraph, baseURL, graphName, graph]);

  return (
    <div
      className={css({
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
      })}
    >
      <ChartInput
        isFreeExplore={true}
        isClusterMode={isClusterMode}
        placeHolder={placeHolder}
        sendIcon={sendIcon}
        queryType="pattern"
        searchPattern={searchPattern}
        onSearchPatternChanged={(searchPattern) => {
          setSearchPattern(searchPattern);
          if (isShowSchema(searchPattern?.[0])) {
            setGraph({
              nodes: [],
              links: [],
            });
            setTimeout(() => {
              if (graphRef.current) {
                graphRef.current.runLayout({
                  randomize: true,
                  fit: false,
                  centerGraph: true,
                });

                // reset undo/redo stack
                graphRef.current.undoRedoRef.current.reset();
              }
            }, 50);
          }
        }}
        patternLimit={patternLimit}
        hopLimit={hopLimit}
        timeLimit={timeLimit}
        memoryLimit={memoryLimit}
        interpretQuery=""
        staticData={'{}'}
        graphName={graphName}
        graphs={graphs}
        onGraphChanged={() => {}}
        chartProps={chartProps}
        globalParameters={{}}
        queries={filteredQueries}
        isLoading={isLoadingSchema || isLoadingQueries}
        isError={isSchemaError || isQueriesError}
        onQueryTypeChange={() => {}}
        onInterpretQueryChanged={() => {}}
        onPatternLimitChanged={setPatternLimit}
        onHopLimitChanged={setHopLimit}
        onMemoryLimitChanged={(limit) => setMemoryLimit(Number(limit))}
        onTimeLimitChanged={(limit) => setTimeLimit(Number(limit))}
        onStaticDataChanged={() => {}}
        onRunQuery={() => {
          setCachedSearchPattern(searchPattern);
          setTimeout(() => {
            refetch();
          });
        }}
      />
      {isError ? <ErrorDisplay error={error} label="GSQL Error:" isGSQLError={true} /> : null}
      <ErrorBoundary
        FallbackComponent={({ error }) => {
          return <ErrorDisplay error={error} label={`Rendering Error:`} />;
        }}
        // isLoading indicate when rerun the query
        resetKeys={[isFetching]}
      >
        <div
          className={css({
            flexGrow: 1,
            flexBasis: 0,
            minHeight: 0,
            boxSizing: 'border-box',
            position: 'relative',
            overflow: 'hidden',
          })}
        >
          {isFetching ? (
            <div
              className={css({
                position: 'absolute',
                left: 0,
                right: 0,
                top: 0,
                display: 'flex',
                justifyContent: 'center',
                paddingTop: '16px',
              })}
            >
              <Spinner $size={'16px'} $borderWidth={'2px'} />
            </div>
          ) : null}
          {!haveShowGraph ? (
            <Empty
              onNewVertex={() => {
                setHaveShowGraph(true);
                setTimeout(() => {
                  graphRef.current.onNewVertex();
                });
              }}
              hideAddVertex={!isEditMode || isLoadingSchema || isSchemaError || schema.results.VertexTypes.length === 0}
            />
          ) : null}
          {/* wait until schema is load */}
          {!isLoadingSchema && !isSchemaError && haveShowGraph ? (
            <Graph
              baseURL={baseURL}
              id="explore-graph"
              graphName={graphName}
              parentRef={cyRef}
              onCytoscapeMount={() => {
                // !!! wait until the existing effect is fired
                setTimeout(() => {
                  restore();
                }, 20);
              }}
              ref={(ref) => {
                graphRef.current = ref;
                if (ref) {
                  ref.setDrawMode(isEditMode);
                }
              }}
              isEditMode={isEditMode}
              schema={schema.results}
              graph={unifiedChartData.graph}
              // if we have initialGraphData, we do want auto layout
              disableAutoLayout={!initialGraphData}
              onGraphChange={setGraph}
              isSchemaGraph={showSchema}
              settings={settings}
              onSettingUpdate={onSettingUpdate}
              insights={true}
              showEdgeHandler={() => {
                if (showSchema) {
                  return false;
                }
                return true;
              }}
              onCreateLink={(source, target) => {
                if ('id' in target) {
                  graphRef.current.onNewEdge(source, target);
                }
              }}
              userUploadedIconPathPrefix={userUploadedIconPathPrefix}
              themeType={themeType}
              hoverTooltipDelay={0}
            >
              {!showSchema ? (
                <GraphFilter graphData={graph} schema={schema} settings={settings} onSettingUpdate={onSettingUpdate} />
              ) : null}
            </Graph>
          ) : null}
        </div>
      </ErrorBoundary>
      <GraphSizeWarning isOpen={showWarning} onClose={() => setShowWarning(false)} />
    </div>
  );
}

type CacheData = {
  baseURL: string;
  graphName: string;

  // graph data and position
  graph: ExternalGraph;
  positions: NodePositions;

  // 1. graph filter
  // 2. layout option
  settings: Record<string, any>;

  viewport: { zoom: number; pan: Position };
};

function Empty({ onNewVertex, hideAddVertex }: { onNewVertex: () => void; hideAddVertex: boolean }) {
  const [css, theme] = useStyletron();

  return (
    <div
      className={css({
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        width: 'fit-content',
        marginLeft: 'auto',
        marginRight: 'auto',
        height: '100%',
      })}
    >
      <EmptyIcon color={theme.colors['background.accent.neutral.subtler']} />
      <Body2
        className={css({
          marginTop: '24px',
          color: theme.colors['text.secondary'],
        })}
      >
        Search for a pattern in the search bar above to explore the graph
      </Body2>
      {hideAddVertex ? null : (
        <>
          <div
            className={css({
              padding: '24px 0',
              display: 'flex',
              gap: '8px',
              alignItems: 'center',
              alignSelf: 'stretch',
            })}
          >
            <div
              className={css({
                height: '1px',
                background: theme.colors['border.tertiary'],
                flex: 1,
              })}
            />
            <Body2
              className={css({
                color: theme.colors['text.secondary'],
              })}
            >
              or
            </Body2>
            <div
              className={css({
                height: '1px',
                background: theme.colors['border.tertiary'],
                flex: 1,
              })}
            />
          </div>
          <Body2
            className={css({
              color: theme.colors['text.secondary'],
              marginBottom: '16px',
            })}
          >
            You can add a new vertex to get started.
          </Body2>
          <Button kind="secondary" size="large" startEnhancer={<Plus />} onClick={onNewVertex}>
            Add Vertex
          </Button>
        </>
      )}
    </div>
  );
}

function haveCacheResult(baseURL: string, graphName: string) {
  const cacheResult = sessionStorage.getItem('explore_cache');
  if (cacheResult) {
    const cache = JSON.parse(cacheResult) as CacheData;
    if (cache.baseURL === baseURL && cache.graphName === graphName) {
      return true;
    }
  }
  return false;
}

function GraphSizeWarning({ isOpen, onClose }: { isOpen: boolean; onClose: () => void }) {
  return (
    <Modal onClose={onClose} isOpen={isOpen}>
      <ModalHeader>Warning</ModalHeader>
      <ModalBody>Unable to render graph since graph size is too large.</ModalBody>
      <ModalFooter>
        <ModalButton kind="secondary" onClick={onClose}>
          Confirm
        </ModalButton>
      </ModalFooter>
    </Modal>
  );
}
