import cytoscape, {
  Core,
  Layouts,
  ElementDefinition,
  Ext,
  Stylesheet,
  LayoutOptions,
  EventHandler,
  Collection,
  EventObject,
} from 'cytoscape';
import React, { useRef, useMemo, useEffect, useState, useCallback, MutableRefObject, forwardRef } from 'react';

import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import useSize from 'ahooks/lib/useSize';
import { NavigateFunction } from 'react-router';
import { StatefulPopover } from '@tigergraph/app-ui-lib/popover';
import { TRIGGER_TYPE } from 'baseui/popover';
import { Button } from '@tigergraph/app-ui-lib/button';
import { SHAPE } from 'baseui/button';
import { expand } from 'inline-style-expand-shorthand';

import { TbLayoutSidebarLeftExpand, TbLayoutSidebarRightExpand } from '../react-icons';

import { get, toJ<PERSON>, forEach } from './react-cytoscapejs/json';
import { patchElements } from './react-cytoscapejs/patch';
import { shallowObjDiff } from './react-cytoscapejs/diff';

import popper from 'cytoscape-popper';
import dagre from 'cytoscape-dagre';
// @ts-ignore
import fcose from 'cytoscape-fcose';
import cytoscapeCola from './cytoscape-cola';
// @ts-ignore
import edgeConnections from './cytoscape-edge-connections';
// @ts-ignore
import edgehandles from '@tigergraph/cytoscape-edgehandles';

import { Provider, createGraphForCytoscape } from './context';

import { createStyleSheet } from './stylesheet';
import { convertGraphToCytoscape, canonicalizeGraph, removeDuplicateEdges, isSameGraph } from './data';

import Bottom from './ui/bottom';

import { CytoscapeExtensions, GraphEvents, GraphRef, NodePositions, Schema, SettingType, VertexType } from './type';
import { ExternalGraph } from '@tigergraph/tools-models/gvis/insights';
import { Hover, shouldHandleEvent } from './ui/hover';
import { useGraphEvents } from './hooks';
import { useUndoRedo } from './useUndoRedo';
import TopRight from './ui/topRight';
import LeftUI from './ui/leftUI';
import { updateGraphStyle, updateGraphLabel, elementsToGraph } from './util';
import { GlobalParams } from '../insights/chart';
import { getLayoutByLabel } from './layouts';
import { CytoscapeComponent2 } from './react-cytoscapejs/component';
import { CreateEdgeTooltip } from './ui/createEdgeTooltip';
import StyledToasterContainer from '@tigergraph/app-ui-lib/styledToasterContainer';
import GraphContextMenu from './react-menu/ContextMenu';
import { useEditHook } from './edit/useEditHook';
import { ElementPanel } from './edit/elementPanel';
import { useCallbackRef } from '../lib/useCallbackRef';
import { getGraphNodesIconStyleSheet } from './icon';
import { useAsyncEffect } from 'ahooks';

cytoscape.use(dagre);
// @ts-ignore
cytoscape.use(cytoscapeCola as Ext);
cytoscape.use(fcose);
cytoscape.use(edgeConnections);
cytoscape.use(popper);
cytoscape.use(edgehandles);

// the following code is comment out, we may later need to debug memory leak issue

// let wasmInstance: any;
// async function initWasm() {
//   // @ts-ignore
//   wasmInstance = await cytoscapeCola.cola.getDerivativeComputerWasm();
// }

// initWasm();

// function debugMemory() {
//   if (!wasmInstance) {
//     return;
//   }
//   // @ts-ignore
//   console.log(wasmInstance.getWasm().memory.buffer.byteLength / 1024 / 1024);
// }

// setInterval(() => {
//   debugMemory();
// }, 1000);

const graphWidgetStyle = { width: '100%', height: '100%', position: 'relative', zIndex: '0' };

const defaultSetting: Required<SettingType> = {
  layout: 'Force',
  skipAutoLayout: false,

  edgeLength: 250,

  spacingFactor: 1.5,

  nodeSep: 50,
  edgeSep: 50,
  rankSep: 200,
  rankDir: 'TB',
  align: 'Center',

  showThumbNail: false,
  showUndoRedo: false,

  actionsByType: {},
  rulesByType: {},
  attrMapGraphLabel: {},
  attrMapGraphLabelShow: {},
};

// how the graph data (ExternalGraph data is managed)
// case 1: as insight widget, chart.tsx/useGSQLQuery.ts will handle it as useQuery state
// case 2: graph studio/cloud-portal design schema
// case 3: graph studio explore graph: the state is managed by parent component
// case 4: cloud portal explore graph

export type GraphProps = {
  baseURL: string;
  id: string;
  schema: Schema;
  globalTypes?: string[];

  graphName: string;

  graph: ExternalGraph;
  // for schema graph, we will hide some ui like context menu, table etc.
  isSchemaGraph?: boolean;
  hideLeftUI?: boolean;
  hideContextMenu?: boolean;
  hideSearch?: boolean;
  // for view mode, we can switch layout, but for edit mode, we use preset layout
  schemaMode?: 'edit' | 'view';
  presetNodePositions?: NodePositions;
  disableAutoLayout?: boolean;

  onGraphChange: (graph: ExternalGraph) => void;

  settings: SettingType;
  onSettingUpdate?: (key: string, value: any) => void;

  parentRef?: MutableRefObject<(Core & CytoscapeExtensions) | null>;
  onCytoscapeMount?: () => void;

  // this is needed for insights, we evaluate graph styling based on GlobalParams
  globalParameters?: GlobalParams;
  // for mapping data and load data, we need to createAuxNode.
  createEdgeAuxNode?: boolean;

  navigate?: NavigateFunction;
  links?: { pageName: string; pageID: string; params: GlobalParams }[];
  getLink?: (pageID: string, params: Record<string, string>) => string;
  isEditMode?: boolean; // support graph edit mode
  isWidgetEditMode?: boolean; // edit mode for widget
  showHoverTooltip?: boolean;
  hoverTooltipDelay?: number;
  showCreateEdgeTooltip?: boolean;

  userUploadedIconPathPrefix?: string;

  children?: React.ReactNode;

  insights?: boolean;

  // control whether to show ghost node
  showGhostNode?: boolean;

  themeType?: 'light' | 'dark';
} & GraphEvents;

const defaultGlobalType: string[] = [];
const defaultOnSettingUpdate = () => {};
const defaultGlobalParameters = {};

const Graph = forwardRef<GraphRef, GraphProps>((props, ref) => {
  const {
    baseURL,
    id,
    schema,
    globalTypes = defaultGlobalType,
    graphName,
    isSchemaGraph = false,
    hideLeftUI,
    hideContextMenu,
    hideSearch,
    presetNodePositions,
    disableAutoLayout,
    onGraphChange,
    settings,
    onSettingUpdate = defaultOnSettingUpdate,
    parentRef,
    onCytoscapeMount,
    globalParameters = defaultGlobalParameters,
    createEdgeAuxNode,

    navigate,
    links,
    getLink,
    isEditMode = false,
    isWidgetEditMode = false,
    showHoverTooltip = true,
    showCreateEdgeTooltip = false,
    hoverTooltipDelay = 500,

    userUploadedIconPathPrefix,

    insights = false,
  } = props;

  const effectiveSettings = useMemo(() => {
    return {
      ...defaultSetting,
      ...settings,
    };
  }, [settings]);

  const {
    layout,
    skipAutoLayout,

    // Force Cola
    edgeLength,

    // Radial && Layer
    spacingFactor,

    // Tree
    nodeSep,
    edgeSep,
    rankSep,
    rankDir,
    align,

    showThumbNail,
    showUndoRedo,

    actionsByType,
    rulesByType,
  } = effectiveSettings;

  // treat cy as state,as we want to rerender when get cy instance.
  const [cy, setCy] = useState<(Core & CytoscapeExtensions) | null>(null);

  const layoutsRef = useRef<Layouts | null>(null);
  const layoutTimerRef = useRef<NodeJS.Timeout | null>(null);

  const elementsRef = useRef<ElementDefinition[]>([]);

  const [showAnalysis, setShowAnalysis] = useState(false);

  const {
    onNewEdge,
    setSelectEdge,
    setSelectNode,
    showDetailPanel,
    setShowDetailPanel,
    setShowNewVertexForm,
    setShowNewEdgeForm,
    onNewVertex,
    ...editProps
  } = useEditHook(isSchemaGraph, props.graph, isEditMode);
  const isFirstElementClickRef = useRef(false);

  const setShowThumbNail = useCallback(
    (showThumbNail: boolean) => {
      onSettingUpdate('showThumbNail', showThumbNail);
    },
    [onSettingUpdate]
  );

  const [css, theme] = useStyletron();

  const domRef = useRef<HTMLElement | null>(null);
  const size = useSize(domRef);

  const [previousGraph, setPreviousGraph] = useState<ExternalGraph>(props.graph);

  const memoGraphFromProps = useMemo(() => {
    if (isSameGraph(props.graph, previousGraph)) {
      return previousGraph;
    }
    // track previous graph
    setPreviousGraph(props.graph);
    return props.graph;
  }, [props.graph, previousGraph]);

  const graph = useMemo(() => {
    let canonicalizedGraph = canonicalizeGraph(memoGraphFromProps, schema);
    // for init data loading, after reverse the reverse edge, remove duplicate edge.
    canonicalizedGraph = removeDuplicateEdges(canonicalizedGraph);
    return canonicalizedGraph;
  }, [schema, memoGraphFromProps]);

  const layoutName = getLayoutByLabel(layout);

  const layoutOptions: LayoutOptions = useMemo(() => {
    return {
      name: layoutName,
      // todo(lin)
      // set avoidOverlap to false to improve layout performance for cola layout
      // also for wasm-cola integration, we should set avoidOverlap to false
      avoidOverlap: layoutName === 'cola' ? false : true,
      // animate should set to true, or else cytoscape layout algorithm will block ui thread
      animate: true,
      randomize: true,
      fit: false,
      padding: 24,

      // todo(lin)
      //  for cola, this may effect the layout performance, need to do some test
      nodeDimensionsIncludeLabels: true,
      quality: 'proof',

      // since we show reverse label in schema graph, we need to invert the edge length
      edgeLength: edgeLength,
      handleDisconnected: true,
      // known issue in cola layout: if we start graph with nodes and no edges
      // the if we drag and drop node, the node will move to the initial position
      infinite: true,

      spacingFactor,
      directed: true,

      nodeSep,
      edgeSep,
      rankSep,
      rankDir,
      // special handling for tree align
      align: align === 'Center' ? undefined : align,
    };
  }, [layoutName, edgeLength, spacingFactor, nodeSep, edgeSep, rankSep, rankDir, align]);

  // known issue:  aux node/edge will cause cola layout unstable
  // we currently do not run force layout on graph with aux node/edge
  const runLayout = useCallback(
    (extraParams?: Record<string, any>, onLayoutStop?: () => void) => {
      if (!cy) {
        return;
      }

      if (layoutsRef.current) {
        // when runLayout, we may lock node before layout and unlock after layout stop
        // In case we have previous layout still running, we want to first off the event listener
        // so the layout stop event will not tigger to unlock node incorrectly
        cy.off('layoutstop');
        layoutsRef.current.stop();
        layoutsRef.current = null;
      }

      if (layoutTimerRef.current) {
        clearTimeout(layoutTimerRef.current);
        layoutTimerRef.current = null;
      }

      // default fit to false
      let fit = true;

      const haveFitSetting = extraParams && 'fit' in extraParams;

      if (haveFitSetting) {
        fit = extraParams['fit'] as boolean;
      } else if (layoutOptions.name === 'preset') {
        // !!! for preset layout, we need set fit to false
        fit = false;
      }

      const eles: Collection | undefined = extraParams?.eles;
      if (eles) {
        const { eleType, ...props } = extraParams;
        extraParams = props;
      }

      // support runLayout to pass layout as extraParams
      const layoutName = extraParams?.layout || layoutOptions.name;

      const finalOptions = {
        ...layoutOptions,
        fit,
        name: layoutName,
        avoidOverlap: layoutName === 'cola' ? false : true,
        ...extraParams,
        // for large graph, we want large refresh rate so the layout is much quicker to converge but also more jerky
        // refresh: getRefreshSettingForGraph(cy),
      };

      cy.maxZoom(2);

      let layout = cy.layout(finalOptions);
      if (eles) {
        layout = eles.layout(finalOptions);
      }

      cy.one('layoutstop', () => {
        if (onLayoutStop) {
          onLayoutStop();
        }

        // for graph without edges, we stop force layout so nodes drag works (a known issue for cola layout)
        if (finalOptions.name === 'cola' && cy.edges().length === 0) {
          if (layoutsRef.current) {
            layoutsRef.current.stop();
            layoutsRef.current = null;
          }
        }
      });
      // Skip running layout if it's preset layout
      if (finalOptions.name !== 'preset') {
        // todo
        // for webcola layout, it use priority queue to calculate min distance between nodes
        // for 5k node/edges, it may take 1000ms to do the calculation
        // we can try to use https://github.com/lemire/FastPriorityQueue.js to optimize this.
        layout.run();
        // when layout is preset, we need to manually call layoutstop
      } else if (onLayoutStop) {
        onLayoutStop();
      }
      cy.maxZoom(10);

      layoutsRef.current = layout;

      if (finalOptions.name === 'cola') {
        // refer https://www.sciencedirect.com/science/article/pii/S0012365X08000083 4.1. Running time
        let verticesCount = cy.nodes().length;
        let edgesCount = cy.edges().length;

        if (eles) {
          verticesCount = eles.nodes().length;
          edgesCount = eles.edges().length;
        }

        // n: 1337, m: 1208, maxLayoutTime 5958ms
        // n: 2085, m: 2679, maxLayoutTime 18619ms
        // @ts-ignore for node expansion (with randomize false), reduce the maxLayoutTime
        let maxLayoutTime =
          Math.max(verticesCount * verticesCount, verticesCount * edgesCount) / (finalOptions['randomize'] ? 50 : 100);

        if (edgesCount === 0) {
          maxLayoutTime = 1000;
        }
        if (maxLayoutTime <= 1500) {
          maxLayoutTime = 1500;
        }

        if (
          layoutsRef.current &&
          // @ts-ignore
          layoutsRef.current.options.name === 'cola' &&
          // when grabon, use release event to stop layout.
          !(extraParams && extraParams['grabon'])
        ) {
          layoutTimerRef.current = setTimeout(() => {
            if (layoutsRef.current) {
              layoutsRef.current.stop();
              layoutsRef.current = null;
            }
            layoutTimerRef.current = null;
          }, maxLayoutTime);
        }
      }
      // for force layout
      // first layout with `fit: true`
      // then set layout options to `fit: false`, so user can drag and move nodes.
      // we could do this in 'layoutstop' callback, but for large graph, it take long time (> 10s) to reach layoutstop state,
      // we need set this to false so we can render edge correctly.
      if (finalOptions.name === 'cola' && fit) {
        setTimeout(() => {
          if (layoutsRef.current) {
            // @ts-ignore
            layoutsRef.current.options.fit = false;
          }
        }, 50);
      }
    },
    [layoutOptions, cy]
  );

  useEffect(() => {
    // handle node dragging
    // type for collection event (grab free) is missing type cytoscape, so we use any
    const grabHandler: EventHandler = function (e: any) {
      const target = e.target;

      if (!target || !target.isNode()) {
        return;
      }

      // @ts-ignore, only consider cola layout
      if (layoutsRef.current && layoutsRef.current.options.name !== 'cola') {
        return;
      }

      // if no edge, skip force layout
      if (cy?.edges().length === 0) {
        return;
      }

      // add check if cola layout is running, is so, just return
      if (layoutTimerRef.current) {
        return;
      }

      switch (e.type) {
        case 'grabon':
          const originalEvent = e.originalEvent;
          // disable interactive drag if alt/options key is pressed
          if (originalEvent.altKey) {
            return;
          }
          target.removeScratch('__cola_locked');
          // if (cy && shouldDisableInteractiveLayout(cy)) {
          //   return;
          // }
          runLayout({
            randomize: false,
            fit: false,
            centerGraph: false,
            grabon: true,
          });
          break;
        case 'freeon':
          target.scratch('__cola_locked', true);

          // @ts-ignore
          if (layoutsRef.current && layoutsRef.current.options.name === 'cola') {
            // @ts-ignore
            layoutsRef.current.stop();
            layoutsRef.current = null;
          }
          break;
      }
    };

    // we need to runLayout before drag start so we listen on `grabon` event
    // if we listen on `grab` event, the code on cytoscape-cola will run first and the drag will not work
    cy?.on('grabon freeon ', grabHandler);

    return () => {
      cy?.off('grabon freeon', grabHandler);
    };
  }, [cy, runLayout, layoutsRef]);

  // handle widget resize
  useEffect(() => {
    if (cy) {
      cy.resize();
      // Could cause joggle when GST is changing window size
      // fitView(cyRef.current);
    }
  }, [size, cy]);

  // generate elements based on graph data
  useEffect(() => {
    if (!cy) {
      return;
    }

    if (cy.destroyed()) {
      console.error('cy is destroyed');
      return;
    }

    let elements: ElementDefinition[] = [];
    elements = convertGraphToCytoscape(graph, presetNodePositions);

    // we skip run layout when partially change graph
    const isFirstTime = elementsRef.current.length === 0;

    cy.batch(() => {
      // Need to remove edge scratch first
      // Otherwise, it would conflict with removing aux nodes and throw errors
      if (createEdgeAuxNode) {
        cy.edges().forEach((e) => {
          e.removeScratch('edgeConnections');
        });

        // assume if createEdgeAuxNode is true, we use preset layout, so clean all then recreate is fine here.
        cy.remove('*');

        // Make batch updates to nodes only
        const nodeEles = elements.slice(0, graph.nodes.length);
        cy.add(nodeEles);
      } else {
        if (isFirstTime) {
          cy.add(elements);
        } else {
          // patch use batch internally
          patchElements(cy, elementsRef.current, elements, toJson, get, forEach, shallowObjDiff);
        }
      }
    });

    elementsRef.current = elements;

    // Can't make batch updates to aux nodes,
    // Otherwise the aux node position of curved edges would be incorrect.
    if (createEdgeAuxNode) {
      const edgeEles = elements.slice(graph.nodes.length);
      // create aux nodes for each edge
      cy.edgeConnections().addEdges(edgeEles);
    }
  }, [cy, graph, presetNodePositions, schema, runLayout, createEdgeAuxNode]);

  // any layout options change, we rerun the layouts
  useEffect(() => {
    if (!cy) {
      return;
    }
    // todo: unify these into single props
    if (skipAutoLayout || disableAutoLayout) {
      return;
    }
    runLayout();
  }, [runLayout, skipAutoLayout, disableAutoLayout, cy]);

  // @ts-ignore
  const commonStyleSheets: Stylesheet[] = useMemo(() => {
    return createStyleSheet(baseURL, schema, theme, userUploadedIconPathPrefix);
  }, [baseURL, schema, theme, userUploadedIconPathPrefix]);
  const [iconStyleSheets, setIconStyleSheets] = useState<Stylesheet[]>([]);
  const styleSheets = useMemo(() => [...iconStyleSheets, ...commonStyleSheets], [iconStyleSheets, commonStyleSheets]);

  // update node style as CytoscapeComponent WILL NOT do that.
  useEffect(() => {
    if (cy) {
      updateGraphStyle(cy, rulesByType, globalParameters);
    }
  }, [cy, rulesByType, globalParameters]);

  useAsyncEffect(async () => {
    const iconStyles = await getGraphNodesIconStyleSheet({
      schema,
      userUploadedIconPathPrefix,
      svgAttributes: { color: '#fff', width: '32px', height: '32px' },
    });
    setIconStyleSheets(iconStyles);
  }, [schema, userUploadedIconPathPrefix]);

  const { attrMapGraphLabel, attrMapGraphLabelShow } = settings;
  useEffect(() => {
    // schema graph will show reverse edge label, so we do not update label
    if (cy && schema && !isSchemaGraph) {
      updateGraphLabel(cy, schema, attrMapGraphLabel, attrMapGraphLabelShow);
    }
  }, [cy, attrMapGraphLabel, attrMapGraphLabelShow, schema, graph.nodes, isSchemaGraph]);

  const graphT = useMemo(() => {
    return createGraphForCytoscape(cy!, runLayout);
  }, [cy, runLayout]);

  const { undoRedoRef, undoRedoState } = useUndoRedo(cy, graph, onGraphChange, settings, onSettingUpdate, runLayout);

  useGraphEvents(cy, ref, props, runLayout, onNewEdge, undoRedoRef, onNewVertex);

  const onCytoscapeMountCallback = useCallbackRef(onCytoscapeMount);
  // note:
  // the variable closured by this callback is not upto the latest state.
  // the correct way is to use cy state
  const cyCallback = useCallback(
    (cyInstance: Core & CytoscapeExtensions) => {
      setCy(cyInstance);
      if (cyInstance) {
        if (parentRef) {
          parentRef.current = cyInstance;
        }

        // fix cy.headless issue.
        // @ts-ignore
        cyInstance.headless = () => false;

        // init resize ref
        domRef.current = cyInstance.container();

        onCytoscapeMountCallback();
      }
    },
    [parentRef, onCytoscapeMountCallback]
  );

  useEffect(() => {
    if (cy) {
      const clearSelection = (event: EventObject) => {
        const evtTarget = event.target;

        if (evtTarget === cy) {
          // clear all inactive state in nodes/edges when click background
          cy.nodes().removeClass('inactive');
          cy.edges().removeClass('inactive');
        }
      };

      const onSelect = (event: EventObject) => {
        if (isSchemaGraph) {
          return;
        }
        if (!shouldHandleEvent(event)) {
          return;
        }
        const target = event.target;

        const deltaGraph = elementsToGraph(graph, target);
        if (target && target.isNode() && deltaGraph.nodes.length > 0) {
          setSelectNode(deltaGraph.nodes[0]);
          setSelectEdge(null);
          setShowNewEdgeForm(false);
          setShowNewVertexForm(false);
        } else if (target && target.isEdge() && deltaGraph.links.length > 0) {
          setSelectEdge(deltaGraph.links[0]);
          setSelectNode(null);
          setShowNewEdgeForm(false);
          setShowNewVertexForm(false);
        }

        // show detail pane when first click element
        // only automatically show detail panel once.
        if (!isFirstElementClickRef.current) {
          isFirstElementClickRef.current = true;
          setShowDetailPanel(true);
        }
      };

      cy.on('tap', clearSelection);

      // track node/edge selection
      cy.on('tap', 'node, edge', onSelect);

      return () => {
        cy.off('tap', clearSelection);
        cy.off('tap', 'node, edge', onSelect);
      };
    }
  }, [
    cy,
    graph,
    isSchemaGraph,
    setSelectEdge,
    setSelectNode,
    setShowNewEdgeForm,
    setShowNewVertexForm,
    setShowDetailPanel,
  ]);

  return (
    <div
      className={css({
        width: '100%',
        height: '100%',
        position: 'relative',
        display: 'flex',
      })}
    >
      <Provider
        value={{
          id: id,
          schema,
          graphName: graphName,
          isSchemaGraph: isSchemaGraph,
          globalTypes,
          graphData: graph,
          onGraphDataChanged: onGraphChange,
          graphT,
        }}
      >
        {/* insights already add toastContainer, but gst and others do not, so we add it here */}
        {!insights ? <StyledToasterContainer /> : null}
        <div
          className={css({
            flexGrow: 0,
            flexShrink: 0,
            width: showDetailPanel ? '321px' : 0,
            height: '100%',
            overflow: 'hidden',
            boxSizing: 'border-box',
            borderRight: showDetailPanel ? `1px solid ${theme.colors.divider}` : 'none',
            display: 'flex',
            flexDirection: 'column',
          })}
        >
          <ElementPanel
            baseURL={baseURL}
            setSelectNode={setSelectNode}
            setSelectEdge={setSelectEdge}
            setShowNewEdgeForm={setShowNewEdgeForm}
            setShowNewVertexForm={setShowNewVertexForm}
            resetUndo={undoRedoState.reset}
            {...editProps}
          />
        </div>
        <div
          className={css({
            flexGrow: 0,
            flexShrink: 0,
            width: showDetailPanel ? 'calc(100% - 321px)' : '100%',
            height: '100%',
            position: 'relative',
          })}
        >
          <CytoscapeComponent2
            cy={cyCallback}
            style={graphWidgetStyle}
            stylesheet={styleSheets}
            wheelSensitivity={0.1}
            isSchemaGraph={isSchemaGraph}
            hideEdgesOnViewport={false}
          />
          {cy ? (
            <>
              {!isSchemaGraph && !hideLeftUI && (
                <LeftUI
                  cy={cy}
                  schema={schema}
                  graph={graph}
                  onElementSelect={(id) => {
                    cy.centre(cy.getElementById(id).select());
                  }}
                  showAnalysis={showAnalysis}
                  setShowAnalysis={setShowAnalysis}
                  settings={settings}
                  onSettingUpdate={onSettingUpdate}
                  undoRedoInstance={undoRedoRef.current!}
                />
              )}
              <Bottom
                layout={layout}
                onToggleThumbNail={() => {
                  setShowThumbNail(!showThumbNail);
                }}
                hideThumbNailButton={isSchemaGraph}
                hideLayoutsButton={isSchemaGraph}
                hideSearch={hideSearch}
                showThumbNail={showThumbNail}
                onLayoutChange={(newLayout) => {
                  // clear all locked state
                  if (cy && newLayout === 'Force') {
                    cy.nodes().forEach((e) => {
                      e.removeScratch('__cola_locked');
                    });
                  }
                  if (newLayout === layout) {
                    // rerun layout
                    runLayout();
                  } else {
                    onSettingUpdate('layout', newLayout);
                    // if auto layout is disabled, we need to call run layout by hand
                    if (disableAutoLayout) {
                      runLayout({ layout: getLayoutByLabel(newLayout) });
                    }
                  }
                }}
                themeType={props.themeType}
              />
              {showHoverTooltip && <Hover hoverTooltipDelay={hoverTooltipDelay} />}
              {showCreateEdgeTooltip && <CreateEdgeTooltip themeType={props.themeType} />}
              {!isSchemaGraph && !hideContextMenu ? (
                <GraphContextMenu
                  baseURL={baseURL}
                  layout={layoutOptions.name}
                  actionsByType={actionsByType}
                  navigate={navigate}
                  links={links}
                  getLink={getLink}
                  globalParameters={globalParameters}
                  isEditMode={isEditMode}
                  isWidgetEditMode={isWidgetEditMode}
                  undoRedoRef={undoRedoRef}
                  settings={settings}
                  onSettingUpdate={onSettingUpdate}
                  onShowAnalysis={() => setShowAnalysis(true)}
                  onVertexDoubleClick={props.onDoubleClick}
                  onNewVertex={onNewVertex}
                  resetUndo={undoRedoState.reset}
                />
              ) : null}
              {showUndoRedo && !isSchemaGraph && (
                <TopRight
                  baseURL={baseURL}
                  {...undoRedoState}
                  undoRedoRef={undoRedoRef}
                  onNewVertex={onNewVertex}
                  isEditMode={isEditMode}
                />
              )}
              {!isSchemaGraph ? (
                <StatefulPopover
                  triggerType={TRIGGER_TYPE.hover}
                  content="Display detailed info for selected vertex or edge"
                >
                  <Button
                    kind="secondary"
                    shape={SHAPE.square}
                    onClick={() => {
                      setShowDetailPanel(!showDetailPanel);
                    }}
                    overrides={{
                      BaseButton: {
                        style: {
                          ...expand({
                            border: '1px solid #C2BFBB',
                            borderRadius: '2px',
                          }),
                          position: 'absolute',
                          left: '8px',
                          top: 'calc(50% - 12px)',
                        },
                      },
                    }}
                  >
                    {showDetailPanel ? (
                      <TbLayoutSidebarRightExpand width={16} height={16} pointerEvents="none" />
                    ) : (
                      <TbLayoutSidebarLeftExpand width={16} height={16} pointerEvents="none" />
                    )}
                  </Button>
                </StatefulPopover>
              ) : null}
              {props.children}
            </>
          ) : null}
        </div>
      </Provider>
    </div>
  );
});

export default Graph;
