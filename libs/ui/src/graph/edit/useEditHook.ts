import { ExternalGraph, ExternalLink, ExternalNode, getLinkID, getNodeID } from '@tigergraph/tools-models';
import { useCallback, useEffect, useState } from 'react';

export function useEditHook(isSchemaGraph: boolean, graph: ExternalGraph, isEditMode?: boolean) {
  const [selectNode, setSelectNode] = useState<ExternalNode | null>(null);
  const [selectEdge, setSelectEdge] = useState<ExternalLink | null>(null);
  const [sourceNode, setSourceNode] = useState<ExternalNode | null>(null);
  const [targetNode, setTargetNode] = useState<ExternalNode | null>(null);
  const [showDetailPanel, setShowDetailPanel] = useState(false);

  const [showNewVertexForm, setShowNewVertexForm] = useState(false);
  const [showNewEdgeForm, setShowNewEdgeForm] = useState(false);

  const onNewEdge = useCallback((source: ExternalNode, target: ExternalNode) => {
    setSourceNode(source);
    setTargetNode(target);
    setShowNewEdgeForm(true);
    setShowNewVertexForm(false);
    setShowDetailPanel(true);
  }, []);

  const onNewVertex = useCallback(() => {
    setShowNewVertexForm(true);
    setShowNewEdgeForm(false);
    setShowDetailPanel(true);
    setSelectNode(null);
  }, []);

  useEffect(() => {
    if (isSchemaGraph) {
      setShowDetailPanel(false);
    }
  }, [isSchemaGraph]);

  useEffect(() => {
    // check if the selected node is in the graph
    if (selectNode && !graph.nodes.find((node) => getNodeID(node) === getNodeID(selectNode))) {
      setSelectNode(null);
      setShowDetailPanel(false);
    }
    // check if the selected edge is in the graph
    if (selectEdge && !graph.links.find((link) => getLinkID(link) === getLinkID(selectEdge))) {
      setSelectEdge(null);
      setShowDetailPanel(false);
    }
  }, [graph, selectNode, selectEdge]);

  return {
    selectNode,
    setSelectNode,
    selectEdge,
    setSelectEdge,
    sourceNode,
    setSourceNode,
    targetNode,
    setTargetNode,
    showNewVertexForm,
    setShowNewVertexForm,
    showNewEdgeForm,
    setShowNewEdgeForm,
    showDetailPanel,
    setShowDetailPanel,
    onNewEdge,
    onNewVertex,
    isEditMode,
  };
}
