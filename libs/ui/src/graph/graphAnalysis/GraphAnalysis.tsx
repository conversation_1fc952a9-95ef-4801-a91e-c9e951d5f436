import React from 'react';
import { Button } from '@tigergraph/app-ui-lib/button';
import { Popover } from '@tigergraph/app-ui-lib/popover';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { KIND, SIZE } from 'baseui/button';
import { PLACEMENT } from 'baseui/popover';
import { Core } from 'cytoscape';
import { useMemo, useRef } from 'react';
import { KIND as ToastKind } from 'baseui/toast';
import ChartSettingItemContainer from '../../chartSetting/chartSettingItemContainer';
import { FilterItem, Schema, UndoRedoInstance } from '../type';
import { popoverOverrides } from '../ui/baseui';
import Settings from './settings';
import { AlgorithmSettings, PageRankSettings } from './types';
import { runLouvain, runPageRank } from './utils';
import { ExternalGraph } from '@tigergraph/tools-models/gvis/insights';
import { mergeOverrides } from 'baseui';
import { Overrides } from 'baseui/overrides';
import AlgorithmDesc from './AlgorithmDesc';
import { StyledSelect } from '../../insights/components';
import { showToast } from '@tigergraph/app-ui-lib/styledToasterContainer';

export type GraphAnalysisProps = {
  cy: Core;
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
  nodeItems: FilterItem[];
  edgeItems: FilterItem[];
  graph: ExternalGraph;
  schema: Schema;
  settings: Record<string, any>; // chart settings
  onSettingUpdate: (key: string, value: any) => void; // can update chart setting.
  undoRedoInstance: UndoRedoInstance;
};

export const pageRankDefaultSettings: PageRankSettings = {
  type: 'pageRank',
  vertexType: '',
  edgeType: '',
  weighted: false,
  edgeAttr: '',
  styleBy: 'size',
};

export const louvainDefaultSettings: AlgorithmSettings = {
  type: 'louvain',
  styleBy: 'color',
};

export function GraphAnalysis({
  cy,
  isOpen,
  onClose,
  children,
  nodeItems,
  edgeItems,
  graph,
  schema,
  settings,
  onSettingUpdate,
  undoRedoInstance,
}: GraphAnalysisProps) {
  const [css, theme] = useStyletron();
  const settingKey = 'algorithm';
  const algorithmSetting: AlgorithmSettings = settings[settingKey] || pageRankDefaultSettings;
  const { type } = algorithmSetting;
  const cachedAlgorithmSettings = useRef<Record<string, AlgorithmSettings>>({
    pageRank: pageRankDefaultSettings,
    louvain: louvainDefaultSettings,
  });

  const settingsFilled = useMemo(() => {
    if (algorithmSetting.type === 'pageRank') {
      const settings = algorithmSetting as PageRankSettings;
      const { vertexType, edgeType, weighted, edgeAttr } = settings;
      return vertexType && edgeType && (!weighted || edgeAttr);
    }
    return true;
  }, [algorithmSetting]);

  const runAlgorithm = () => {
    try {
      if (type === 'pageRank') {
        const pageRankResult = runPageRank(cy, algorithmSetting as PageRankSettings);
        if (!pageRankResult) {
          return;
        }
        undoRedoInstance.do('applyPageRankResult', {
          pageRankResult,
        });
      } else {
        const louvainOutput = runLouvain(graph);
        undoRedoInstance.do('applyLouvainResult', {
          louvainOutput,
        });
      }
      showToast({
        kind: ToastKind.positive,
        message: 'Algorithm run successfully.',
      });
    } catch (error: any) {
      showToast({
        kind: ToastKind.negative,
        message: `Run algorithm failed. \n${error?.message}`,
      });
    }
  };

  return (
    <Popover
      isOpen={isOpen}
      onClickOutside={onClose}
      onEsc={onClose}
      showArrow={false}
      autoFocus={false}
      content={() => {
        return (
          <div
            className={css({
              width: '320px',
              boxSizing: 'border-box',
            })}
          >
            <div
              className={css({
                fontFamily: 'Urbanist',
                fontSize: '16px',
                lineHeight: '24px',
                fontWeight: 600,
                padding: '8px',
                borderBottom: `1px solid ${theme.colors.divider}`,
              })}
            >
              Graph Analytics (Preview)
            </div>
            <div
              className={css({
                padding: '8px',
                borderBottom: `1px solid ${theme.colors.divider}`,
              })}
            >
              <ChartSettingItemContainer label={'Algorithm'} helpText={''}>
                <StyledSelect
                  clearable={false}
                  value={[{ id: type }]}
                  options={[
                    { id: 'pageRank', label: 'Page Rank' },
                    { id: 'louvain', label: 'Louvain' },
                  ]}
                  onChange={({ value }) => {
                    cachedAlgorithmSettings.current[type] = { ...algorithmSetting };
                    // @ts-ignore
                    onSettingUpdate(settingKey, cachedAlgorithmSettings.current[value[0].id]);
                  }}
                  labelKey="label"
                />
              </ChartSettingItemContainer>
              <div
                className={css({
                  fontSize: '12px',
                  lineHeight: '16px',
                  color: theme.colors['text.secondary'],
                  marginTop: '4px',
                })}
              >
                <AlgorithmDesc type={type} />
              </div>
              {type === 'pageRank' && (
                <Settings
                  nodeItems={nodeItems}
                  edgeItems={edgeItems}
                  graph={graph}
                  schema={schema}
                  settings={algorithmSetting as PageRankSettings}
                  updateSettings={(key: string, value: any) => {
                    onSettingUpdate(settingKey, { ...algorithmSetting, [key]: value });
                  }}
                />
              )}
            </div>
            <div
              className={css({
                padding: '12px 8px',
                display: 'flex',
                justifyContent: 'flex-end',
              })}
            >
              <Button
                overrides={{
                  Root: {
                    style: {
                      marginLeft: 'auto',
                    },
                  },
                  BaseButton: {
                    style: {
                      fontWeight: 600,
                    },
                  },
                }}
                onClick={runAlgorithm}
                kind={KIND.secondary}
                disabled={!settingsFilled || !graph.nodes.length}
              >
                Run algorithm
              </Button>
            </div>
          </div>
        );
      }}
      overrides={mergeOverrides<any>(popoverOverrides as Overrides<any>, {
        Inner: {
          style: {
            paddingLeft: 0,
            paddingRight: 0,
            paddingTop: 0,
            paddingBottom: 0,
          },
        },
      })}
      placement={PLACEMENT.rightTop}
    >
      {children}
    </Popover>
  );
}
