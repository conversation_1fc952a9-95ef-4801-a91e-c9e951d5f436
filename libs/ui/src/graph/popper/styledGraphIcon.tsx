import React, { useMemo } from 'react';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { ReactComponent as GlobalIcon } from '../icons/global-icon.svg';
import { ReactComponent as DeleteIcon } from '../icons/cross-circle.svg';
import { ReactComponent as CheckIcon } from '../icons/check.svg';
import { StyleObject } from 'styletron-react';

export const GRAPH_ICON = {
  global: 'global',
  delete: 'delete',
  check: 'check',
};

export const POSITION = {
  top: 'top',
  rightTop: 'rightTop',
};

export interface StyledGraphIconProps {
  icon: (typeof GRAPH_ICON)[keyof typeof GRAPH_ICON];
  position?: (typeof POSITION)[keyof typeof POSITION];
  onClick?: () => void;
}

export const StyledGraphIcon = ({ icon, position = POSITION.top, onClick }: StyledGraphIconProps) => {
  const [css, theme] = useStyletron();

  const buildIcon = () => {
    if (icon === GRAPH_ICON.delete) {
      return <DeleteIcon fill={theme.colors.primary1000} style={{ height: '20px', width: '20px' }} />;
    } else if (icon === GRAPH_ICON.global) {
      return (
        <GlobalIcon
          fill={theme.colors.primary1000}
          stroke="#fff"
          style={{ height: '20px', width: '20px' }}
          viewBox="0 0 85 85"
        />
      );
    } else if (icon === GRAPH_ICON.check) {
      return <CheckIcon />;
    }
  };

  const iconPosition = useMemo((): StyleObject => {
    if (position === POSITION.top) {
      return {};
    } else if (position === POSITION.rightTop) {
      return {
        position: 'absolute',
        right: '-36px',
        top: '-8px',
      };
    }
  }, [position]);

  return (
    <div
      className={css({
        transform: 'translateY(50%)',
        cursor: icon === GRAPH_ICON.delete ? 'pointer' : 'default',
        width: '20px',
        height: '20px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        ...iconPosition,
      })}
      onClick={onClick}
    >
      {buildIcon()}
    </div>
  );
};
