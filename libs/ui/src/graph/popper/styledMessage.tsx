import React, { ReactNode } from 'react';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { KIND } from 'baseui/toast';
import { Spinner } from 'baseui/spinner';

export interface StyledMessageProps {
  message: string;
  isLoading?: boolean;
  children?: ReactNode;
  kind?: (typeof KIND)[keyof typeof KIND];
}

const styleMap = {
  [KIND.positive]: {
    color: '#1F6C2B',
    backgroundColor: '#EAF6EC',
  },
  [KIND.negative]: {
    color: '#A43030',
    backgroundColor: '#F8EBEB',
  },
  [KIND.warning]: {
    color: '#846107',
    backgroundColor: '#FDF5E7',
  },
  [KIND.info]: {
    color: '#1564B1',
    backgroundColor: '#E7F3FC',
  },
};

const StyledMessage = (props: StyledMessageProps) => {
  const [css] = useStyletron();
  const { kind, message, isLoading, children } = props;

  return (
    <div
      className={css({
        display: 'flex',
        position: 'relative',
        columnGap: '4px',
        alignItems: 'center',
      })}
    >
      {isLoading && (
        <div
          className={css({
            position: 'absolute',
            left: '-24px',
          })}
        >
          <Spinner $size="12px" $borderWidth="4px" />
        </div>
      )}
      <div
        className={css({
          width: '100%',
          fontSize: '14px',
          fontWeight: 400,
          color: kind ? styleMap[kind].color : '#3F5870',
          backgroundColor: kind ? styleMap[kind].backgroundColor : '#F1F2F4',
          padding: '2px 12px',
          borderRadius: '20px',
          whiteSpace: 'pre-wrap',
          wordBreak: 'break-word',
        })}
      >
        {message}
      </div>
      {children}
    </div>
  );
};

export default StyledMessage;
