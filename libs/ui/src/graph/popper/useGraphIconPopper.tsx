import React, { ReactNode, useRef } from 'react';
import <PERSON>actD<PERSON> from 'react-dom';
import { Core, CollectionReturnValue } from 'cytoscape';
import { CytoscapeExtensions, GraphIcons } from '../type';
import { StyledGraphIcon, GRAPH_ICON, POSITION } from './styledGraphIcon';
import Provider from '@tigergraph/app-ui-lib/Provider';
import { ExternalNode, ExternalLink, getLinkID, getNodeID } from '@tigergraph/tools-models/gvis/insights';
import { Instance } from '@popperjs/core';
import { GraphProps } from '../Graph';

const POPPER_ICON_ID = '__popper_icon';

export const useGraphIconPopper = (cy: (Core & CytoscapeExtensions) | null, props: GraphProps): GraphIcons => {
  const instanceMapRef = useRef<Record<string, Instance>>({});

  const renderElement = (container: HTMLElement | null, element: ReactNode) => {
    if (container && element) {
      ReactDOM.render(
        <React.StrictMode>
          <Provider>{element}</Provider>
        </React.StrictMode>,
        container
      );
    }
  };

  const displayElementWithId = (id: string, item: CollectionReturnValue, element: ReactNode) => {
    if (cy && item) {
      let container: HTMLElement | null;
      let popper: Instance;
      // If the popper exists in DOM, we update the content.
      if (id in instanceMapRef.current) {
        popper = instanceMapRef.current[id];
        container = document.getElementById(id);
        if (container) {
          const zoom = cy.zoom();
          const ext = cy.extent();
          const bb = item.boundingBox();
          if (zoom < 0.25 || !(bb.x2 > ext.x1 && bb.x1 < ext.x2 && bb.y2 > ext.y1 && bb.y1 < ext.y2)) {
            container.style.display = 'none';
          } else {
            container.style.display = 'block';
          }
        }

        renderElement(container, element);
        popper.update();
      } else {
        // Create a new popper instance
        popper = item.popper({
          content: () => {
            container = document.createElement('div');
            container.id = id;
            container.style.zIndex = '1';
            renderElement(container, element);
            const zoom = cy.zoom();
            const ext = cy.extent();
            const bb = item.boundingBox();
            if (zoom < 0.25 || !(bb.x2 > ext.x1 && bb.x1 < ext.x2 && bb.y2 > ext.y1 && bb.y1 < ext.y2)) {
              container.style.display = 'none';
            } else {
              container.style.display = 'block';
            }
            if (container.firstElementChild) {
              (container.firstElementChild as HTMLElement).style['transform'] = `scale(${zoom})`;
              (container.firstElementChild as HTMLElement).style['transformOrigin'] = 'bottom';
            }
            // Append to the cytoscape container
            cy.container()?.appendChild(container);
            return container;
          },
          popper: {
            placement: 'top',
            modifiers: [
              {
                name: 'preventOverflow',
                enabled: false,
              },
              {
                name: 'flip',
                enabled: false,
              },
            ],
            strategy: 'absolute',
          },
        });
        instanceMapRef.current[id] = popper;

        // Update the message position
        const update = () => {
          if (container?.firstElementChild) {
            const zoom = cy.zoom();
            const ext = cy.extent();
            const bb = item.boundingBox();
            if (zoom < 0.25 || !(bb.x2 > ext.x1 && bb.x1 < ext.x2 && bb.y2 > ext.y1 && bb.y1 < ext.y2)) {
              container.style.display = 'none';
            } else {
              container.style.display = 'block';
              (container.firstElementChild as HTMLElement).style['transform'] = `scale(${zoom})`;
              (container.firstElementChild as HTMLElement).style['transformOrigin'] = 'bottom';
              popper.update();
            }
          }
        };

        const remove = () => {
          item.off('position');
          item.connectedNodes().off('position');
          item.off('remove');
          item.off('mouseover');
          cy.off('pan dragpan zoom resize', update);
          if (container) {
            ReactDOM.unmountComponentAtNode(container);
            container.remove();
            container = undefined;
          }
          if (popper) {
            popper.state.elements.popper.remove();
            popper.destroy();
            delete instanceMapRef.current[id];
            popper = undefined;
          }
        };

        item.on('position', update);
        item.connectedNodes().on('position', update);
        item.on('remove', remove);
        item.on('mouseover', update);
        cy.on('pan dragpan zoom resize', update);
        cy.on('destroy', remove);
      }
    }
  };

  const displayIconsWithId = (
    id: string,
    icon: (typeof GRAPH_ICON)[keyof typeof GRAPH_ICON],
    onDelete: () => void,
    position?: (typeof POSITION)[keyof typeof POSITION]
  ) => {
    if (cy) {
      const item = cy.getElementById(id);
      displayElementWithId(
        `${POPPER_ICON_ID}_${id}`,
        item,
        <StyledGraphIcon icon={icon} position={position} onClick={icon === GRAPH_ICON.delete ? onDelete : undefined} />
      );
    }
  };

  // Remove the message if it exists
  const removeIconWithId = (id: string) => {
    if (id in instanceMapRef.current) {
      const popper = instanceMapRef.current[id];
      const el = document.getElementById(id);
      ReactDOM.unmountComponentAtNode(el);
      el?.remove();
      popper.state.elements.popper.remove();
      popper?.destroy();
      delete instanceMapRef.current[id];
    }
  };

  const removeIconStartWithId = (id: string) => {
    cy?.batch(() => {
      for (let key in instanceMapRef.current) {
        if (key.startsWith(id)) {
          removeIconWithId(key);
        }
      }
    });
  };

  const displayNodeIcon = (
    node: ExternalNode,
    icon: (typeof GRAPH_ICON)[keyof typeof GRAPH_ICON],
    position?: (typeof POSITION)[keyof typeof POSITION]
  ) => {
    displayIconsWithId(getNodeID(node), icon, () => props.onDelete?.(node), position);
  };

  const displayLinkIcon = (link: ExternalLink, icon: (typeof GRAPH_ICON)[keyof typeof GRAPH_ICON]) => {
    displayIconsWithId(getLinkID(link), icon, () => props.onDelete?.(link));
  };

  const displayNodesIcon = (
    nodes: ExternalNode[],
    icon: (typeof GRAPH_ICON)[keyof typeof GRAPH_ICON],
    position?: (typeof POSITION)[keyof typeof POSITION]
  ) => {
    cy?.batch(() => {
      nodes.forEach((node) => {
        displayIconsWithId(getNodeID(node), icon, () => props.onDelete?.(node), position);
      });
    });
  };

  const displayLinksIcon = (links: ExternalLink[], icon: (typeof GRAPH_ICON)[keyof typeof GRAPH_ICON]) => {
    cy?.batch(() => {
      links.forEach((link) => {
        displayIconsWithId(getLinkID(link), icon, () => props.onDelete?.(link));
      });
    });
  };

  const removeNodeIcon = (node: ExternalNode) => {
    removeIconWithId(`${POPPER_ICON_ID}_${getNodeID(node)}`);
  };

  const removeLinkIcon = (link: ExternalLink) => {
    removeIconWithId(`${POPPER_ICON_ID}_${getLinkID(link)}`);
  };

  const removeAllIcons = () => {
    // Get all the elements start with the popper message id.
    removeIconStartWithId(POPPER_ICON_ID);
  };

  return {
    displayNodeIcon,
    displayLinkIcon,
    displayNodesIcon,
    displayLinksIcon,
    removeNodeIcon,
    removeLinkIcon,
    removeAllIcons,
  };
};
