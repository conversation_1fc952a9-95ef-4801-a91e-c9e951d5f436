import React, { ReactNode, useRef } from 'react';
import ReactDOM from 'react-dom';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { Core, CollectionReturnValue } from 'cytoscape';
import { CytoscapeExtensions, GraphMessages, LinkMessageProps, NodeMessageProps } from '../type';
import StyledMessage from './styledMessage';
import StyledProgress from './styledProgress';
import Provider from '@tigergraph/app-ui-lib/Provider';
import { ExternalNode, ExternalLink, getLinkID, getNodeID } from '@tigergraph/tools-models/gvis/insights';
import { KIND } from 'baseui/toast';
import { Instance } from '@popperjs/core';

const POPPER_MESSAGE_ID = '__popper_message';
const POPPER_PROGRESS_ID = '__popper_progress';

export interface ElementMessageProps {
  id: string;
  message: string;
  isLoading?: boolean;
  kind?: (typeof KIND)[keyof typeof KIND];
}

export const useGraphMessagePopper = (cy: (Core & CytoscapeExtensions) | null): GraphMessages => {
  const [css] = useStyletron();
  const instanceMapRef = useRef<Record<string, Instance>>({});

  const renderElement = (container: HTMLElement | null, element: ReactNode) => {
    if (container && element) {
      ReactDOM.render(
        <React.StrictMode>
          <Provider>{element}</Provider>
        </React.StrictMode>,
        container
      );
    }
  };

  const displayElementWithId = (id: string, item: CollectionReturnValue, element: ReactNode) => {
    if (cy && item) {
      let popper: Instance;
      let container: HTMLElement | null;
      // If the popper exists in DOM, we update the content.
      if (id in instanceMapRef.current) {
        popper = instanceMapRef.current[id];
        container = document.getElementById(id);
        if (container) {
          const zoom = cy.zoom();
          const ext = cy.extent();
          const bb = item.boundingBox();
          if (zoom < 0.25 || !(bb.x2 > ext.x1 && bb.x1 < ext.x2 && bb.y2 > ext.y1 && bb.y1 < ext.y2)) {
            container.style.display = 'none';
          } else {
            container.style.display = 'block';
          }
        }
        renderElement(container, element);
        popper.update();
      } else {
        // Create a new popper instance
        popper = item.popper({
          content: () => {
            container = document.createElement('div');
            container.id = id;
            renderElement(container, element);
            if (container.firstElementChild) {
              const zoom = cy.zoom();
              (container.firstElementChild as HTMLElement).style['transform'] = `scale(${zoom})`;
              (container.firstElementChild as HTMLElement).style['transformOrigin'] = 'bottom';
            }
            container.style['pointerEvents'] = 'none';
            // Append to the cytoscape container
            cy.container()?.appendChild(container);
            return container;
          },
          popper: {
            placement: 'top',
            modifiers: [
              {
                name: 'offset',
                options: {
                  offset: () => {
                    if (item.isNode()) {
                      return [0, 4];
                    } else {
                      const zoom = cy.zoom();
                      return [0, -24 * zoom];
                    }
                  },
                },
              },
              {
                name: 'preventOverflow',
                enabled: false,
              },
            ],
            strategy: 'absolute',
          },
        });
        instanceMapRef.current[id] = popper;

        // Update the message position
        const update = () => {
          if (container?.firstElementChild) {
            const zoom = cy.zoom();
            const ext = cy.extent();
            const bb = item.boundingBox();
            if (zoom < 0.25 || !(bb.x2 > ext.x1 && bb.x1 < ext.x2 && bb.y2 > ext.y1 && bb.y1 < ext.y2)) {
              container.style['display'] = 'none';
            } else {
              container.style['display'] = 'block';
              (container.firstElementChild as HTMLElement).style['transform'] = `scale(${zoom})`;
            }
          }
          popper.update();
        };

        const remove = () => {
          item.off('position');
          item.connectedNodes().off('position');
          item.off('remove');
          cy.off('pan zoom resize', update);
          if (container) {
            ReactDOM.unmountComponentAtNode(container);
            container.remove();
            container = undefined;
          }
          if (popper) {
            popper.state.elements.popper.remove();
            popper.destroy();
            delete instanceMapRef.current[id];
            popper = undefined;
          }
        };

        item.on('position', update);
        item.connectedNodes().on('position', update);
        item.on('remove', remove);
        cy.on('pan zoom resize', update);
        cy.on('destroy', remove);
      }
    }
  };

  const displayMessageWithId = ({ id, ...props }: ElementMessageProps) => {
    if (cy) {
      // Remove the progress if it exists
      removeMessageWithId(`${POPPER_PROGRESS_ID}_${id}`);
      const item = cy.getElementById(id);
      displayElementWithId(`${POPPER_MESSAGE_ID}_${id}`, item, <StyledMessage {...props}></StyledMessage>);
    }
  };

  // Remove the message if it exists
  const removeMessageWithId = (id: string) => {
    if (id in instanceMapRef.current) {
      const popper = instanceMapRef.current[id];
      const el = document.getElementById(id);
      ReactDOM.unmountComponentAtNode(el);
      el?.remove();
      popper.state.elements.popper.remove();
      popper?.destroy();
      delete instanceMapRef.current[id];
    }
  };

  // Remove the messages if they start with id
  const removeMessageStartWithId = (id: string) => {
    cy?.batch(() => {
      for (let key in instanceMapRef.current) {
        if (key.startsWith(id)) {
          removeMessageWithId(key);
        }
      }
    });
  };

  const displayNodeMessage = ({ node, ...props }: NodeMessageProps) => {
    displayMessageWithId({
      id: getNodeID(node),
      ...props,
    });
  };

  const displayLinkMessage = ({ link, ...props }: LinkMessageProps) => {
    displayMessageWithId({
      id: getLinkID(link),
      ...props,
    });
  };

  const removeNodeMessage = (exNode: ExternalNode) => {
    removeMessageWithId(`${POPPER_MESSAGE_ID}_${getNodeID(exNode)}`);
  };

  const removeLinkMessage = (exLink: ExternalLink) => {
    removeMessageWithId(`${POPPER_MESSAGE_ID}_${getLinkID(exLink)}`);
  };

  const removeAllMessages = () => {
    // Get all the elements start with the popper message id.
    removeMessageStartWithId(POPPER_MESSAGE_ID);
  };

  const displayProgressWithId = (id: string, value: number, message?: string) => {
    if (cy) {
      // Remove the message if it exists
      removeMessageWithId(`${POPPER_MESSAGE_ID}_${id}`);
      const item = cy.getElementById(id);
      const label = message || `${value}%`;
      displayElementWithId(
        `${POPPER_PROGRESS_ID}_${id}`,
        item,
        <div
          className={css({
            display: 'flex',
            alignItems: 'center',
          })}
        >
          <StyledProgress value={value} />
          <div>{label}</div>
        </div>
      );
    }
  };

  const displayInfiniteProgressWithId = (id: string, message?: string) => {
    if (cy) {
      // Remove the message if it exists
      removeMessageWithId(`${POPPER_MESSAGE_ID}_${id}`);
      const item = cy.getElementById(id);
      displayElementWithId(
        `${POPPER_PROGRESS_ID}_${id}`,
        item,
        <div
          className={css({
            display: 'flex',
            alignItems: 'center',
          })}
        >
          <StyledProgress infinite />
          {message && <div>{message}</div>}
        </div>
      );
    }
  };

  const displayNodeProgress = (exNode: ExternalNode, value: number, message?: string) => {
    displayProgressWithId(getNodeID(exNode), value, message);
  };

  const displayNodeInfiniteProgress = (exNode: ExternalNode, message?: string) => {
    displayInfiniteProgressWithId(getNodeID(exNode), message);
  };

  const removeNodeProgress = (exNode: ExternalNode) => {
    removeMessageWithId(`${POPPER_PROGRESS_ID}_${getNodeID(exNode)}`);
  };

  const removeAllProgress = () => {
    // Get all the elements start with the popper progress id.
    removeMessageStartWithId(POPPER_PROGRESS_ID);
  };

  return {
    displayNodeMessage,
    displayLinkMessage,
    displayNodeProgress,
    displayNodeInfiniteProgress,
    removeNodeMessage,
    removeLinkMessage,
    removeAllMessages,
    removeNodeProgress,
    removeAllProgress,
  };
};
