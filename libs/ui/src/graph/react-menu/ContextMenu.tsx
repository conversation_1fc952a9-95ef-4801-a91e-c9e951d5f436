import React, { useEffect, useState, useRef, useCallback, MutableRefObject, useMemo } from 'react';

import { KIND } from 'baseui/toast';
import { NavigateFunction } from 'react-router';
import { ExternalGraph, ExternalNode, ExternalLink } from '@tigergraph/tools-models/gvis/insights';

import { NodeCollection, NodeSingular, EventHandler, EventObject } from 'cytoscape';

import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuTrigger,
  ContextMenuSeparator,
  ContextMenuSub,
  ContextMenuSubTrigger,
  ContextMenuSubContent,
} from '../../components/ui/context-menu';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '../../components/ui/tooltip';

import { Spinner } from '@tigergraph/app-ui-lib/spinner';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { showToast } from '@tigergraph/app-ui-lib/styledToasterContainer';
import { MdSwapCalls } from '@react-icons/all-files/md/MdSwapCalls';
import { ReactComponent as AnalyticsIcon } from '../icons/analytics.svg';
// import { ReactComponent as AddEdgeIcon } from '../icons/add_edge.svg';
import { ReactComponent as AddVertexIcon } from '../icons/add_vertex.svg';

import { Action } from '../../action/type';
import { UndoRedoInstance } from '../type';
import { GlobalParams } from '../../insights/chart';
import { addGraph, canonicalizeGraph, convertGraphToCytoscape } from '../data';
import { useMutationExpansionNode, useMutationShortestPath } from '../api';
import { runLouvain } from '../graphAnalysis/utils';
import { PageRankSettings } from '../graphAnalysis/types';
import { pageRankDefaultSettings } from '../graphAnalysis';
import { addElements, getErrorMessage } from '../util';
import { useGraphContext } from '../context';
import { NeighborsMenu, NodeExpand } from './neighbors';
import HighLightMenu from './highlight';
import ActionMenu from './action';
import { HideDeleteMenu, ConfirmDelete } from './hide_delete';

export default function GraphContextMenu({
  baseURL,
  layout,
  actionsByType,
  navigate,
  links,
  getLink,
  globalParameters,
  isEditMode,
  isWidgetEditMode,
  undoRedoRef,
  settings,
  onSettingUpdate,
  onShowAnalysis,
  onVertexDoubleClick,
  onNewVertex,
  resetUndo,
}: {
  baseURL: string;
  layout: string;
  actionsByType: Record<string, Action[]>;
  navigate?: NavigateFunction;
  links?: { pageName: string; pageID: string; params: GlobalParams }[];
  getLink?: (pageID: string, params: Record<string, string>) => string;
  globalParameters?: GlobalParams;
  isEditMode: boolean;
  isWidgetEditMode: boolean;
  undoRedoRef: MutableRefObject<UndoRedoInstance | null>;
  settings: Record<string, any>;
  onSettingUpdate: (key: string, value: any) => void;
  onShowAnalysis: () => void;
  onVertexDoubleClick?: (element: ExternalNode | ExternalLink | undefined) => void;
  onNewVertex: () => void;
  resetUndo: () => void;
}) {
  const [css, theme] = useStyletron();

  const { isSchemaGraph, schema, graphName, graphData, graphT } = useGraphContext();
  if (graphT.core.type !== 'canvas') {
    throw new Error('only support cytoscape!');
  }
  const cy = graphT.core.cy;

  const { EdgeTypes: edgeTypes, VertexTypes: vertexTypes, DiscriminatorMap: discriminatorMap } = schema;
  const vTypes = useMemo(() => {
    return vertexTypes.reduce((acc, v) => {
      acc.push(v['Name']);
      return acc;
    }, [] as string[]);
  }, [vertexTypes]);
  const eTypes = useMemo(() => {
    return edgeTypes.reduce((acc, e) => {
      acc.push(e['Name']);
      if (e['Config']['REVERSE_EDGE']) {
        acc.push(e['Config']['REVERSE_EDGE']);
      }
      return acc;
    }, [] as string[]);
  }, [edgeTypes]);

  const [clickedNode, setClickedNode] = useState<NodeSingular>();
  const hiddenTriggerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const onCxttap: EventHandler = (event) => {
      const isClickOnNode = event.target.isNode && event.target.isNode();
      if (isClickOnNode) {
        const node = (event.target as NodeCollection).first();
        setClickedNode(node);
      } else {
        setClickedNode(undefined);
      }

      if (hiddenTriggerRef.current) {
        const contextMenuEvent = new MouseEvent('contextmenu', {
          bubbles: true,
          clientX: event.originalEvent.clientX,
          clientY: event.originalEvent.clientY,
        });
        hiddenTriggerRef.current.dispatchEvent(contextMenuEvent);
      }
    };
    cy.on('cxttap', onCxttap);

    const onViewport: EventHandler = () => {
      // This will trigger the DismissibleLayer's onDismiss function, which will close the ContextMenu.
      document.dispatchEvent(new KeyboardEvent('keydown', { key: 'Escape' }));
    };
    cy.on('viewport', onViewport);
    // for click out side of context menu is already handled by Menu component.

    const onContextMenu = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      // windows will fire a second context menu event in html, we also need to prevent it
      if (target.classList.contains('hidden-context-menu') || target.tagName === 'HTML') {
        e.preventDefault();
      }
    };

    window.addEventListener('contextmenu', onContextMenu);

    return () => {
      cy.off('ctxtap', onCxttap);
      cy.off('viewport', onViewport);
      window.removeEventListener('contextmenu', onContextMenu);
    };
  }, [cy]);

  const [expandMap, setExpandMap] = useState<Map<string, NodeExpand>>(new Map());

  const onResponse = useCallback(
    (newGraph: ExternalGraph, target: NodeCollection, highLightAll: boolean) => {
      newGraph = canonicalizeGraph(newGraph, schema);
      // no need to call removeDuplicateEdges as later the addGraph/addElements will prevent duplicate node/edges

      const newElements = convertGraphToCytoscape(newGraph);
      const expandedElements = addElements(cy, newElements);

      if (expandedElements.length > 0 || (highLightAll && newElements.length > 0)) {
        if (expandedElements.length + cy.elements.length > 5000) {
          showToast({
            kind: KIND.warning,
            message: `This widget may take longer to load due to a volume of vertices and edges exceeding 5000.`,
          });
        }

        if (!undoRedoRef.current) {
          return;
        }

        // highligh the expansion starting node
        target.style({
          'underlay-color': 'rgb(251, 154, 68)',
        });

        target.one('unselect', (evt) => {
          evt.target.removeStyle('underlay-color');
        });

        // we nee to
        // 1 when expand node after node is added to graph, run layout on new added nodes/edges
        // 2 when undo/redo, run layout on restored nodes/edges
        // there two case do not share data

        // need setTimeout so select can take effect
        setTimeout(() => {
          // we need to highlight the new expanded elements or all new elements.
          const highLightElements = highLightAll ? newElements : expandedElements;

          // before add elements to graph, lock all existing nodes
          cy.nodes().lock();

          const { deltaGraph } = addGraph(graphData, newGraph);
          undoRedoRef.current.do('add', {
            deltaGraph,
          });

          // wait until cytoscape get latest elements
          setTimeout(() => {
            const collection = cy.collection();
            for (let ele of highLightElements) {
              collection.merge(cy.getElementById(ele.data.id!));
            }

            // we need to add target to subgraph for layout
            collection.merge(target);

            graphT.runLayout(
              {
                eles: collection,
                randomize: false,
                fit: false,
                centerGraph: false,
              },
              () => {
                // after layout, unlock all nodes
                cy.nodes().unlock();
              }
            );

            // perf optimization: only select when the collection is small
            if (collection.length <= 100) {
              collection.select();
            }
          }, 100);
        });
      } else {
        showToast({
          kind: KIND.info,
          message: 'Empty result',
        });
      }
    },
    [cy, graphT, graphData, undoRedoRef, schema]
  );

  const { mutate: mutateExpandNode, isLoading: isExpandNodeLoading } = useMutationExpansionNode(baseURL);
  const { mutate: mutateShortestPath, isLoading: isShortestPathLoading } = useMutationShortestPath(baseURL);
  const [lastSelections, setLastSelections] = useState<string[]>([]);

  // handle expand node
  const onExpandNode = useCallback(
    (target: NodeCollection, relatedType?: string, limit?: number) => {
      const nodeID = target.first().data('nodeID');
      const type = target.first().data('type') as string;

      mutateExpandNode(
        {
          graphName,
          id: nodeID,
          vTypes,
          eTypes,
          type,
          relatedType,
          discriminatorMap,
          limit,
        },
        {
          onSuccess: (newGraph) => {
            onResponse(newGraph, target, false);
          },
          onError: (err) => {
            showToast({
              kind: 'negative',
              message: `Vertex expansion failed: \n${getErrorMessage(err)}`,
            });
          },
        }
      );
    },
    [eTypes, vTypes, graphName, discriminatorMap, mutateExpandNode, onResponse]
  );

  const onFindShortestPath = useCallback(
    (algorithm: 'oneShortestPath' | 'allShortestPath') => {
      const currentSelection = cy.$('node:selected');
      let source = currentSelection[0];
      let target = currentSelection[1];

      // we only track the last two selection
      // check if target' id is equal to last selection
      // if not, swap source and target
      if (target.id() !== lastSelections[1]) {
        const temp = source;
        source = target;
        target = temp;
      }

      mutateShortestPath(
        {
          graphName,
          vTypes,
          eTypes,
          sourceID: source.data('nodeID'),
          sourceType: source.data('type') as string,
          targetID: target.data('nodeID'),
          targetType: target.data('type') as string,
          allShortestPaths: algorithm === 'allShortestPath',
          allPath: false,
          discriminatorMap,
        },
        {
          onSuccess: (newGraph) => {
            onResponse(newGraph, currentSelection, true);
          },
          onError: (err) => {
            showToast({
              kind: KIND.negative,
              message: `Find shortest path failed: \n${getErrorMessage(err)}`,
            });
          },
        }
      );
    },
    [cy, eTypes, vTypes, graphName, lastSelections, discriminatorMap, mutateShortestPath, onResponse]
  );

  const onFindCommunity = useCallback(() => {
    if (!undoRedoRef.current) {
      return;
    }
    const louvainOutput = runLouvain(graphData);
    undoRedoRef.current.do('applyLouvainResult', {
      louvainOutput,
    });
  }, [graphData, undoRedoRef]);

  const onRunPageRank = useCallback(
    (pageRankSettings: Partial<PageRankSettings> = {}) => {
      const currentAlgorithmSettings = settings.algorithm;
      if (currentAlgorithmSettings?.type !== 'pageRank') {
        onSettingUpdate('algorithm', { ...pageRankDefaultSettings, ...pageRankSettings });
      } else {
        onSettingUpdate('algorithm', { ...currentAlgorithmSettings, ...pageRankSettings });
      }
      onShowAnalysis();
    },
    [onSettingUpdate, onShowAnalysis, settings.algorithm]
  );

  useEffect(() => {
    cy.on('select', 'node', (event) => {
      const id = event.target.first().id();
      setLastSelections((lastSelections) => {
        return lastSelections.concat(id).slice(0, 2);
      });
    });
  }, [cy, setLastSelections]);

  // handle double click
  useEffect(() => {
    const onDoubleTap = (evt: EventObject) => {
      if (isSchemaGraph || onVertexDoubleClick) {
        return;
      }
      onExpandNode(evt.target);
      return cy;
    };

    cy.on('dbltap', 'node', onDoubleTap);

    return () => {
      cy.off('dbltap', 'node', onDoubleTap);
    };
  }, [cy, isSchemaGraph, onExpandNode, onVertexDoubleClick]);

  const selectedNodes = cy.$('node:selected');

  const [deleteSelection, setDeleteSelection] = useState(false);
  const [deleteAll, setDeleteAll] = useState(false);

  return (
    <>
      <div
        className={css({
          position: 'absolute',
          top: '12px',
          left: 0,
          right: 0,
          display: 'flex',
          justifyContent: 'center',
        })}
      >
        {isExpandNodeLoading || isShortestPathLoading ? <Spinner $size="small" /> : null}
      </div>
      <ContextMenu>
        <ContextMenuTrigger>
          <div className="hidden-context-menu" ref={hiddenTriggerRef} />
        </ContextMenuTrigger>
        <ContextMenuContent>
          <NeighborsMenu
            clickedNode={clickedNode}
            expandMap={expandMap}
            setExpandMap={setExpandMap}
            onExpandNode={onExpandNode}
          />
          {isEditMode && schema.VertexTypes.length > 0 ? (
            <>
              <ContextMenuItem onClick={onNewVertex}>
                <AddVertexIcon color={theme.colors['icon.primary']} />
                Add Vertex
              </ContextMenuItem>
              {/* <ContextMenuItem>
                <AddEdgeIcon color={theme.colors['icon.primary']} />
                Add Edge
              </ContextMenuItem> */}
              <ContextMenuSeparator />
            </>
          ) : null}
          {settings['disablePathFinding'] ? null : selectedNodes.length === 2 ? (
            <ContextMenuSub>
              <ContextMenuSubTrigger>
                <MdSwapCalls size={18} color={theme.colors['icon.primary']} />
                PathFinding
                <ContextMenuSubContent>
                  <ContextMenuItem onClick={() => onFindShortestPath('oneShortestPath')}>
                    One shortest path
                  </ContextMenuItem>
                  <ContextMenuItem onClick={() => onFindShortestPath('allShortestPath')}>
                    All shortest paths
                  </ContextMenuItem>
                </ContextMenuSubContent>
              </ContextMenuSubTrigger>
            </ContextMenuSub>
          ) : (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <ContextMenuItem className="opacity-50">
                    <MdSwapCalls size={18} color={theme.colors['icon.primary']} />
                    PathFinding
                  </ContextMenuItem>
                </TooltipTrigger>
                <TooltipContent>Please first use shift/ctrl + click to select source and target vertex</TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
          <ContextMenuSub>
            <ContextMenuSubTrigger>
              <AnalyticsIcon color={theme.colors['icon.primary']} />
              Graph Analytics
            </ContextMenuSubTrigger>
            <ContextMenuSubContent>
              <ContextMenuItem onClick={() => onFindCommunity()}>Louvain</ContextMenuItem>
              <ContextMenuItem
                onClick={() => onRunPageRank(clickedNode ? { vertexType: clickedNode.data('type') } : {})}
              >
                Page Rank
              </ContextMenuItem>
            </ContextMenuSubContent>
          </ContextMenuSub>
          <ContextMenuSeparator />
          <ActionMenu
            clickedNode={clickedNode}
            isSchemaGraph={isSchemaGraph}
            schema={schema}
            actionsByType={actionsByType}
            isWidgetEditMode={isWidgetEditMode}
            navigate={navigate}
            links={links}
            getLink={getLink}
            globalParameters={globalParameters}
          />
          <HighLightMenu clickedNode={clickedNode} />
          <HideDeleteMenu
            undoRedoRef={undoRedoRef}
            onDeleteAll={() => setDeleteAll(true)}
            onDeleteSelection={() => setDeleteSelection(true)}
            isEditMode={isEditMode}
          />
        </ContextMenuContent>
      </ContextMenu>
      {deleteSelection || deleteAll ? (
        <ConfirmDelete
          baseURL={baseURL}
          isOpen={deleteSelection || deleteAll}
          deleteAll={deleteAll}
          undoRedoRef={undoRedoRef}
          onClose={() => {
            setDeleteSelection(false);
            setDeleteAll(false);
          }}
          resetUndo={resetUndo}
        />
      ) : null}
    </>
  );
}
