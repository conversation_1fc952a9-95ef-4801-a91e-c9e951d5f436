import React from 'react';
import {
  ContextMenuItem,
  ContextMenuSub,
  ContextMenuSubTrigger,
  ContextMenuSubContent,
  ContextMenuSeparator,
} from '../../components/ui/context-menu';
import { NodeSingular } from 'cytoscape';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';

import { MdMenu } from '@react-icons/all-files/md/MdMenu';
import { Schema } from '../type';
import { Action, ActionParam } from '../../action/type';
import { GlobalParams } from '../../insights/chart';
import { NavigateFunction } from 'react-router-dom';
import { getElementAttributes } from '../util';
import { buildLinkFromAction } from '../../action/util';

export default function ActionMenu({
  clickedNode,
  isSchemaGraph,
  schema,
  actionsByType,
  globalParameters = {},
  isWidgetEditMode,
  navigate,
  links = [],
  getLink = () => '',
}: {
  clickedNode: NodeSingular;
  isSchemaGraph: boolean;
  schema: Schema;
  actionsByType: Record<string, Action[]>;
  globalParameters: GlobalParams;
  isWidgetEditMode: boolean;
  navigate?: NavigateFunction;
  links: { pageName: string; pageID: string; params: GlobalParams }[];
  getLink: (pageID: string, params: Record<string, string>) => string;
}) {
  const [, theme] = useStyletron();

  if (!clickedNode) {
    return null;
  }

  const nodeType = clickedNode.data('type') as string;
  const actions = actionsByType[nodeType] || [];

  if (isSchemaGraph || actions.length === 0) {
    return null;
  }

  if (!links || !getLink) {
    return null;
  }

  return (
    <>
      <ContextMenuSub>
        <ContextMenuSubTrigger>
          <MdMenu color={theme.colors['icon.primary']} />
          Actions
        </ContextMenuSubTrigger>
        <ContextMenuSubContent>
          {actions.map((action, index) => {
            return (
              <ContextMenuItem
                key={index}
                onClick={() => {
                  const { attributes, nodeID } = getElementAttributes(clickedNode.data(), schema);

                  // put node id into the attributes
                  attributes['id'] = nodeID;

                  const params: GlobalParams = links.find((link) => link.pageID === action.pageID)?.params || {};

                  const getParam = (param: ActionParam) => {
                    // hand vertex type
                    if (params[param.name]?.type === 'VERTEX') {
                      const key = param.name;
                      return {
                        [key + '.type']: nodeType,
                        [key + '.id']: nodeID,
                      };
                    }

                    if (param.paramGlobalInput) {
                      return {
                        [param.name]: globalParameters[param.paramGlobalInput].value,
                      };
                    }
                    return {
                      [param.name]: param.isCreatable ? param.value : attributes[param.value],
                    };
                  };
                  const href = buildLinkFromAction(action, getLink, getParam, isWidgetEditMode);

                  // if in edit mode, we want to open new tab
                  if (navigate && action.pageID && !isWidgetEditMode) {
                    navigate(href);
                  } else {
                    window.open(href, '_blank');
                  }
                }}
              >
                {action.text}
              </ContextMenuItem>
            );
          })}
        </ContextMenuSubContent>
      </ContextMenuSub>
      <ContextMenuSeparator />
    </>
  );
}
