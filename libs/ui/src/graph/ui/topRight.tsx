import React, { MutableRefObject, useState } from 'react';
import { BiUndo } from '@react-icons/all-files/bi/BiUndo';
import { BiRedo } from '@react-icons/all-files/bi/BiRedo';
import { Button } from '@tigergraph/app-ui-lib/button';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { SHAPE, SIZE } from 'baseui/button';
import { StatefulPopover } from '@tigergraph/app-ui-lib/popover';
import { TRIGGER_TYPE, PLACEMENT } from 'baseui/popover';
import { Plus } from 'baseui/icon';
import { MdDelete } from '@react-icons/all-files/md/MdDelete';
import { MdVisibilityOff } from '@react-icons/all-files/md/MdVisibilityOff';
import { UndoRedoInstance } from '../type';
import { ConfirmDelete, useVertexDelete } from '../react-menu/hide_delete';
import { useGraphContext } from '../context';

type Props = {
  baseURL: string;
  isUndoStackEmpty: boolean;
  isRedoStackEmpty: boolean;
  undo: () => void;
  redo: () => void;
  onNewVertex: () => void;
  reset: () => void;
  undoRedoRef: MutableRefObject<UndoRedoInstance | null>;
  isEditMode: boolean;
};

export default function TopRight({
  baseURL,
  isUndoStackEmpty,
  isRedoStackEmpty,
  undo,
  redo,
  onNewVertex,
  undoRedoRef,
  reset,
  isEditMode,
}: Props) {
  const [css, theme] = useStyletron();

  const [deleteSelection, setDeleteSelection] = useState(false);

  const { onRemoveNode, selectedNodes, selectedEdges, selectedElements } = useVertexDelete(undoRedoRef);
  const { schema } = useGraphContext();

  const onHideSelection = () => {
    const connectedEdges = selectedElements.connectedEdges();
    onRemoveNode(selectedElements.union(connectedEdges));
  };

  return (
    <div
      className={css({
        position: 'absolute',
        right: '8px',
        top: '8px',
        display: 'flex',
        gap: '8px',
      })}
      data-testid="undo-redo"
    >
      <div>
        <StatefulPopover triggerType={TRIGGER_TYPE.hover} placement={PLACEMENT.bottom} content="Undo">
          <Button
            kind="secondary"
            shape={SHAPE.square}
            overrides={{
              BaseButton: {
                style: {
                  borderTopLeftRadius: '2px',
                  borderBottomLeftRadius: '2px',
                  borderTopRightRadius: 0,
                  borderBottomRightRadius: 0,
                },
              },
            }}
            onClick={() => {
              if (!isUndoStackEmpty) {
                undo();
              }
            }}
          >
            <BiUndo size={16} color={!isUndoStackEmpty ? undefined : theme.colors.gray400} />
          </Button>
        </StatefulPopover>

        <StatefulPopover triggerType={TRIGGER_TYPE.hover} placement={PLACEMENT.bottom} content="Redo">
          <Button
            kind="secondary"
            shape={SHAPE.square}
            overrides={{
              BaseButton: {
                style: {
                  borderTopLeftRadius: 0,
                  borderBottomLeftRadius: 0,
                  borderTopRightRadius: '2px',
                  borderBottomRightRadius: '2px',
                  borderLeftWidth: 0,
                },
              },
            }}
            onClick={() => {
              if (!isRedoStackEmpty) {
                redo();
              }
            }}
          >
            <BiRedo size={16} color={!isRedoStackEmpty ? undefined : theme.colors.gray400} />
          </Button>
        </StatefulPopover>
      </div>
      {selectedNodes.length + selectedEdges.length === 0 && schema.VertexTypes.length > 0 && isEditMode ? (
        <>
          <Button
            kind="secondary"
            startEnhancer={<Plus size={18} color={theme.colors['icon.primary']} />}
            onClick={onNewVertex}
          >
            Add Vertex
          </Button>
          <Tips />
        </>
      ) : (
        selectedNodes.length + selectedEdges.length > 0 && (
          <>
            <Button
              kind="secondary"
              startEnhancer={<MdVisibilityOff size={18} color={theme.colors['icon.primary']} />}
              onClick={onHideSelection}
            >
              Hide Selections
            </Button>
            {isEditMode ? (
              <Button
                kind="destructive"
                startEnhancer={<MdDelete size={18} color={theme.colors['dropdown.text.error']} />}
                onClick={() => setDeleteSelection(true)}
              >
                Delete Selections
              </Button>
            ) : null}
          </>
        )
      )}
      {deleteSelection ? (
        <ConfirmDelete
          baseURL={baseURL}
          isOpen={deleteSelection}
          deleteAll={false}
          undoRedoRef={undoRedoRef}
          onClose={() => {
            setDeleteSelection(false);
          }}
          resetUndo={reset}
        />
      ) : null}
    </div>
  );
}

function Tips() {
  const [css, theme] = useStyletron();
  return (
    <StatefulPopover
      showArrow={true}
      content={({ close }) => (
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            gap: '8px',
            borderRadius: '5px',
            backgroundColor: theme.colors['background.primary'],
            maxWidth: '300px',
          }}
        >
          <div style={{ fontSize: '14px', fontWeight: 600, color: theme.colors['text.primary'] }}>Create edge</div>
          <img
            alt="gif animation for how to add edge"
            src={
              theme.name === 'light'
                ? 'https://app-tools-cdn.s3.us-west-1.amazonaws.com/create-edge.gif'
                : 'https://app-tools-cdn.s3.us-west-1.amazonaws.com/create-edge-dark.gif'
            }
          />
          <div
            style={{
              fontSize: '14px',
              lineHeight: '16px',
              color: theme.colors['text.primary'],
            }}
          >
            When you move your mouse over the grey ring around a vertex, an edge sign will appear. Simply drag it to an
            existing vertex to create a new edge.
          </div>
          <div
            className={css({
              display: 'flex',
              justifyContent: 'flex-end',
            })}
          >
            <Button onClick={close}>Got it!</Button>
          </div>
        </div>
      )}
      placement={PLACEMENT.bottomRight}
      overrides={{
        Body: {
          style: {
            backgroundColor: theme.colors['background.primary'],
          },
        },
        Inner: {
          style: {
            backgroundColor: theme.colors['background.primary'],
          },
        },
        Arrow: {
          style: {
            backgroundColor: theme.colors['background.primary'],
          },
        },
      }}
    >
      <Button
        kind="secondary"
        startEnhancer={<Plus size={18} color={theme.colors['icon.primary']} />}
        onClick={() => {}}
      >
        Add Edge
      </Button>
    </StatefulPopover>
  );
}
