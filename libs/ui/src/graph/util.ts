import { CollectionReturnValue, Core, ElementDefinition } from 'cytoscape';
import { AxiosError } from 'axios';

import { ExternalGraph, ExternalNode, ExternalLink, getLinkID } from '@tigergraph/tools-models/gvis/insights';
import { Rules } from '../ruleStyle/type';
import { Schema } from './type';
import { getElementAttribute } from './data';
import { evaluateRules } from '../insights/components/ruleStyle';
import { dataSourceVertexTypes } from './stylesheet';
import { GlobalParams } from '../insights/chart';

// fit with clamped max zoom
export function fitView(cy: Core) {
  cy.maxZoom(2);

  try {
    cy.fit(undefined, 24);
  } catch (error) {}

  cy.maxZoom(10);
}

export function addElements(cy: Core, newElements: ElementDefinition[]) {
  let results: ElementDefinition[] = [];

  for (let element of newElements) {
    if (!cy.hasElementWithId(element.data.id!)) {
      results.push(element);
    }
  }

  return results;
}

// assume all elements are in graph
export function elementsToGraph(graph: ExternalGraph, collection: CollectionReturnValue): ExternalGraph {
  const nodes: ExternalNode[] = [];
  const links: ExternalLink[] = [];

  const nodeMapping = new Map<string, ExternalNode>();
  for (let node of graph.nodes) {
    const id = `${node.type}#${node.id}`;
    nodeMapping.set(id, node);
  }
  const linkMapping = new Map<string, ExternalLink>();
  for (let link of graph.links) {
    const id = getLinkID(link);
    linkMapping.set(id, link);
  }

  for (let ele of collection.toArray()) {
    if (nodeMapping.has(ele.data('id'))) {
      nodes.push(nodeMapping.get(ele.data('id'))!);
    } else if (linkMapping.has(ele.data('id'))) {
      links.push(linkMapping.get(ele.data('id'))!);
    } else {
      console.warn('could not find data in graph', ele);
    }
  }

  return {
    nodes,
    links,
  };
}

export function getRefreshSettingForGraph(cy: Core) {
  const length = cy.elements().length;

  if (length < 100) {
    return 1;
  }
  if (length < 200) {
    return 2;
  }
  if (length < 1000) {
    return 3;
  }
  if (length < 2000) {
    return 4;
  }
  if (length < 3000) {
    return 5;
  }
  if (length < 4000) {
    return 6;
  }
  if (length < 5000) {
    return 8;
  }
  return 10;
}
export function getErrorMessage(error: AxiosError<{ message?: string }> | Error) {
  let message = error.message;

  if ('response' in error) {
    // check for axios error
    // @ts-ignore
    if (error.response['error']) {
      // @ts-ignore
      message = error.response['error'];
      // @ts-ignore
    } else if (error?.response?.data?.message) {
      // error message from rest api
      // @ts-ignore
      message = (error as AxiosError).response?.data?.message;
    }
  }

  if (!message) {
    message = '';
  }

  return message;
}

export function updateGraphLabel(
  cy: Core,
  schema: Schema,
  attrMapGraphLabel?: Record<string, string>,
  attrMapGraphLabelShow?: Record<string, boolean>
) {
  const { VertexTypes: vertexTypes, EdgeTypes: edgeTypes } = schema;
  cy.batch(() => {
    cy.elements().forEach((ele) => {
      const type = ele.data().type;

      if (type in dataSourceVertexTypes) {
        return;
      }

      if (ele.isEdge() && ele.source().data('type') in dataSourceVertexTypes) {
        return;
      }

      const attr = attrMapGraphLabel?.[type];

      let label = String(getElementAttribute(ele, attr));
      let opacity = String(getElementAttribute(ele, attr)) === '' ? 0 : 1;

      if (ele.isNode()) {
        const vertexType = vertexTypes.find((vertexType) => vertexType.Name === type);
        if (!attr || !vertexType || vertexType.PrimaryId.AttributeName === attr) {
          label = ele.data('nodeID');
          opacity = 1;
        }
      } else if (ele) {
        if (!attr) {
          label = type;
          opacity = 1;
        }
        // refer: https://github.com/cytoscape/cytoscape.js/issues/2329
        label = `\u2060${label}\n\n\u2060`;
      }

      const showLabel = attrMapGraphLabelShow?.[type] ?? true;
      if (!showLabel) {
        opacity = 0;
      }

      ele.style({
        label,
        'text-opacity': opacity,
      });
    });
  });
}

export function updateGraphStyle(
  cy: Core,
  rulesByType: Record<string, Rules> | undefined,
  globalParams?: GlobalParams
) {
  cy.batch(() => {
    cy.nodes().forEach((node) => {
      try {
        const type = node.data('type');
        node.removeStyle('background-color width height');
        if (rulesByType && rulesByType[type]) {
          const rules = rulesByType[type];
          const { attributes, ...data } = node.data();

          // original id is for cytoscape
          // but we need id data from tigergraph to do vertex styling.
          const mergedAttrutes = {
            ...data,
            id: data['nodeID'],
            ...attributes,
          };
          const style = evaluateRules(mergedAttrutes, rules, globalParams);

          let nodeStyle: Record<string, any> = {};
          if (style['background-color']) {
            nodeStyle['background-color'] = style['background-color'];
          }
          if (style['node-radius']) {
            nodeStyle['width'] = `${style['node-radius']}px`;
            nodeStyle['height'] = `${style['node-radius']}px`;
          }
          node.style(nodeStyle);
        }
      } catch (error) {
        console.error(error);
      }
    });
    cy.edges().forEach((edge) => {
      try {
        const type = edge.data('type');
        edge.removeStyle('line-color line-style width');
        if (rulesByType && rulesByType[type]) {
          const rules = rulesByType[type];
          const style = evaluateRules(edge.data('attributes'), rules, globalParams);
          let edgeStyle: Record<string, any> = {};
          if (style['line-color']) {
            edgeStyle['line-color'] = style['line-color'];
          }
          if (style['line-style']) {
            edgeStyle['line-style'] = style['line-style'];
          }
          if (style['edge-width']) {
            edgeStyle['width'] = `${style['edge-width']}px`;
          }
          edge.style(edgeStyle);
        }
      } catch (error) {
        console.error(error);
      }
    });
  });
}

export function getElementAttributes(elementData: Record<string, any>, schema: Schema) {
  const isNode = !!elementData['nodeID'];
  const type = elementData['type'];
  let showTitle = true;

  let attributes = { ...elementData.attributes };
  let embeddingAttributes = elementData.embeddingAttributes ? [...elementData.embeddingAttributes] : [];

  if (isNode) {
    const { VertexTypes: vertexTypes } = schema;
    const vertexType = vertexTypes.find((vertexType) => vertexType.Name === type);

    if (!vertexType) {
      // no schema type for this node, means this is a csv file node, just show attrs
      showTitle = false;
    }
  }

  return {
    nodeID: elementData['nodeID'],
    attributes,
    embeddingAttributes,
    showTitle,
  };
}

// for thumbnail, we need to overwrite some global style
export const injectCSS = (css: string) => {
  let el = document.createElement('style');
  el.innerText = css;
  document.head.appendChild(el);
  return el;
};
