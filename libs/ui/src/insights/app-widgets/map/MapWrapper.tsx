import { TGMap } from './tg-map/tg-map';
import { MapConfiguration, MapData, Marker } from './tg-map/models';
import React, { MutableRefObject, useEffect, useRef } from 'react';
import throttle from 'lodash/throttle';
import { Action, ActionParam, buildLinkFromAction } from '../../components/action';
import { useNavigate } from 'react-router-dom';
import { CustomMapSettings } from '../../charts/map/settings/types';
import { GlobalParams } from '../../chart';

export function MapWrapper(props: {
  config: MapConfiguration;
  data: MapData;
  id: string;
  actionsByType?: Record<string, Action[]>;
  settings: CustomMapSettings;
  links: { pageName: string; pageID: string; params: GlobalParams }[];
  getLink: (pageID: string, params: Record<string, string>) => string;
  globalParameters: GlobalParams;
  isEditMode: boolean;
}) {
  const component = useRef<TGMap>();
  const mapContainerRef = useRef<HTMLDivElement>(null);

  const { data, config, id, actionsByType, getLink, settings, globalParameters, isEditMode, links } = props;
  const { useWMS, wms } = settings;
  const navigate = useNavigate();

  useEffect(() => {
    /**
     * Update the component if it already exists.
     */
    if (component.current) {
      updateMap(component, config, data);
      return;
    }
    /**
     * Create a new component instance
     */
    if (config && data) {
      component.current = new TGMap(id, config, data);
    }
  }, [id, config, data]);

  useEffect(() => {
    if (component.current) {
      component.current.setMapTileLayer(useWMS, wms);
    }
  }, [useWMS, wms]);

  useEffect(() => {
    const ro = new ResizeObserver(
      throttle(() => {
        component.current?.updateSize();
      }, 400)
    );

    ro.observe(mapContainerRef.current);
  }, []);

  useEffect(() => {
    const handleMarkerAction = (event) => {
      const customEvent = event as CustomEvent<Marker>;

      if (!actionsByType) {
        return;
      }
      const nodeType = customEvent.detail.type;
      const actions = actionsByType[nodeType];
      if (!actions || !actions.length) {
        return;
      }

      // Get only first action.
      const action = actions[0];

      let attributes = { ...event.detail };
      // make id correct
      attributes.id = attributes['dbId'];

      const params: GlobalParams = links.find((link) => link.pageID === action.pageID)?.params || {};

      const getParam = (param: ActionParam) => {
        // hand vertex type
        if (params[param.name]?.type === 'VERTEX') {
          const key = param.name;
          return {
            [key + '.type']: nodeType,
            [key + '.id']: attributes['id'],
          };
        }

        if (param.paramGlobalInput?.length > 0) {
          return {
            [param.name]: globalParameters[param.paramGlobalInput].value,
          };
        }
        return {
          [param.name]: param.isCreatable ? param.value : attributes[param.value],
        };
      };
      const href = buildLinkFromAction(action, getLink, getParam, isEditMode);

      // if in edit mode, we want to open new tab
      if (action.pageID && !isEditMode) {
        navigate(href);
      } else {
        window.open(href, '_blank');
      }
    };

    const mapContainer = mapContainerRef.current;
    if (mapContainer) {
      mapContainer.addEventListener('onMarkerAction', handleMarkerAction);
    }
    return () => {
      if (mapContainer) {
        mapContainer.removeEventListener('onMarkerAction', handleMarkerAction);
      }
    };
  }, [actionsByType, links, getLink, navigate, globalParameters, isEditMode]);

  function updateMap(component: MutableRefObject<TGMap>, config: MapConfiguration, data: MapData): void {
    if (!data || !config || !component.current) return;
    // Data update (re-render)
    if (JSON.stringify(component.current?.data) !== JSON.stringify(data) || config !== component.current?.config) {
      component.current?.setData(data, config);
    }
  }

  return (
    <div
      id={props.id}
      ref={mapContainerRef}
      style={{
        height: '100%',
        width: '100%',
        zIndex: 0,
      }}
    ></div>
  );
}
