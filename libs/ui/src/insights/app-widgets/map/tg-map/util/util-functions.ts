import { MapMarker, SchemaEntityStyle } from '../types';
import { ChartSchema } from '../../../../chart/schema';
import { LatLngTuple, Polyline } from '../models';
import { CoordinateControlValue } from '../../../../charts/map/settings/types';
import { evaluateRules, Rule } from '../../../../components/ruleStyle';
import { ExternalNode, ExternalLink, ExternalGraph, Color } from '@tigergraph/tools-models/gvis/insights';
import { HelperFunctions } from '@tigergraph/tools-models/utils';
import { escapedBase64, parseNumber } from '../../../../utils';
import { GlobalParams } from '../../../../chart';
import { iconMap } from '../../../../../graph/icons/iconMap';

const color = new Color();
/**
 *
 * Calculates the average geolocation.
 * @param coords has a type MapMarkers. Type MapMarkers contains longitude, latitude, icon and iconColor.
 * By taking all of longitudes and latitudes, it calculates the average geolocation,
 * that means the center of all of the locations
 * @returns  the longitude and latitude from the center point
 */
export function averageGeolocation(coords: MapMarker[]): CoordinateControlValue {
  if (coords.length === 1) {
    return coords[0];
  }
  let x = 0.0;
  let y = 0.0;
  let z = 0.0;

  for (let coord of coords) {
    let latitude = (coord.latitude * Math.PI) / 180;
    let longitude = (coord.longitude * Math.PI) / 180;
    x += Math.cos(latitude) * Math.cos(longitude);
    y += Math.cos(latitude) * Math.sin(longitude);
    z += Math.sin(latitude);
  }
  let total = coords.length;
  x = x / total;
  y = y / total;
  z = z / total;

  let centralLongitude = Math.atan2(y, x);
  let centralSquareRoot = Math.sqrt(x * x + y * y);
  let centralLatitude = Math.atan2(z, centralSquareRoot);

  return {
    latitude: (centralLatitude * 180) / Math.PI,
    longitude: (centralLongitude * 180) / Math.PI,
  };
}

/**
 * Converts the data from chartData to get longitude and latitude from settings
 * @param map in the format of NodeDefinition
 * @param settings in the format settings from chartData
 * @param coordinates in the format MapMarkers
 *
 */

export function getMapData(
  nodes: ExternalNode[],
  coordinates: Record<string, any>,
  schema: ChartSchema,
  globalParameters: GlobalParams,
  rulesByType?: Record<string, Rule[]>
) {
  if (!nodes) return;
  const markers: MapMarker[] = [];
  if (!coordinates) return markers;
  const metadata = extractSchemaMetadata(schema);
  for (const node of nodes) {
    if (typeof node.type !== 'string') continue;
    let latitude = 0;
    let longitude = 0;
    const entries = Object.entries(node.attrs);
    const latitudeKey = coordinates[node.type]?.['latitude'];
    const longitudeKey = coordinates[node.type]?.['longitude'];
    if (!latitudeKey || !longitudeKey) continue;

    const iconImage =
      metadata.get(node.type) && metadata.get(node.type).icon ? iconMap[metadata.get(node.type).icon] : '';
    const nodeColor: string =
      metadata.get(node.type) && metadata.get(node.type).fillColor
        ? metadata.get(node.type).fillColor
        : color.getColor(node.type);

    entries.forEach(([key, value]) => {
      if (latitudeKey === key) {
        latitude = parseNumber(value) as number;
      }
      if (longitudeKey === key) {
        longitude = parseNumber(value) as number;
      }
    });
    let ruleNodeStyle = {};
    if (rulesByType && rulesByType[node.type]) {
      const rules = rulesByType[node.type];
      ruleNodeStyle = evaluateRules(node.attrs, rules, globalParameters);
    }

    if (!isNaN(latitude) && !isNaN(longitude)) {
      const data = {
        ...node.attrs,
        type: node.type,
        dbId: node.id,
        id: escapedBase64(`${node.type}-${node.id}`),
        latitude,
        longitude,
        icon: iconImage ? `/studio/assets/gvis/icons/builtin/${iconImage}` : '',
        iconHTMLColor: ruleNodeStyle['Marker Color'] ?? nodeColor ?? color.getColor(node.type),
        tooltip: generateTooltip(node),
      };
      markers.push(data);
    }
  }
  return markers;
}

function generateTooltip(node: ExternalNode): string {
  const data = {
    id: node.id,
    type: node.type,
    ...node.attrs,
  };

  return Object.entries(data)
    .map(([key, value]) => `<b>${[key]}:</b> ${HelperFunctions.isObject(value) ? JSON.stringify(value) : value}</br>`)
    .join('');
}

function generateLineTooltip(line: ExternalLink): string {
  const data = {
    type: line.type,
    ...line.attrs,
  };
  return Object.entries(data)
    .map(([key, value]) => `<b>${[key]}:</b> ${HelperFunctions.isObject(value) ? JSON.stringify(value) : value}</br>`)
    .join('');
}

export const defaultCircleStyle = {
  color: 'white',
  fillColor: 'white',
  fillOpacity: 0.5,
  radius: 500000,
};

function extractSchemaMetadata(schema: ChartSchema) {
  const output = new Map<string, SchemaEntityStyle>();
  if (!schema.results?.['VertexTypes']) return output;

  for (const type of schema.results['VertexTypes']) {
    output.set(type['Name'], {
      fillColor: type['style']['fillColor'] ?? color.getColor(type['Name']),
      icon: type['style']['icon'] ?? '',
    });
  }

  return output;
}

export function generatePolylines(
  data: ExternalGraph,
  markers: MapMarker[],
  schema: ChartSchema,
  showEdges = false,
  globalParameters: GlobalParams,
  rulesByType?: Record<string, Rule[]>
): Polyline[] {
  if (!showEdges) return [];
  const output: Polyline[] = [];
  const typeColorMap = new Map<string, string>();

  if (schema.results?.['EdgeTypes']) {
    for (const type of schema.results['EdgeTypes']) {
      typeColorMap.set(type['Name'], type?.['style']?.['fillColor'] ?? color.getColor(type['Name']));
    }
  }

  if (!data?.links || !data?.nodes) return output;

  // map entry's key is id of the links, map entry's value is all links between the same two vertices
  const linkMap: Map<string, ExternalLink[]> = new Map();
  for (const link of data.links) {
    const source = markers.find((m) => m.dbId === link.source.id && m.type === link.source.type);
    const target = markers.find((m) => m.dbId === link.target.id && m.type === link.target.type);

    if (source && target) {
      const id =
        source.type > target.type
          ? `${source.type}#${source.id}_${target.type}#${target.id}`
          : `${target.type}#${target.id}_${source.type}#${source.id}`;
      if (linkMap.has(id)) {
        linkMap.get(id).push(link);
      } else {
        linkMap.set(id, [link]);
      }
    }
  }

  /* eslint-disable-next-line */
  for (let [_, links] of linkMap) {
    const marker1: MapMarker = markers.find((m) => m.dbId === links[0].source.id && m.type === links[0].source.type);
    const marker2: MapMarker = markers.find((m) => m.dbId === links[0].target.id && m.type === links[0].target.type);

    // the straight line between marker1 and marker2
    let [point1, point2]: LatLngTuple[] = [
      [marker1.latitude, marker1.longitude],
      [marker2.latitude, marker2.longitude],
    ];

    // if links.length is odd, choose the first link and draw it as a straight line
    if (links.length & 1) {
      const straightLink = links.pop();
      let ruleEdgeStyle = {};
      if (rulesByType && rulesByType[straightLink.type]) {
        const rules = rulesByType[straightLink.type];
        ruleEdgeStyle = evaluateRules(straightLink.attrs, rules, globalParameters);
      }
      output.push({
        coordinates: [point1, point2],
        color: ruleEdgeStyle['line-color'] ?? typeColorMap.get(straightLink.type) ?? color.getColor(straightLink.type),
        weight: ruleEdgeStyle['edge-width'] ?? 4,
        tooltip: generateLineTooltip(straightLink),
      });
    }

    // move the straight line to origin and convert to polar coordinates
    let offsetX = point2[1] - point1[1],
      offsetY = point2[0] - point1[0];
    let r = Math.sqrt(Math.pow(offsetX, 2) + Math.pow(offsetY, 2)),
      theta = Math.atan2(offsetY, offsetX);

    // draw the remain links as curves
    let k = 1;
    for (let link of links) {
      // the offset from the straight line is select in turn from theta, -theta, 2theta, -2theta, ...
      const thetaOffset = (3.14 / 10) * k;
      k = k > 0 ? -k : 1 - k;

      // get the polar coordinate of the control point of the bezier curve
      const r2 = r / 2 / Math.cos(thetaOffset),
        theta2 = theta + thetaOffset;

      // convert to cartesian coordinates
      const midpointX = r2 * Math.cos(theta2) + point1[1],
        midpointY = r2 * Math.sin(theta2) + point1[0];
      const mindPoint: LatLngTuple = [midpointY, midpointX];

      const coordinates = link.source.type === marker1.type ? [point1, mindPoint, point2] : [point2, mindPoint, point1];
      let ruleEdgeStyle = {};
      if (rulesByType && rulesByType[link.type]) {
        const rules = rulesByType[link.type];
        ruleEdgeStyle = evaluateRules(link.attrs, rules, globalParameters);
      }
      output.push({
        coordinates,
        weight: ruleEdgeStyle['edge-width'] ?? 4,
        color: ruleEdgeStyle['line-color'] ?? typeColorMap.get(link.type) ?? color.getColor(link.type),
        tooltip: generateLineTooltip(link),
      });
    }
  }
  return output;
}
