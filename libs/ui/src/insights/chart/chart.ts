import { ChartData } from './type';
import { ChartSchema } from './schema';
import { GlobalParams, GlobalParam } from './globalParams';

export interface ChartProps {
  id: string;
  graphName: string;
  schema: ChartSchema;
  chartData: ChartData; // Query output
  isEditMode: boolean;
  settings: Record<string, any>; // Chart setting
  onSettingUpdate: (key: string, value: any) => void; // Chart can update setting.
  chartStates: Record<string, any>; // Chart state that need reach to custom setting component.
  onChartStateUpdate: (key: string, value: any) => void;

  setGlobalParameter: (name: string, value: GlobalParam, prevName?: string) => void; // Allows a chart to update a global parameter to be used in queries for other charts.
  getGlobalParameter: (name: string) => GlobalParam; // Allows a chart to get a global parameter.
  globalParameters: GlobalParams;

  links: { pageName: string; pageID: string; params: GlobalParams }[];
  getLink: (pageID: string, params: Record<string, string>) => string;
  emptyNodesLinksView?: () => React.ReactElement;

  isCloud?: boolean;
  isClusterMode?: boolean;
  themeType?: 'light' | 'dark';
  initNodeRadius?: number;
  baseURL: string;
}

export interface ChartSettingProps {
  graphName: string;
  schema: ChartSchema;
  chartData: ChartData;
  isEditMode: boolean;
  settings: Record<string, any>;
  onSettingUpdate: (key: string, value: any) => void;
  chartStates: Record<string, any>;
  onChartStateUpdate: (key: string, value: any) => void;
  setGlobalParameter: (name: string, value: GlobalParam, prevName?: string) => void;
  deleteGlobalParameter: (name: string) => void; // Allows a chart to delete a global parameter.
  getGlobalParameter: (name: string) => GlobalParam;
  globalParameters: GlobalParams;

  links: { pageName: string; pageID: string; params: GlobalParams }[];
  getLink: (pageID: string, params: Record<string, string>) => string;

  isCloud?: boolean;
  isClusterMode?: boolean;
  themeType?: 'light' | 'dark';
  baseURL: string;
}
