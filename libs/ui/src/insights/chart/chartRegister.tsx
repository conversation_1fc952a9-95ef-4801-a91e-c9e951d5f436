import React from 'react';
import { ChartProps, ChartSettingProps } from './chart';
import { Table, TableDataSetting, TableDisplaySetting, TableAdvancedSetting } from '../charts/table';
import { Inputs, InputsSettingTool } from '../charts/inputs';
import {
  Bar<PERSON>hart,
  LineChart,
  PieChart,
  ChartsCartesianCoordinateSetting,
  ChartsPolarCoordinateSetting,
  ScatterChart,
  ChartsScatterSetting,
} from '../charts/charts';
import TemplateDrivenChartSettings from '../charts/chartTools/chartSetting/templateDrivenChartSettings';
import { MapSettingTool } from '../charts/map/settings/mapSettingTool';

import GraphIcon from './icons/graph-icon.png';
import BarchartIcon from './icons/barchart-icon.png';
import LinechartIcon from './icons/linechart-icon.png';
import PiechartIcon from './icons/piechart-icon.png';
import TableIcon from './icons/table-icon.png';
import SingleValueIcon from './icons/singleValue-icon.png';
import InputsContainerIcon from './icons/inputsContainer-icon.png';
import MapIcon from './icons/map-icon.png';
import EmptyIcon from './icons/empty.png';
import MarkdownIcon from './icons/markdown-icon.png';
import ScatterIcon from './icons/scatter-icon.png';
import SankeyIcon from './icons/sankey-icon.png';

import SankeyChartSettings from '../charts/charts/sankeyChart/components/sankeyChartSettings';
import ChartSettingSectionContainer from '../charts/chartTools/chartSetting/chartSettingSectionContainer';
import { ReactNode } from 'react';
import { lazyWithPreload } from '../utils';
import { GraphSettings } from '../charts/graphNew/settings';
import DocLink from '../components/docLink';
import MultiValueChart from '../charts/multiValue';
import MultiValueSetting from '../charts/multiValue/multiValueSetting';

const MapChart = lazyWithPreload(
  () =>
    import(
      /* webpackChunkName: "mapChart" */
      '../charts/map/map'
    )
);

const Graph = lazyWithPreload(
  () =>
    import(
      /* webpackChunkName: "graphChart" */
      '../charts/graphNew/index2'
    )
);

const SankeyChart = lazyWithPreload(
  () =>
    import(
      /* webpackChunkName: "sankeyChart" */
      '../charts/charts/sankeyChart/components/sankeyChart'
    )
);

const MarkdownChart = lazyWithPreload(
  () =>
    import(
      /* webpackChunkName: "markdown" */
      '../chart/markdownChart'
    )
);

export type BaseSetting = {
  helpText?: ReactNode;
};

export type SpecificSetting =
  | {
      type: 'toggle';
      label: string;
      default: boolean;
    }
  | {
      type: 'number';
      label: string;
      default: number;
    }
  | {
      type: 'slider';
      label: string;
      default: number;
      min: number;
      max: number;
      step?: number;
    }
  | { type: 'text'; label: string; default: string }
  | { type: 'name'; label: string; default: string } // special type for global variable name
  | { type: 'color'; label: string; default: string }
  | { type: 'list'; label: string; default: any; values: any[] };

export type SETTING_TYPE = BaseSetting & SpecificSetting;

export type ChartSettingsTemplate = {
  [key: string]: SETTING_TYPE;
};

export type ChartSettingsInfo = {
  defaultSettings(): Record<string, any>;
  createUIComponent(): (props: ChartSettingProps) => JSX.Element;
};

export function chartSettingFromTemplate(
  template: ChartSettingsTemplate,
  sectionTitle?: React.ReactNode
): ChartSettingsInfo {
  return {
    defaultSettings() {
      const result: Record<string, any> = {};
      Object.keys(template).forEach((key) => (result[key] = template[key].default));
      return result;
    },
    createUIComponent() {
      if (sectionTitle) {
        return (props) => (
          <ChartSettingSectionContainer title={sectionTitle}>
            {TemplateDrivenChartSettings({
              settingsTemplate: template,
              settings: props.settings,
              updateSetting: props.onSettingUpdate,
              getGlobalParameter: props.getGlobalParameter,
              setGlobalParameter: props.setGlobalParameter,
            })}
          </ChartSettingSectionContainer>
        );
      }
      return (props) =>
        TemplateDrivenChartSettings({
          settingsTemplate: template,
          settings: props.settings,
          updateSetting: props.onSettingUpdate,
          getGlobalParameter: props.getGlobalParameter,
          setGlobalParameter: props.setGlobalParameter,
        });
    },
  };
}

export type Chart = {
  type: string;
  label: string;
  threshold?: number;
  component: (props: ChartProps) => JSX.Element;
  noQuery?: boolean;
  settingsInfo?: ChartSettingsInfo;
  icon?: string;
  description?: ReactNode;
};

export type ChartCore = Omit<Chart, 'type' | 'label'>;

const CHART_TYPES: Chart[] = [];

export function registerChart(chart: Chart) {
  CHART_TYPES.push(chart);
}

export function chartForType(type: string): Chart | undefined {
  for (let chart of CHART_TYPES) {
    if (chart.type === type) {
      return chart;
    }
  }
  return undefined;
}

export function chartTypes(): Array<{ id: string; label: string; icon: string }> {
  return CHART_TYPES.map((chart) => ({ id: chart.type, label: chart.label, icon: chart.icon || EmptyIcon }));
}

function getDocLink(pageName: string) {
  return `https://docs.tigergraph.com/insights/current/widgets/${pageName}`;
}

registerChart({
  type: 'internal-graph',
  label: 'Graph',
  threshold: 5000,
  component: Graph,
  settingsInfo: (() => {
    return {
      defaultSettings() {
        return {};
      },
      createUIComponent() {
        return (props: ChartSettingProps) => <GraphSettings {...props} />;
      },
    };
  })(),

  icon: GraphIcon,
  description: (
    <>
      Graph is best used when you want to show the relationships between multiple vertices.<br></br>Pattern search
      results can be easily displayed on the graph without configuration.<br></br>For GSQL query result, you will need
      to return lists of vertices and edges to display on the graph.&nbsp;
      <DocLink href={getDocLink('graph-widget')} />
    </>
  ),
});

registerChart({
  type: 'bar',
  label: 'Bar chart',
  threshold: 10 ** 4,
  component: BarChart,
  settingsInfo: {
    createUIComponent() {
      return ChartsCartesianCoordinateSetting;
    },
    defaultSettings() {
      return {};
    },
  },
  icon: BarchartIcon,
  description: (
    <>
      Bar chart is to show different data through the height of a bar.<br></br>Pattern search resutls can be easily
      displayed if there is an attribute as the category and another attribute that is number as the value.<br></br>For
      GSQL query result, you will need to return a list of objects, each of them contains key-value pairs to be category
      and value.&nbsp;
      <DocLink href={getDocLink('bar-chart')} />
    </>
  ),
});

registerChart({
  type: 'line',
  label: 'Line chart',
  threshold: 10 ** 4,
  component: LineChart,
  settingsInfo: {
    createUIComponent() {
      return ChartsCartesianCoordinateSetting;
    },
    defaultSettings() {
      return {};
    },
  },
  icon: LinechartIcon,
  description: (
    <>
      Line chart is to show different data through points and lines.<br></br>Pattern search resutls can be easily
      displayed if there is an attribute as the category and another attribute that is number as the value.<br></br>For
      GSQL query result, you will need to return a list of objects, each of them contains key-value pairs to be category
      and value.&nbsp;
      <DocLink href={getDocLink('line-chart')} />
    </>
  ),
});

registerChart({
  type: 'scatter',
  label: 'Scatter chart',
  threshold: 10 ** 4,
  component: ScatterChart,
  settingsInfo: {
    createUIComponent() {
      return ChartsScatterSetting;
    },
    defaultSettings() {
      return {};
    },
  },
  icon: ScatterIcon,
  description: (
    <>
      Scatter chart is to show different data through points.<br></br>Pattern search resutls can be easily displayed if
      there is an attribute as the category and another attribute that is number as the value.<br></br>For GSQL query
      result, you will need to return a list of objects, each of them contains key-value pairs to be category and
      value.&nbsp;
      <DocLink href={getDocLink('line-chart')} />
    </>
  ),
});

registerChart({
  type: 'pie',
  label: 'Pie chart',
  threshold: 10 ** 4,
  component: PieChart,
  settingsInfo: {
    createUIComponent() {
      return ChartsPolarCoordinateSetting;
    },
    defaultSettings() {
      return {};
    },
  },
  icon: PiechartIcon,
  description: (
    <>
      Pie chart is to show proportion of different data through sectors.<br></br>Pattern search resutls can be easily
      displayed if there is an attribute as the category and another attribute that is number as the value.<br></br>For
      GSQL query result, you will need to return a list of objects, each of them contains key-value pairs to be category
      and value.&nbsp;
      <DocLink href={getDocLink('pie-chart')} />
    </>
  ),
});

registerChart({
  type: 'sankey',
  label: 'Sankey chart',
  threshold: 10 ** 4,
  component: SankeyChart,
  settingsInfo: {
    createUIComponent() {
      return SankeyChartSettings;
    },
    defaultSettings() {
      return {};
    },
  },
  icon: SankeyIcon,
  description: (
    <>
      A Sankey chart is best used when you want to show a many-to-many mapping between two domains.<br></br>In order to
      maximize the benefit of displaying a Sankey chart, each edge should have a numeric attribute, so that we can use
      the numeric attirbute to visualize how value flows from one to another.&nbsp;
      <DocLink href={getDocLink('sankey')} />
    </>
  ),
});

registerChart({
  type: 'table',
  label: 'Table',
  threshold: 10 ** 5,
  component: Table,
  settingsInfo: (() => {
    const dataTemplated = chartSettingFromTemplate({
      pageSize: {
        label: 'Page Size',
        type: 'list',
        default: 10,
        values: [5, 10, 20, 50],
        helpText: 'The size of pages that are used for the table space',
      },
    });
    const DataTemplatedUI = dataTemplated.createUIComponent();
    return {
      defaultSettings() {
        return dataTemplated.defaultSettings();
      },
      createUIComponent() {
        return (props) => (
          <>
            <ChartSettingSectionContainer title={'Data'}>
              <TableDataSetting {...props} />
              <DataTemplatedUI {...props} />
            </ChartSettingSectionContainer>
            <ChartSettingSectionContainer title={'Display'}>
              <TableDisplaySetting {...props} />
            </ChartSettingSectionContainer>
            <ChartSettingSectionContainer title={'Advanced'}>
              <TableAdvancedSetting {...props} />
            </ChartSettingSectionContainer>
          </>
        );
      },
    };
  })(),
  icon: TableIcon,
  description: (
    <>
      Table widget allows you to view the data in a table. Both pattern search result and query result can be easily
      adapted to table view automatically without configuration.&nbsp;
      <DocLink href={getDocLink('table-widget')} />
    </>
  ),
});

registerChart({
  type: 'value',
  label: 'Value widget',
  component: MultiValueChart,
  settingsInfo: {
    createUIComponent() {
      return MultiValueSetting;
    },
    defaultSettings() {
      return {};
    },
  },
  icon: SingleValueIcon,
  description: (
    <>
      Multi value widget allows you to show multi pieces of information with the option to add icon.&nbsp;
      <DocLink href={getDocLink('single-value')} />
    </>
  ),
});

registerChart({
  type: 'Inputs',
  label: 'Inputs',
  component: Inputs,
  noQuery: true,
  // settingsComponent: InputsSettingTool,
  settingsInfo: {
    createUIComponent() {
      return InputsSettingTool;
    },
    defaultSettings() {
      return {};
    },
  },
  icon: InputsContainerIcon,
  description: (
    <>
      Inputs allow you to add named user inputs to your dashboard.<br></br>You can bind the input as a parameter to
      queries or search pattern filters so that the result can be based on the user input.&nbsp;
      <DocLink href={getDocLink('inputs')} />
    </>
  ),
});

registerChart({
  type: 'map',
  label: 'Map',
  threshold: 5000,
  component: MapChart,
  settingsInfo: (() => {
    const templated = chartSettingFromTemplate({
      showEdges: {
        label: 'Show Edges',
        type: 'toggle',
        default: false,
        helpText: 'Show edges on the map.',
      },
      clustering: {
        label: 'Enable Clustering',
        type: 'toggle',
        default: false,
        helpText: 'Show geographically close vertices as a cluster. Click a cluster maker to expand it',
      },
    });
    const TemplatedUI = templated.createUIComponent();
    return {
      defaultSettings() {
        return templated.defaultSettings();
      },
      createUIComponent() {
        return (props) => (
          <>
            <MapSettingTool {...props} TemplatedUI={TemplatedUI} />
          </>
        );
      },
    };
  })(),
  icon: MapIcon,
  description: (
    <>
      Map widget is a special type of visualization for graph. We show the vertices on the map based on its geospatial
      attribute values. The vertex will be automatically picked up if it has both "longitude" and "latitude" attributes
      defined. The mapping for different types of vertices can be changed if needed.&nbsp;
      <DocLink href={getDocLink('map-widget')} />
    </>
  ),
});

registerChart({
  type: 'markdown',
  label: 'Markdown',
  component: MarkdownChart,
  noQuery: true,
  icon: MarkdownIcon,
  description: (
    <>
      The Markdown widget allows you to add formatted text, links, images, and other rich content to your dashboards in
      TigerGraph Insights. You can use Markdown syntax to create visually appealing and informative content.&nbsp;
    </>
  ),
});
