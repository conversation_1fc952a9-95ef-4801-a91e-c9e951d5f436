import {
  constructGlobalParams,
  duplicateGlobalParam,
  getParamTypeDefaultValue,
  buildURLSearchParams,
  globalParamsFromURLSearchParams,
  globalParamsToURLSearchParams,
  globalParametersMigration,
  isGlobalParamTypeCompatible,
  GlobalParams,
  ParamType,
} from './globalParams';

describe('constructGlobalParams', () => {
  it('should return existing param with updated values when name exists in params', () => {
    const params: GlobalParams = {
      test: { name: 'test', id: '1', type: 'STRING', value: 'value' },
    };
    const paramType = { type: 'STRING', value: 'newValue' } as const;
    const result = constructGlobalParams(params, 'test', paramType);
    expect(result).toMatchObject({
      name: 'test',
      id: '1',
      type: 'STRING',
      value: 'newValue',
    });
  });

  it('should create new param when name does not exist in params', () => {
    const params: GlobalParams = {};
    const paramType: ParamType = { type: 'STRING', value: 'defaultValue' };
    const result = constructGlobalParams(params, 'newParam', paramType);
    expect(result).toMatchObject({
      name: 'newParam',
      type: 'STRING',
      value: 'defaultValue',
    });
  });

  it('should handle NUMBER type', () => {
    const params: GlobalParams = {};
    const paramType: ParamType = { type: 'NUMBER', value: 42 };
    const result = constructGlobalParams(params, 'numberParam', paramType);
    expect(result).toMatchObject({
      name: 'numberParam',
      type: 'NUMBER',
      value: 42,
    });
  });

  it('should handle LIST of NUMBER type', () => {
    const params: GlobalParams = {};
    const paramType: ParamType = { type: 'LIST', elementType: 'NUMBER', value: [1, 2, 3] };
    const result = constructGlobalParams(params, 'listNumberParam', paramType);
    expect(result).toMatchObject({
      name: 'listNumberParam',
      type: 'LIST',
      elementType: 'NUMBER',
      value: [1, 2, 3],
    });
  });

  it('should handle LIST of DATETIME type', () => {
    const params: GlobalParams = {};
    const paramType: ParamType = { type: 'LIST', elementType: 'DATETIME', value: ['2023-12-09', '2023-12-10'] };
    const result = constructGlobalParams(params, 'listDatetimeParam', paramType);
    expect(result).toMatchObject({
      name: 'listDatetimeParam',
      type: 'LIST',
      elementType: 'DATETIME',
      value: ['2023-12-09', '2023-12-10'],
    });
  });

  it('should handle LIST of VERTEX type', () => {
    const params: GlobalParams = {};
    const paramType: ParamType = {
      type: 'LIST',
      elementType: 'VERTEX',
      value: [
        { vertexType: 'Person', vertexID: '123' },
        { vertexType: 'Animal', vertexID: '456' },
      ],
    };
    const result = constructGlobalParams(params, 'listVertexParam', paramType);
    expect(result).toMatchObject({
      name: 'listVertexParam',
      type: 'LIST',
      elementType: 'VERTEX',
      value: [
        { vertexType: 'Person', vertexID: '123' },
        { vertexType: 'Animal', vertexID: '456' },
      ],
    });
  });
});

describe('duplicateGlobalParam', () => {
  it('should duplicate global param', () => {
    const origParams: GlobalParams = {
      test: { id: '1', name: 'test', type: 'STRING', value: 'value' },
    };
    const newParams = {};
    const result = duplicateGlobalParam('test', origParams, 'testCopy', newParams);
    expect(result).toEqual({
      testCopy: { id: '1', type: 'STRING', value: 'value', name: 'testCopy' },
    });
  });

  it('should handle LIST type correctly', () => {
    const origParams: GlobalParams = {
      test: { id: '1', name: 'origParams', type: 'LIST', elementType: 'STRING', value: ['value1', 'value2'] },
    };
    const newParams = {};
    const result = duplicateGlobalParam('test', origParams, 'testCopy', newParams);
    expect(result).toEqual({
      testCopy: { id: '1', type: 'LIST', elementType: 'STRING', value: ['value1', 'value2'], name: 'testCopy' },
    });
  });

  it('default', () => {
    const origParams: GlobalParams = {
      test: { id: '1', name: 'test', type: 'STRING', value: 'value' },
    };
    const newParams = {};
    const result = duplicateGlobalParam('test1', origParams, 'testCopy', newParams);
    expect(result).toEqual(newParams);
  });
});

describe('getParamTypeDefaultValue', () => {
  it('should return default value for STRING', () => {
    const result = getParamTypeDefaultValue('STRING');
    expect(result).toEqual({ type: 'STRING', value: '' });
  });

  it('should return default value for NUMBER', () => {
    const result = getParamTypeDefaultValue('NUMBER');
    expect(result).toEqual({ type: 'NUMBER', value: 0 });
  });

  it('should return default value for DATETIME', () => {
    const result = getParamTypeDefaultValue('DATETIME');
    const expectedValue = new Date().toISOString().replace('T', ' ').slice(0, 19);
    expect(result).toEqual({ type: 'DATETIME', value: expectedValue });
  });

  it('should return default value for VERTEX', () => {
    const result = getParamTypeDefaultValue('VERTEX');
    expect(result).toEqual({ type: 'VERTEX', value: { vertexType: '', vertexID: '' } });
  });
  it('should return undefined for unknown', () => {
    const result = getParamTypeDefaultValue('UNKNOWN' as any);
    expect(result).toBeUndefined();
  });
});

describe('buildURLSearchParams', () => {
  it('should build URL search params', () => {
    const params: GlobalParams = {
      param1: { type: 'STRING', name: 'param1', id: '1', value: 'value1' },
      param2: { type: 'NUMBER', name: 'param2', id: '2', value: 123 },
      // @ts-ignore
      param3: { type: 'DOUBLE', name: 'param3', id: '3', value: 123 },
    };
    const queryParams = [
      { paramName: 'param1', paramType: { type: 'STRING' } },
      { paramName: 'param2', paramType: { type: 'INT' } },
      { paramName: 'param3', paramType: { type: 'INT' }, paramDefaultValue: '1234' },
    ];
    const result = buildURLSearchParams(params, queryParams);
    expect(result.urlSearchParams.toString()).toBe('param1=value1&param2=123&param3=1234');
  });

  it('should build URL search params for vertices', () => {
    const params: GlobalParams = {
      param1: { type: 'VERTEX', name: 'param1', id: '4', value: { vertexID: 'id', vertexType: 'type' } },
    };
    const queryParams = [{ paramName: 'param1', paramType: { type: 'VERTEX', vertexType: '*' } }];
    const result = buildURLSearchParams(params, queryParams);
    expect(result.urlSearchParams.toString()).toBe('param1=id&param1.type=type');
  });

  it('should build URL search params for vertices interpreted', () => {
    const params: GlobalParams = {
      param1: { type: 'VERTEX', name: 'param1', id: '4', value: { vertexID: 'id', vertexType: 'type' } },
    };
    const queryParams = [{ paramName: 'param1', paramType: { type: 'VERTEX' } }];
    const result = buildURLSearchParams(params, queryParams, true);
    expect(result.urlSearchParams.toString()).toBe(encodeURI('param1[0]=id&param1[0].type=type'));
  });

  it('should handle incompatible types', () => {
    const params: GlobalParams = {
      param1: { name: 'param1', id: '1', type: 'VERTEX', value: { vertexID: 'id1', vertexType: 'type1' } },
    };
    const queryParams = [{ paramName: 'param1', paramType: { type: 'STRING' } }];
    const result = buildURLSearchParams(params, queryParams);
    expect(result.error).toBeTruthy();
  });
});

describe('globalParamsFromURLSearchParams', () => {
  it('should convert URLSearchParams to global params', () => {
    const globalParams: GlobalParams = {
      param1: { type: 'STRING', name: 'param1', id: '1', value: 'oldValue' },
      // @ts-ignore
      param2: { type: 'INT', value: 123 },
    };
    const urlSearchParams = new URLSearchParams('param1=newValue&param2=456');
    const result = globalParamsFromURLSearchParams(globalParams, urlSearchParams);
    expect(result).toEqual({
      param1: { type: 'STRING', name: 'param1', id: '1', value: 'newValue' },
    });
  });
});

describe('globalParamsToURLSearchParams', () => {
  it('should convert global params to URLSearchParams', () => {
    const globalParams: GlobalParams = {
      param1: { type: 'STRING', name: 'param1', id: '1', value: 'value1' },
      param2: { type: 'NUMBER', name: 'param2', id: '2', value: 123 },
      param3: { type: 'DATETIME', name: 'param3', id: '1', value: 'value3' },
      param4: { type: 'VERTEX', name: 'param4', id: '1', value: { vertexID: 'id', vertexType: 'type' } },
      param5: { type: 'LIST', elementType: 'STRING', name: 'param1', id: '1', value: ['1', '2'] },
    };
    const urlSearchParams = new URLSearchParams(Object.keys(globalParams).map((i) => [i, '']));
    const result = globalParamsToURLSearchParams(globalParams, urlSearchParams);
    expect(result.toString()).toBe('param1=value1&param2=123&param3=value3&param4=&param5=1&param5=2');
  });
});

describe('globalParametersMigration', () => {
  it('should migrate old format to new format', () => {
    const oldParams = {
      param1: 'value1',
      param2: 123,
      param3: { type: 'type' },
    };
    const result = globalParametersMigration(oldParams);
    expect(result).toEqual({
      param1: { name: 'param1', id: 'param1', type: 'STRING', value: 'value1' },
      param2: { name: 'param2', id: 'param2', type: 'NUMBER', value: 123 },
      param3: { type: 'type' },
    });
  });
});

describe('isGlobalParamTypeCompatible', () => {
  it('should return true for compatible types', () => {
    expect(isGlobalParamTypeCompatible('STRING', 'STRING')).toBe(true);
    expect(isGlobalParamTypeCompatible('NUMBER', 'INT')).toBe(true);
    expect(isGlobalParamTypeCompatible('DATETIME', 'DATETIME')).toBe(true);
    expect(isGlobalParamTypeCompatible('VERTEX', 'VERTEX')).toBe(true);
    expect(isGlobalParamTypeCompatible('VERTEX', 'VERTEX', true)).toBe(true);
  });

  it('should return false for incompatible types', () => {
    expect(isGlobalParamTypeCompatible('STRING', 'VERTEX')).toBe(false);
    expect(isGlobalParamTypeCompatible('DOUBLE', 'NUMBER')).toBe(false);
    expect(isGlobalParamTypeCompatible('DOUBLE', 'BOOL')).toBe(false);
  });
});
