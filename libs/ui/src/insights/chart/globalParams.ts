import short from 'short-uuid';
import { QueryParam, QueryParamVertexType, QueryParamListType } from '@tigergraph/tools-models/query';

type PrimitiveType =
  | {
      type: 'STRING';
      value: string;
    }
  | {
      type: 'NUMBER';
      value: number;
    }
  | {
      type: 'BOOLEAN';
      value: boolean;
    }
  | {
      // should be yyyy-MM-dd or yyyy-MM-dd hh:mm:ss or timestamp
      // but we do not validate this yet.
      type: 'DATETIME';
      value: string;
    }
  | {
      type: 'VERTEX';
      value: {
        vertexType: string;
        vertexID: string;
      };
    };

type PrimitiveMapType = {
  keyType: string;
  valueType: string;
  value: { key: unknown; value: unknown }[];
};

export type PrimitiveArrayType =
  | {
      elementType: 'STRING';
      value: Array<string>;
    }
  | {
      elementType: 'NUMBER';
      value: Array<number>;
    }
  | {
      elementType: 'BOOLEAN';
      value: Array<boolean>;
    }
  | {
      elementType: 'DATETIME';
      value: Array<string>;
    }
  | {
      elementType: 'VERTEX';
      value: Array<{
        vertexType: string;
        vertexID: string;
      }>;
    };

export type ParamType =
  | PrimitiveType
  | ({
      type: 'LIST';
    } & PrimitiveArrayType)
  | ({
      type: 'MAP';
    } & PrimitiveMapType);

export type GlobalParam = ParamType & {
  name: string;
  id: string; // stable unique id
};

export type GlobalParams = Record<string, GlobalParam>;

export function constructGlobalParams(params: GlobalParams, name: string, paramType: ParamType): GlobalParam {
  if (params[name]) {
    return {
      ...params[name],
      name,
      ...paramType,
    };
  }

  return {
    name,
    id: short.uuid(),
    ...paramType,
  };
}

export function duplicateGlobalParam(
  origName: string,
  origParams: GlobalParams,
  newName: string,
  newParams: GlobalParams
): GlobalParams {
  const origParam = origParams[origName];
  if (origParam) {
    const newParam: GlobalParam = {
      ...origParam,
      name: newName,
    };

    // deep copy for list value
    if (origParam.type === 'LIST') {
      // @ts-ignore
      newParam.value = [...origParam.value];
    }
    return {
      ...newParams,
      [newName]: newParam,
    };
  } else {
    return newParams;
  }
}

export function getParamTypeDefaultValue(type: 'STRING' | 'NUMBER' | 'BOOL' | 'DATETIME' | 'VERTEX'): ParamType {
  switch (type) {
    case 'STRING':
      return {
        type,
        value: '',
      };
    case 'NUMBER':
      return {
        type,
        value: 0,
      };
    case 'DATETIME':
      return {
        type,
        // format date string according to the gsql data types reference
        value: new Date().toISOString().replace('T', ' ').slice(0, 19),
      };
    case 'VERTEX':
      return {
        type,
        value: {
          vertexType: '',
          vertexID: '',
        },
      };
    case 'BOOL':
      return {
        // we use BOOLEAN for bool type in global parameters
        type: 'BOOLEAN',
        value: false,
      };
    default:
      break;
  }
}

// original code at `question-form-builder.service.ts`
export function buildURLSearchParams(
  params: GlobalParams,
  queryParams: QueryParam[],
  interpreted?: boolean
): {
  error?: Error;
  urlSearchParams: URLSearchParams;
} {
  let urlSearchParams = new URLSearchParams();

  for (let queryParam of queryParams) {
    const paramName = queryParam.paramName;
    if (params[paramName]) {
      const param = params[paramName];
      const type = param.type;

      switch (queryParam.paramType.type) {
        case 'STRING':
        case 'BOOL':
        case 'DATETIME':
        case 'INT':
        case 'UINT':
        case 'FLOAT':
        case 'DOUBLE': {
          // double check params type is compatible with query params type
          if (type === 'STRING' || type === 'NUMBER' || type === 'DATETIME') {
            urlSearchParams.set(paramName, String(param.value));
          } else if (queryParam.paramDefaultValue) {
            urlSearchParams.set(paramName, String(queryParam.paramDefaultValue));
          } else {
            return {
              error: new Error(
                `parameter "${paramName}" require type "${queryParam.paramType.type}", but for variable, we have type "${type}".`
              ),
              urlSearchParams,
            };
          }
          break;
        }
        case 'VERTEX': {
          /**
           * When run query in interpreted mode, "[0]" should be suffix of the parameter name
           * and type. Vertex type should be added.
           * For example:
           *   localhost:14240/gsqlserver/interpreted_query?a[0]=A&a[0].type=Person
           */
          if (type === 'VERTEX') {
            urlSearchParams.set(interpreted ? `${paramName}[0]` : paramName, param.value.vertexID);
            if (interpreted) {
              urlSearchParams.set(paramName + '[0].type', param.value.vertexType);
            } else {
              if ((queryParam.paramType as QueryParamVertexType).vertexType === '*') {
                urlSearchParams.set(paramName + '.type', param.value.vertexType);
              }
            }
          } else {
            return {
              error: new Error(
                `parameter "${paramName}" require type "${queryParam.paramType.type}", but for variable, we have type "${type}".`
              ),
              urlSearchParams,
            };
          }
          break;
        }
        case 'LIST': {
          const elementType = (queryParam.paramType as QueryParamListType).elementType;

          if (type === 'LIST') {
            switch (elementType.type) {
              case 'STRING':
              case 'BOOL':
              case 'DATETIME':
              case 'INT':
              case 'UINT':
              case 'FLOAT':
              case 'DOUBLE': {
                if (
                  param.elementType === 'STRING' ||
                  param.elementType === 'NUMBER' ||
                  param.elementType === 'DATETIME'
                ) {
                  for (let subValue of param.value) {
                    urlSearchParams.append(paramName, String(subValue));
                  }
                } else {
                  return {
                    error: new Error(
                      `parameter "${paramName}" require type "<${elementType.type}>", but for variable, we have type "<${param.elementType}>".`
                    ),
                    urlSearchParams,
                  };
                }
                break;
              }
              case 'VERTEX': {
                if (param.elementType === 'VERTEX') {
                  for (let i = 0; i < param.value.length; i++) {
                    const subValue = param.value[i];
                    // If it is vertex without type, will push paramName[index] and paramName[index].type
                    if ((elementType as QueryParamVertexType).vertexType === '*' || interpreted) {
                      urlSearchParams.set(paramName + `[${i}]`, subValue.vertexID);
                      urlSearchParams.set(paramName + `[${i}].type`, subValue.vertexType);
                    } else {
                      // If it is vertex with type running in compiled mode, only send vertex id
                      urlSearchParams.append(paramName, subValue.vertexID);
                    }
                  }
                } else {
                  // currently, insights input widget do not support list of vertex,
                  // so we need to convert list of string|number to list of vertex
                  for (let i = 0; i < param.value.length; i++) {
                    const subValue = param.value[i];
                    // If it is vertex without type, will push paramName[index] and paramName[index].type
                    if ((elementType as QueryParamVertexType).vertexType === '*' || interpreted) {
                      urlSearchParams.set(paramName + `[${i}]`, `${subValue}`);
                      // use type from elementType
                      urlSearchParams.set(paramName + `[${i}].type`, elementType.vertexType);
                    } else {
                      // If it is vertex with type running in compiled mode, only send vertex id
                      urlSearchParams.append(paramName, `${subValue}`);
                    }
                  }
                }
                break;
              }
            }
          } else {
            return {
              error: new Error(
                `parameter "${paramName}" require type "${queryParam.paramType.type}", but for variable, we have type "${type}".`
              ),
              urlSearchParams,
            };
          }

          break;
        }
      }
    } else {
      return {
        error: new Error(
          `No variable available for parameter "${paramName}".\nYou can create variable by creating Inputs Widget.`
        ),
        urlSearchParams,
      };
    }
  }

  return {
    urlSearchParams,
  };
}

export function globalParamsFromURLSearchParams(
  globalParams: GlobalParams,
  urlSearchParams: URLSearchParams
): GlobalParams {
  let ret: GlobalParams = {};

  let keys = Object.keys(globalParams);
  for (let key of keys) {
    const param = globalParams[key];
    const type = param.type;
    if (type === 'STRING' || type === 'NUMBER' || type === 'DATETIME') {
      if (urlSearchParams.has(key)) {
        const valueInURL = urlSearchParams.get(key);
        // @ts-ignore: we hit ts limitation here.
        ret[key] = {
          ...param,
          value: type === 'STRING' || type === 'DATETIME' ? valueInURL : Number(valueInURL),
        };
      }
    } else if (type === 'VERTEX') {
      if (urlSearchParams.has(`${key}.type`) && urlSearchParams.has(`${key}.id`)) {
        ret[key] = {
          ...param,
          value: {
            vertexID: urlSearchParams.get(`${key}.id`),
            vertexType: urlSearchParams.get(`${key}.type`),
          },
        };
      } else if (urlSearchParams.has(key)) {
        // if only contains key and no type, just update vertex ID
        ret[key] = {
          ...param,
          value: {
            ...param.value,
            vertexID: urlSearchParams.get(key),
          },
        };
      }
    } else if (type === 'LIST') {
      const elementType = param.elementType;
      if (elementType === 'STRING' || elementType === 'NUMBER' || elementType === 'DATETIME') {
        if (urlSearchParams.has(key)) {
          const valueInURL = urlSearchParams.getAll(key);
          // @ts-ignore
          ret[key] = {
            ...param,
            value: elementType === 'STRING' || elementType === 'DATETIME' ? valueInURL : valueInURL.map(Number),
          };
        }
      } else if (elementType === 'VERTEX') {
        for (let i = 0; ; i++) {
          if (urlSearchParams.has(key + `[${i}].type`) && urlSearchParams.has(key + `[${i}].id`)) {
            ret[key] = {
              ...param,
              value: [
                ...param.value,
                {
                  vertexID: urlSearchParams.get(key + `[${i}].id`),
                  vertexType: urlSearchParams.get(key + `[${i}].type`),
                },
              ],
            };
          } else {
            break;
          }
        }
      }
    }
  }

  return ret;
}

export function globalParamsToURLSearchParams(
  globalParams: GlobalParams,
  urlSearchParams: URLSearchParams
): URLSearchParams {
  let newSearchParams = new URLSearchParams(urlSearchParams.toString());
  let paramNames = Object.keys(globalParams);
  for (let paramName of paramNames) {
    const param = globalParams[paramName];
    const type = param.type;
    // only update url when we have key in url
    if (isGlobalParamInSearchParams(newSearchParams, param)) {
      if (type === 'STRING' || type === 'NUMBER' || type === 'DATETIME') {
        newSearchParams.set(paramName, String(param.value));
      } else if (type === 'VERTEX') {
        newSearchParams.set(`${paramName}.type`, param.value.vertexType);
        newSearchParams.set(`${paramName}.id`, param.value.vertexID);
      } else if (type === 'LIST') {
        const elementType = param.elementType;
        if (elementType === 'STRING' || elementType === 'NUMBER' || elementType === 'DATETIME') {
          newSearchParams.delete(paramName);

          for (let subValue of param.value) {
            newSearchParams.append(paramName, String(subValue));
          }
        } else if (elementType === 'VERTEX') {
          const urlSearchParamsKeys = urlSearchParams.keys();
          for (let key of urlSearchParamsKeys) {
            if (isMatchSearchParamsListVertexKey(param.name, key)) {
              newSearchParams.delete(key);
            }
          }

          for (let i = 0; i < param.value.length; i++) {
            const subValue = param.value[i];
            newSearchParams.set(`${paramName}[${i}].type`, subValue.vertexType);
            newSearchParams.set(`${paramName}[${i}].id`, subValue.vertexID);
          }
        }
      }
    }
  }
  return newSearchParams;
}

export function globalParametersMigration(params: Record<string, any>): GlobalParams {
  let ret: GlobalParams = {};

  let keys = Object.keys(params);
  for (let key of keys) {
    let value = params[key];
    // if have type, we assume it's new format
    if (value.type) {
      ret[key] = value;
    } else {
      let param: GlobalParam = {
        name: key,
        // for migration old data, we keep id the same as name,
        id: key,
        type: typeof value === 'number' ? 'NUMBER' : 'STRING',
        value: value,
      };
      ret[key] = param;
    }
  }

  return ret;
}

// todo
// insights's type check have issue (graph studio do support this check)
//  1 both write query/call installed query will not check vertex concrete type (so input Vertex<Tag> will pass to query need Vertex<Person>)
//  2 call installed query will not check list element type
export function isGlobalParamTypeCompatible(
  globalParameterType: string,
  queryParamType: string,
  isInList?: boolean
): boolean {
  switch (queryParamType) {
    case 'STRING':
    case 'BOOL':
    case 'BOOLEAN':
    case 'DATETIME':
    case 'INT':
    case 'UINT':
    case 'FLOAT':
    case 'DOUBLE': {
      if (['STRING', 'NUMBER', 'DATETIME', 'BOOLEAN', 'BOOL'].includes(globalParameterType)) {
        return true;
      }
      return false;
    }
    case 'VERTEX':
      // for list of vertex, we convert list of string|number to list of vertex
      if (isInList) {
        return true;
      }
      if (globalParameterType === 'VERTEX') {
        return true;
      }
      return false;
    default:
      return false;
  }
}

export function isMatchSearchParamsListVertexKey(paramName: string, searchParamKey: string) {
  const reg = new RegExp(`^${paramName}\\[\\d+\\]\\.(type|id)$`);

  if (searchParamKey.startsWith(paramName) && reg.test(searchParamKey)) {
    return true;
  }

  return false;
}

export function isGlobalParamInSearchParams(urlSearchParams: URLSearchParams, globalParam: GlobalParam) {
  const paramName = globalParam.name;
  const type = globalParam.type;

  if (type === 'STRING' || type === 'NUMBER' || type === 'DATETIME') {
    return urlSearchParams.has(paramName);
  } else if (type === 'VERTEX') {
    return urlSearchParams.has(`${paramName}.type`) && urlSearchParams.has(`${paramName}.id`);
  } else if (type === 'LIST') {
    const elementType = globalParam.elementType;

    if (elementType === 'STRING' || elementType === 'NUMBER' || elementType === 'DATETIME') {
      return urlSearchParams.has(paramName);
    } else if (elementType === 'VERTEX') {
      const urlSearchParamsKeys = urlSearchParams.keys();
      for (let key of urlSearchParamsKeys) {
        if (isMatchSearchParamsListVertexKey(paramName, key)) {
          return true;
        };
      }
    }
  }
  return false;
}
