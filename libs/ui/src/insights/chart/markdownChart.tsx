import { ChartProps } from './chart';
import React from 'react';
import { useSandbox } from './sandbox';
import MDEditor, { ICommand, commands } from '@uiw/react-md-editor';
import { StatefulPopover } from '@tigergraph/app-ui-lib/popover';
import { ParametersTable } from '../components/parametersTable';
import { GlobalParams } from './globalParams';
import { PLACEMENT } from 'baseui/popover';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import MarkdownPreview from '@uiw/react-markdown-preview';

type SettingType = {
  markdown: string;
};

function genVariableCommand(globalParameters: GlobalParams, [css, theme]: ReturnType<typeof useStyletron>) {
  const variable: ICommand = {
    name: 'var',
    keyCommand: 'var',
    buttonProps: { 'aria-label': 'variables help' },
    render: (command, disabled, executeCommand) => {
      return (
        <StatefulPopover
          showArrow={false}
          triggerType="hover"
          placement={PLACEMENT.bottom}
          ignoreBoundary={true}
          overrides={{
            Body: {
              style: {
                backgroundColor: '#fff',
                marginTop: '8px',
                marginRight: '16px',
              },
            },
            Inner: {
              style: {
                backgroundColor: '#fff',
                paddingLeft: '8px',
                paddingRight: '8px',
                paddingTop: '8px',
                color: 'rgb(34, 34, 34)',
                fontSize: '14px',
              },
            },
          }}
          content={
            <div>
              {<ParametersTable params={globalParameters} />}{' '}
              <ol
                className={css({
                  fontSize: '14px',
                  color: theme.colors.gray600,
                  padding: 0,
                  margin: '8px 0 0 16px ',
                  display: 'flex',
                  flexDirection: 'column',
                  gap: '8px',
                })}
              >
                <li>
                  Variable name can be used directly in your markdown content.
                  <div>
                    <div
                      className={css({
                        marginTop: '8px',
                      })}
                    >
                      For example:
                      <pre
                        className={css({
                          display: 'inline',
                          paddingLeft: '10px',
                        })}
                      >
                        {'${'}variables.<strong>variableName</strong>
                        {'}'}
                      </pre>
                    </div>
                  </div>
                </li>
                <li>
                  You can create variables by creating <strong>Inputs Widget</strong>.
                </li>
              </ol>
            </div>
          }
        >
          <button
            className={css({
              position: 'relative',
              top: '-2px',
            })}
          >
            var
          </button>
        </StatefulPopover>
      );
    },
  };

  return variable;
}

export default function MarkdownChart({ settings, isEditMode, onSettingUpdate, globalParameters }: ChartProps) {
  const markdown = (settings as SettingType).markdown || '';

  const { errMsg, evaluatedTitle: evaluatedMarkdown } = useSandbox(markdown, globalParameters);

  let finalMarkdown = markdown;
  if (markdown.indexOf('${variables.') >= 0 && evaluatedMarkdown) {
    finalMarkdown = evaluatedMarkdown;

    if (errMsg) {
      finalMarkdown = errMsg;
    }
  }

  const [css, theme] = useStyletron();

  return (
    <div
      style={{
        width: '100%',
        height: '100%',
        display: 'flex',
        position: 'relative',
      }}
    >
      {/* need absolute position to fix the height */}
      <div style={{ position: 'absolute', left: 0, right: 0, top: 0, bottom: 0, display: 'flex' }}>
        <>
          {isEditMode ? (
            <div style={{ height: '100%', width: '100%' }} data-color-mode="light">
              <MDEditor
                textareaProps={{ maxLength: 10000 }}
                commands={[...commands.getCommands(), genVariableCommand(globalParameters, [css, theme])]}
                height="100%"
                value={markdown}
                onChange={(value) => onSettingUpdate('markdown', value)}
                components={{
                  preview: () => {
                    return <MarkdownPreview source={finalMarkdown} />;
                  },
                }}
              />
            </div>
          ) : (
            <div
              style={{
                height: '100%',
                width: '100%',
                padding: 12,
                paddingTop: 0,
              }}
              data-color-mode="light"
            >
              <MDEditor preview="preview" hideToolbar={true} height="100%" value={finalMarkdown} />
            </div>
          )}
        </>
      </div>
    </div>
  );
}
