import { renderHook, act } from '@testing-library/react-hooks';
import EventEmitter from 'eventemitter3';
import { useSandbox } from './sandbox';
import { GlobalParams } from './globalParams';

vi.mock('websandbox', () => ({
  default: {
    create: vi.fn(() => ({
      promise: Promise.resolve(),
      run: vi.fn(),
      destroy: vi.fn(),
    })),
  },
}));

vi.mock('short-uuid', () => ({
  __esModule: true,
  default: {
    uuid: vi.fn(() => 'mock-uuid'),
  },
}));

vi.mock('eventemitter3', () => ({
  __esModule: true,
  default: class EventEmitter2 {
    static eventNames = {
      'mock-uuid': () => {},
    };
    on(event, listener) {
      EventEmitter2.eventNames[event] = listener;
    }
    emit(event, ...args) {
      if (EventEmitter2.eventNames[event]) {
        EventEmitter2.eventNames[event](...args);
      }
    }
    off(event) {
      EventEmitter2.eventNames[event] = undefined;
    }
  },
}));

describe('useSandbox', () => {
  const mockGlobalParams: GlobalParams = {
    variable1: { type: 'STRING', name: 'v1', id: '1', value: 'test1' },
    variable2: { type: 'STRING', name: 'v2', id: '2', value: 'test2' },
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should initialize with empty variables', () => {
    const { result } = renderHook(() => useSandbox('Hello World', mockGlobalParams));

    expect(result.current.errMsg).toBeNull();
    expect(result.current.evaluatedTitle).toBe('');
  });

  it('should initialize with null error and empty evaluatedTitle', () => {
    const { result } = renderHook(() => useSandbox('Hello ${variables.variable1}', mockGlobalParams));

    expect(result.current.errMsg).toBeNull();
    expect(result.current.evaluatedTitle).toBe('');
  });

  it('should evaluate title with sandbox and update evaluatedTitle on success', async () => {
    const { result } = renderHook(() => useSandbox('Hello ${variables.variable1}', mockGlobalParams));

    act(() => {
      EventEmitter.prototype.emit('mock-uuid', null, 'Hello test1');
    });

    expect(result.current.errMsg).toBeNull();
    expect(result.current.evaluatedTitle).toBe('Hello test1');
  });

  it('should handle sandbox errors and update errMsg', async () => {
    const { result } = renderHook(() => useSandbox('Hello ${variables.variable1}', mockGlobalParams));

    act(() => {
      EventEmitter.prototype.emit('mock-uuid', 'Syntax error in code', null);
    });

    expect(result.current.errMsg).toBe('Syntax error in code');
    expect(result.current.evaluatedTitle).toBe('');
  });

  it('should cleanup sandbox and event listeners on unmount', () => {
    const { unmount } = renderHook(() => useSandbox('Hello ${variables.variable1}', mockGlobalParams));

    unmount();
    expect(EventEmitter.prototype.eventNames?.['mock-uuid']).toBeUndefined();
  });
});
