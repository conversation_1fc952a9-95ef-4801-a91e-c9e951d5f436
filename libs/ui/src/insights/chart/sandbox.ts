import { useState, useEffect } from 'react';
import short from 'short-uuid';
import Sandbox, { PluginInstance } from 'websandbox';
import EventEmitter from 'eventemitter3';

import { GlobalParams } from './globalParams';

const eventemitter = new EventEmitter();

const localApi = {
  onResult: function (errMsg: string | null, result: string | null, messageID: string) {
    eventemitter.emit(messageID, errMsg, result);
  },
};

function initSandbox(): PluginInstance {
  // @ts-ignore
  return Sandbox.create(localApi, { frameContainer: '.iframe__container', frameClassName: 'hidden_frame' });
}

function evaluateInSandbox(sandbox: PluginInstance, code: string) {
  sandbox.promise.then(() => {
    sandbox.run(code);
  });
}

export function useSandbox(
  title: string,
  globalParams: GlobalParams
): {
  errMsg: string | null;
  evaluatedTitle: string | null;
} {
  const [errMsg, setErrMsg] = useState<string | null>(null);
  const [evaluatedTitle, setEvaluatedTitle] = useState<string>('');

  useEffect(() => {
    // reset err message
    setErrMsg(null);

    if (title.indexOf('${variables.') < 0) {
      return;
    }

    const keyValues: Record<string, any> = {};
    for (let key of Object.keys(globalParams)) {
      keyValues[key] = globalParams[key].value;
    }

    let paramsStr = JSON.stringify(keyValues);
    const messageID = short.uuid();

    let sandbox = initSandbox();

    const onEvaluationResult = (errMsg: string, result: string) => {
      sandbox.destroy();
      sandbox = null;

      if (errMsg) {
        setErrMsg(errMsg);
      } else {
        setEvaluatedTitle(result);
      }
    };

    eventemitter.on(messageID, onEvaluationResult);

    // need setup error listener first
    evaluateInSandbox(
      sandbox,
      `
      const messageID = \`${messageID}\`;

      window.addEventListener('error', (e) => {
        Websandbox.connection.remote.onResult(e.message,null, messageID)
      })
    `
    );

    // for handle \n in paramsStr, we use `String.raw`
    // refer https://stackoverflow.com/a/66531090
    evaluateInSandbox(
      sandbox,
      `
      (function() {
        const messageID = '${messageID}';        
        const paramsStr = String.raw\`${paramsStr}\`;

        try {
          const variables = JSON.parse(paramsStr);
  
          const title = \`${title}\`;
          Websandbox.connection.remote.onResult(null,title, messageID);
        } catch(e) {
          Websandbox.connection.remote.onResult(e.message,null, messageID);
        }
      })()
    `
    );

    return () => {
      eventemitter.off(messageID, onEvaluationResult);
      if (sandbox) {
        sandbox.destroy();
      }
    };
  }, [title, globalParams]);

  return {
    errMsg,
    evaluatedTitle,
  };
}
