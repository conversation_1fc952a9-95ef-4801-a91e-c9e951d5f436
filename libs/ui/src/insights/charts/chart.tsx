import React, { useCallback, useMemo, useState, useEffect, Suspense } from 'react';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { ErrorBoundary } from 'react-error-boundary';

import { isShowSchema } from './chartSearch';
import { ChartSetting } from './chartTools/chartSetting/chartSetting';

import { ChartAction, ChartState, SearchItem } from './chartSlice';

import { useGSQLQuery } from './query/useGSQLQuery';

import { ChartProps, ChartSettingProps, GlobalParam, GlobalParams } from '../chart';
import { chartForType } from '../chart/chartRegister';

import { ChartSchema } from '../chart/schema';
import { QueryMeta } from '@tigergraph/tools-models/query';
import { ChartStatus } from '../pages/chart/type';
import WidgetAction from './widgetAction';
import ChartInput from './query/chartInput';
import { ErrorDisplay } from '../components/error';
import SkeletonLoading from '../components/skeletonLoading/skeletonLoading';
import { showToast, StyledToast } from '../components/styledToasterContainer';
import { KIND } from 'baseui/toast';
import { convertSchemaToGraph } from '../../graph/data';
import { Button } from '@tigergraph/app-ui-lib/button';
import { MultiStepTourProvider } from '../components/onboard/TourProvider';
import { lazyWithPreload } from '../utils';
import { AxiosError } from 'axios';

import './global.css';

const Graph = lazyWithPreload(() => import('../charts/graphNew/index2'));

type Props = {
  baseURL?: string;
  chart: ChartState;
  force?: boolean;
  isCloud?: boolean;
  isClusterMode?: boolean;
  themeType?: 'light' | 'dark';
  dispatch: React.Dispatch<ChartAction>;
  schema?: ChartSchema;
  graphs: string[];
  isLoading: boolean;
  isError: boolean;
  schemaError?: AxiosError;
  queriesError?: AxiosError;
  onGraphChanged: (graph: string) => void;
  queries: { [key: string]: QueryMeta };
  chartStatus: ChartStatus;
  globalParameters: GlobalParams;
  setGlobalParameter: (name: string, value: GlobalParam, prevName?: string) => void;
  deleteGlobalParameter: (name: string) => void;
  links: { pageName: string; pageID: string; params: GlobalParams }[];
  getLink: (pageID: string, params: Record<string, string>) => string;
  onChartSave: () => void;
  onChartDiscard: () => void;

  hideTour?: boolean;
  onCopilot?: (prompt: string) => void;
  placeHolder?: string;
  emptyView?: React.ReactNode;
  emptyNodesLinksView?: () => React.ReactElement;
  sendIcon?: React.ReactNode;
  onChartDataUpdate?: (chartData: Record<string, any>) => void;
};

const settingsWidth = 320;

export default function Chart({
  baseURL = '',
  chart,
  force = false,
  isCloud = false,
  isClusterMode = false,
  themeType,
  dispatch,
  schema,
  graphs,
  queries,
  isLoading: isLoadingSchemaOrQueries,
  isError: isSchemaOrQueriesError,
  schemaError,
  queriesError,
  onGraphChanged,
  chartStatus,
  globalParameters,
  setGlobalParameter,
  deleteGlobalParameter,
  links,
  getLink,
  onChartSave,
  onChartDiscard,
  hideTour,
  onCopilot,
  placeHolder,
  emptyView,
  sendIcon,
  emptyNodesLinksView,
  onChartDataUpdate,
}: Props) {
  const {
    searchPattern,
    queryType,
    query,
    patternLimit,
    timeLimit,
    memoryLimit,
    hopLimit = 5,
    graphName,
    chartSettings,
    cache = false,
  } = chart;

  let staticData = chart.staticData;

  // migration for static data, we may have '{}' in the past
  if (staticData === '{}') {
    staticData = JSON.stringify([{ key: 'value' }], null, 4);
  }

  let { type } = chart;

  const isEditMode = chartStatus === 'edit';

  // alias for new Graph
  if (type === 'new Graph') {
    type = 'internal-graph';
  }
  const chartType = chartForType(type);

  const [css, theme] = useStyletron();

  // cachedQuery vs query
  //    cachedQuery: state that take effect when running
  //    query: query text save to configuration
  const [cachedChartSettings, setCachedChartSettings] = useState(chartSettings);
  const [cachedQuery, setCachedQuery] = useState(query);
  const [cachedSearchPattern, setCachedSearchPattern] = useState(searchPattern);
  const [cachedPatternLimit, setCachedPatternLimit] = useState(patternLimit);
  const [confirmProceed, setConfirmProceed] = useState(false);
  const showSchema = isShowSchema(searchPattern?.[0]);
  const component: any = showSchema ? Graph : chartType.component;
  const [componentLoaded, setComponentLoaded] = useState(!component.preload);

  useEffect(() => {
    if (component && component.preload) {
      setComponentLoaded(false);
      component.preload().then(() => {
        setComponentLoaded(true);
      });
    }
  }, [component]);

  const { isError, error, chartData, refetch, isFetching } = useGSQLQuery({
    baseURL,
    cache,
    force,
    queryType,
    searchPattern: cachedSearchPattern,
    query: cachedQuery,
    staticData: staticData,
    patternLimit: cachedPatternLimit,
    globalParameters,
    queries,
    queriesError,
    schema,
    schemaError,
    graphName,
    refreshRate: chart.refreshRate,
    isClusterMode,
    memoryLimit,
    timeLimit,
    onSuccess: () => {
      if (isEditMode && queryType !== 'static_data') {
        showToast({
          kind: KIND.positive,
          message: isShowSchema(cachedSearchPattern[0]) ? 'Show schema successfully.' : 'Query executed successfully.',
        });
      }
    },
  });

  const isFreeExplore = type === 'internal-graph' && chartSettings['freeExplore'] && !isEditMode;
  // show spinner
  // 1 edit mod and is fetching data
  // 2 isFreeExplore and is fetching data
  const showLoading = (isFetching || !componentLoaded || ((isEditMode || isFreeExplore) && isFetching)) && !isError;
  const showChartInput = isEditMode && !chartType.noQuery;

  const onSettingUpdate = useCallback(
    (field: string, value: any) => {
      dispatch({
        actionType: 'updateChartSetting',
        field,
        value,
      });
    },
    [dispatch]
  );

  const onCachedSettingUpdate = useCallback((field: string, value: any) => {
    setCachedChartSettings((cachedChartSettings) => ({
      ...cachedChartSettings,
      [field]: value,
    }));
  }, []);

  const [chartStates, setChartStates] = useState<Record<string, any>>({});
  const handleChartStateUpdate = useCallback((field: string, value: any) => {
    setChartStates((chartStates) => {
      if (chartStates[field] === value) {
        return chartStates;
      }
      return {
        ...chartStates,
        [field]: value,
      };
    });
  }, []);

  const getGlobalParameter = useCallback(
    (name: string) => {
      return globalParameters[name];
    },
    [globalParameters]
  );

  const settingDefaultValues: Record<string, any> = useMemo(
    () => chartType?.settingsInfo?.defaultSettings() ?? {},
    [chartType?.settingsInfo]
  );
  const settings = useMemo(
    () => ({
      ...settingDefaultValues,
      ...(isEditMode ? chartSettings : cachedChartSettings),
    }),
    [cachedChartSettings, chartSettings, isEditMode, settingDefaultValues]
  );

  const unifiedChartData = useMemo(() => {
    if (!showSchema) {
      return chartData;
    }
    const graph = convertSchemaToGraph(schema.results);

    return {
      graph: graph,
      results: {},
      tables: [],
    };
  }, [schema, chartData, showSchema]);

  useEffect(() => {
    onChartDataUpdate?.({
      chartData: unifiedChartData,
      chartStates,
    });
  }, [chartStates, unifiedChartData, onChartDataUpdate]);

  const chartProps: ChartProps = {
    id: chart.id,
    graphName: chart.graphName,
    schema: schema,
    chartData: unifiedChartData,
    isEditMode,
    settings,
    onSettingUpdate: isEditMode ? onSettingUpdate : onCachedSettingUpdate,
    chartStates: chartStates,
    onChartStateUpdate: handleChartStateUpdate,
    setGlobalParameter,
    getGlobalParameter,
    emptyNodesLinksView,
    globalParameters,
    links,
    getLink,
    isCloud,
    isClusterMode,
    themeType,
    baseURL,
  };

  const chartSettingProps: ChartSettingProps = {
    graphName: chart.graphName,
    schema: schema,
    chartData: unifiedChartData,
    isEditMode,
    settings,
    onSettingUpdate: isEditMode ? onSettingUpdate : onCachedSettingUpdate,
    chartStates: chartStates,
    onChartStateUpdate: handleChartStateUpdate,
    setGlobalParameter,
    deleteGlobalParameter,
    getGlobalParameter,
    globalParameters,
    links,
    getLink,
    isCloud,
    isClusterMode,
    themeType,
    baseURL,
  };

  // single value and inputs widgets don't have threshold
  const getGraphThreshold = () => {
    return chartType?.threshold ? chartType.threshold : Number.MAX_SAFE_INTEGER;
  };

  const overLimit = (): boolean => {
    if (!unifiedChartData) {
      return false;
    }
    const {
      graph: { nodes, links },
    } = unifiedChartData;
    return nodes?.length + links?.length > getGraphThreshold();
  };

  // we rely two things to reset internal widget status
  // 1. key
  // 2. run query will switch between loading and reset status
  const getSpecificWidget = () => {
    if (showSchema) {
      return <Graph {...chartProps} isSchemaGraph={true} key="schema" />;
    }
    if (overLimit() && !confirmProceed) {
      return (
        <StyledToast
          message={`This widget may take longer to load due to a volume of vertices and edges exceeding ${getGraphThreshold()}.`}
          kind={KIND.warning}
          closeable={false}
        >
          <div
            className={css({
              display: 'flex',
              justifyContent: 'flex-end',
            })}
          >
            <Button
              onClick={() => {
                setConfirmProceed(true);
              }}
            >
              Continue
            </Button>
          </div>
        </StyledToast>
      );
    }
    if (chart) {
      const Component = chartType.component;
      return <Component {...chartProps} key={chart.id} />;
    }

    return <div>Not found</div>;
  };

  const steps = [
    {
      selector: '[data-tour="step-1"]',
      content: <div>Select a graph to read from.</div>,
    },
    {
      selector: '[data-tour="step-2"]',
      content: <div>Search graph pattern or an existing query.</div>,
    },
    {
      selector: '[data-tour="step-3"]',
      content: <div>Click to run query.</div>,
    },
    {
      selector: '[data-tour="step-4"]',
      content: (
        <div>
          You can also switch to <strong>write query</strong> from here to write your own query.
        </div>
      ),
    },
  ];

  const chartInput = (
    <MultiStepTourProvider steps={!hideTour ? steps : []} disableInteraction={true}>
      <ChartInput
        isFreeExplore={isFreeExplore}
        isClusterMode={isClusterMode}
        onCopilot={onCopilot}
        placeHolder={placeHolder}
        sendIcon={sendIcon}
        queryType={chart.queryType}
        searchPattern={chart.searchPattern}
        patternLimit={patternLimit}
        hopLimit={hopLimit}
        timeLimit={timeLimit}
        memoryLimit={memoryLimit}
        interpretQuery={chart.query}
        staticData={staticData}
        graphName={chart.graphName}
        graphs={graphs}
        onGraphChanged={(graph) => {
          setCachedSearchPattern([]);
          dispatch({
            searchPattern: [],
          });
          onGraphChanged(graph);
        }}
        chartProps={chartProps}
        globalParameters={globalParameters}
        queries={queries}
        isLoading={isLoadingSchemaOrQueries}
        isError={isSchemaOrQueriesError}
        onQueryTypeChange={(queryType) =>
          dispatch({
            queryType,
          })
        }
        onSearchPatternChanged={(searchPattern) =>
          dispatch({
            searchPattern,
          })
        }
        onPatternLimitChanged={(limit) =>
          dispatch({
            patternLimit: limit,
          })
        }
        onHopLimitChanged={(limit) =>
          dispatch({
            hopLimit: limit,
          })
        }
        onInterpretQueryChanged={(interpretQuery) =>
          dispatch({
            query: interpretQuery,
          })
        }
        onMemoryLimitChanged={(limit) =>
          dispatch({
            memoryLimit: limit as number,
          })
        }
        onTimeLimitChanged={(limit) =>
          dispatch({
            timeLimit: limit as number,
          })
        }
        onStaticDataChanged={(staticData) =>
          dispatch({
            staticData,
          })
        }
        onRunQuery={(value, limit?: number) => {
          if (queryType === 'pattern') {
            if (value === cachedSearchPattern && limit === cachedPatternLimit) {
              // if the state is not changed, just run refetch
              refetch();
            } else {
              // else update react-query input
              setCachedSearchPattern(value as Array<SearchItem>);
              setCachedPatternLimit(limit as number);
            }
          } else if (queryType === 'interactive') {
            if (value === cachedQuery) {
              // if the state is not changed, just run refetch
              refetch();
            } else {
              // else update react-query input
              setCachedQuery(value as string);
            }
          } else if (queryType === 'static_data') {
            // there is no need to run query for static data
          }
        }}
      />
    </MultiStepTourProvider>
  );

  const widgetActions = settings['widgetActions'] ?? [];

  return (
    <div
      className={css({
        position: 'relative',
        display: 'flex',
        height: '100%',
        padding: '8px',
        borderRadius: '5px',
        boxSizing: 'border-box',
      })}
    >
      <div
        className={css({
          // 1px for border
          width: !isEditMode ? '100%' : `calc(100% - ${settingsWidth}px - 1px)`,
          marginRight: isEditMode ? '8px' : 0,
          display: 'flex',
          flexDirection: 'column',
          height: '100%',
          minHeight: 0,
        })}
      >
        {showChartInput || isFreeExplore ? chartInput : null}
        {/* error from query hook  */}
        {isError ? <ErrorDisplay error={error} label="GSQL Error:" isGSQLError={true} /> : null}
        <ErrorBoundary
          FallbackComponent={({ error }) => {
            // Echarts provider no way to overwrite this error message.
            if (error.message === 'Sankey is a DAG, the original data has cycle!') {
              error.message = 'Sankey charts are not supported for graphs with cycles.';
            }
            return <ErrorDisplay error={error} label={`Rendering Error for ${chartType.label} Widget:`} />;
          }}
          // isLoading indicate when rerun the query
          resetKeys={[type, isFetching]}
        >
          <div
            className={css({
              flexGrow: 1,
              flexBasis: 0,
              minHeight: 0,
              boxSizing: 'border-box',
              position: 'relative',
              overflow: 'hidden',
            })}
          >
            {showLoading && (
              <div
                data-testid={chart.id}
                data-cy="loading"
                className={css({
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  position: 'absolute',
                  left: 0,
                  right: 0,
                  top: 0,
                  bottom: 0,
                })}
              >
                <SkeletonLoading type={chartType?.type} />
              </div>
            )}
            <WidgetAction {...chartProps} />
            {/* if run different query, will reset widget internal state by return null in between  */}
            <Suspense fallback={null}>
              {(unifiedChartData || chartType?.noQuery) && !showLoading ? (
                <div
                  className={css({
                    width: '100%',
                    height: widgetActions.length > 0 ? 'calc(100% - 30px)' : '100%',
                  })}
                >
                  {getSpecificWidget()}
                </div>
              ) : null}
              {!showLoading && !unifiedChartData && searchPattern.length === 0 && emptyView ? (
                <div
                  className={css({
                    width: '100%',
                    height: widgetActions.length > 0 ? 'calc(100% - 30px)' : '100%',
                  })}
                >
                  {emptyView}
                </div>
              ) : null}
            </Suspense>
          </div>
        </ErrorBoundary>
      </div>
      {isEditMode ? (
        <div
          className={css({
            width: `${settingsWidth}px`,
            borderLeft: `1px solid ${theme.colors.gray200}`,
            height: '100%',
            position: 'relative',
          })}
        >
          <div
            className={css({
              position: 'absolute',
              left: 0,
              top: 0,
              right: 0,
              bottom: 0,
              overflow: 'hidden',
            })}
          >
            <ChartSetting
              chartSettingProps={chartSettingProps}
              chart={chartType}
              chartTitle={chart.title}
              hideWidgetName={chart.hideWidgetName}
              chartRefreshRate={chart.refreshRate}
              onChartTitleChange={(title) => {
                dispatch({
                  title,
                });
              }}
              onChartTypeChange={(type) => {
                dispatch({
                  type,
                });
              }}
              onChartRefreshRateChange={(refreshRate) => {
                dispatch({
                  refreshRate,
                });
              }}
              onChartSave={onChartSave}
              onChartDiscard={onChartDiscard}
              onHideWidgetNameChange={() => {
                dispatch({
                  hideWidgetName: !chart.hideWidgetName,
                });
              }}
            />
          </div>
        </div>
      ) : null}
    </div>
  );
}
