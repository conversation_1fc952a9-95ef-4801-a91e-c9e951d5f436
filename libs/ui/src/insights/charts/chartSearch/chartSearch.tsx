import React from 'react';
import { useStyletron } from 'baseui';

import { SearchProps } from './type';
import ChartSearchBox from './chartSearchBox';

export function ChartSearch(
  props: SearchProps & {
    isFreeExplore?: boolean;
    onCopilot?: (prompt: string) => void;
    placeHolder?: string;
    sendIcon?: React.ReactNode;
  }
) {
  const [css] = useStyletron();

  const margin = 6;

  return (
    <div
      className={css({
        position: 'relative',
        display: 'inline-flex',
        width: `calc(100% - ${margin * 2}px)`,
        margin: `${margin}px`,
        cursor: 'text',
      })}
    >
      <ChartSearchBox {...props} />
    </div>
  );
}
