import React, { forwardRef, Ref, useEffect, useMemo, useState } from 'react';
import { useStyletron, styled } from '@tigergraph/app-ui-lib/Theme';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { IconProp } from '@fortawesome/fontawesome-svg-core';
import { faPlay } from '@fortawesome/free-solid-svg-icons/faPlay';
import { faGear } from '@fortawesome/free-solid-svg-icons/faGear';
import { PLACEMENT, StatefulPopover } from 'baseui/popover';
import { Input } from '@tigergraph/app-ui-lib/input';
import { Button, ButtonProps } from '@tigergraph/app-ui-lib/button';
import { OPERANDS_TYPES, OrderBy, SEARCH_KEYWORDS } from './type';
import useClipboard from 'react-use-clipboard';
import { KIND as TOAST_KIND } from 'baseui/toast';
import { showToast } from '../../components/styledToasterContainer';
import { StyledSelect } from '../../components';
import { ChartSchema } from '../../chart/schema';
import { SearchItem } from '../chartSlice';
import { GSQLAttributeJson } from '@tigergraph/tools-models/topology';
import { isShowSchema } from './util';
import { GlobalParams } from '../../chart/globalParams';
import { buildPatternQuery } from '../data/pattern';
import { ReactComponent as AsceIcon } from '../../assets/images/ascending.svg';
import { ReactComponent as DescIcon } from '../../assets/images/descending.svg';
import { useTimeMemoryDefaultLimit } from '../../pages/chart/useMetaHook';
import IconButton from '../../components/iconButton';

const StyledButton = forwardRef((props: ButtonProps, ref: Ref<HTMLButtonElement>) => (
  <Button ref={ref} kind="secondary" {...props} />
));

const StyledLabel = styled('span', () => {
  return {
    display: 'block',
    marginBottom: '4px',
    fontWeight: 500,
  };
});

type Props = {
  searchItems: SearchItem[];
  onChangeSearchItems: (searchItems: SearchItem[]) => void;
  patternLimit: number;
  onPatternLimitChanged: (limit: number) => void;
  hopLimit: number;
  timeLimit: number;
  memoryLimit: number;
  onHopLimitChanged: (limit: number) => void;
  onTimeLimitChanged?: (limit: number | string) => void;
  onMemoryLimitChanged?: (limit: number | string) => void;
  globalParameters: GlobalParams;
  handleSearch: () => void;
  onControlRefStateChange: () => void;
  schema: ChartSchema;
  isError: boolean;
  sendIcon?: React.ReactNode;
  isClusterMode?: boolean;
};

export default function ChartSearchActions(props: Props) {
  const [css, theme] = useStyletron();

  const {
    searchItems,
    onChangeSearchItems,
    patternLimit,
    onPatternLimitChanged,
    onMemoryLimitChanged,
    onTimeLimitChanged,
    hopLimit,
    timeLimit,
    memoryLimit,
    onHopLimitChanged,
    globalParameters,
    handleSearch,
    onControlRefStateChange,
    schema,
    isError,
    sendIcon,
    isClusterMode,
  } = props;

  const { graph } = schema;
  const seenTypes = new Map();
  const orderByOptions = [] as OrderBy[];
  const { data: timeMemoryLimit } = useTimeMemoryDefaultLimit(true);

  searchItems?.forEach((item) => {
    const { type, data } = item;
    if (type === SEARCH_KEYWORDS.vertex || type === SEARCH_KEYWORDS.edge) {
      const schemaType = type === SEARCH_KEYWORDS.vertex ? 'vertices' : 'edges';
      const vertexOrEdge = graph[schemaType][data];
      if (!vertexOrEdge) {
        return;
      }
      // get the id of vertex
      if (
        vertexOrEdge?.['PrimaryId'] &&
        vertexOrEdge['PrimaryId']['PrimaryIdAsAttribute'] &&
        OPERANDS_TYPES[vertexOrEdge['PrimaryId']['AttributeType']['Name'].toLowerCase()]
      ) {
        let alias = data;
        if (seenTypes.has(data)) {
          alias += `_${seenTypes.get(data) + 1}`;
        }
        orderByOptions.push({
          id: `${alias}.${vertexOrEdge['PrimaryId']['AttributeName']}`,
          valueType: vertexOrEdge['PrimaryId']['AttributeType']['Name'],
        });
      }
      // get attributes of the vertex and edge
      vertexOrEdge?.Attributes?.forEach((attr: GSQLAttributeJson) => {
        // order by only support numeric and string, datetime
        if (
          !['INT', 'UINT', 'FLOAT', 'DOUBLE', 'STRING', 'STRING COMPRESS', 'DATETIME'].includes(
            attr['AttributeType'].Name
          )
        ) {
          return;
        }
        let alias = data;
        if (seenTypes.has(data)) {
          alias += `_${seenTypes.get(data) + 1}`;
        }
        if (OPERANDS_TYPES[attr['AttributeType']['Name'].toLowerCase()]) {
          orderByOptions.push({ id: `${alias}.${attr['AttributeName']}`, valueType: attr['AttributeType']['Name'] });
        }
      });
      seenTypes.set(data, (seenTypes.get(data) ?? 0) + 1);
    }
  });

  // we want to generate query based on latest search pattern
  // the query return from useGSQLQuery is not latest(as it use cached search pattern)
  const constructedQuery = useMemo(() => {
    // the following logic is just copy from useGSQLQuery
    if (searchItems.length === 0 || isShowSchema(searchItems[0])) {
      return '';
    }
    if (searchItems[0].type === SEARCH_KEYWORDS.query) {
      return '';
    }
    if (Object.keys(schema.results).length === 0) {
      return '';
    }
    try {
      return buildPatternQuery(searchItems, schema.results, globalParameters, patternLimit);
    } catch (error) {
      // ignore error
      return '';
    }
  }, [searchItems, schema.results, globalParameters, patternLimit]);

  const [queryCopied, setQueryCopied] = useClipboard(constructedQuery, {
    successDuration: 1000,
  });

  useEffect(() => {
    if (queryCopied) {
      showToast({ kind: TOAST_KIND.positive, message: 'Copied to clipboard.' });
    }
  }, [queryCopied]);

  useEffect(() => {
    if (searchItems.length === 0) {
      return;
    }
    const itemWithFilters = searchItems.find((item) => item.orderBy?.length > 0);
    if (!itemWithFilters && existValidItemForOrdering(searchItems, orderByOptions)) {
      buildOrderByAttribute(orderByOptions[0].id);
    }
  });

  const existValidItemForOrdering = (searchItems: SearchItem[], orderByOptions: OrderBy[]) => {
    return (
      orderByOptions.length > 0 &&
      // compatible with the previous version without aliases
      searchItems.find((item) => item.alias?.indexOf(orderByOptions[0].id.split('.')[0]) > -1)
    );
  };

  const buildOrderByAttribute = (attribute: string) => {
    onChangeSearchItems(
      searchItems.map((item, index) => {
        const newItem = { ...item };
        if (newItem.alias === attribute.split('.')[0]) {
          newItem['orderBy'] = [
            {
              asc: true,
              expression: {
                type: 'AttrVariable',
                value: `alias_schema_${item.data}_${index}.${attribute.split('.')[1]}`,
              },
              label: attribute,
            },
          ];
        } else {
          newItem['orderBy'] = [];
        }
        return newItem;
      })
    );
  };
  const formItemClassName = css({ marginBottom: '8px' });
  const orderByAsc = searchItems.find((item) => item.orderBy?.length > 0)?.orderBy.find((attr) => attr.asc);

  let hideSettings = false;
  // in cluster mode we hide settings if search item is not query
  if (isClusterMode) {
    hideSettings = searchItems.length > 0 && searchItems[0].type !== SEARCH_KEYWORDS.query;
  }

  return (
    <div
      className={css({ display: 'flex', alignItems: 'center', height: '100%' })}
      onClick={onControlRefStateChange}
      data-tour="step-3"
    >
      {!hideSettings && (
        <StatefulPopover
          content={({ close }) => (
            <div
              className={css({
                padding: '10px',
                rowGap: '6px',
                minWidth: '340px',
              })}
            >
              {searchItems?.[0]?.type !== SEARCH_KEYWORDS.query && (
                <>
                  <div className={formItemClassName}>
                    <StyledLabel>Order by</StyledLabel>
                    <div className={css({ display: 'flex', alignItems: 'center' })}>
                      <div className={css({ flex: '1 1 auto', width: 0 })}>
                        <StyledSelect
                          clearable={false}
                          options={orderByOptions}
                          size="default"
                          value={
                            searchItems
                              .find((item) => item.orderBy?.length > 0)
                              ?.orderBy.map((attr) => ({ id: attr.label })) ?? []
                          }
                          onChange={(params) => {
                            if (params.option) {
                              buildOrderByAttribute(String(params.option.id));
                            }
                          }}
                          placeholder="Select an attribute"
                          labelKey="id"
                        />
                      </div>
                      <IconButton
                        className={css({
                          marginLeft: '8px',
                          width: '32px',
                          height: '32px',
                          border: `1px solid ${theme.colors.gray400} !important`,
                        })}
                        onClick={() => {
                          const newSearchItems = searchItems.map((item) => {
                            const newItem = { ...item };
                            if (newItem.orderBy?.length) {
                              newItem.orderBy = [
                                {
                                  ...newItem.orderBy[0],
                                  asc: !Boolean(orderByAsc),
                                },
                              ];
                            }
                            return newItem;
                          });
                          onChangeSearchItems(newSearchItems);
                        }}
                      >
                        {orderByAsc ? <AsceIcon /> : <DescIcon />}
                      </IconButton>
                    </div>
                  </div>
                  <div className={formItemClassName}>
                    <StyledLabel>Limit</StyledLabel>
                    <Input
                      value={patternLimit ?? 500}
                      onChange={(e) => {
                        const re = /^[0-9\b]+$/;
                        if (e.target['value'] === '' || re.test(e.target['value'])) {
                          onPatternLimitChanged(e.target['value']);
                        }
                      }}
                      type={'number'}
                      placeholder="Pattern limit"
                    />
                  </div>
                  <div className={formItemClassName}>
                    <StyledLabel>Path hops limit</StyledLabel>
                    <Input
                      value={hopLimit ?? 5}
                      onChange={(e) => {
                        const re = /^[0-9\b]+$/;
                        if (e.target['value'] === '' || re.test(e.target['value'])) {
                          onHopLimitChanged(e.target['value']);
                        }
                      }}
                      type={'number'}
                      placeholder="Hop limit"
                    />
                  </div>
                </>
              )}
              <div className={formItemClassName}>
                <StyledLabel>Timeout (seconds)</StyledLabel>
                <Input
                  value={timeLimit === 0 ? '' : timeLimit}
                  onChange={(e) => onTimeLimitChanged?.(e.target['value'])}
                  onBlur={(e) => {
                    const value = Number(e.target.value);
                    if (value >= timeMemoryLimit.timeLimit) {
                      onTimeLimitChanged?.(value);
                    } else if (value > 0) {
                      onTimeLimitChanged?.(timeMemoryLimit.timeLimit);
                    }
                  }}
                  type={'number'}
                  placeholder="default 0"
                />
                <div></div>
              </div>
              <div className={formItemClassName}>
                <StyledLabel>Memory Limit (MBs)</StyledLabel>
                <Input
                  value={memoryLimit === 0 ? '' : memoryLimit}
                  onChange={(e) => onMemoryLimitChanged?.(e.target['value'])}
                  onBlur={(e) => {
                    const value = Number(e.target.value);
                    if (value >= (timeMemoryLimit.memoryLimit || 0)) {
                      onMemoryLimitChanged?.(value);
                    } else if (value > 0) {
                      onMemoryLimitChanged?.(timeMemoryLimit.memoryLimit || '');
                    }
                  }}
                  type={'number'}
                  placeholder="default 0"
                />
              </div>
              <div
                className={css({
                  display: 'flex',
                  justifyContent: 'center',
                  borderTop: `2px solid ${theme.colors.divider}`,
                  paddingTop: '8px',
                })}
              >
                <StyledButton
                  kind="text"
                  onClick={() => {
                    setQueryCopied();
                    close();
                  }}
                >
                  <span className={css({ color: theme.colors.secondary800 })}>Copy generated query to clipboard</span>
                </StyledButton>
              </div>
            </div>
          )}
          placement={PLACEMENT.bottomLeft}
          returnFocus
          autoFocus
          overrides={{
            Body: {
              style: {
                marginTop: '8px',
                boxShadow: theme.colors['shadow.popup'],
                border: `1px solid ${theme.colors.divider}`,
              },
            },
            Inner: {
              style: {
                backgroundColor: theme.colors['background.primary'],
                paddingLeft: 0,
                paddingRight: 0,
                fontSize: '14px',
              },
            },
          }}
        >
          <StyledButton
            colors={{ color: theme.colors.gray300, backgroundColor: '' }}
            kind="text"
            shape="square"
            size="large"
          >
            <FontAwesomeIcon icon={faGear as IconProp} width={16} height={16} />
          </StyledButton>
        </StatefulPopover>
      )}
      <StyledButton
        kind="text"
        shape="square"
        size="large"
        onClick={handleSearch}
        disabled={isError || isShowSchema(searchItems[0]) || searchItems.length === 0}
      >
        {sendIcon ? sendIcon : <FontAwesomeIcon icon={faPlay as IconProp} width={16} height={16} />}
      </StyledButton>
    </div>
  );
}
