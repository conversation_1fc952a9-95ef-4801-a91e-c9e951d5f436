import React, { useState, useRef } from 'react';
import { useStyletron } from 'baseui';
import { ImperativeMethods } from 'baseui/select';
import { SEARCH_KEYWORDS } from '../type';
import ChartSearchActions from '../chartSearchActions';
import ChartSearchSelect from './chartSearchSelect';
import ChartSearchItems from './chartSearchItems';
import ChartSearchGraphs from './chartSearchGraphs';
import { SearchProps } from '../type';

interface Props extends SearchProps {
  isFreeExplore?: boolean;
  onCopilot?: (prompt: string) => void;
  placeHolder?: string;
  sendIcon?: React.ReactNode;
}

export default function ChartSearchBox(props: Props) {
  const [css] = useStyletron();
  const {
    isFreeExplore,
    sendIcon,
    schema,
    globalParameters,
    isLoading: isLoadingSchemaOrQueries,
    isError: isSchemaOrQueriesError,
    searchPattern: searchItems,
    queryTypeSelect,
    patternLimit,
    hopLimit,
    timeLimit,
    memoryLimit,
    onPatternLimitChanged,
    onHopLimitChanged,
    onSearchPatternChanged,
    onRunQuery,
    onTimeLimitChanged,
    onMemoryLimitChanged,
    isClusterMode,
  } = props;

  const [searchInput, setSearchInput] = useState('');
  const [hoverItemIndex, setHoverItemIndex] = useState(-1);
  const [isLoadingParameters, setIsLoadingParameters] = useState(false);
  const controlRef = useRef<ImperativeMethods>(null);
  const popoverControlRef = useRef(false);
  const inputControlRef = useRef(false);
  const newFilterRef = useRef(-1);

  const hoverOffset = 16;

  const onChangeGlobalPopoverState = (state: boolean) => {
    setTimeout(() => (popoverControlRef.current = state), 0);
  };

  const onSearch = () => {
    onRunQuery(searchItems, patternLimit);
  };

  const handleKeyPress = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter' && searchItems.length > 0) {
      if (!popoverControlRef.current) {
        newFilterRef.current = -1;
        onSearch();
      }
    } else if (
      event.key === 'Backspace' &&
      inputControlRef.current &&
      searchInput.length === 0 &&
      searchItems.length > 0
    ) {
      // close and reopen the dropdown
      controlRef.current.setDropdownOpen(false);
      setTimeout(() => controlRef.current.setDropdownOpen(true), 0);
      if (searchItems[0].type === SEARCH_KEYWORDS.query) {
        onSearchPatternChanged([]);
      } else if (isClusterMode) {
        // for cluster mode, remove all the items
        onSearchPatternChanged([]);
      } else {
        onSearchPatternChanged(searchItems.slice(0, -1));
      }
    }
  };

  return (
    <div
      className={css({
        display: 'flex',
        alignItems: 'center',
        flexWrap: 'wrap',
        rowGap: '4px',
        columnGap: '2px',
        width: '100%',
        // reserve expansion space for hovering a node, edge, or attribute
        paddingRight: hoverItemIndex > -1 ? '0' : `${hoverOffset}px`,
        // zIndex: 4,
      })}
      onKeyDown={handleKeyPress}
    >
      {/* for cluster mode, we do not support interpret query */}
      {isFreeExplore || isClusterMode ? null : queryTypeSelect}
      {isFreeExplore ? null : (
        <ChartSearchGraphs {...props} inputControlRef={inputControlRef} newFilterRef={newFilterRef} />
      )}
      <ChartSearchItems
        {...props}
        hoverItemIndex={hoverItemIndex}
        onChangeHoverItemIndex={(index) => setHoverItemIndex(index)}
        controlRef={controlRef}
        popoverControlRef={popoverControlRef}
        newFilterRef={newFilterRef}
      />
      <ChartSearchSelect
        {...props}
        isLoadingParameters={isLoadingParameters}
        onChangeSearchInput={(input) => setSearchInput(input)}
        onChangeIsloadingParameters={(isLoading) => setIsLoadingParameters(isLoading)}
        controlRef={controlRef}
        popoverControlRef={popoverControlRef}
        inputControlRef={inputControlRef}
        newFilterRef={newFilterRef}
      />
      {!isLoadingParameters && !isLoadingSchemaOrQueries && (
        <div
          className={css({
            position: 'absolute',
            bottom: '0',
            top: '0',
            right: '0',
          })}
        >
          <ChartSearchActions
            searchItems={searchItems}
            onChangeSearchItems={onSearchPatternChanged}
            patternLimit={patternLimit}
            onPatternLimitChanged={onPatternLimitChanged}
            hopLimit={hopLimit}
            timeLimit={timeLimit}
            memoryLimit={memoryLimit}
            onHopLimitChanged={onHopLimitChanged}
            onMemoryLimitChanged={onMemoryLimitChanged}
            onTimeLimitChanged={onTimeLimitChanged}
            globalParameters={globalParameters}
            handleSearch={onSearch}
            onControlRefStateChange={() => {
              controlRef.current.setDropdownOpen(false);
              popoverControlRef.current = false;
              onChangeGlobalPopoverState(false);
              newFilterRef.current = -1;
            }}
            schema={schema}
            isError={isSchemaOrQueriesError}
            sendIcon={sendIcon}
            isClusterMode={isClusterMode}
          />
        </div>
      )}
    </div>
  );
}
