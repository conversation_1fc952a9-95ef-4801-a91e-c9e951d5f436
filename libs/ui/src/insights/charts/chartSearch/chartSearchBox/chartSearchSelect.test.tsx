import { screen, fireEvent } from '@testing-library/react';
import ChartSearchSelect, { Props } from './chartSearchSelect';
import { createRef } from 'react';
import { render } from '../../../../tests/test-utils';

vi.mock('@fortawesome/react-fontawesome', () => ({ FontAwesomeIcon: () => <span /> }));
vi.mock('short-uuid', () => ({ uuid: () => 'mock-uuid' }));

const mockProps = {
  schema: { results: { GraphName: '', VertexTypes: [], EdgeTypes: [] }, graph: { vertices: {}, edges: {} } },
  graphName: 'testGraph',
  isLoadingParameters: false,
  globalParameters: {},
  queries: {
    testQuery: {
      queryName: 'testQuery',
      installed: true,
      enabled: true,
      params: [],
      callerQueries: [],
      installing: false,
      originalCode: '',
      draftCode: '',
      graphUpdate: false,
      isHidden: false,
      isACLSpecified: false,
    },
  },
  isLoading: false,
  searchPattern: [],
  hopLimit: 3,
  onSearchPatternChanged: vi.fn(),
  onChangeSearchInput: vi.fn(),
  onChangeIsloadingParameters: vi.fn(),
  controlRef: createRef(),
  popoverControlRef: createRef(),
  inputControlRef: createRef(),
  newFilterRef: createRef(),
} as any;

describe('ChartSearchSelect Component', () => {
  it('renders the component with placeholder', () => {
    render(<ChartSearchSelect {...mockProps} />);

    const select = screen.getByRole('combobox');
    expect(select).toBeInTheDocument();
  });

  it('calls onSearchPatternChanged when a query is selected', async () => {
    render(<ChartSearchSelect {...mockProps} />);

    const input = screen.getByRole('combobox');

    // Simulate typing a query
    fireEvent.change(input, { target: { value: 'testQuery' } });
    const opt = screen.getAllByText('testQuery');
    fireEvent.click(opt[1]);

    expect(mockProps.onSearchPatternChanged).toHaveBeenCalledWith([
      { id: 'testQuery', data: 'testQuery', type: 'QUERY' },
    ]);
  });

  it('renders loading state when isLoadingParameters is true', () => {
    render(<ChartSearchSelect {...mockProps} isLoadingParameters={true} />);

    const loadingStatus = screen.getByRole('status');
    const loading = screen.getByText('Loading');
    expect(loadingStatus).toBeInTheDocument();
    expect(loading).toBeInTheDocument();
  });

  it('does not render dropdown when isQueryOrSchema is true', () => {
    render(<ChartSearchSelect {...mockProps} />);

    const dropdown = screen.queryByTestId('dropdown');
    expect(dropdown).not.toBeInTheDocument();
  });
});
