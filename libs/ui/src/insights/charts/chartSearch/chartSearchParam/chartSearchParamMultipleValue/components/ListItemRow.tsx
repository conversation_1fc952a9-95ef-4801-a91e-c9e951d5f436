import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { UseFormReturn } from 'react-hook-form';
import { Label } from './Label';
import { DeleteIcon } from './DeleteIcon';
import { ValueInput } from './ValueInput';
import { GraphState } from '../../../../../chart/schema';
import { VertexListItemRow } from './VertexListItemRow';
import { ErrorMessage } from './ErrorMessage';
import { ItemType } from '../types';

export const ListItemsRowLabel = ({ item }: { item: { elementType?: string } }) => {
  const [css] = useStyletron();

  if (item.elementType !== 'VERTEX') {
    return <Label className={css({ gridColumn: '1/-1' })}>Value</Label>;
  }

  return (
    <>
      <Label>Vertex Type</Label>
      <Label className={css({ gridColumn: '2/-1' })}>Vertex ID</Label>
    </>
  );
};

export const ListItemRow = ({
  index,
  item,
  form,
  schemaGraph,
  disabled,
  onDelete,
}: {
  index: number;
  form: UseFormReturn;
  item: ItemType;
  schemaGraph: GraphState;
  disabled?: boolean;
  onDelete?(): void;
}) => {
  const [css] = useStyletron();
  const { elementType } = item;
  const {
    formState: { errors },
  } = form;

  return (
    <>
      {elementType === 'VERTEX' ? (
        <VertexListItemRow
          disabled={disabled}
          schemaGraph={schemaGraph}
          form={form}
          field={`items.${index}`}
          item={item}
        />
      ) : (
        <div className={css({ gridColumn: '1/3' })}>
          <ValueInput disabled={disabled} type={elementType} form={form} field={`items.${index}.value`} />
        </div>
      )}
      {disabled ? (
        <span />
      ) : (
        <DeleteIcon
          className={css({ cursor: 'pointer', alignSelf: 'center', justifySelf: 'center' })}
          onClick={() => onDelete?.()}
        />
      )}
      {/* error display */}
      {elementType === 'VERTEX' && errors.items?.[index] && (
        <>
          <ErrorMessage>{errors.items?.[index]?.type?.message}</ErrorMessage>
          <ErrorMessage className={css({ gridColumn: '2/-1' })}>{errors.items?.[index]?.id?.message}</ErrorMessage>
        </>
      )}
      {elementType !== 'VERTEX' && errors.items?.[index] && (
        <ErrorMessage className={css({ gridColumn: '1/-1' })}>{errors.items?.[index]?.value?.message}</ErrorMessage>
      )}
    </>
  );
};
