import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { UseFormReturn } from 'react-hook-form';
import { DeleteIcon } from './DeleteIcon';
import { ValueInput } from './ValueInput';
import { ErrorMessage } from './ErrorMessage';
import { HybridItemsFormType } from '../types';

export type MapFormValue = {
  items: { key: unknown; value: unknown }[];
};

export const MapItemsRowLabel = () => {
  const [css] = useStyletron();
  return (
    <>
      <div
        className={css({
          fontSize: '12px',
          lineHeight: '16px',
          fontWeight: '500',
          color: '#2C3237',
        })}
      >
        Key
      </div>
      <div
        className={css({
          gridColumn: '2/-1',
          fontSize: '12px',
          lineHeight: '16px',
          fontWeight: '500',
          color: '#2C3237',
        })}
      >
        Value
      </div>
    </>
  );
};

export const MapItemRow = ({
  index,
  item,
  form,
  disabled,
  onDelete,
}: {
  index: number;
  form: UseFormReturn<HybridItemsFormType>;
  item: { keyType?: string; valueType?: string };
  disabled?: boolean;
  onDelete?(): void;
}) => {
  const [css] = useStyletron();
  const {
    formState: { errors },
  } = form;

  return (
    <>
      {['key', 'value'].map((i) => (
        <div key={i}>
          <ValueInput
            type={i === 'key' ? item.keyType : item.valueType}
            form={form as UseFormReturn<any>}
            field={`items.${index}.${i}`}
            disabled={disabled}
          />
        </div>
      ))}
      {disabled ? (
        <span />
      ) : (
        <DeleteIcon className={css({ cursor: 'pointer', alignSelf: 'center' })} onClick={() => onDelete?.()} />
      )}
      {errors.items?.[index] &&
        ['key', 'value'].map((i) => (
          <ErrorMessage key={i} className={css({ gridColumn: i === 'value' ? '2/-1' : '1' })}>
            {errors.items?.[index]?.[i]?.message}
          </ErrorMessage>
        ))}
    </>
  );
};
