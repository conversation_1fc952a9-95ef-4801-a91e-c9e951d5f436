import { Controller, UseFormReturn } from 'react-hook-form';
import { Select } from '@tigergraph/app-ui-lib/select';
import { Input } from '@tigergraph/app-ui-lib/input';
import { FormatValidator } from '@tigergraph/tools-models';

const isNumber = (value: number | string | bigint) => {
  if (typeof value === 'number' || typeof value === 'bigint') {
    return true;
  }
  return isDouble(value);
};

const isDouble = (value: number | string) => {
  if (typeof value === 'number') {
    return true;
  }
  if (!value?.trim()) {
    return false;
  }
  if (value.endsWith('.')) {
    return false;
  }

  return !Number.isNaN(Number(value));
};

const isInt = (value: string | number | bigint) => {
  return /^-?[0-9]+$/.test(String(value));
};

const isUInt = (value: string | number | bigint) => {
  return /^[0-9]+$/.test(String(value));
};

const parseBigInt = (value: string) => {
  if (value === '') {
    return '';
  }
  try {
    return BigInt(value);
  } catch {
    return value;
  }
};

const parseDouble = (value: string) => {
  const parsedValue = Number(value);
  if (value && !Number.isNaN(parsedValue) && !value.endsWith('.')) {
    return parsedValue;
  } else {
    return value;
  }
};

export const ValueInput = ({
  type,
  form,
  field,
  disabled,
}: {
  type: string;
  field: string;
  form: UseFormReturn;
  allowEmpty?: boolean;
  disabled?: boolean;
}) => {
  switch (type) {
    case 'BOOL': {
      return (
        <Controller
          name={field}
          control={form.control}
          rules={{
            validate: (value) => {
              if (value === true || value === false) {
                return;
              }
              return 'required';
            },
          }}
          render={({ field: { ref, ...field } }) => (
            <Select
              inputRef={ref}
              placeholder={type}
              options={[
                { value: true, label: 'true' },
                { value: false, label: 'false' },
              ]}
              valueKey="value"
              clearable={false}
              disabled={disabled}
              {...field}
              value={[{ value: field.value }]}
              onChange={(params) => field.onChange(params.value?.[0]?.value)}
            />
          )}
        />
      );
    }
    default: {
      return (
        <Controller
          name={field}
          control={form.control}
          rules={{
            validate: (value) => {
              switch (type) {
                case 'INT':
                  return isInt(value) ? undefined : 'invalid int value';
                case 'UINT':
                  return isUInt(value) ? undefined : 'invalid uint value';
                case 'FLOAT':
                case 'NUMBER':
                  return isNumber(value) ? undefined : 'invalid';
                case 'DOUBLE':
                  return isDouble(value) ? undefined : 'invalid';
                case 'DATETIME':
                  return FormatValidator.isDatetime(value).success ? undefined : 'invalid datetime format';
                case 'STRING':
                  return;
                default:
                  return value ? undefined : 'required';
              }
            },
          }}
          render={({ field: { ref, onChange, value, ...field } }) => (
            <Input
              size="compact"
              placeholder={type}
              inputRef={ref}
              value={value === undefined ? '' : String(value)}
              disabled={disabled}
              onChange={(e: React.FormEvent<HTMLInputElement>) => {
                const value = e.currentTarget.value;
                switch (type) {
                  case 'INT':
                  case 'UINT': {
                    const parsedValue = parseBigInt(value);
                    onChange(parsedValue);
                    break;
                  }
                  case 'FLOAT':
                  case 'NUMBER': {
                    let parsedValue: number | string | bigint = parseBigInt(value);
                    if (typeof parsedValue === 'string') {
                      parsedValue = parseDouble(value);
                    }
                    onChange(parsedValue);
                    break;
                  }
                  case 'DOUBLE': {
                    const parsedValue = Number(value);
                    onChange(parsedValue);
                    break;
                  }
                  default:
                    onChange(value);
                    break;
                }
              }}
              {...field}
            />
          )}
        />
      );
    }
  }
};
