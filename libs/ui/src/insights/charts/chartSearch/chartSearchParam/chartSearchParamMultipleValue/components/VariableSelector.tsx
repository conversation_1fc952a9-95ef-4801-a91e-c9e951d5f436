import { Select } from '@tigergraph/app-ui-lib/select';
import { useMemo } from 'react';
import { forEach, upperCase } from 'lodash';
import { Controller, UseFormReturn } from 'react-hook-form';
import { GlobalParams } from '../../../../../chart/globalParams';
import { SearchItem } from '../../../../chartSlice';
import { Label } from './Label';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { HybridItemsFormType } from '../types';

const isArrayLike = (t: string) => {
  return ['LIST', 'SET'].includes(upperCase(t));
};

const isNumber = (t: string) => {
  return ['UINT', 'INT', 'INT64', 'INT8', 'DOUBLE', 'FLOAT', 'NUMBER'].includes(upperCase(t));
};

const compareType = (l: string, r: string) => {
  if (upperCase(l) === upperCase(r)) {
    return true;
  }
  return false;
};

const comparePrimitiveType = (l: string, r: string) => {
  if (isNumber(l) && isNumber(r)) {
    return true;
  }

  return compareType(l, r);
};

export const INTERNAL_CUSTOM = '@@__CUSTOM/INTERNAL__@@-9FDFC7';

export const VariableSelector = ({
  form,
  globalParams,
  item,
  onSelect,
}: {
  form: UseFormReturn<HybridItemsFormType>;
  globalParams: GlobalParams;
  item: SearchItem;
  onSelect?(inputName: string, isInternal: boolean): void;
}) => {
  const { control } = form;
  const [css] = useStyletron();
  const options = useMemo(() => {
    const result = [{ label: 'Custom', id: INTERNAL_CUSTOM }];
    forEach(globalParams, (gp, paramName) => {
      if (compareType(gp.type, item.paramType)) {
        // equal pass
      } else if (isArrayLike(gp.type) && isArrayLike(item.paramType)) {
        // list/set is compatible
      } else {
        return;
      }

      if (gp.type === 'MAP') {
        if (!comparePrimitiveType(gp.keyType, item.keyType)) {
          return;
        }
        if (!comparePrimitiveType(gp.valueType, item.valueType)) {
          return;
        }
      }
      if (gp.type === 'LIST') {
        if (!comparePrimitiveType(gp.elementType, item.elementType)) {
          return;
        }
      }
      const name = gp.name || paramName;
      result.push({ label: name, id: name });
    });

    return result;
  }, [globalParams, item]);

  return (
    <div>
      <Label className={css({ marginBottom: '8px' })}>Select a Variable</Label>
      <Controller
        control={control}
        name="inputName"
        render={({ field: { value, onChange, ref, ...field } }) => (
          <Select
            size="compact"
            clearable={false}
            inputRef={ref}
            {...field}
            onChange={(p) => {
              const inputName = p.value?.[0]?.id as string;
              if (inputName) {
                onChange(inputName);
                onSelect?.(inputName, inputName === INTERNAL_CUSTOM);
              }
            }}
            value={[{ id: value }]}
            options={options}
          />
        )}
      />
    </div>
  );
};
