import { Input } from '@tigergraph/app-ui-lib/input';
import { Select } from '@tigergraph/app-ui-lib/select';
import { useMemo } from 'react';
import { Controller, UseFormReturn } from 'react-hook-form';
import { GraphState } from '../../../../../chart/schema';
import { ItemType } from '../types';

export const VertexListItemRow = ({
  form,
  item,
  field,
  schemaGraph,
  required = true,
  disabled,
}: {
  required?: boolean;
  form: UseFormReturn;
  item: ItemType;
  field: string;
  schemaGraph: GraphState;
  disabled?: boolean;
}) => {
  const { vertexType } = item;
  const vertexTypes = useMemo(() => Object.keys(schemaGraph.vertices).map((type) => ({ id: type })), [schemaGraph]);
  const rules = required ? { required: 'required' } : undefined;

  return (
    <>
      <Controller
        name={`${field}.type`}
        control={form.control}
        rules={rules}
        render={({ field: { ref, value, onChange, ...field } }) => (
          <Select
            inputRef={ref}
            clearable={false}
            disabled={disabled || (vertexType && vertexType !== '*')}
            labelKey="id"
            options={vertexTypes}
            value={[{ id: value }]}
            onChange={(params) => onChange(params.value?.[0]?.id)}
            {...field}
          />
        )}
      />
      <Controller
        name={`${field}.id`}
        control={form.control}
        rules={rules}
        render={({ field: { ref, value, ...field } }) => (
          <Input disabled={disabled} inputRef={ref} value={value ? value : ''} {...field} />
        )}
      />
    </>
  );
};
