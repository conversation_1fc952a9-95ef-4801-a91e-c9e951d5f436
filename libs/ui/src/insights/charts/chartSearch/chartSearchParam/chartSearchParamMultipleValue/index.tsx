import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { SearchParameterProps } from '../type';
import { ReactComponent as CirclePlus } from '../../../../assets/images/circle-plus.svg';
import { Popover } from '@tigergraph/app-ui-lib/popover';
import { isMap, isMultiple } from '../util';
import { useEffect, useMemo, useState } from 'react';
import { PLACEMENT } from 'baseui/popover';
import { SearchItem } from '../../../chartSlice';
import { getDefaultVertexType } from './utils/getDefaultVertexType';
import { HybridItemsFormWithSubmit } from './components/HybridItemsFormWithSubmit';
import { INTERNAL_CUSTOM, VariableSelector } from './components/VariableSelector';
import { useForm } from 'react-hook-form';
import { HybridItemsFormType } from './types';

const getTypeLabel = (types: string[]) => {
  const [type, ...ot] = types;
  if (types.length === 1) {
    return type || '';
  }
  return `${type} < ${getTypeLabel(ot)} >`;
};

const mapItems2Form = ({ mapData }: SearchItem) => {
  if (Array.isArray(mapData?.keylist) && Array.isArray(mapData?.valuelist)) {
    return mapData.keylist.map((key, idx) => ({ key, value: mapData.valuelist[idx] }));
  }
  return [{ key: '' as unknown, value: '' as unknown }];
};

const listItems2Form = ({ elementType, vertexType, paramList }: SearchItem) => {
  if (elementType === 'VERTEX') {
    return paramList || [{ id: '', type: getDefaultVertexType(vertexType) }];
  }
  return paramList || [{ value: '' }];
};

const getInitialValue = (item: SearchItem) => {
  if (isMap(item)) {
    return mapItems2Form(item);
  }
  return listItems2Form(item);
};

export default function ChartSearchParamMultipleValue(props: SearchParameterProps) {
  const { index, searchItems, globalParameters, onSearchPatternChanged, schemaGraph } = props;
  const item = searchItems[index];
  const form = useForm<HybridItemsFormType>({
    defaultValues: {
      inputName: item.paramGlobalInput || INTERNAL_CUSTOM,
      items: getInitialValue(item),
    },
  });
  const [css] = useStyletron();
  const [isOpen, setIsOpen] = useState(false);
  const [showPlusIcon, setShowPlusIcon] = useState(false);

  const inputName = form.watch('inputName');
  const formDisabled = useMemo(() => inputName !== INTERNAL_CUSTOM, [inputName]);

  useEffect(() => {
    if (isOpen) {
      setShowPlusIcon(true);
    } else {
      setShowPlusIcon(false);
    }
  }, [isOpen]);

  const handleMapDataSubmit = (globalInputName, mapData) => {
    const newSearchItems = [...searchItems];

    newSearchItems[index] = {
      ...item,
      paramGlobalInput: '',
      data: '',
    };
    if (globalInputName && globalInputName !== INTERNAL_CUSTOM) {
      newSearchItems[index].paramGlobalInput = globalInputName;
    } else {
      const keylist = mapData.map((i) => i.key);
      const valuelist = mapData.map((i) => i.value);
      newSearchItems[index].mapData = { keylist, valuelist };
    }

    onSearchPatternChanged(newSearchItems);
  };
  const handleListDataSubmit = (globalInputName, items) => {
    const newSearchItems = [...searchItems];
    newSearchItems[index] = {
      ...item,
      paramGlobalInput: '',
    };
    if (globalInputName && globalInputName !== INTERNAL_CUSTOM) {
      newSearchItems[index].paramGlobalInput = globalInputName;
    } else {
      newSearchItems[index].paramList = items.map((i) => ({ ...i, isCreatable: true }));
    }

    onSearchPatternChanged(newSearchItems);
  };

  return (
    <Popover
      placement={PLACEMENT.bottom}
      overrides={{
        Arrow: {
          style: {
            backgroundColor: '#fff',
          },
        },
        Inner: {
          style: {
            paddingLeft: 0,
            paddingRight: 0,
            paddingTop: 0,
            paddingBottom: 0,
            backgroundColor: '#fff',
          },
        },
      }}
      isOpen={isOpen}
      autoFocus
      content={
        <HybridItemsFormWithSubmit
          form={form}
          disabled={formDisabled}
          item={item}
          schemaGraph={schemaGraph}
          renderHeader={() => (
            <div className={css({ marginBottom: '12px' })}>
              <VariableSelector form={form} globalParams={globalParameters} item={item} />
            </div>
          )}
          onClose={() => {
            setIsOpen(false);
          }}
          onSubmit={(values) => {
            if (isMap(item)) {
              handleMapDataSubmit(values?.inputName, values?.items);
            } else {
              handleListDataSubmit(values?.inputName, values?.items);
            }
            form.reset(values);
          }}
        />
      }
      onClickOutside={() => setIsOpen(false)}
    >
      <div
        onMouseEnter={() => {
          setShowPlusIcon(true);
        }}
        onMouseLeave={() => {
          if (!isOpen) {
            setShowPlusIcon(false);
          }
        }}
      >
        <div
          className={css({
            display: 'flex',
            alignItems: 'center',
            fontSize: '12px',
            lineHeight: '16px',
            color: '#656565',
            cursor: 'pointer',
          })}
          onClick={() => {
            setIsOpen(true);
            form.reset();
          }}
        >
          {/* label start */}
          <span>
            {isMultiple(item) &&
              getTypeLabel([item.paramType, item.elementType, item.vertexType].filter((i) => Boolean(i) && i !== '*'))}
            {isMap(item) && getTypeLabel([item.paramType, `${item.keyType} | ${item.valueType}`])}
          </span>
          {/* label end */}
          <span
            className={css({
              display: 'flex',
              opacity: showPlusIcon ? 1 : 0,
              justifyContent: 'center',
              marginLeft: '8px',
              cursor: 'pointer',
            })}
          >
            <CirclePlus />
          </span>
        </div>
      </div>
    </Popover>
  );
}
