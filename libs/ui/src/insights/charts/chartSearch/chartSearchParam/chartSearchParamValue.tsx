import React from 'react';
import { OnChangeParams } from 'baseui/select';
import { isGlobalParamTypeCompatible } from '../../../chart/globalParams';

import ChartSearchStyledSelect from '../../../components/chartSearchStyledSelect';
import { SearchParameterSelectProps } from './type';

export default function ChartSearchParamValue(props: SearchParameterSelectProps) {
  const {
    globalParameters,
    index,
    searchItems,
    onSearchPatternChanged,
    inputControlRef,
    onChangeGlobalPopoverState,

    openFirstRef,
    paramValueRef,
    onChangeDropdown,
  } = props;

  const item = searchItems[index];

  const onChangeParameterValue = (params: OnChangeParams) => {
    const { value, type } = params;
    const newSearchItems = [...searchItems];
    if (value.length === 0 && type === 'remove') {
      newSearchItems[index] = {
        ...item,
        data: '',
        paramGlobalInput: '',
      };
    } else {
      newSearchItems[index] = {
        ...item,
        data: String(value[0].id),
        paramGlobalInput: String(value[0].id),
      };
      const globalParameter = globalParameters[value[0].id];
      if (globalParameter?.type === 'VERTEX' && !item.paramTypeReadonly) {
        newSearchItems[index] = {
          ...item,
          data: globalParameter.value.vertexID,
          paramGlobalInput: String(value[0].id),
          vertexType: globalParameter.value.vertexType,
          vertexTypeGlobalInput: String(value[0].id),
        };
      }
      inputControlRef.current && inputControlRef.current.setInputFocus();
    }
    onSearchPatternChanged(newSearchItems);
    onChangeDropdown(paramValueRef, false);
  };

  return (
    <ChartSearchStyledSelect
      controlRef={paramValueRef}
      options={Object.keys(globalParameters).reduce((prev, key) => {
        const { value, type } = globalParameters[key];
        if (item.paramType) {
          if (isGlobalParamTypeCompatible(type, item.paramType)) {
            prev.push({ id: key, data: `${key}: ${typeof value === 'object' ? JSON.stringify(value) : value}` });
          }
        } else {
          prev.push({ id: key, data: `${key}: ${typeof value === 'object' ? JSON.stringify(value) : value}` });
        }
        return prev;
      }, [])}
      value={(function () {
        const res = [];
        if (item.paramGlobalInput && item.paramGlobalInput in globalParameters) {
          res.push({ id: item.id });
          if (item.paramGlobalInput && item.paramGlobalInput in globalParameters) {
            const globalParamValue = globalParameters[item.paramGlobalInput];
            if (globalParamValue.type === 'VERTEX') {
              res[0]['data'] = `${item.paramGlobalInput}: ${globalParamValue.value.vertexID}`;
            } else {
              res[0]['data'] = `${item.paramGlobalInput}: ${globalParamValue.value}`;
            }
          }
        } else if (item.data) {
          res.push({ id: item.id, data: item.data });
        }
        return res;
      })()}
      onChange={onChangeParameterValue}
      filterOptions={(options, filterValue: string) => {
        let filteredOptions = options;
        if (filterValue) {
          filterValue = filterValue.toLowerCase();
          filteredOptions = filteredOptions
            .filter((item) => item.data.toLowerCase().indexOf(filterValue) > -1)
            .sort((a, b) => a.data.toLowerCase().indexOf(filterValue) - b.data.toLowerCase().indexOf(filterValue));
        }
        return filteredOptions;
      }}
      onInputChange={(e) => {
        const newSearchItems = [...searchItems];
        newSearchItems[index] = {
          ...item,
          data: e.target['value'],
          paramGlobalInput: '',
        };
        onSearchPatternChanged(newSearchItems);
        if (!Object.keys(globalParameters).find((key) => key.indexOf(e.target['value']) > -1)) {
          onChangeDropdown(paramValueRef, false);
        }
      }}
      onBlurResetsInput={false}
      onCloseResetsInput={false}
      onFocus={() => {
        openFirstRef.current = false;
        onChangeDropdown(paramValueRef, true);
      }}
      onBlur={() => {
        onChangeDropdown(paramValueRef, false);
      }}
      onOpen={() => {
        onChangeGlobalPopoverState(true);
      }}
      onClose={() => {
        onChangeGlobalPopoverState(false);
      }}
      backspaceClearsInputValue={!!item.paramGlobalInput}
      placeholder={item.paramType ? (item.paramType === 'VERTEX' ? 'ID' : item.paramType) : 'Param value'}
      labelKey="data"
    />
  );
}
