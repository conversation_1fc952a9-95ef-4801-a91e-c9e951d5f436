import React from 'react';
import { OnChangeParams } from 'baseui/select';

import ChartSearchStyledSelect from '../../../components/chartSearchStyledSelect';
import { SearchParameterSelectProps } from './type';

export default function ChartSearchParamVertexType(props: SearchParameterSelectProps) {
  const {
    globalParameters,
    index,
    searchItems,
    schemaGraph,
    onSearchPatternChanged,
    onChangeGlobalPopoverState,

    openFirstRef,
    vertexTypeRef,
    onChangeDropdown,
  } = props;

  const item = searchItems[index];

  const onChangeVertexType = (params: OnChangeParams) => {
    const { value } = params;
    const newSearchItems = [...searchItems];
    if (value.length === 0) {
      newSearchItems[index] = {
        ...item,
        vertexType: '',
        vertexTypeGlobalInput: '',
      };
    } else if (String(value[0].id).indexOf('type:') > -1) {
      newSearchItems[index] = {
        ...item,
        vertexType: value[0].data + '',
        vertexTypeGlobalInput: '',
      };
      onChangeDropdown(vertexTypeRef, false);
    } else {
      const globalParameter = globalParameters[value[0].data];
      if (globalParameter.type === 'VERTEX') {
        newSearchItems[index] = {
          ...item,
          vertexType: globalParameter.value.vertexType,
          vertexTypeGlobalInput: value[0].data,
          data: globalParameter.value.vertexID,
          paramGlobalInput: value[0].data,
        };
      }
      onChangeDropdown(vertexTypeRef, false);
    }
    onSearchPatternChanged(newSearchItems);
    onChangeDropdown(vertexTypeRef, false);
  };

  return (
    <ChartSearchStyledSelect
      controlRef={vertexTypeRef}
      options={Object.keys(globalParameters).reduce(
        (prev, key) => {
          const { value, type } = globalParameters[key];
          if (!item.paramType || item.paramType === type) {
            if (item.paramType === type) {
              prev.push({
                id: 'global:' + key,
                label: `${key}: ${typeof value === 'object' ? JSON.stringify(value) : value}`,
                data: key,
              });
            }
          }
          return prev;
        },
        Object.keys(schemaGraph.vertices).map((key) => ({ id: 'type:' + key, label: key, data: key }))
      )}
      value={(function () {
        const res = [];
        if (item.vertexTypeGlobalInput && item.vertexTypeGlobalInput in globalParameters) {
          res.push({ id: item.id });
          const globalParamValue = globalParameters[item.vertexTypeGlobalInput];
          if (globalParamValue.type === 'VERTEX') {
            res[0]['label'] = `${item.vertexTypeGlobalInput}: ${globalParamValue.value.vertexType}`;
          } else {
            res[0]['label'] = `${item.vertexTypeGlobalInput}: ${globalParamValue}`;
          }
        } else if (item.vertexType) {
          res.push({ id: item.id, label: item.vertexType });
        }
        return res;
      })()}
      onChange={onChangeVertexType}
      onFocus={() => {
        openFirstRef.current = false;
        onChangeDropdown(vertexTypeRef, true);
      }}
      onBlur={() => {
        onChangeDropdown(vertexTypeRef, false);
      }}
      onOpen={() => {
        onChangeGlobalPopoverState(true);
      }}
      onClose={() => {
        onChangeGlobalPopoverState(false);
      }}
      backspaceClearsInputValue={!!item.vertexTypeGlobalInput}
      placeholder={'Type'}
      labelKey="label"
      disabled={!!item.paramTypeReadonly}
    />
  );
}
