import { getUID } from '../utils';

export interface SearchItem {
  id: string;
  data: string;
  type: string;
  alias?: string;
  // 0: bidirectional,
  // 1: to the right,
  // 2: to the left
  direction?: 0 | 1 | 2;
  from?: string;
  to?: string;
  shouldBeBold?: string;
  edgePairs?: Array<any>;
  paramName?: string;
  paramType?: string;
  elementType?: string;
  paramTypeReadonly?: boolean;
  paramGlobalInput?: string;
  paramOperator?: string;
  paramList?: { value: string; isCreatable: boolean; id?: string; type?: string }[];
  vertexType?: string;
  vertexTypeGlobalInput?: string;
  orderBy?: { asc: boolean; expression: { type: string; value: string }; label: string }[];
  // for map input parameters
  keyType?: string;
  valueType?: string;
  // mapData?: { key: unknown; value: unknown }[];
  mapData?: { keylist: unknown[]; valuelist: unknown[] };
}

export type QueryType = 'pattern' | 'interactive' | 'static_data';

export interface ChartState {
  version: string;
  title: string; // chart title
  id: string; // chart unique id
  graphName: string; // graph name from TigerGraph
  type: string; // chart type, like graph, table, bar chart, etc.
  refreshRate: number; // chart auto refresh time.
  queryType: QueryType;
  searchPattern: Array<SearchItem>; // chart search pattern.
  query: string;
  staticData: string; // mocked json data for chart
  patternLimit: number;
  memoryLimit: number;
  timeLimit: number;
  hopLimit?: number;
  chartSettings: Record<string, any>;
  hideWidgetName?: boolean;
  cache?: boolean;
}

export const initializeChart = (type: string): ChartState => {
  const chartId = getUID();
  return {
    id: chartId,
    graphName: '',
    title: 'New Widget',
    type: type,
    refreshRate: 0,
    queryType: 'pattern',
    searchPattern: [],
    query: '',
    staticData: JSON.stringify([{ key: 'value' }], null, 4),
    patternLimit: 5,
    timeLimit: undefined,
    memoryLimit: undefined,
    chartSettings: {},
    version: '',
    hideWidgetName: false,
  };
};

type ChartUpdate = Omit<Partial<ChartState>, 'chartSettings'>;

type ChartNestedUpdate = {
  actionType: 'updateChartSetting';
  field: string;
  value: any;
};

function isChartNestedUpdate(value: ChartUpdate | ChartNestedUpdate): value is ChartNestedUpdate {
  return value.hasOwnProperty('actionType');
}

export type ChartAction = ChartUpdate | ChartNestedUpdate;

export function chartReducer(state: ChartState, action: ChartAction) {
  if (isChartNestedUpdate(action)) {
    const { actionType, field, value } = action;
    const { chartSettings } = state;

    switch (actionType) {
      case 'updateChartSetting':
        return {
          ...state,
          chartSettings: {
            ...chartSettings,
            [field]: value,
          },
        };
    }
  }

  return {
    ...state,
    ...action,
  };
}
