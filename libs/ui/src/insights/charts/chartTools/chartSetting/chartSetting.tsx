import React, { useState, useMemo, forwardRef } from 'react';
import { Value } from 'baseui/select';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { KIND as ToastKind } from 'baseui/toast';
import { Button } from '@tigergraph/app-ui-lib/button';
import { KIND, SIZE } from 'baseui/button';
import { useIsMutating } from 'react-query';
import { ErrorBoundary } from 'react-error-boundary';

import { ChartSettingItemContainer } from './chartSettingItemContainer';
import { chartTypes, Chart } from '../../../chart/chartRegister';
import { ChartSettingProps } from '../../../chart/chart';
import { ActionSetting } from '../../../components/action/actionSetting';
import ChartSettingSectionContainer from './chartSettingSectionContainer';
import { filterProps } from '../../../utils';
import { StyledInput, StyledSelect } from '../../../components';
import { StyledToast } from '../../../components/styledToasterContainer';
import { ErrorDisplay } from '../../../components/error';
import StyledToggle from '../../../components/styledToggle';
import DynamicChartTitle from './dynamicChartTitle';

type WidgetItemProps = {
  [x: string]: any;
  item: { id: string; label: string; icon: string };
};

const WidgetItem = forwardRef<HTMLLIElement, WidgetItemProps>((props, ref) => {
  const [css, theme] = useStyletron();
  const { item, ...rest } = props;
  return (
    <li
      // todo(lin):
      // for now, we ignore focus/active state
      {...filterProps(rest)}
      ref={ref}
      className={css({
        cursor: 'pointer',
        listStyle: 'none',
        marginBottom: '8px',
        ':nth-child(odd)': {
          marginRight: '8px',
        },
        width: 'calc(50% - 4px)',
      })}
    >
      <div
        className={css({
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'stretch',
        })}
      >
        <div
          className={css({
            padding: '7px',
            borderRadius: '4px',
            border: `1px solid ${theme.colors.gray300}`,
          })}
        >
          <img
            src={item.icon}
            alt=""
            className={css({
              width: '100%',
              height: '60%',
            })}
          />
        </div>
        <span
          className={css({
            ...theme.typography.Body2,
            marginTop: '4px',
            textAlign: 'center',
          })}
        >
          {item.label}
        </span>
      </div>
    </li>
  );
});

export function ChartSetting({
  chartSettingProps,
  chart,
  chartTitle,
  chartRefreshRate,
  hideWidgetName,
  onChartTitleChange,
  onChartTypeChange,
  onChartRefreshRateChange,
  onChartSave,
  onChartDiscard,
  onHideWidgetNameChange,
}: {
  chartSettingProps: ChartSettingProps;
  chart: Chart;
  chartTitle: string;
  chartRefreshRate: number;
  hideWidgetName?: boolean;
  onChartTitleChange: (title: string) => void;
  onChartTypeChange: (type: string) => void;
  onChartRefreshRateChange: (refreshRate: number) => void;
  onChartSave: () => void;
  onChartDiscard: () => void;
  onHideWidgetNameChange?: () => void;
}) {
  const [css, theme] = useStyletron();
  const { settingsInfo } = chart;
  const { chartData, globalParameters } = chartSettingProps;
  const isMutating = useIsMutating('upsertApp');

  const [type, setType] = useState<Value>([{ id: chart.type }]);
  const [showDynamicPopover, setShowDynamicPopover] = useState(false);

  const widgets = chartTypes();

  const refreshInteval = [
    { id: 0, label: 'No auto refresh' },
    { id: 30, label: '30 seconds' },
    { id: 60, label: '60 seconds' },
    { id: 60 * 5, label: '5 mins' },
    { id: 60 * 15, label: '15 mins' },
  ];

  const SettingsComponent = useMemo<(props: ChartSettingProps) => JSX.Element | null>(() => {
    if (settingsInfo) {
      return settingsInfo.createUIComponent();
    }
    return () => null;
  }, [settingsInfo]);

  const canRefresh = chart.type !== 'Inputs' && chart.type !== 'markdown';

  return (
    <div
      className={css({
        position: 'absolute',
        left: 0,
        top: 0,
        right: 0,
        bottom: 0,
        overflow: 'hidden',
        display: 'flex',
        flexDirection: 'column',
      })}
    >
      <div
        className={css({
          position: 'absolute',
          left: 0,
          top: 0,
          right: 0,
          bottom: '52px',
          overflowY: 'auto',
          scrollbarWidth: 'thin',
        })}
      >
        <ChartSettingSectionContainer title="General">
          <ChartSettingItemContainer
            label="Widget Type"
            helpText="Try out different widgets to display your data in different ways"
          >
            <StyledSelect
              id="select_widget_dropdown"
              maxDropdownHeight={'400px'}
              overrides={{
                Dropdown: {
                  style: {
                    display: 'flex',
                    flexWrap: 'wrap',
                    marginTop: '8px',
                    paddingBottom: '0px',
                    paddingLeft: '8px',
                    paddingRight: '8px',
                  },
                },
                DropdownListItem: {
                  component: WidgetItem,
                },
              }}
              options={widgets}
              value={type}
              onChange={(params) => {
                setType(params.value);
                if (params.option) {
                  onChartTypeChange(params.option.id.toString());
                }
              }}
              placeholder="Please select a widget type"
              clearable={false}
            />
          </ChartSettingItemContainer>
          {chart.description && (
            <div
              className={css({
                fontSize: '12px',
                fontStyle: 'italic',
                color: theme.colors.black03,
              })}
            >
              {chart.description}
            </div>
          )}
          <ChartSettingItemContainer label="Widget Name" helpText="The widget title that will appear in the dashboard.">
            <StyledInput value={chartTitle} onChange={(e) => onChartTitleChange(e.currentTarget.value)} />
          </ChartSettingItemContainer>
          <div
            className={css({
              marginTop: '-15px',
              position: 'relative',
            })}
          >
            <ChartSettingItemContainer
              label="Advanced"
              helpText="Enable advanced options to create a dynamic widget name based on an embedded variable"
              right={
                <StyledToggle
                  checked={showDynamicPopover}
                  onChange={() => setShowDynamicPopover(!showDynamicPopover)}
                ></StyledToggle>
              }
            >
              <DynamicChartTitle
                params={globalParameters}
                isOpen={showDynamicPopover}
                chartTitle={chartTitle}
                onChartTitleChanged={onChartTitleChange}
                onClose={() =>
                  setTimeout(() => {
                    setShowDynamicPopover(false);
                  }, 200)
                }
              >
                <span
                  className={css({
                    position: 'absolute',
                    left: 0,
                    right: 0,
                    top: '6px',
                    bottom: '12px',
                    zIndex: -1,
                  })}
                ></span>
              </DynamicChartTitle>
            </ChartSettingItemContainer>
          </div>
          <div
            className={css({
              marginTop: '-15px',
              position: 'relative',
            })}
          >
            <ChartSettingItemContainer
              label="Hide Widget Name"
              helpText="Hide Widget Name"
              right={<StyledToggle checked={hideWidgetName} onChange={onHideWidgetNameChange}></StyledToggle>}
            ></ChartSettingItemContainer>
          </div>
          {canRefresh && (
            <ChartSettingItemContainer
              label="Auto Refresh Interval"
              helpText="How often to refresh data in the dashboard display"
            >
              <StyledSelect
                id="select_auto_refresh_interval"
                maxDropdownHeight={'400px'}
                options={refreshInteval}
                value={[{ id: chartRefreshRate }]}
                onChange={({ value }) => {
                  onChartRefreshRateChange(Number(value[0].id));
                }}
                placeholder="Auto refresh interval"
                clearable={false}
              />
            </ChartSettingItemContainer>
          )}
          <ActionSetting
            {...chartSettingProps}
            field="widgetActions"
            label="Widget Actions"
            helpText="Allows you to create customized links inside your widget that appear in the upper right corner. Each action can link to another page or to customized parameters in the same widget."
          />
        </ChartSettingSectionContainer>
        {/* for graph widget, we always want to render graph setting  */}
        {chart.noQuery || chartData || chart.type === 'internal-graph' ? (
          <ErrorBoundary
            FallbackComponent={({ error }) => (
              <ErrorDisplay error={error} label={`Rendering Error for ${chart.label} Widget Settings:`} />
            )}
            // may be the error is trigger by chartData
            resetKeys={[chart.type, chartData]}
          >
            <SettingsComponent {...chartSettingProps} />
          </ErrorBoundary>
        ) : (
          <div className={css({ padding: '16px' })}>
            <StyledToast
              message="No data is available for configure. You can search graph pattern or write GSQL query to retrieve data from the database."
              kind={ToastKind.info}
              size={'compact'}
              closeable={false}
            />
          </div>
        )}
      </div>
      <div
        className={css({
          position: 'absolute',
          left: 0,
          right: 0,
          bottom: 0,
          padding: '12px',
          textAlign: 'center',
        })}
      >
        <Button
          overrides={{
            BaseButton: {
              style: {
                marginRight: '24px',
                fontWeight: 600,
              },
            },
          }}
          onClick={onChartDiscard}
          kind={KIND.secondary}
        >
          Discard
        </Button>
        <Button
          overrides={{
            BaseButton: {
              style: {
                fontWeight: 600,
              },
            },
          }}
          kind={KIND.primary}
          onClick={onChartSave}
          isLoading={isMutating > 0}
        >
          Apply
        </Button>
      </div>
    </div>
  );
}
