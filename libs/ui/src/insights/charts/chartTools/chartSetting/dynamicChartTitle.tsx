import React, { useEffect, useState } from 'react';
import { PLACEMENT } from 'baseui/popover';
import { Popover } from '@tigergraph/app-ui-lib/popover';

import { GlobalParams } from '../../../chart/globalParams';
import { ParametersTable } from '../../../components/parametersTable';
import ConfirmButtons from '../../../components/confirmButtons';
import { StyledInput } from '../../../components';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { useSandbox } from '../../../chart/sandbox';

type Props = {
  params: GlobalParams;
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
  chartTitle: string;
  onChartTitleChanged: (title: string) => void;
};

export default function DynamicChartTitle({
  params,
  isOpen,
  onClose,
  children,
  chartTitle,
  onChartTitleChanged,
}: Props) {
  const [input, setInput] = useState(chartTitle);
  const [debouncedInput, setDebouncedInput] = useState(input);

  const [css, theme] = useStyletron();

  const { errMsg, evaluatedTitle } = useSandbox(debouncedInput, params);

  let finalTitle = input.indexOf('${variables.') >= 0 ? evaluatedTitle : input;

  useEffect(() => {
    setInput(chartTitle);
  }, [chartTitle]);

  useEffect(() => {
    setTimeout(() => {
      setDebouncedInput(input);
    }, 250);
  }, [input]);

  const content = (
    <div>
      <ParametersTable params={params} />
      <div
        className={css({
          marginTop: '8px',
          marginBottom: '16px',
        })}
      >
        <div
          className={css({
            marginBottom: '4px',
          })}
        >
          Widget Name
        </div>
        <StyledInput value={input} onChange={(e) => setInput(e.currentTarget.value)} />
        <div
          className={css({
            marginTop: '8px',
            marginBottom: '12px',
          })}
        >
          <div
            className={css({
              marginBottom: '4px',
            })}
          >
            Evaluated Widget Name
          </div>
          <StyledInput value={errMsg || finalTitle} readOnly error={!!errMsg} />
        </div>
        <ol
          className={css({
            fontSize: '14px',
            color: theme.colors.gray600,
            padding: 0,
            margin: '8px 0 0 16px ',
          })}
        >
          <li>
            You can have a dynamic widget name by embedding variables,
            <br />
            variable name can be used directly in your code.
            <div>
              <div
                className={css({
                  marginTop: '8px',
                  marginBottom: '8px',
                })}
              >
                For example:
                <pre
                  className={css({
                    display: 'inline',
                    paddingLeft: '10px',
                  })}
                >
                  {'${'}variables.<strong>variableName</strong>
                  {'}'}
                </pre>
              </div>
            </div>
          </li>
          <li>
            You can create variables by creating <strong>Inputs Widget</strong>.
          </li>
        </ol>
      </div>
      <ConfirmButtons
        onConfirm={() => {
          onClose();
          onChartTitleChanged(input);
        }}
        onCancel={() => {
          onClose();
        }}
      />
    </div>
  );

  return (
    <Popover
      isOpen={isOpen}
      onClickOutside={onClose}
      onEsc={onClose}
      showArrow={false}
      autoFocus={true}
      content={content}
      placement={PLACEMENT.bottom}
      overrides={{
        Body: {
          style: {
            backgroundColor: '#fff',
            marginTop: '8px',
            // todo
            // there seems have bug in popover's placement
            // should use float-ui library to do popover
            marginRight: '16px',
          },
        },
        Inner: {
          style: {
            backgroundColor: '#fff',
            paddingLeft: '8px',
            paddingRight: '8px',
            paddingTop: '8px',
            color: 'rgb(34, 34, 34)',
            fontSize: '14px',
          },
        },
      }}
    >
      {children}
    </Popover>
  );
}
