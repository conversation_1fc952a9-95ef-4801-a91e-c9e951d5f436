import { useNavigate } from 'react-router-dom';

import { SettingType } from '../type';
import { ChartProps } from '../../../chart/chart';
import { ActionParam, buildLinkFromAction } from '../../../components/action';
import { useCallback, useMemo } from 'react';

export default function useChartsAction(props: ChartProps) {
  const navigate = useNavigate();

  const { globalParameters, isEditMode, getLink } = props;
  const settings = props.settings as SettingType;
  const chartActions = useMemo(() => {
    return settings.chartActions || [];
  }, [settings.chartActions]);

  const tables = props.chartData.tables;
  const tableIndex = settings['tableIndex'] ?? 0;

  const { category, series } = settings;

  const getScatterValue = (value: string, data: any) => {
    try {
      const [type, key] = value.split('.');
      let node = null;
      if (data.value) {
        [, , node] = data.value;
      } else {
        [, , node] = data;
      }
      if (node && type && key) {
        if (node.type === type) {
          if (node.attrs && node.attrs[key]) return node.attrs[key];
        }
      }
      return '';
    } catch (error) {
      return '';
    }
  };

  const onChartClick = useCallback(
    (e) => {
      // for echarts click event, we need to find the original data from table based on settings and chart event (as this is how action use params)
      // 1. for non group charts, we just use x-axis key to find the row. (find from right to left to keep with consistent other data transformation)
      // 2. for group charts, we need use both x-axis key and groupKey.
      let target: object = e.data;
      const isScatter: boolean = e.seriesType === 'scatter';

      if (category?.[0] && tables?.[0]) {
        const xKey = category[0].id;
        const table = tables[tableIndex];

        const xValue = e.data[xKey];
        const groupKey = series?.[0]?.id;

        if (groupKey) {
          const seriesName = e.seriesName as string;
          for (let i = table.rows.length - 1; i >= 0; i--) {
            const row = table.rows[i];
            if (row[xKey] === xValue && row[groupKey] === seriesName) {
              target = row;
              break;
            }
          }
        } else {
          for (let i = table.rows.length - 1; i >= 0; i--) {
            const row = table.rows[i];
            if (row[xKey] === xValue) {
              target = row;
              break;
            }
          }
        }
      }

      if (chartActions.length > 0) {
        const action = chartActions[chartActions.length - 1];
        const getParam = (param: ActionParam) => {
          if (param.paramGlobalInput?.length > 0) {
            return {
              [param.name]: globalParameters[param.paramGlobalInput].value,
            };
          }
          return {
            [param.name]: param.isCreatable
              ? param.value
              : isScatter
              ? getScatterValue(param.value, e.data)
              : target[param.value],
          };
        };
        const href = buildLinkFromAction(action, getLink, getParam, isEditMode);
        if (action.pageID && !isEditMode) {
          navigate(href);
        } else {
          window.open(href, '_blank');
        }
      }
    },
    [category, chartActions, getLink, globalParameters, isEditMode, navigate, series, tableIndex, tables]
  );

  return onChartClick;
}
