import { useCallback } from 'react';
import { EChartsOption } from 'echarts-for-react';
import { Color } from '@tigergraph/tools-models/gvis/insights';
import { evaluateRules } from '../../../components/ruleStyle';

import { ChartProps } from '../../../chart/chart';
import { SettingType, CartesianCoordinateGroupMode } from '../type';
import { DatasetProps } from '../../../utils/dataTransform';
import { Vertex } from '../../../chart/schema';
const color = new Color();

function generateOption(props: ChartProps) {
  const { settings, chartData, globalParameters, schema } = props;
  const { graph } = chartData;
  const { nodes } = graph;
  const { values, rulesByType } = settings;
  if (!values) return [];
  return values.map((val) => {
    let serie: any = {
      name: val.name,
      symbolSize: 20,
      data: [],
      type: 'scatter',
      itemStyle: {
        color: schema.graph.vertices[val.name]?.style?.fillColor || color.getColor(val.name),
      },
      progressive: 0,
    };
    nodes.forEach((node) => {
      if (node.type === val.name) {
        if (rulesByType && rulesByType[node.type]) {
          const rules = rulesByType[node.type];
          let ruleNodeStyle = evaluateRules(node.attrs, rules, globalParameters, 1);
          serie.data.push({
            value: [node.attrs[val.xaxis], node.attrs[val.yaxis], node],
            itemStyle: {
              color: ruleNodeStyle['background-color'],
            },
            symbolSize: ruleNodeStyle['node-radius'] ? Number(ruleNodeStyle['node-radius']) * 20 : 20,
          });
        } else {
          serie.data.push([node.attrs[val.xaxis], node.attrs[val.yaxis], node]);
          serie.label = {
            formatter: () => {
              const labelKey = val.labelKey;

              if (!labelKey) {
                const primaryIDKey = (schema.graph.vertices[node.type] as Vertex)?.PrimaryId?.AttributeName;
                return node.attrs[primaryIDKey] ?? '';
              }

              if (labelKey === '<nothing>') {
                return '';
              }

              if (labelKey === 'id') {
                return node?.id || node?.attrs?.id;
              }
              return node?.attrs?.[labelKey];
            },
          };
        }
      }
    });
    return serie;
  });
}

function getAxisTypeBySchema(props: ChartProps) {
  const { settings } = props;
  const vertices = props?.schema?.graph?.vertices;
  let axis = {
    x: 'value',
    y: 'value',
  };
  const { values = [] } = settings;
  values.some((value) => {
    const xAxisAttribute = vertices[value.name]?.Attributes.find(
      (attribute) => attribute.AttributeName === value.xaxis && attribute.AttributeType.Name === 'DATETIME'
    );
    const yAxisAttribute = vertices[value.name]?.Attributes.find(
      (attribute) => attribute.AttributeName === value.yaxis && attribute.AttributeType.Name === 'DATETIME'
    );
    if (xAxisAttribute) {
      axis.x = 'time';
    }
    if (yAxisAttribute) {
      axis.y = 'time';
    }
    return axis.x === 'time' && axis.y === 'time';
  });
  return axis;
}

export default function useScatterCoordinate(props: ChartProps, chartType: string) {
  const settings = props.settings as SettingType;
  const series = generateOption(props);
  return useCallback(
    (option: EChartsOption, dataset: DatasetProps) => {
      const groupMode = settings.groupMode ?? [];
      const showLabel = settings['showLabel'] ?? true;
      const showDataZoom = settings['showDataZoom'] ?? false;
      const xNameGap = settings['xNameGap'] ?? 20;
      const yNameGap = settings['yNameGap'] ?? 20;
      const xRotate = settings['xRotate'] ?? 0;
      const yRotate = settings['yRotate'] ?? 0;
      option['dataset'] = dataset;

      option['xAxis'] = {
        type: getAxisTypeBySchema(props).x || 'value',
        axisLabel: {
          formatter: getAxisTypeBySchema(props).x === 'time' ? '{MM}-{dd}-{yyyy}' : null,
          showMaxLabel: true,
          showMinLabel: true,
          margin: xNameGap / 2,
        },
        axisLine: {
          show: true,
        },
      };
      option['yAxis'] = {
        type: getAxisTypeBySchema(props).y || 'value',
        nameTextStyle: {
          padding: [0, 0, 0, yNameGap],
        },
        axisLabel: {
          formatter: getAxisTypeBySchema(props).y === 'time' ? '{MM}-{dd}-{yyyy}' : null,
          showMaxLabel: true,
          showMinLabel: true,
          margin: yNameGap / 2,
        },
        axisLine: {
          show: true,
        },
      };

      if (showLabel) {
        option['label'] = {
          fontSize: 10,
          show: true,
          position:
            groupMode?.length > 0 && groupMode[0].id === CartesianCoordinateGroupMode.STACKED ? 'inside' : 'top',
        };
      }
      if (xRotate !== 0) {
        option.xAxis.axisLabel['rotate'] = xRotate;
      }
      if (yRotate !== 0) {
        option.yAxis.axisLabel['rotate'] = yRotate;
      }

      option.xAxis['nameLocation'] = 'middle';
      option.xAxis['nameTextStyle'] = { padding: [10, 0] };
      if (showDataZoom) {
        option['dataZoom'] = [
          {
            type: 'inside',
          },
          {
            type: 'slider',
            showDataShadow: false,
            handleIcon:
              'path://M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
            handleSize: '80%',
          },
          {
            type: 'inside',
            orient: 'vertical',
          },
          {
            type: 'slider',
            orient: 'vertical',
            showDataShadow: false,
            handleIcon:
              'path://M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
            handleSize: '80%',
          },
        ];
      }
      option['series'] = series;
      option['tooltip'] = {
        appendToBody: true,
        formatter: function (params) {
          let tooltipText = '';
          if (params.data && params.data.value) {
            tooltipText =
              '<span style="display: inline-block; width: 10px; height: 10px; border-radius: 10px; background-color: ' +
              params.color +
              ';"></span>&nbsp;' +
              params.seriesName +
              '<br />' +
              'X: ' +
              params.data.value[0] +
              '<br />' +
              'Y: ' +
              params.data.value[1];
          } else {
            tooltipText =
              '<span style="display: inline-block; width: 10px; height: 10px; border-radius: 10px; background-color: ' +
              params.color +
              ';"></span>&nbsp;' +
              params.seriesName +
              '<br />' +
              'X: ' +
              params.data[0] +
              '<br />' +
              'Y: ' +
              params.data[1];
          }
          return tooltipText;
        },
      };

      return option;
    },
    [settings, series, props]
  );
}
