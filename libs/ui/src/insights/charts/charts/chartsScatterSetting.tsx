import React, { useMemo, useState } from 'react';
import { But<PERSON> } from '@tigergraph/app-ui-lib/button';
import { KIND, SHAPE, SIZE } from 'baseui/button';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { arrayMove, arrayRemove } from 'react-movable';
import shortUUID from 'short-uuid';
import { MdAdd } from '@react-icons/all-files/md/MdAdd';
import { Rules } from '../../components/ruleStyle';

import { ChartSettingProps } from '../../chart/chart';
import { StyledDraggableList } from '../../components/styledDraggableList';
import ChartSettingSectionContainer from '../chartTools/chartSetting/chartSettingSectionContainer';
import { ChartSettingItemContainer } from '../chartTools/chartSetting/chartSettingItemContainer';
import IconButton from '../../components/iconButton';
import { StyledSelect } from '../../components';

import { ReactComponent as TriangleDown } from '../../assets/images/triangleDown.svg';
import { ReactComponent as TriangleRight } from '../../assets/images/triangleRight.svg';
import { getKeyValues } from '../multiValue/util';
import { getNodesAttributes } from '../graphNew/data';
import GraphStyle from '../graphNew/ui/graphStyle';
import {
  ChartsScatterAxisSetting,
  ChartsInfoSetting,
  ChartsScatterInfoSetting,
  ChartsAction,
} from './chartsSettingTools';

export type ValueType = {
  id: string;
  styleRule: Rules;
  name: string;
  label: string;
  xaxis: string;
  yaxis: string;
  labelKey: string;
};

export type SettingType = {
  values: ValueType[];
  horizontalAlign: 'left' | 'center' | 'right';
  verticalAlign: 'bottom' | 'middle' | 'top';
  textAlign?: 'start' | 'end' | 'left' | 'right' | 'center' | 'justify' | 'match-parent';
  labelPostion?: string;
};

function newValue(): ValueType {
  return {
    id: shortUUID.generate(),
    styleRule: [],
    name: '',
    label: 'untitled',
    xaxis: '',
    yaxis: '',
    labelKey: '',
  };
}

const nodeRuleConfigs: any[] = [
  {
    styleKey: 'background-color',
    styleLabel: 'Vertex color',
    styleType: 'color',
  },
  {
    styleKey: 'node-radius',
    styleLabel: 'Vertex size',
    styleType: 'numeric',
    styleOptions: [
      {
        label: '0.25x',
        value: 0.25,
      },
      {
        label: '0.5x',
        value: 0.5,
      },
      {
        label: '1x',
        value: 1,
      },
      {
        label: '2x',
        value: 2,
      },
      {
        label: '4x',
        value: 4,
      },
    ],
  },
];

export default function ChartsScatterSetting(props: ChartSettingProps) {
  const [, theme] = useStyletron();

  const { onSettingUpdate, chartData } = props;
  const settings = props.settings as SettingType;

  const { values = [] } = settings;

  const keyValues = useMemo(() => {
    return getKeyValues(chartData);
  }, [chartData]);

  const onAddValue = () => {
    onSettingUpdate('values', [...values, newValue()]);
  };

  const onValueChange = (value: ValueType, index: number) => {
    onSettingUpdate('values', [...values.slice(0, index), value, ...values.slice(index + 1)]);
  };
  return (
    <>
      <ChartSettingSectionContainer title={'Data'}>
        <ChartSettingItemContainer
          label="Series"
          helpText="The list of values to be shown on the dashboard"
          right={
            <IconButton onClick={onAddValue}>
              <MdAdd size={16} color={theme.colors.accent} />
            </IconButton>
          }
        >
          <StyledDraggableList
            items={values.map((value, index) => (
              <ValueItem
                key={value.id}
                value={value}
                index={index}
                onValueChange={onValueChange}
                keyValues={keyValues}
                chartSetting={props}
              />
            ))}
            onChange={({ oldIndex, newIndex }) => onSettingUpdate('values', arrayMove(values, oldIndex, newIndex))}
            onRemove={(index) => {
              onSettingUpdate('values', arrayRemove(values, index));
            }}
          />
        </ChartSettingItemContainer>
      </ChartSettingSectionContainer>
      <ChartSettingSectionContainer title="Display">
        <ChartsScatterAxisSetting {...props} />
        <ChartsInfoSetting {...props} />
        <ChartsScatterInfoSetting {...props} />
        {props.chartData?.graph ? (
          <ChartSettingItemContainer
            style={{ marginTop: '7px' }}
            label="Conditional Styling"
            helpText="Specify a rule-based style to enrich visualization. You can define simple rules based on expression evaluation, select a color palette based on the range of an attribute value, or let the system pick a color based on the uniqueness of the value."
          >
            <GraphStyle {...props} chartData={chartData} ruleConfigs={nodeRuleConfigs} chartType="scatter" />
          </ChartSettingItemContainer>
        ) : null}
      </ChartSettingSectionContainer>
      <ChartSettingSectionContainer title="Advanced">
        <ChartsAction {...props} />
      </ChartSettingSectionContainer>
    </>
  );
}

function ValueItem({
  value,
  index,
  onValueChange,
  keyValues,
  chartSetting,
}: {
  value: ValueType;
  index: number;
  onValueChange: (value: ValueType, index: number) => void;
  keyValues: ReturnType<typeof getKeyValues>;
  chartSetting: ChartSettingProps;
}) {
  const [css] = useStyletron();
  const [expand, setExpand] = useState(false);
  const { chartData, settings } = chartSetting;
  const { graph } = chartData;
  const { values = [] } = settings;
  const nodeAttributes = useMemo(() => getNodesAttributes(graph), [graph]);
  const vertexTypes = useMemo(() => {
    return Array.from(nodeAttributes.keys()).filter((type) => {
      return !values.map((v) => v.name).includes(type);
    });
  }, [nodeAttributes, values]);
  const xaxis = useMemo(() => {
    if (!value.name) {
      return [];
    }
    try {
      return Array.from(nodeAttributes.get(value.name).keys()) || [];
    } catch (error) {
      return [];
    }
  }, [value.name, nodeAttributes]);
  const yaxis = xaxis;
  const labelKeys = ['<nothing>', ...xaxis];

  return (
    <div>
      <div
        className={css({
          display: 'flex',
          alignItems: 'center',
          columnGap: '8px',
        })}
        onClick={() => setExpand(!expand)}
      >
        <Button kind="text" shape={SHAPE.square}>
          {expand ? <TriangleDown /> : <TriangleRight />}
        </Button>
        <div
          className={css({
            flex: 1,
            overflow: 'hidden',
            whiteSpace: 'nowrap',
            textOverflow: 'ellipsis',
          })}
        >
          {value.label}
        </div>
      </div>
      {expand ? (
        <>
          {!value.name && (
            <ChartSettingItemContainer label="Vertices" helpText="Vertices">
              <StyledSelect
                maxDropdownHeight={'400px'}
                options={vertexTypes.map((key) => ({ id: key, label: key }))}
                value={value.name ? [{ id: value.name }] : []}
                onChange={({ option }) => {
                  onValueChange(
                    {
                      ...value,
                      name: String(option.id),
                      label: String(option.id),
                    },
                    index
                  );
                }}
                clearable={false}
              />
            </ChartSettingItemContainer>
          )}
          {value.name && (
            <>
              <ChartSettingItemContainer label="X-Axis" helpText="Index of data item categories">
                <StyledSelect
                  maxDropdownHeight={'400px'}
                  options={xaxis.map((key) => ({ id: key, label: key }))}
                  value={value.xaxis ? [{ id: value.xaxis }] : []}
                  onChange={({ option }) => {
                    onValueChange(
                      {
                        ...value,
                        xaxis: String(option.id),
                      },
                      index
                    );
                  }}
                  clearable={false}
                />
              </ChartSettingItemContainer>
              <ChartSettingItemContainer label="Y-Axis" helpText="Data item value">
                <StyledSelect
                  maxDropdownHeight={'400px'}
                  options={yaxis.map((key) => ({ id: key, label: key }))}
                  value={value.yaxis ? [{ id: value.yaxis }] : []}
                  onChange={({ option }) => {
                    onValueChange(
                      {
                        ...value,
                        yaxis: String(option.id),
                      },
                      index
                    );
                  }}
                  clearable={false}
                />
              </ChartSettingItemContainer>
              <ChartSettingItemContainer label="Label key" helpText="Used for the label">
                <StyledSelect
                  maxDropdownHeight={'400px'}
                  options={labelKeys.map((key) => ({ id: key, label: key }))}
                  value={value.labelKey ? [{ id: value.labelKey }] : []}
                  onChange={({ option }) => {
                    onValueChange(
                      {
                        ...value,
                        labelKey: String(option.id),
                      },
                      index
                    );
                  }}
                  clearable={false}
                />
              </ChartSettingItemContainer>
            </>
          )}
        </>
      ) : null}
    </div>
  );
}
