import React, { useMemo } from 'react';
import { ChartSettingItemContainer } from '../../chartTools/chartSetting/chartSettingItemContainer';
import { StyledSelect } from '../../../components';
import { Aggregation, CartesianCoordinateGroupMode, SettingType } from '../type';
import { ChartSettingProps } from '../../../chart/chart';
import { createCategoryAndValue } from '../../../utils/dataTransform';
import BarChartVertical from '../../../assets/images/barChartVertical.svg';
import BarChartStacked from '../../../assets/images/barChartStacked.svg';
import IconButtonGroup from '../../../components/iconButtonGroup';

export default function ChartsAggregationSetting(props: ChartSettingProps) {
  const { onSettingUpdate, chartData } = props;
  const settings = props.settings as SettingType;

  const tables = chartData?.tables;
  const value = settings.value ?? [];
  const category = settings.category ?? [];
  const series = settings.series ?? [];
  const groupMode = settings.groupMode ?? [];
  const valueAggregation = settings.valueAggregation ?? [];
  const tableIndex = settings['tableIndex'] ?? 0;

  const [categories] = useMemo(() => {
    return tables && tables[tableIndex] ? createCategoryAndValue(tables[tableIndex]) : [[], []];
  }, [tables, tableIndex]);

  const filteredSeries = categories.filter(
    (item) => category.length > 0 && item.id !== category[0].id && value.length > 0 && item.id !== value[0].id
  );

  const groupModeItems = [
    {
      label: CartesianCoordinateGroupMode.GROUPED,
      icon: <BarChartVertical />,
    },
    {
      label: CartesianCoordinateGroupMode.STACKED,
      icon: <BarChartStacked />,
    },
  ];

  return (
    <>
      {category.length > 0 && value.length > 0 && (
        <ChartSettingItemContainer label="Grouping" helpText="Group rows that have the same values into summary rows">
          <StyledSelect
            value={series}
            options={filteredSeries}
            disabled={value.length >= 2}
            onChange={({ value }) => {
              onSettingUpdate('series', value);
            }}
            placeholder="Select grouping"
            labelKey="id"
          />
        </ChartSettingItemContainer>
      )}
      {category.length > 0 && (
        <ChartSettingItemContainer
          label="Value Aggregation"
          helpText="Multiple row values are grouped together to form a single summary value"
        >
          <StyledSelect
            value={valueAggregation}
            options={
              value.length > 0 && value[0].type === 'number'
                ? Object.keys(Aggregation).map((item) => ({ id: item }))
                : [{ id: Aggregation.COUNT }]
            }
            onChange={({ value }) => {
              onSettingUpdate('valueAggregation', value);
            }}
            labelKey="id"
            placeholder="Select the value aggregation"
          />
        </ChartSettingItemContainer>
      )}
      <ChartSettingItemContainer label="Group Mode" helpText="Values will be grouped by category name">
        <IconButtonGroup
          items={groupModeItems}
          selected={groupMode[0]?.id ?? groupModeItems[0].label}
          onClick={(index) => onSettingUpdate('groupMode', [{ id: groupModeItems[index].label }])}
        />
      </ChartSettingItemContainer>
    </>
  );
}
