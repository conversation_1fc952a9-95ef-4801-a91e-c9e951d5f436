import React from 'react';
import { ChartSettingItemContainer } from '../../chartTools/chartSetting/chartSettingItemContainer';
import { StyledSelect } from '../../../components';
import { ChartDirection, SettingType } from '../type';
import { ChartSettingProps } from '../../../chart/chart';
import BrushHorizontal from '../../../assets/images/brushHorizontal.svg';
import BrushVertical from '../../../assets/images/brushVertical.svg';
import IconButtonGroup from '../../../components/iconButtonGroup';
import StyledToggle from '../../../components/styledToggle';
import HelpText from '../../chartTools/chartSetting/helpText';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';

export default function ChartsBrushAction(props: ChartSettingProps) {
  const { onSettingUpdate, globalParameters } = props;
  const settings = props.settings as SettingType;
  const [css, theme] = useStyletron();

  const showBrush = settings['showBrush'] ?? false;
  const brushDirection = settings['brushDirection'] ?? ChartDirection.HORIZONTAL;
  const lowerBoundParameterName = settings['lowerBoundParameterName'] ?? '';
  const upperBoundParameterName = settings['upperBoundParameterName'] ?? '';

  const onChangeInputName = (field: string, name: string) => {
    onSettingUpdate(field, name);
  };

  const brushItems = [
    {
      label: ChartDirection.HORIZONTAL,
      icon: <BrushHorizontal />,
    },
    {
      label: ChartDirection.VERTICAL,
      icon: <BrushVertical />,
    },
  ];

  return (
    <>
      <ChartSettingItemContainer>
        <StyledToggle
          checked={showBrush}
          onChange={(e) => {
            onSettingUpdate('showBrush', e.target['checked']);
          }}
        >
          <HelpText content="Select an area on the chart with the range bound to two inputs">Use Selection</HelpText>
        </StyledToggle>
      </ChartSettingItemContainer>
      {showBrush && (
        <>
          <div
            className={css({
              fontSize: '12px',
              fontStyle: 'italic',
              color: theme.colors.black03,
            })}
          >
            This setting enables you to manually adjust the chart's axis range. The specified range will automatically
            update the selected variables below, triggering a rerun of the relevant queries.
          </div>
          <ChartSettingItemContainer
            label="Lower Bound Variable Name"
            helpText="Select a variable name to bind to the lower bound"
          >
            <StyledSelect
              value={[{ id: lowerBoundParameterName }]}
              options={Object.keys(globalParameters).map((item) => ({ id: item }))}
              onChange={({ value }) => {
                onChangeInputName('lowerBoundParameterName', String(value[0]?.id ?? ''));
              }}
              placeholder="Chart lower bound"
              labelKey="id"
            />
          </ChartSettingItemContainer>
          <ChartSettingItemContainer
            label="Upper Bound Variable Name"
            helpText="Select a variable name to bind to the upper bound"
          >
            <StyledSelect
              value={[{ id: upperBoundParameterName }]}
              options={Object.keys(globalParameters).map((item) => ({ id: item }))}
              onChange={({ value }) => {
                onChangeInputName('upperBoundParameterName', String(value[0]?.id ?? ''));
              }}
              placeholder="Chart upper bound"
              labelKey="id"
            />
          </ChartSettingItemContainer>
          <ChartSettingItemContainer
            label="Brush Type"
            helpText="Choose the brush type to be either vertical or horizontal"
          >
            <IconButtonGroup
              items={brushItems}
              selected={brushDirection}
              onClick={(index) => onSettingUpdate('brushDirection', brushItems[index].label)}
            />
          </ChartSettingItemContainer>
        </>
      )}
    </>
  );
}
