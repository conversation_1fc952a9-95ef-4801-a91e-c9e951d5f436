import React from 'react';
import { ChartSettingItemContainer } from '../../chartTools/chartSetting/chartSettingItemContainer';
import { SettingType } from '../type';
import { ChartSettingProps } from '../../../chart/chart';
import StyledSlider from '../../../components/styledSlider';

export default function ChartsScatterAxisSetting(props: ChartSettingProps) {
  const { onSettingUpdate } = props;
  const settings = props.settings as SettingType;

  const xNameGap = settings['xNameGap'] ?? 20;
  const yNameGap = settings['yNameGap'] ?? 20;
  const xRotate = settings['xRotate'] ?? 0;
  const yRotate = settings['yRotate'] ?? 0;

  return (
    <>
      <ChartSettingItemContainer label="X-Axis Name Spacing" helpText="Space between axis name and axis line">
        <StyledSlider
          value={[xNameGap]}
          min={0}
          max={100}
          onChange={({ value }) => {
            onSettingUpdate('xNameGap', value[0]);
          }}
        />
      </ChartSettingItemContainer>
      <ChartSettingItemContainer label="X-Axis Rotation" helpText="Rotation of axis name">
        <StyledSlider
          value={[xRotate]}
          min={0}
          max={90}
          onChange={({ value }) => {
            onSettingUpdate('xRotate', value[0]);
          }}
        />
      </ChartSettingItemContainer>
      <ChartSettingItemContainer label="Y-Axis Name Spacing" helpText="Space between axis name and axis line">
        <StyledSlider
          value={[yNameGap]}
          min={0}
          max={100}
          onChange={({ value }) => {
            onSettingUpdate('yNameGap', value[0]);
          }}
        />
      </ChartSettingItemContainer>
      <ChartSettingItemContainer label="Y-Axis Rotation" helpText="Rotation of axis name">
        <StyledSlider
          value={[yRotate]}
          min={0}
          max={90}
          onChange={({ value }) => {
            onSettingUpdate('yRotate', value[0]);
          }}
        />
      </ChartSettingItemContainer>
    </>
  );
}
