import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@tigergraph/tools-models/visual-query-pattern';
import { getLastVertexEdge, getNextVertexEdge } from '../chartSearch/util';
import { OPERANDS_TYPES, SEARCH_KEYWORDS, SEARCH_OPERATOR } from '../chartSearch/type';
import { SearchItem } from '../chartSlice';
import { Edge, Vertex } from '@tigergraph/app-ui-lib/lineage';
import { Graph, GSQLGraphJson } from '@tigergraph/tools-models/topology';
import { ConditionJson } from '@tigergraph/tools-models/expression';
import { AliasSchemaType, buildAliasSchema } from '@tigergraph/tools-models/query-builder';
import { ChartSchema } from '../../chart/schema';
import { buildSchemaVertexIdMap } from '../../utils/schemaVertexIdMapping';
import { GlobalParams } from '../../chart/globalParams';
import { valueComparator } from '../../utils';
export { Pattern } from '@tigergraph/tools-models/visual-query-pattern';

export interface PatternView {
  [key: string]: Vertex | Edge;
}

export const buildPatternJson = (
  searchPattern: Array<SearchItem>,
  globalParameters: GlobalParams,
  patternLimit: number
): PatternJson => {
  const patternVertexSets = [],
    patternEdgeSets = [],
    patternViews = [];
  const patternJson = {
    name: 'search_pattern',
    description: '',
    patternWidgets: [],
    version: '3.3',
    filters: [],
    aggregations: [],
    postAggregationFilters: [],
    orderBy: [],
    skip: 0,
    limit: -1,
    patternVertexSets,
    patternEdgeSets,
    patternViews,
  } as PatternJson;
  const patternVertex = {
    id: '',
    alias: '',
    output: false,
    existentialCheck: false,
    position: {
      x: 0,
      y: 0,
    },
    vertices: [],
    isParam: false,
    ids: [],
    paramName: '',
    filters: [],
    aggregations: [],
    orderBy: [],
    hasLimit: false,
    limit: 10,
  };
  const patternEdge = {
    id: '',
    alias: '',
    output: false,
    existentialCheck: false,
    position: {
      x: 0,
      y: 0,
    },
    sourcePatternId: '',
    targetPatternId: '',
    edges: [],
    repeatAtLeast: 1,
    repeatAtMost: 1,
    notExist: false,
    filters: [],
  };
  const patternView = {
    id: 'pattern_view',
    alias: 'pattern_view',
    output: true,
    existentialCheck: false,
    position: {
      x: 0,
      y: 0,
    },
    vertexSetIds: [],
    edgeSetIds: [],
    filters: [],
    orderBy: [],
    hasLimit: true,
    limit: patternLimit || 500,
  };

  for (let i = 0; i < searchPattern.length; i++) {
    const item = searchPattern[i];
    const id = `${item.data}_${i}`;
    if (item.type === 'vertex') {
      patternVertexSets.push({
        ...JSON.parse(JSON.stringify(patternVertex)),
        id: id,
        vertices: [item.data],
        orderBy: item.orderBy || [],
      });
      if (item.orderBy) {
        patternView.orderBy = item.orderBy;
      }
      patternView.vertexSetIds.push(id);
    } else if (
      patternVertexSets.length > 0 &&
      item.type === 'edge' &&
      i > 0 &&
      item !== getLastVertexEdge(searchPattern, 1)
    ) {
      const [nextItem, nextIndex] = getNextVertexEdge(searchPattern, i + 1);
      const source = patternVertexSets[patternVertexSets.length - 1].id,
        target = `${nextItem.data}_${nextIndex}`;
      patternEdgeSets.push({
        ...JSON.parse(JSON.stringify(patternEdge)),
        id: id,
        sourcePatternId: item.direction === 0 || item.direction === 1 ? source : target,
        targetPatternId: item.direction === 0 || item.direction === 1 ? target : source,
        edges: [item.data],
      });
      patternView.edgeSetIds.push(id);
    } else if (patternVertexSets.length > 0 && item.type === SEARCH_KEYWORDS.param) {
      if (!item.paramGlobalInput && !item.data) {
        throw Error('Invalid filter.');
      }
      if (item.paramName === 'id') {
        if (item.paramGlobalInput && item.paramGlobalInput in globalParameters) {
          patternVertexSets[patternVertexSets.length - 1].ids.push(
            String(globalParameters[item.paramGlobalInput].value)
          );
        } else {
          patternVertexSets[patternVertexSets.length - 1].ids.push(String(item.data));
        }
      } else if (item.paramName === 'vertex_limit') {
        patternVertexSets[patternVertexSets.length - 1].hasLimit = true;
        if (item.paramGlobalInput && item.paramGlobalInput in globalParameters) {
          patternVertexSets[patternVertexSets.length - 1].limit = globalParameters[item.paramGlobalInput].value;
        } else {
          patternVertexSets[patternVertexSets.length - 1].limit = item.data;
        }
      } else {
        const filterOption = {
          operands: [],
          operator: item.paramOperator ? SEARCH_OPERATOR[item.paramOperator] : SEARCH_OPERATOR['eq'],
          type: 'ComparisonCondition',
        };
        const lastItem = getLastVertexEdge(searchPattern.slice(0, i), 1);
        const patternSets = lastItem.type === 'vertex' ? patternVertexSets : patternEdgeSets;
        filterOption.operands.push({
          type: 'AttrVariable',
          value: `alias_schema_${patternSets[patternSets.length - 1].id}.${item.paramName}`,
          valueType: 'Real',
        });
        if (!OPERANDS_TYPES[item.paramType.toLowerCase()]) {
          throw new Error('The data type is unsupported yet but will be supported later.');
        }
        if (item.paramGlobalInput && item.paramGlobalInput in globalParameters) {
          filterOption.operands.push({
            type: OPERANDS_TYPES[item.paramType.toLowerCase()],
            value: globalParameters[item.paramGlobalInput].value,
          });
        } else {
          filterOption.operands.push({
            type: OPERANDS_TYPES[item.paramType.toLowerCase()],
            value: item.data,
          });
        }
        patternSets[patternSets.length - 1].filters.push(filterOption as ConditionJson);
      }
    }
  }
  patternViews.push(patternView);
  return patternJson;
};

export const buildPatternQuery = (
  searchPattern: Array<SearchItem>,
  schemaResults: GSQLGraphJson,
  globalParameters: GlobalParams,
  patternLimit: number
) => {
  const patternJson = buildPatternJson(searchPattern, globalParameters, patternLimit);
  const GSQLGraph = new Graph();
  GSQLGraph.loadFromGSQLJson(schemaResults);
  const pattern = new Pattern(
    patternJson,
    buildAliasSchema(GSQLGraph, patternJson, AliasSchemaType.INITIAL_ALIAS_SCHEMA)
  );
  const gsql = pattern.toGSQL(GSQLGraph);
  const regexp = new RegExp(/\bCREATE\b[\s\S]*?\bQUERY\b[\s\S]*?\(/i);
  return gsql.replace(regexp, 'INTERPRET QUERY (');
};

export const processPatternData = (results: any, searchPattern: Array<SearchItem>, schema: ChartSchema) => {
  // const id = `${item.data}_${typeCount[item.data] > 1 ? typeCount[item.data] : ''}`;
  // target = `${searchPattern[i + 1].data}_${searchPattern[i + 1].data in typeCount ? (typeCount[searchPattern[i + 1].data] + 1) : ''}`;

  const vertexIdMap = buildSchemaVertexIdMap(schema);
  const patternView = results[0]['pattern_view'].map((pattern) => {
    const typeCount = {};
    return Object.keys(pattern)
      .sort((a, b) => {
        return a.length === b.length ? a.localeCompare(b) : a.length - b.length;
      })
      .reduce((prev, key, index) => {
        const value = pattern[key];
        prev['Matched pattern'] = [];
        if (typeof value === 'string') {
          const vertex = (Object.values(results[index + 1])[0] as Vertex[]).find((v) => v['v_id'] === value);
          const type = vertex['v_type'];
          typeCount[type] = (type in typeCount ? typeCount[type] : 0) + 1;
          vertex.attributes = { [vertexIdMap[type]]: vertex['v_id'], ...vertex.attributes };
          prev[`${type}${typeCount[type] > 1 ? ` (${typeCount[type]})` : ''}`] = vertex;
        } else {
          const type = value['e_type'];
          typeCount[type] = (type in typeCount ? typeCount[type] : 0) + 1;
          prev[`${type}${typeCount[type] > 1 ? ` (${typeCount[type]})` : ''}`] = value;
        }
        return prev;
      }, {});
  });

  // build matched pattern
  patternView.forEach((pattern) => {
    const typeCount = {};
    searchPattern.forEach((item, i) => {
      if (item.type === 'vertex') {
        const type = item.data;
        typeCount[type] = (type in typeCount ? typeCount[type] : 0) + 1;
        pattern['Matched pattern'].push({
          ...item,
          id: pattern[`${type}${typeCount[type] > 1 ? ` (${typeCount[type]})` : ''}`].v_id,
        });
      } else if (item.type === 'edge' && item !== getLastVertexEdge(searchPattern, 1)) {
        pattern['Matched pattern'].push({ ...item });
      }
    });
  });

  // sort with orderBy
  const seenTypes = {};
  for (let item of searchPattern) {
    if (item.type === SEARCH_KEYWORDS.vertex || item.type === SEARCH_KEYWORDS.edge) {
      const type = item.data;
      seenTypes[type] = (type in seenTypes ? seenTypes[type] : 0) + 1;
      if (item.orderBy?.length > 0) {
        const key = seenTypes[type] > 1 ? `${type} (${seenTypes[type]})` : type;
        const attr = item.orderBy[0].label.split('.')[1];
        if (attr === vertexIdMap[type]) {
          patternView.sort((a: Vertex, b: Vertex) => {
            return valueComparator(a[key].v_id, b[key].v_id);
          });
        } else {
          patternView.sort((a: Vertex | Edge, b: Vertex | Edge) => {
            return valueComparator(a[key]['attributes'][attr], b[key]['attributes'][attr]);
          });
        }
        if (!item.orderBy[0].asc) {
          patternView.reverse();
        }
        break;
      }
    }
  }

  return [{ patternView }];
};
