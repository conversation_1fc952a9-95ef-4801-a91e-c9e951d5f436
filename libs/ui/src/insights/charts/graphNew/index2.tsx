import React from 'react';
import { Graph } from '../../../graph';
import { CytoscapeExtensions } from '../../../graph/type';
import { Core } from 'cytoscape';
import { useEffect, useRef, useMemo, useState } from 'react';

import { ChartProps } from '../../chart/chart';
import { convertGraphToCytoscape } from '../../../graph/data';
import { GraphFilter, filterGraphData, GraphFilters } from './graphFilter';
import { useNavigate } from 'react-router-dom';

import { getUserUploadedIconPathPrefix } from '../../utils/path';

export default function Graph2(props: ChartProps & { isSchemaGraph?: boolean }) {
  const navigate = useNavigate();
  const userUploadedIconPathPrefix = useMemo(() => {
    return getUserUploadedIconPathPrefix(props.isCloud);
  }, [props.isCloud]);

  const {
    schema,
    chartData,
    isSchemaGraph,
    settings,
    onSettingUpdate,
    id,
    graphName,
    onChartStateUpdate,
    getLink,
    links,
    isEditMode,
    themeType,
    baseURL,
    emptyNodesLinksView,
  } = props;
  const cyRef = useRef<(Core & CytoscapeExtensions) | null>(null);

  const [graph, setGraph] = useState(() => {
    return chartData.graph;
  });

  // notify setting ui graph is changed.
  useEffect(() => {
    onChartStateUpdate('graph', graph);
  }, [onChartStateUpdate, graph]);

  const graphSettings = useMemo(() => {
    return {
      ...settings,
      showUndoRedo: true,
    };
  }, [settings]);

  // graph filtering
  useEffect(() => {
    const graphFilters: GraphFilters = settings.graphFilters ?? {
      nodes: {},
      links: {},
    };
    const activeElements = convertGraphToCytoscape(filterGraphData(graph, graphFilters)[0]),
      inactiveElements = convertGraphToCytoscape(filterGraphData(graph, graphFilters)[1]);

    for (let ele of activeElements) {
      cyRef.current.getElementById(ele.data.id).removeClass('filterInactive');
    }
    for (let ele of inactiveElements) {
      cyRef.current.getElementById(ele.data.id).addClass('filterInactive');
    }
  }, [graph, settings.graphFilters]);

  if (graph.links.length <= 0 && graph.nodes.length <= 0 && typeof emptyNodesLinksView === 'function') {
    return emptyNodesLinksView();
  }

  return (
    <Graph
      baseURL={baseURL}
      id={id}
      parentRef={cyRef}
      graphName={graphName}
      schema={schema.results}
      isSchemaGraph={isSchemaGraph}
      graph={graph}
      onGraphChange={setGraph}
      settings={graphSettings}
      onSettingUpdate={onSettingUpdate}
      links={links}
      getLink={getLink}
      navigate={navigate}
      insights={true}
      isWidgetEditMode={isEditMode}
      userUploadedIconPathPrefix={userUploadedIconPathPrefix}
      themeType={themeType}
    >
      {!isSchemaGraph ? (
        <GraphFilter graphData={graph} schema={schema} settings={settings} onSettingUpdate={props.onSettingUpdate} />
      ) : null}
    </Graph>
  );
}
