import React, { useMemo } from 'react';

import { ChartSettingProps } from '../../chart/chart';
import { Rules } from '../../components/ruleStyle';

import { StyleDesc } from '../../components/ruleStyle';
import { ChartSettingItemContainer } from '../chartTools/chartSetting/chartSettingItemContainer';
import GraphStyle from './ui/graphStyle';
import { getLayoutTemplateByLayout, getLayoutByLabel } from './layouts';
import TemplateDrivenChartSettings from '../chartTools/chartSetting/templateDrivenChartSettings';
import { ChartSettingsTemplate } from '../../chart/chartRegister';
import ChartSettingSectionContainer from '../chartTools/chartSetting/chartSettingSectionContainer';
import { ExternalGraph } from '@tigergraph/tools-models/gvis/insights';
import { Action } from '../../components/action';
import { GraphFilters } from './graphFilter';
import GraphAction from './ui/graphAction';
import GraphDetail from './ui/graphDetail';

const nodeRuleConfigs: StyleDesc[] = [
  {
    styleKey: 'background-color',
    styleLabel: 'Vertex color',
    styleType: 'color',
  },
  {
    styleKey: 'node-radius',
    styleLabel: 'Vertex size',
    styleType: 'numeric',
    styleOptions: [
      {
        label: '0.25x',
        value: 0.25,
      },
      {
        label: '0.5x',
        value: 0.5,
      },
      {
        label: '1x',
        value: 1,
      },
      {
        label: '2x',
        value: 2,
      },
      {
        label: '4x',
        value: 4,
      },
    ],
  },
];

const edgeRuleConfigs: StyleDesc[] = [
  {
    styleKey: 'line-color',
    styleLabel: 'Edge color',
    styleType: 'color',
  },
  {
    styleKey: 'line-style',
    styleLabel: 'Edge type',
    styleType: 'edgetype',
  },
  {
    styleKey: 'edge-width',
    styleLabel: 'Edge width',
    styleType: 'numeric',
    styleOptions: [
      {
        label: '2',
        value: 2,
      },
      {
        label: '3',
        value: 3,
      },
      {
        label: '4',
        value: 4,
      },
      {
        label: '5',
        value: 5,
      },
      {
        label: '6',
        value: 6,
      },
    ],
  },
];

const ruleConfigs = {
  nodeRuleConfigs,
  edgeRuleConfigs,
};

export type SettingType = {
  layout: string;

  idealEdgeLength: number;
  nodeSeparation: number;
  nodeRepulsion: number;

  edgeLength: number;

  spacingFactor: number;

  nodeSep: number;
  edgeSep: number;
  rankSep: number;
  rankDir: string;
  align: string;

  actionsByType: Record<string, Action[]>;
  rulesByType: Record<string, Rules>;
  graphFilters?: GraphFilters;
};

export const layoutOptions: ChartSettingsTemplate = {
  layout: {
    label: 'Default Layout',
    type: 'list',
    values: ['Force', 'Radial', 'Tree', 'Circle', 'Grid', 'Layered'],
    default: 'Force',
    helpText: 'Default graph layout. This can be changed by viewers later.',
  },
};

export const commonOptions: ChartSettingsTemplate = {
  freeExplore: {
    label: 'Show Search Bar',
    type: 'toggle',
    default: false,
    helpText: 'Show Search Bar when in application page.',
  },
};

export const advancedOptions: ChartSettingsTemplate = {
  disablePathFinding: {
    label: 'Disable PathFinding',
    type: 'toggle',
    default: false,
    helpText: 'Hide PathFinding menu.',
  },
};

function getTemplateDefaultValue(template: ChartSettingsTemplate): Record<string, any> {
  return Object.keys(template).reduce((acc, key) => {
    const settingType = template[key];
    return {
      ...acc,
      [key]: settingType.default,
    };
  }, {});
}

export function GraphSettings(props: ChartSettingProps) {
  let { settings, onSettingUpdate, chartStates, chartData, setGlobalParameter, getGlobalParameter } = props;
  let layoutLabelFromSettings = settings['layout'] || 'Force';
  if (layoutLabelFromSettings === 'Force Cola') {
    layoutLabelFromSettings = 'Force';
  }
  const layoutTemplate = getLayoutTemplateByLayout(getLayoutByLabel(layoutLabelFromSettings));

  const layoutSettingDefaultValues = getTemplateDefaultValue(layoutTemplate);
  const commonDefaultValue = getTemplateDefaultValue(commonOptions);

  const effectSettings = {
    ...layoutSettingDefaultValues,
    ...commonDefaultValue,
    ...settings,
    layout: layoutLabelFromSettings,
  };

  const graph: ExternalGraph = useMemo(() => {
    return (
      chartStates['graph'] || {
        links: [],
        nodes: [],
      }
    );
  }, [chartStates]);

  chartData = {
    /* eslint-disable-next-line */
    ...chartData,
    graph,
  };

  return (
    <>
      <ChartSettingSectionContainer title="Display">
        <TemplateDrivenChartSettings
          settingsTemplate={layoutOptions}
          settings={effectSettings}
          updateSetting={onSettingUpdate}
          setGlobalParameter={setGlobalParameter}
          getGlobalParameter={getGlobalParameter}
        />
        <TemplateDrivenChartSettings
          settingsTemplate={layoutTemplate}
          settings={effectSettings}
          updateSetting={onSettingUpdate}
          setGlobalParameter={setGlobalParameter}
          getGlobalParameter={getGlobalParameter}
        />
        <TemplateDrivenChartSettings
          settingsTemplate={commonOptions}
          settings={effectSettings}
          updateSetting={onSettingUpdate}
          setGlobalParameter={setGlobalParameter}
          getGlobalParameter={getGlobalParameter}
        />
        {props.chartData?.graph ? (
          <ChartSettingItemContainer
            style={{ marginTop: '7px' }}
            label="Display Available Label"
            helpText="Display Available Label"
          >
            <GraphDetail {...props} chartData={chartData} />
          </ChartSettingItemContainer>
        ) : null}
        {props.chartData?.graph ? (
          <ChartSettingItemContainer
            style={{ marginTop: '7px' }}
            label="Conditional Styling"
            helpText="Specify a rule-based style to enrich visualization. You can define simple rules based on expression evaluation, select a color palette based on the range of an attribute value, or let the system pick a color based on the uniqueness of the value."
          >
            <GraphStyle {...props} chartData={chartData} ruleConfigs={ruleConfigs} />
          </ChartSettingItemContainer>
        ) : null}
      </ChartSettingSectionContainer>
      <ChartSettingSectionContainer title="Advanced">
        <div style={{ marginTop: '-8px', marginBottom: '-12px' }} aria-label="disable_path_finding">
          <TemplateDrivenChartSettings
            settingsTemplate={advancedOptions}
            settings={effectSettings}
            updateSetting={onSettingUpdate}
            setGlobalParameter={setGlobalParameter}
            getGlobalParameter={getGlobalParameter}
          />
        </div>
        <ChartSettingItemContainer
          label="Graph Actions"
          helpText="Graph action allows you to create customized links on vertices, it will be displayed on the context menu for each vertex via right click. For each action, you can link to another page or self with customized parameters."
        >
          <GraphAction {...props} chartData={chartData} />
        </ChartSettingItemContainer>
      </ChartSettingSectionContainer>
    </>
  );
}
