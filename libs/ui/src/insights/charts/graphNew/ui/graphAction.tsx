import React, { useMemo, useState } from 'react';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { Button } from '@tigergraph/app-ui-lib/button';
import { KIND, SHAPE, SIZE } from 'baseui/button';
import { Input } from '@tigergraph/app-ui-lib/input';
import { MdSearch } from '@react-icons/all-files/md/MdSearch';
import { MdAdd } from '@react-icons/all-files/md/MdAdd';

import { ChartSettingProps } from '../../../chart/chart';

import { getNodesAttributes } from '../data';

import { Action } from '../../../components/action';
import IconPopover from '../../../components/iconPopover';
import IconButton from '../../../components/iconButton';
import ActionDetail from '../../../components/action/actionDetail';
import { ActionList } from '../../../components/action/actionList';

import { ReactComponent as TriangleDown } from '../../../assets/images/triangleDown.svg';
import { ReactComponent as TriangleRight } from '../../../assets/images/triangleRight.svg';
import { stringToColor } from '../../../utils';

export default function GraphAction(props: ChartSettingProps) {
  const [css, theme] = useStyletron();
  const [search, setSearch] = useState('');

  const { settings, onSettingUpdate, chartData, schema } = props;
  const { graph } = chartData;

  const { types, optionsArray } = useMemo(() => {
    const nodesAttributes = getNodesAttributes(graph);

    const optionsArray: string[][] = [];
    const types: string[] = [];

    for (let [type, nodeAttributes] of nodesAttributes.entries()) {
      types.push(type);
      const options: string[] = [];

      for (let [name, nodeAttribute] of nodeAttributes.entries()) {
        const values = Array.from(nodeAttribute);
        const valueType = typeof values[0];
        if (valueType === 'object') {
          continue;
        }
        // filter out internal props
        if (name.startsWith('__')) {
          continue;
        }
        options.push(name);
      }

      optionsArray.push(options);
    }

    return {
      types,
      optionsArray,
    };
  }, [graph]);

  return (
    <div>
      <div>
        <Input
          placeholder="Search vertices"
          startEnhancer={() => <MdSearch size={24} color={theme.colors.gray600} />}
          value={search}
          onChange={(e) => setSearch(e.currentTarget.value)}
          overrides={{
            Root: {
              style: {
                borderLeftWidth: 0,
                borderTopWidth: 0,
                borderRightWidth: 0,
                borderBottomWidth: 0,
                paddingLeft: '3px',
              },
            },
          }}
        />
      </div>
      <div
        className={css({
          maxHeight: '200px',
          overflowY: 'auto',
          marginLeft: '-10px',
        })}
      >
        {types
          .filter((type) => type.toLowerCase().includes(search.toLowerCase()))
          .map((type) => {
            const typeIndex = types.indexOf(type);
            const options = optionsArray[typeIndex];

            const defaultColor = schema.graph.vertices[type]?.style?.fillColor || stringToColor(type);

            return (
              <NodeActionItem
                defaultColor={defaultColor}
                key={type}
                type={type}
                options={options}
                settings={settings}
                onSettingUpdate={onSettingUpdate}
                chartSettingProps={props}
              />
            );
          })}
      </div>
    </div>
  );
}

// for action params, data may come from
//  1 node's attributes
//  2 global params (the ux is weird, maybe later we just remove it from action params)
//  3 created
// currently, we do no do any value validation, just garbage in garbage out.
// for old graph action data, we just ignore as now the actions is based on node type.
function NodeActionItem({
  defaultColor,
  options,
  type,
  settings,
  onSettingUpdate,
  chartSettingProps,
}: {
  defaultColor: string | undefined;
  options: string[];
  type: string;
  settings: Record<string, any>;
  onSettingUpdate: (key: string, value: any) => void;
  chartSettingProps: ChartSettingProps;
}) {
  const [css, theme] = useStyletron();
  const [expand, setExpand] = useState(false);
  const actions: Action[] = settings['actionsByType']?.[type] || [];
  const { links, globalParameters } = chartSettingProps;

  return (
    <div
      className={css({
        paddingTop: '4px',
        paddingBottom: '4px',
        ...theme.typography.Body2,
      })}
    >
      <div
        className={css({
          display: 'flex',
          alignItems: 'center',
        })}
      >
        {actions.length === 0 ? (
          <div
            className={css({
              width: '16px',
            })}
          />
        ) : (
          <Button kind="text" shape={SHAPE.square} onClick={() => setExpand(!expand)}>
            {expand ? <TriangleDown /> : <TriangleRight />}
          </Button>
        )}
        <div
          className={css({
            padding: '4px 12px',
            borderRadius: '20px',
            backgroundColor: defaultColor,
            ...theme.typography.Body2,
            color: '#fffffe',
          })}
        >
          {type}
        </div>
        <div
          className={css({
            flex: 1,
          })}
        />
        <IconPopover
          label={
            <IconButton>
              <MdAdd size={16} color={theme.colors.accent} />
            </IconButton>
          }
          popover={({ close }) => (
            <ActionDetail
              action={
                {
                  text: '',
                  url: '',
                  params: [],
                } as Action
              }
              links={links}
              options={options}
              globalParameters={globalParameters}
              onUpdateAction={(action) => {
                onSettingUpdate('actionsByType', { ...settings['actionsByType'], [type]: actions.concat(action) });
                setExpand(true);
              }}
              close={close}
              supportVertexType={true}
            />
          )}
        />
      </div>
      {actions.length > 0 && expand ? (
        <div
          className={css({
            backgroundColor: theme.colors.gray50,
            marginTop: '4px',
            marginLeft: '4px',
          })}
        >
          <ActionList
            actions={actions}
            onActionChanged={(actions) => {
              onSettingUpdate('actionsByType', { ...settings['actionsByType'], [type]: actions });
            }}
            options={options}
            globalParameters={globalParameters}
            links={links}
            supportVertexType={true}
          />
        </div>
      ) : null}
    </div>
  );
}
