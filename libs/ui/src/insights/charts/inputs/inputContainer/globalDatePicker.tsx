import { forwardRef } from 'react';
import { DatePicker } from '@tigergraph/app-ui-lib/datepicker';
import { inputOverrides } from '@tigergraph/app-ui-lib/input';
import { GlobalInputProps } from '../type';
import { BaseInput } from 'baseui/input';

export default function GlobalDatePicker(props: GlobalInputProps) {
  const { globalParam, inputState, onChangeInputValue, onChangeGlobalParameter } = props;
  const isEditMode = props.chartProps?.isEditMode;
  const dates = [];
  if (!isNaN(Date.parse(String(globalParam.value)))) {
    // format the gsql date and make it ISO8601-compliant
    const ISOStr = `${String(globalParam.value).replace(' ', 'T')}.000Z`;
    const date = new Date(ISOStr);
    dates.push(date);
  }

  return (
    <DatePicker
      value={dates}
      onChange={({ date }) => {
        // format date string according to the gsql data types reference
        const formatDate = (date: Date) => {
          return date.toISOString().replace('T', ' ').slice(0, 19);
        };

        const value = Array.isArray(date) ? formatDate(date[0]) : formatDate(date);
        onChangeInputValue({
          ...globalParam,
          type: 'DATETIME',
          value,
        });
        onChangeGlobalParameter({
          ...globalParam,
          type: 'DATETIME',
          value,
        });
      }}
      disabled={isEditMode}
      // the format is consistent with TigerGraph documentations
      // ref: https://docs.tigergraph.com/gsql-ref/3.2/querying/data-types#_base_types
      formatString="yyyy-MM-dd"
      placeholder={inputState.placeholder || 'yyyy-MM-dd'}
      overrides={{
        Input: {
          props: {
            overrides: {
              Input: {
                component: forwardRef<HTMLInputElement>(function ForwardedInput(props, ref) {
                  return <BaseInput overrides={inputOverrides} {...props} inputRef={ref} />;
                }),
              },
            },
          },
        },
      }}
    />
  );
}
