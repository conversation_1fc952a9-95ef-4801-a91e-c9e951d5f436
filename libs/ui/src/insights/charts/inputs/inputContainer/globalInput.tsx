import { Input } from '@tigergraph/app-ui-lib/input';
import { GlobalParam } from '../../../chart/globalParams';
import { DataType, GlobalInputProps } from '../type';
import GlobalVertexInput from './globalVertexInput';
import { lowerCase } from 'lodash';
import { convert2Number } from '../../../utils';

const isNumber = (value: unknown) => {
  return typeof value === 'number' || typeof value === 'bigint';
};

export default function GlobalInput(props: GlobalInputProps) {
  const { globalParam, inputState, chartProps, onChangeInputValue, onChangeGlobalParameter } = props;
  const { dataType } = inputState;
  const { isEditMode } = chartProps;

  const onChange = (value: string): GlobalParam => {
    let newGlobalParam = undefined;
    if (lowerCase(dataType as string) === 'number') {
      const min = convert2Number(inputState.settings.min),
        max = convert2Number(inputState.settings.max);
      const convertedValue = convert2Number(value);
      if (isNumber(min) && isNumber(convertedValue) && convertedValue < min) {
        value = String(min);
      } else if (isNumber(max) && isNumber(convertedValue) && convertedValue > max) {
        value = String(max);
      }
      newGlobalParam = { ...globalParam, type: 'NUMBER', value };
    } else {
      newGlobalParam = { ...globalParam, type: 'STRING', value: String(value) };
    }
    onChangeInputValue(newGlobalParam);
    return newGlobalParam;
  };

  const onFinalChange = (value: string) => {
    const newGlobalParam = onChange(value);
    onChangeGlobalParameter(newGlobalParam);
  };

  return (
    <>
      {dataType === DataType.Vertex ? (
        <GlobalVertexInput {...props} />
      ) : (
        <Input
          value={String(globalParam.value)}
          onChange={(e) => {
            onChange(String(e.target['value']));
          }}
          onBlur={(e) => {
            onFinalChange(String(e.target['value']));
          }}
          onKeyPress={(e) => {
            if (e.key === 'Enter') {
              onFinalChange(String(e.target['value']));
            }
          }}
          disabled={isEditMode}
          placeholder={inputState.placeholder || 'Input value'}
          type={lowerCase(dataType as string) === 'number' ? 'number' : 'text'}
          min={Number(inputState.settings.min) || 0}
          max={Number(inputState.settings.max) || 100}
          step={Number(inputState.settings.step) || 1}
        />
      )}
    </>
  );
}
