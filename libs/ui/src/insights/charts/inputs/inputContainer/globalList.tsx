import { Input } from '@tigergraph/app-ui-lib/input';
import { GlobalInputProps, InputWidgetType } from '../type';
import { upperCase } from 'lodash';
import { EditIcon } from '../../../components/icons';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import IconButton from '../../../components/iconButton';
import { Popover } from '@tigergraph/app-ui-lib/popover';
import { useState } from 'react';
import { PLACEMENT } from 'baseui/popover';
import { HybridItemsFormWithSubmit } from '../../chartSearch/chartSearchParam/chartSearchParamMultipleValue/components/HybridItemsFormWithSubmit';
import { useSchema } from '../../../pages/chart/useMetaHook';
import { GlobalParam, PrimitiveArrayType } from '../../../chart/globalParams';
import { useForm } from 'react-hook-form';
import {
  HybridFormItemsDataType,
  HybridItemsFormType,
} from '../../chartSearch/chartSearchParam/chartSearchParamMultipleValue/types';

const mapValues2GlobalParam = (values, initialGlobalParam, elementType) => {
  let newGlobalParam: GlobalParam;
  if (elementType === 'VERTEX') {
    newGlobalParam = {
      ...initialGlobalParam,
      type: 'LIST',
      elementType,
      value: (values || []).map((i) => ({ vertexType: i.type, vertexID: i.id })),
    };
  } else {
    newGlobalParam = {
      ...initialGlobalParam,
      type: 'LIST',
      elementType,
      value: (values || []).map((i) => i.value),
    };
  }
  return newGlobalParam;
};

const mapGlobalParam2DefaultValues = (globalParam: GlobalParam) => {
  let defaultValue: HybridFormItemsDataType = [];
  if (globalParam.type === 'LIST') {
    if (globalParam.elementType === 'VERTEX') {
      defaultValue = globalParam.value.map((i) => ({ id: i.vertexID, type: i.vertexType }));
    } else {
      defaultValue = globalParam.value.map((i) => ({ value: i }));
    }
  }

  return defaultValue;
};

export default function GlobalList({
  globalParam,
  inputState: { vertexType, selectedGraph, dataType, settings },
  chartProps: { isEditMode, baseURL },
  onChangeGlobalParameter,
  onChangeInputValue,
}: GlobalInputProps) {
  const [css] = useStyletron();
  const [isOpen, setIsOpen] = useState(false);
  const elementType = upperCase(dataType as string) as PrimitiveArrayType['elementType'];
  const placeholder = `<${elementType}>`;
  const { schema } = useSchema(baseURL, selectedGraph);
  const defaultItems = mapGlobalParam2DefaultValues(globalParam);
  const form = useForm<HybridItemsFormType>({
    defaultValues: {
      items: defaultItems,
    },
  });

  const onSubmit = (values: Record<string, any>) => {
    const newGlobalParam: GlobalParam = mapValues2GlobalParam(values?.items, globalParam, elementType);
    onChangeInputValue(newGlobalParam);
    onChangeGlobalParameter(newGlobalParam);
    form.reset(values);
  };

  const cancel = () => {
    setIsOpen(false);
  };

  const edit = () => {
    setIsOpen(true);
    form.reset();
  };

  return (
    <Popover
      placement={PLACEMENT.bottom}
      overrides={{
        Arrow: {
          style: {
            backgroundColor: '#fff',
          },
        },
        Inner: {
          style: {
            paddingLeft: 0,
            paddingRight: 0,
            paddingTop: 0,
            paddingBottom: 0,
            backgroundColor: '#fff',
          },
        },
      }}
      isOpen={isOpen}
      autoFocus
      content={
        <HybridItemsFormWithSubmit
          form={form}
          item={{
            paramType: upperCase(InputWidgetType.LIST),
            elementType,
            vertexType,
          }}
          schemaGraph={schema?.graph}
          onSubmit={onSubmit}
          onClose={cancel}
        />
      }
      onClickOutside={cancel}
    >
      <div className={css({ display: 'flex', gap: '4px', alignItems: 'center' })}>
        <Input disabled placeholder={placeholder} />
        <IconButton disabled={isEditMode} onClick={edit}>
          <EditIcon />
        </IconButton>
      </div>
    </Popover>
  );
}
