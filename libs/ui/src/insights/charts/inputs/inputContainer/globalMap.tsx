import { Input } from '@tigergraph/app-ui-lib/input';
import { GlobalInputProps } from '../type';
import { upperCase } from 'lodash';
import { EditIcon } from '../../../components/icons';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import IconButton from '../../../components/iconButton';
import { Popover } from '@tigergraph/app-ui-lib/popover';
import { PLACEMENT } from 'baseui/popover';
import { useRef, useState } from 'react';
import { HybridItemsFormWithSubmit } from '../../chartSearch/chartSearchParam/chartSearchParamMultipleValue/components/HybridItemsFormWithSubmit';
import { GlobalParam } from '../../../chart/globalParams';
import { useForm } from 'react-hook-form';
import {
  HybridFormItemsDataType,
  HybridItemsFormType,
} from '../../chartSearch/chartSearchParam/chartSearchParamMultipleValue/types';

const mapGlobalParam2DefaultFormItems = (globalParams: GlobalParam) => {
  return (globalParams.value || []) as HybridFormItemsDataType;
};

const mapValues2GlobalParam = (values, globalParam, keyType, valueType) => {
  return {
    ...globalParam,
    type: 'MAP',
    keyType,
    valueType,
    value: values,
  };
};

export default function GlobalMap({
  schema,
  inputState,
  globalParam,
  chartProps: { isEditMode },
  onChangeInputValue,
  onChangeGlobalParameter,
}: GlobalInputProps) {
  const { widgetType } = inputState;
  const [css] = useStyletron();
  const [isOpen, setIsOpen] = useState(false);
  const keyType = upperCase(inputState.keyType || 'unknown');
  const valueType = upperCase(inputState.valueType || 'unknown');
  const placeholder = `<${keyType}, ${valueType}>`;
  const defaultItems = mapGlobalParam2DefaultFormItems(globalParam);
  const form = useForm<HybridItemsFormType>({
    defaultValues: {
      items: defaultItems,
    },
  });

  const onSubmit = (values) => {
    const newGlobalParam = mapValues2GlobalParam(values?.items, globalParam, keyType, valueType);
    onChangeInputValue(newGlobalParam);
    onChangeGlobalParameter(newGlobalParam);
    form.reset(values);
  };

  const cancel = () => {
    setIsOpen(false);
  };

  const edit = () => {
    setIsOpen(true);
    form.reset();
  };

  return (
    <Popover
      placement={PLACEMENT.bottom}
      overrides={{
        Arrow: {
          style: {
            backgroundColor: '#fff',
          },
        },
        Inner: {
          style: {
            paddingLeft: 0,
            paddingRight: 0,
            paddingTop: 0,
            paddingBottom: 0,
            backgroundColor: '#fff',
          },
        },
      }}
      isOpen={isOpen}
      autoFocus
      content={
        <HybridItemsFormWithSubmit
          form={form}
          item={{
            paramType: upperCase(widgetType),
            keyType: keyType,
            valueType: valueType,
          }}
          schemaGraph={schema?.graph}
          onSubmit={onSubmit}
          onClose={cancel}
        />
      }
      onClickOutside={cancel}
    >
      <div className={css({ display: 'flex', gap: '4px', alignItems: 'center' })}>
        <Input disabled placeholder={placeholder} />
        <IconButton disabled={isEditMode} onClick={edit}>
          <EditIcon />
        </IconButton>
      </div>
    </Popover>
  );
}
