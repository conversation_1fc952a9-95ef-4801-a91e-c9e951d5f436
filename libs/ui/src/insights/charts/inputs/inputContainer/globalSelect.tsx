import { TYPE } from 'baseui/select';
import { useMemo } from 'react';
import { GlobalParam } from '../../../chart/globalParams';
import { StyledSelect } from '../../../components';
import { buildRow, Row } from '../../table';
import { DataType, GlobalInputProps } from '../type';

export default function GlobalSelect(props: GlobalInputProps) {
  const { globalParam, inputState, onChangeInputValue, onChangeGlobalParameter, chartProps } = props;
  const inputSettings = inputState.settings;
  const chartDataRows = useMemo(() => {
    const data = chartProps.chartData?.tables?.[0]?.rows.map((item) => buildRow(item, chartProps.schema)) || [];
    return data;
  }, [chartProps.chartData?.tables, chartProps.schema]);

  const columns =
    chartDataRows.length > 0 ? Object.keys(chartDataRows[0]).filter((item) => item !== 'Matched pattern') : [];
  const labelKey = inputSettings['labelKey'] ? inputSettings['labelKey'] : columns.length > 0 ? columns[0] : 'label';
  const valueKey = inputSettings['valueKey'] ? inputSettings['valueKey'] : columns.length > 0 ? columns[0] : 'value';
  const rows = useMemo(() => {
    const result = [...chartDataRows];
    if (inputState.dataType === DataType.Bool) {
      return [
        { [labelKey]: true, [valueKey]: true },
        { [labelKey]: false, [valueKey]: false },
      ];
    }

    inputState.settings.options?.forEach((option) => {
      if (typeof option === 'string') {
        result.push({ [labelKey]: option, [valueKey]: option });
      } else {
        result.push({ [labelKey]: option.label, [valueKey]: option.value });
      }
    });
    return result;
  }, [chartDataRows, inputState.dataType, inputState.settings.options, labelKey, valueKey]);

  const multi = !!inputState.settings.multi && globalParam.type === 'LIST';
  const { isEditMode } = chartProps;

  const findInRows = (rows: Row[], value: string, valueKey: string): Row | undefined => {
    return rows.find((row) => String(row[valueKey]) === String(value));
  };

  const selectedValue = useMemo(() => {
    if (globalParam && multi) {
      return (globalParam.value as string[]).reduce((prev: Row[], v: any) => {
        const row = findInRows(rows, String(v), valueKey);
        if (row) {
          prev.push({ [valueKey]: v });
        }
        return prev;
      }, []);
    } else if (findInRows(rows, String(globalParam.value), valueKey)) {
      return [{ [valueKey]: globalParam.value }];
    } else {
      return [];
    }
  }, [multi, rows, globalParam, valueKey]);

  return (
    <StyledSelect
      options={rows}
      value={selectedValue}
      onChange={(e) => {
        let value = undefined;
        if (multi) {
          value = e.value.map((v) => v[valueKey]);
          onChangeGlobalParameter({ ...globalParam, type: 'LIST', value } as GlobalParam);
        } else {
          value = String(e.value[0][valueKey]);
          onChangeGlobalParameter({ ...globalParam, value });
        }
        onChangeInputValue({ ...globalParam, value });
      }}
      disabled={isEditMode}
      type={TYPE.select}
      placeholder={inputState.placeholder || 'Current value'}
      clearable={false}
      labelKey={labelKey}
      getOptionLabel={({ option }) =>
        typeof option?.[labelKey] !== 'undefined' ? String(option?.[labelKey]) : undefined
      }
      getValueLabel={({ option }) =>
        typeof option?.[labelKey] !== 'undefined' ? String(option?.[labelKey]) : undefined
      }
      valueKey={valueKey}
      multi={multi}
    />
  );
}
