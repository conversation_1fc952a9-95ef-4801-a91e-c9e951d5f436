import React from 'react';
import { GlobalInputProps } from '../type';
import StyledSlider from '../../../components/styledSlider';
import { GlobalParam } from '../../../chart/globalParams';

export default function GlobalSlider(props: GlobalInputProps) {
  const { globalParam, inputState, chartProps, onChangeInputValue, onChangeGlobalParameter } = props;
  const { isEditMode } = chartProps;

  const onChange = (value: number): GlobalParam => {
    const min = Number(inputState.settings.min) || 0,
      max = Number(inputState.settings.max) || 100;
    if (Number(value) < min) {
      value = min;
    } else if (Number(value) > max) {
      value = max;
    }
    const newGlobalParam = { ...globalParam, type: 'NUMBER', value } as GlobalParam;
    onChangeInputValue(newGlobalParam);
    return newGlobalParam;
  };

  const onFinalChange = (value: number) => {
    const newGlobalParam = onChange(value);
    onChangeGlobalParameter(newGlobalParam);
  };

  return (
    <StyledSlider
      value={[isNaN(Number.parseFloat(String(globalParam.value))) ? 0 : Number.parseFloat(String(globalParam.value))]}
      min={Number(inputState.settings.min) || 0}
      max={Number(inputState.settings.max) || 100}
      step={Number(inputState.settings.step) || 1}
      disabled={isEditMode}
      onChange={({ value }) => {
        onChange(value[0]);
      }}
      onFinalChange={({ value }) => {
        onFinalChange(value[0]);
      }}
    />
  );
}
