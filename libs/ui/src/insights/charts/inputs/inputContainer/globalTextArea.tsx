import React from 'react';
import { Textarea, SIZE } from 'baseui/textarea';
import { GlobalParam } from '../../../chart/globalParams';
import { GlobalInputProps } from '../type';

export default function GlobalTextArea(props: GlobalInputProps) {
  const { globalParam, inputState, onChangeInputValue, onChangeGlobalParameter } = props;
  const isEditMode = props.chartProps?.isEditMode;

  const onChange = (value: string): GlobalParam => {
    const newGlobalParam = { ...globalParam, type: 'STRING', value: String(value) } as GlobalParam;
    onChangeInputValue(newGlobalParam);
    return newGlobalParam;
  };

  const onFinalChange = (value: string) => {
    const newGlobalParam = onChange(value);
    onChangeGlobalParameter(newGlobalParam);
  };

  return (
    <Textarea
      value={String(globalParam.value)}
      onChange={(e) => {
        onChange(String(e.target['value']));
      }}
      onBlur={(e) => {
        onFinalChange(String(e.target['value']));
      }}
      onKeyPress={(e) => {
        if (e.key === 'Enter') {
          onFinalChange(String(e.target['value']));
        }
      }}
      disabled={isEditMode}
      placeholder={inputState.placeholder || 'Text value'}
    />
  );
}
