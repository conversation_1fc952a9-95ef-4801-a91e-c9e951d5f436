import { useStyletron } from 'baseui';
import { GlobalInputProps } from '../type';
import { Input } from '@tigergraph/app-ui-lib/input';
import { Select } from '@tigergraph/app-ui-lib/select';
import { useSchema } from '../../../pages/chart/useMetaHook';
import { useCallback, useEffect, useMemo, useRef } from 'react';

export default function GlobalVertexInput({
  globalParam,
  inputState,
  chartProps,
  onChangeGlobalParameter,
  onChangeInputValue,
}: GlobalInputProps & { fromSettingPanel?: boolean }) {
  const [css] = useStyletron();
  const paramValue = globalParam.value as { vertexID: string; vertexType: string };

  const { schema } = useSchema(chartProps.baseURL, inputState.selectedGraph);
  const vertexTypes = useMemo(() => Object.keys(schema.graph.vertices).map((type) => ({ id: type })), [schema]);

  const handleChange = useCallback(
    (vertexType: string, vertexID: string, commitChanges: boolean = false) => {
      // defaultVertex { type: string; id: string } <---> { vertexID: string, vertexValue: string }
      const newValue = {
        ...globalParam,
        type: 'VERTEX',
        value: { vertexID, vertexType },
      } as const;
      onChangeInputValue(newValue);
      if (commitChanges) {
        onChangeGlobalParameter(newValue);
      }
    },
    [globalParam, onChangeGlobalParameter, onChangeInputValue]
  );

  return (
    <div className={css({ display: 'flex', gap: '3px' })}>
      <Select
        disabled={chartProps.isEditMode}
        placeholder={inputState.placeholder}
        clearable={false}
        labelKey="id"
        value={[{ id: paramValue?.vertexType }]}
        options={vertexTypes}
        onChange={(params) => {
          const vertexType = params.value?.[0]?.id as string;
          if (vertexType) {
            handleChange(vertexType, paramValue.vertexID, true);
          }
        }}
      />
      <Input
        disabled={chartProps.isEditMode}
        placeholder={inputState.placeholder}
        value={paramValue.vertexID}
        onChange={(e) => {
          handleChange(paramValue.vertexType, e.currentTarget.value);
        }}
        onBlur={(e) => {
          handleChange(paramValue.vertexType, e.currentTarget.value, true);
        }}
        onKeyPress={(e) => {
          if (e.key === 'Enter') {
            handleChange(paramValue.vertexType, e.currentTarget.value, true);
          }
        }}
      />
    </div>
  );
}
