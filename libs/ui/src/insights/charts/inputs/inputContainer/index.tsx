import { InputWidgetType, GlobalInputProps } from '../type';
import GlobalInput from './globalInput';
import GlobalTextArea from './globalTextArea';
import GlobalSlider from './globalSlider';
import GlobalSelect from './globalSelect';
import GlobalDatePicker from './globalDatePicker';
import GlobalList from './globalList';
import GlobalMap from './globalMap';
import { useGSQLQuery } from '../../query/useGSQLQuery';
import { useQueries, useSchema } from '../../../pages/chart/useMetaHook';

export default function InputContainer(props: GlobalInputProps) {
  const { chartProps, inputState } = props;
  const { globalParameters, baseURL, isClusterMode } = chartProps;
  const { widgetType, settings, dataType } = inputState;

  const graphName = settings['graphName'] ?? chartProps.graphName;
  const searchPattern = settings['searchPattern'] ?? [];
  const patternLimit = settings['patternLimit'] ?? 300;
  const query = settings['query'] ?? '';
  const staticData = settings['staticData'];
  const queryType = settings['queryType'] ?? 'pattern';

  const { schema, error: schemaError } = useSchema(baseURL, graphName);
  const { queries, error: queriesError } = useQueries(baseURL, graphName);

  const { chartData } = useGSQLQuery({
    baseURL,
    cache: false,
    force: false,
    queryType,
    searchPattern: settings['useQuery'] ? searchPattern : [],
    query: settings['useQuery'] ? query : '',
    staticData: staticData,
    patternLimit,
    globalParameters,
    queries,
    queriesError,
    schema,
    schemaError,
    graphName,
    refreshRate: 0,
    isClusterMode,
  });

  const components = {
    [InputWidgetType.INPUT]: GlobalInput,
    [InputWidgetType.TEXT]: GlobalTextArea,
    [InputWidgetType.SLIDER]: GlobalSlider,
    [InputWidgetType.DROPDOWN]: GlobalSelect,
    [InputWidgetType.DATEPICKER]: GlobalDatePicker,
    [InputWidgetType.LIST]: GlobalList,
    [InputWidgetType.MAP]: GlobalMap,
  };

  const Component = components[widgetType];

  return (
    <div>
      <Component
        {...props}
        inputState={{
          ...inputState,
          // some old dataType may be not in lower case
          // so we need to convert it to lower case
          dataType: typeof dataType === 'string' ? dataType.toLocaleLowerCase() : dataType,
        }}
        chartProps={{ ...chartProps, chartData, schema }}
      />
    </div>
  );
}
