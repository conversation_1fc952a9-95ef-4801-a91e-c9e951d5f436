import { useMemo } from 'react';
import { FormControl } from './formControl';
import { StyledInput } from '../../../components';
import { ChartSettingItemContainer } from '../../chartTools/chartSetting/chartSettingItemContainer';
import { DataType, InputState, InputWidgetType } from '../type';
import { StyledSelect } from '../../../components';
import { lowerCase } from 'lodash';
import { compareWithInstanceVersion } from '@tigergraph/tools-models';
import { Controller, useFormContext } from 'react-hook-form';
import { GlobalParams } from '../../../chart/globalParams';

export interface SettingProps {
  inputState: InputState;
  globalParameters: GlobalParams;
  onChangeGlobalParamType: (type: string) => void;
  onChangeInputState: (input: InputState) => void;
}

function isValidVariableName(variableName) {
  // Regular expression to match variable name criteria
  const regex = /^[a-zA-Z][a-zA-Z0-9_]*$/;

  // Test the variable name against the regular expression
  return regex.test(variableName);
}

export default function CommonSetting(props: SettingProps) {
  const { inputState, globalParameters, onChangeGlobalParamType, onChangeInputState } = props;
  const {
    control,
    formState: { errors },
  } = useFormContext<{ variableName: string }>();

  const inputTypeOptions = useMemo(() => {
    const result = Object.values(InputWidgetType).map((type) => ({ id: type }));
    if (compareWithInstanceVersion('4.2.0', '>=')) {
      return result;
    }

    return result.filter((i) => i.id !== InputWidgetType.LIST && i.id !== InputWidgetType.MAP);
  }, []);

  const buildDataTypeOptions = (widgetType: string) => {
    let res = Object.keys(DataType).map((type) => ({ id: DataType[type], label: type }));

    if (widgetType === InputWidgetType.INPUT) {
      res = res.filter((item) => item.id !== DataType.Bool);
    }

    if (widgetType === InputWidgetType.DROPDOWN) {
      return res.filter((item) => item.id !== DataType.Vertex);
    }

    return res;
  };

  const needPlaceholder = useMemo(() => {
    if (inputState.widgetType === InputWidgetType.MAP || inputState.widgetType === InputWidgetType.LIST) {
      return false;
    }
    if (inputState.dataType === DataType.Bool) {
      return false;
    }
    return true;
  }, [inputState.widgetType, inputState.dataType]);

  return (
    <>
      <ChartSettingItemContainer
        label="Variable Name"
        helpText="The variable name allows only letters, numbers and underscores."
      >
        <FormControl
          error={errors.variableName?.message}
          overrides={{
            ControlContainer: {
              style: {
                marginBottom: 0,
              },
            },
          }}
        >
          <Controller
            name="variableName"
            rules={{
              validate: (value) => {
                value = value.trim();
                if (value.length < 1) {
                  return 'Variable name minimum length is one.';
                }
                if (inputState.id !== globalParameters[value]?.id && value in globalParameters) {
                  return 'Variable name already exists.';
                }
                if (!isValidVariableName(value)) {
                  return 'A variable name must begin with an alphabet and can only consist of alphabets, underscores, or numbers.';
                }
              },
            }}
            control={control}
            render={({ field: { ref, ...field } }) => (
              <StyledInput
                {...field}
                inputRef={ref}
                placeholder="Enter input name"
                error={Boolean(errors.variableName?.message)}
              />
            )}
          />
        </FormControl>
      </ChartSettingItemContainer>
      <ChartSettingItemContainer label="Input Label (Optional)">
        <StyledInput
          value={inputState.label ?? ''}
          onChange={(e) => {
            const newInputState = {
              ...inputState,
              label: e.target['value'],
            };
            onChangeInputState(newInputState);
          }}
          placeholder="Enter input label"
        />
      </ChartSettingItemContainer>
      <ChartSettingItemContainer label="Input Type">
        <StyledSelect
          clearable={false}
          searchable={false}
          value={[{ id: inputState.widgetType }]}
          options={inputTypeOptions}
          onChange={(params) => {
            const widgetType = String(params.value?.[0]?.id ?? '');
            if (widgetType === inputState.widgetType) {
              return;
            }
            const newInputState = {
              ...inputState,
              widgetType,
            };
            const DataTypeKeys = Object.keys(DataType),
              DataTypeValues = Object.values(DataType);
            let dataType = '';
            if (widgetType === InputWidgetType.SLIDER) {
              dataType = DataTypeKeys[DataTypeValues.indexOf(DataType.Number)];
              newInputState.settings = { min: 0, max: 100, step: 1 };
            } else if (widgetType === InputWidgetType.DATEPICKER) {
              dataType = DataTypeKeys[DataTypeValues.indexOf(DataType.Datetime)];
            } else {
              dataType = DataTypeKeys[DataTypeValues.indexOf(DataType.String)];
            }
            newInputState.dataType = dataType;
            onChangeGlobalParamType(dataType.toUpperCase());
            onChangeInputState(newInputState);
          }}
          placeholder="Select input type"
          labelKey="id"
        />
      </ChartSettingItemContainer>
      {[InputWidgetType.INPUT, InputWidgetType.DROPDOWN, InputWidgetType.LIST].includes(
        inputState.widgetType as InputWidgetType
      ) && (
        <ChartSettingItemContainer label="Data Type">
          <StyledSelect
            clearable={false}
            searchable={false}
            value={[{ id: lowerCase(inputState.dataType.toString()) }]}
            options={buildDataTypeOptions(inputState.widgetType)}
            onChange={(params) => {
              const dataType = String(params.value?.[0]?.id ?? '');
              onChangeGlobalParamType(dataType.toUpperCase());
              if (dataType === inputState.dataType) {
                return;
              }
              const newInputState = {
                ...inputState,
                dataType: dataType,
                value: '',
                settings: {},
              };
              if (newInputState.dataType === 'Number') {
                newInputState.settings = { min: 0, max: 100, step: 1 };
              }
              onChangeInputState(newInputState);
            }}
            placeholder="Select data type"
            labelKey="label"
          />
        </ChartSettingItemContainer>
      )}
      {needPlaceholder && (
        <ChartSettingItemContainer label="Placeholder">
          <StyledInput
            value={inputState.placeholder ?? ''}
            onChange={(e) => {
              const newInputState = {
                ...inputState,
                placeholder: e.target['value'],
              };
              onChangeInputState(newInputState);
            }}
            placeholder="Enter placeholder"
          />
        </ChartSettingItemContainer>
      )}
    </>
  );
}
