import { Controller, useFormContext } from 'react-hook-form';
import { ChartSettingItemContainer } from '../../../../../chartSetting';
import { StyledSelect } from '../../../../components';
import { useGraphList } from '../../../../pages/chart/useMetaHook';
import { ErrorMessage } from '../../../chartSearch/chartSearchParam/chartSearchParamMultipleValue/components/ErrorMessage';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';

export const GraphSelector = ({ baseURL }: { baseURL: string }) => {
  const {
    control,
    formState: { errors },
  } = useFormContext<{
    selectedGraph: string;
  }>();
  const [css] = useStyletron();
  const graphs = useGraphList(baseURL);
  const graphsOptions = graphs.map((i) => ({ id: i }));

  return (
    <ChartSettingItemContainer label="Select Graph">
      <Controller
        control={control}
        name="selectedGraph"
        rules={{
          required: 'required',
        }}
        render={({ field: { ref, value, onChange, ...field } }) => (
          <StyledSelect
            {...field}
            labelKey="id"
            valueKey="id"
            inputRef={ref}
            clearable={false}
            options={graphsOptions}
            value={value ? [{ id: value }] : undefined}
            placeholder="Select graph"
            onChange={(params) => {
              const graph = params.value?.[0]?.id;
              onChange(graph);
            }}
          />
        )}
      />
      {errors['selectedGraph'] && (
        <ErrorMessage className={css({ marginTop: '2px' })}>{errors['selectedGraph'].message}</ErrorMessage>
      )}
    </ChartSettingItemContainer>
  );
};
