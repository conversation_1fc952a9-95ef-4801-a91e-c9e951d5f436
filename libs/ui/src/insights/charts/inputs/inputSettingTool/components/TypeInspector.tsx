import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { InputWidgetType, DataType } from '../../type';
import { upperCase } from 'lodash';

const getTypeFromDataType = (dataType: string) => {
  switch (dataType) {
    case DataType.Datetime:
      return 'DATETIME';
    case DataType.Number:
      return 'INT/INT64/UINT/DOUBLE/FLOAT';
    case DataType.Vertex:
      return 'VERTEX';
    case DataType.Bool:
      return 'BOOL';
    case DataType.String:
    default:
      return 'STRING';
  }
};

const getTypeLabel = ({ inputType, keyType, valueType, dataType, vertexType, isMultiple }) => {
  switch (inputType) {
    case InputWidgetType.INPUT: {
      return getTypeFromDataType(dataType);
    }
    case InputWidgetType.MAP: {
      if (keyType && valueType) {
        return `MapAccum<${getTypeFromDataType(keyType)}, ${getTypeFromDataType(valueType)}>`;
      }
      return 'MapAccum';
    }
    case InputWidgetType.LIST: {
      if (dataType === DataType.Vertex && vertexType) {
        return `ListAccum<Vertex<${vertexType}>>`;
      } else if (dataType === DataType.Vertex) {
        return 'ListAccum<Vertex>';
      }
      return `ListAccum<${getTypeFromDataType(dataType)}>`;
    }
    case InputWidgetType.DROPDOWN: {
      if (isMultiple) {
        if (vertexType) {
          return `ListAccum<Vertex${vertexType ? `<${vertexType}>` : ''}>`;
        }
        return `ListAccum<${getTypeFromDataType(dataType)}>`;
      }
      return getTypeFromDataType(dataType);
    }
    case InputWidgetType.SLIDER:
      return 'INT/INT64/UINT';
    case InputWidgetType.DATEPICKER:
      return 'DATETIME';
    case InputWidgetType.TEXT:
    default:
      return 'STRING';
  }
};

export const TypeInspector = ({ inputType, keyType, valueType, dataType, vertexType, isMultiple }) => {
  const [css] = useStyletron();

  const type = getTypeLabel({ inputType, keyType, valueType, dataType, vertexType, isMultiple });

  return (
    <div
      className={css({
        padding: '8px 0',
        fontSize: '12px',
        fontWeight: 500,
        lineHeight: '16px',
        color: '#656565',
        borderTop: '1px solid #DDDDDE',
      })}
    >
      Corresponding Type in GSQL Query: <span className={css({ color: '#2C3237' })}>{type}</span>
      <span></span>
    </div>
  );
};
