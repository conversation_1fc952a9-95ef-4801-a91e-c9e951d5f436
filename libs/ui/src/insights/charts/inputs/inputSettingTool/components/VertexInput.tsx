import { useStyletron } from 'baseui';
import { useFormContext } from 'react-hook-form';
import { GraphState } from '../../../../chart/schema';
import { VertexListItemRow } from '../../../chartSearch/chartSearchParam/chartSearchParamMultipleValue/components/VertexListItemRow';
import { ErrorMessage } from '../../../chartSearch/chartSearchParam/chartSearchParamMultipleValue/components/ErrorMessage';

export default function VertexInput({ required, schemaGraph }: { required?: boolean; schemaGraph: GraphState }) {
  const [css] = useStyletron();

  // FIXME type
  const form = useFormContext<{ item: { id: string; type0: string } }>();
  const {
    formState: { errors },
  } = form;
  const item = { vertexType: '*' };

  return (
    <>
      <div className={css({ display: 'grid', gap: '4px', gridTemplateColumns: '1fr 1fr' })}>
        <VertexListItemRow required={false} field="item" form={form as any} item={item} schemaGraph={schemaGraph} />
        {errors.item && (
          <>
            <ErrorMessage>{(errors.item.type as any)?.message}</ErrorMessage>
            <ErrorMessage>{(errors.item.id as any)?.message}</ErrorMessage>
          </>
        )}
      </div>
    </>
  );
}
