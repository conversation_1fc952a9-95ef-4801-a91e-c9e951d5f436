import { StyledInput } from '../../../components';

import { ChartSettingItemContainer } from '../../chartTools/chartSetting/chartSettingItemContainer';
import { DataType, GlobalInputProps } from '../type';
import { FormControl } from './formControl';
import NumberSetting from './numberSetting';
import { useFormContext } from 'react-hook-form';
import { GraphSelector } from './components/GraphSelector';
import { useSchema } from '../../../pages/chart/useMetaHook';
import VertexInput from './components/VertexInput';
import { useEffect } from 'react';
import { lowerCase } from 'lodash';

export default function DefaultInputSetting(
  props: GlobalInputProps & { defaultValueError: string; minValueError: string; maxValueError: string }
) {
  const {
    globalParam,
    inputState,
    onChangeGlobalParameter,
    onChangeInputState,
    defaultValueError,
    minValueError,
    maxValueError,
    chartProps: { baseURL },
  } = props;
  const { dataType } = inputState;
  const { watch, setValue } = useFormContext();

  const isNumberType = lowerCase(inputState.dataType as string) === DataType.Number;
  const selectedGraph = watch('selectedGraph');
  const { schema } = useSchema(baseURL, selectedGraph);

  useEffect(() => {
    if (globalParam.type === 'VERTEX') {
      const selectedGraph = inputState.selectedGraph;
      const defaultVertex = { id: globalParam.value?.vertexID, type: globalParam.value?.vertexType };
      setValue('selectedGraph', selectedGraph);
      setValue('item', defaultVertex);
    }
  }, [inputState.settings, globalParam, inputState.selectedGraph, setValue]);

  // for number type in global Params
  // we use NaN to represent invalid value
  // also do validate in save
  return (
    <>
      {dataType === DataType.Vertex && <GraphSelector baseURL={baseURL} />}
      <ChartSettingItemContainer label="Default Value">
        {dataType === DataType.Vertex ? (
          <VertexInput schemaGraph={schema.graph} />
        ) : (
          <FormControl error={defaultValueError}>
            <StyledInput
              value={
                globalParam.type === 'NUMBER'
                  ? isNaN(globalParam.value)
                    ? ''
                    : String(globalParam.value)
                  : String(globalParam.value)
              }
              onChange={(e) => {
                const value = e.target['value'];
                if (isNumberType) {
                  onChangeGlobalParameter({
                    ...globalParam,
                    type: 'NUMBER',
                    value,
                  });
                } else {
                  onChangeGlobalParameter({
                    ...globalParam,
                    value,
                  });
                }
              }}
              type={lowerCase(inputState.dataType as string) === 'number' ? 'number' : 'text'}
              placeholder="Enter default value"
              error={!!defaultValueError}
            />
          </FormControl>
        )}
      </ChartSettingItemContainer>
      {isNumberType && (
        <NumberSetting
          globalParam={globalParam}
          inputState={inputState}
          onChangeInputState={onChangeInputState}
          minValueError={minValueError}
          maxValueError={maxValueError}
        />
      )}
    </>
  );
}
