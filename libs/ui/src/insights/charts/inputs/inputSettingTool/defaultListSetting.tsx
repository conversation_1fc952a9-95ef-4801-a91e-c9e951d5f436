import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { ChartSettingItemContainer } from '../../chartTools/chartSetting/chartSettingItemContainer';
import { GlobalInputProps, InputWidgetType } from '../type';
import { StyledSelect } from '../../../components';
import { ChartSchema } from '../../../chart/schema';
import StyledToggle from '../../../components/styledToggle';
import { useEffect, useMemo } from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import HelpText from '../../chartTools/chartSetting/helpText';
import { upperCase } from 'lodash';
import { HybridItemsForm } from '../../chartSearch/chartSearchParam/chartSearchParamMultipleValue/components/HybridItemsForm';
import { useSchema } from '../../../pages/chart/useMetaHook';
import { ErrorMessage } from '../../chartSearch/chartSearchParam/chartSearchParamMultipleValue/components/ErrorMessage';
import { GraphSelector } from './components/GraphSelector';
import { HybridFormItemsDataType } from '../../chartSearch/chartSearchParam/chartSearchParamMultipleValue/types';

interface Props extends GlobalInputProps {
  chartData: any;
  graphName: string;
  labelKey: string;
  valueKey: string;
  schema: ChartSchema;
  onChangeGlobalParamType: (type: string) => void;
  baseURL: string;
}

export default function DefaultListSetting(props: Props) {
  const [css] = useStyletron();
  const { inputState, globalParam, baseURL } = props;
  const form = useFormContext<{
    items: HybridFormItemsDataType;
    selectedGraph: string;
    fixVertexType: boolean;
    vertexType: string;
  }>();
  const { control, watch, getValues, setValue } = form;
  const isVertexList = inputState.dataType === 'vertex';
  const selectedGraph = watch('selectedGraph');
  const fixVertexType = watch('fixVertexType');
  const vertexType = watch('vertexType');
  const { schema } = useSchema(baseURL, selectedGraph);

  const item = useMemo(() => {
    return {
      paramType: upperCase(InputWidgetType.LIST),
      elementType: upperCase(inputState.dataType as string),
      vertexType,
    };
  }, [inputState.dataType, vertexType]);

  const vertexOptions = useMemo(
    () => Object.keys(schema.graph.vertices).map((id) => ({ id })),
    [schema.graph.vertices]
  );

  useEffect(() => {
    const selectedGraph = inputState.selectedGraph;
    const vertexType = inputState.vertexType;
    const fixVertexType = Boolean(vertexType);
    const items = [];
    if (globalParam.type === 'LIST') {
      if (globalParam.elementType === 'VERTEX') {
        globalParam.value.forEach((gp) => {
          items.push({ id: gp.vertexID, type: gp.vertexType });
        });
      } else {
        globalParam.value.forEach((gp) => {
          items.push({ value: gp });
        });
      }
    } else {
      items.push({ value: '' });
    }

    setValue('selectedGraph', selectedGraph);
    setValue('vertexType', vertexType);
    setValue('fixVertexType', fixVertexType);
    setValue('items', items);
  }, [inputState.selectedGraph, globalParam, inputState.dataType, inputState.vertexType, setValue, getValues]);

  useEffect(() => {
    if (fixVertexType && vertexType) {
      const items = getValues('items');
      const newItems = items.map((i) => ({ ...i, type: vertexType }));
      setValue('items', newItems);
    }
  }, [fixVertexType, vertexType, getValues, setValue]);

  return (
    <>
      {isVertexList && (
        <>
          <GraphSelector baseURL={baseURL} />
          <div className={css({ display: 'flex', marginBottom: '6px', fontSize: '14px' })}>
            <div className={css({ flex: '1 1 auto' })}>
              <HelpText>Fix Vertex Type</HelpText>
            </div>
            <Controller
              control={control}
              name="fixVertexType"
              render={({ field: { ref, value, onChange, ...field } }) => (
                <StyledToggle
                  inputRef={ref}
                  {...field}
                  onChange={(e) => {
                    const fixed = e.currentTarget.checked;
                    onChange(fixed);
                    if (!fixed) {
                      setValue('vertexType', undefined);
                    }
                  }}
                  checked={value}
                />
              )}
            />
          </div>
        </>
      )}
      {fixVertexType && (
        <div className={css({ marginBottom: '6px' })}>
          <Controller
            control={control}
            name="vertexType"
            rules={{
              required: 'required',
            }}
            render={({ field: { ref, value, onChange, ...field } }) => (
              <StyledSelect
                {...field}
                labelKey="id"
                valueKey="id"
                inputRef={ref}
                clearable={false}
                options={vertexOptions}
                value={value ? [{ id: value }] : undefined}
                placeholder="Select vertex"
                onChange={(params) => {
                  const graph = params.value?.[0]?.id;
                  onChange(graph);
                }}
              />
            )}
          />
          {form.formState.errors['vertexType'] && (
            <ErrorMessage className={css({ marginTop: '2px' })}>
              {form.formState.errors['vertexType'].message}
            </ErrorMessage>
          )}
        </div>
      )}
      <ChartSettingItemContainer label="Default Value">
        {/* FIXME any */}
        <HybridItemsForm form={form as any} showLabel={isVertexList} item={item} schemaGraph={schema.graph} />
      </ChartSettingItemContainer>
    </>
  );
}
