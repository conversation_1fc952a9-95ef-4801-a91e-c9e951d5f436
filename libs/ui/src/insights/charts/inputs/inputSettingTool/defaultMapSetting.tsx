import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { ChartSettingItemContainer } from '../../chartTools/chartSetting/chartSettingItemContainer';
import { DataType, GlobalInputProps, InputWidgetType } from '../type';
import { StyledSelect } from '../../../components';
import { ChartSchema } from '../../../chart/schema';
import { useEffect, useMemo } from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import { upperCase } from 'lodash';
import { HybridItemsForm } from '../../chartSearch/chartSearchParam/chartSearchParamMultipleValue/components/HybridItemsForm';
import { ErrorMessage } from '../../chartSearch/chartSearchParam/chartSearchParamMultipleValue/components/ErrorMessage';
import { HybridFormItemsDataType } from '../../chartSearch/chartSearchParam/chartSearchParamMultipleValue/types';

interface Props extends GlobalInputProps {
  chartData: any;
  graphName: string;
  labelKey: string;
  valueKey: string;
  schema: ChartSchema;
  onChangeGlobalParamType: (type: string) => void;
}

const keyValueOptions = Object.keys(DataType)
  .map((type) => ({ id: DataType[type], label: type }))
  .filter((i) => i.id !== DataType.Vertex);

export default function DefaultMapSetting(props: Props) {
  const [css] = useStyletron();
  const { inputState, schema, globalParam } = props;

  const form = useFormContext<{ items: HybridFormItemsDataType; keyType: string; valueType: string }>();
  const { setValue, control, watch } = form;

  const keyType = watch('keyType');
  const valueType = watch('valueType');

  const item = useMemo(() => {
    return {
      paramType: upperCase(InputWidgetType.MAP),
      keyType,
      valueType,
      vertexType: '',
    };
  }, [keyType, valueType]);

  useEffect(() => {
    const keyType = inputState.keyType;
    const valueType = inputState.valueType;

    const items = [];
    if (globalParam.type === 'MAP') {
      globalParam.value.forEach((gp) => items.push(gp));
    } else {
      items.push({ key: '', value: '' });
    }

    if (keyType) {
      setValue('keyType', keyType);
    }
    if (valueType) {
      setValue('valueType', valueType);
    }
    setValue('items', items);
  }, [inputState.keyType, globalParam, inputState.valueType, inputState.settings?.defaultMap, setValue]);

  return (
    <>
      <div className={css({ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '8px' })}>
        {(
          [
            { label: 'Key Type', name: 'keyType' },
            { label: 'Value Type', name: 'valueType' },
          ] as const
        ).map(({ label, name }) => (
          <ChartSettingItemContainer label={label} key={label}>
            <Controller
              control={control}
              rules={{
                required: 'required',
              }}
              name={name}
              render={({ field: { ref, value, onChange, ...field } }) => (
                <StyledSelect
                  {...field}
                  clearable={false}
                  searchable={false}
                  options={keyValueOptions}
                  value={[{ id: value }]}
                  onChange={(params) => onChange(params.value?.[0].id)}
                />
              )}
            />
            {form.formState.errors[name] && (
              <ErrorMessage className={css({ marginTop: '3px' })}>{form.formState.errors[name].message}</ErrorMessage>
            )}
          </ChartSettingItemContainer>
        ))}
      </div>
      <ChartSettingItemContainer label="Default Value">
        {/* FIXME any */}
        <HybridItemsForm form={form as any} item={item} schemaGraph={schema.graph} />
      </ChartSettingItemContainer>
    </>
  );
}
