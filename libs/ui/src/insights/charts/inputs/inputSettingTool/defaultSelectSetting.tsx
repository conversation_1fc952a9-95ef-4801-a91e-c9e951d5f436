import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { StyledInput } from '../../../components';
import { Button } from '@tigergraph/app-ui-lib/button';
import { ChartSettingItemContainer } from '../../chartTools/chartSetting/chartSettingItemContainer';
import { DataType, GlobalInputProps, InputOption } from '../type';
import { StyledSelect } from '../../../components';
import { buildRow } from '../../table';
import { ChartSchema } from '../../../chart/schema';
import StyledToggle from '../../../components/styledToggle';
import { MdAdd } from '@react-icons/all-files/md/MdAdd';
import { MdDeleteOutline } from '../../../../react-icons';
import ReviewSearch from '../../../assets/images/reviewSearch.svg';
import IconButton from '../../../components/iconButton';
import { GlobalParam } from '../../../chart/globalParams';
import { useMemo } from 'react';

interface Props extends GlobalInputProps {
  chartData: any;
  graphName: string;
  labelKey: string;
  valueKey: string;
  schema: ChartSchema;
  onChangeGlobalParamType: (type: string) => void;
}

const getDefaultValue = (globalParam: GlobalParam, multi: boolean, valueKey: string) => {
  if (globalParam) {
    if (multi && Array.isArray(globalParam.value)) {
      return globalParam.value.map((v) => ({ [valueKey]: v }));
    }
    return [{ [valueKey]: globalParam.value }];
  }

  return [];
};

export default function DefaultSelectSetting(props: Props) {
  const [css, theme] = useStyletron();
  const {
    chartData,
    globalParam,
    inputState,
    graphName,
    schema,
    onChangeGlobalParamType,
    onChangeGlobalParameter,
    onChangeInputState,
  } = props;

  const chartDataRows = useMemo(() => {
    const data = chartData?.tables?.[0]?.rows.map((item) => buildRow(item, schema)) ?? [];
    return data;
  }, [chartData?.tables, schema]);
  const columns =
    chartDataRows.length > 0 ? Object.keys(chartDataRows[0]).filter((item) => item !== 'Matched pattern') : [];
  const optionTypes = ['label', 'value'];
  const labelKey = props['labelKey'] ? props['labelKey'] : columns.length > 0 ? columns[0] : 'label';
  const valueKey = props['valueKey'] ? props['valueKey'] : columns.length > 0 ? columns[0] : 'value';
  const multi = !!inputState.settings.multi && globalParam.type === 'LIST';

  const rows = useMemo(() => {
    const result = [...chartDataRows];
    if (inputState.dataType === DataType.Bool) {
      return [
        { [labelKey]: true, [valueKey]: true },
        { [labelKey]: false, [valueKey]: false },
      ];
    }

    inputState.settings.options?.forEach((option) => {
      if (typeof option === 'string') {
        result.push({ [labelKey]: option, [valueKey]: option });
      } else {
        result.push({ [labelKey]: option.label, [valueKey]: option.value });
      }
    });
    return result;
  }, [chartDataRows, inputState.dataType, inputState.settings.options, labelKey, valueKey]);

  return (
    <>
      <ChartSettingItemContainer label="Default Value">
        <StyledSelect
          options={rows}
          value={getDefaultValue(globalParam, multi, valueKey)}
          onChange={(e) => {
            if (multi) {
              onChangeGlobalParameter({
                ...globalParam,
                value: e.value.map((v) => v[valueKey]),
              } as GlobalParam);
            } else {
              onChangeGlobalParameter({
                ...globalParam,
                value: e.value?.[0]?.[valueKey],
              } as GlobalParam);
            }
          }}
          placeholder="Select default value"
          multi={multi}
          clearable={false}
          getOptionLabel={({ option }) =>
            typeof option?.[labelKey] !== 'undefined' ? String(option?.[labelKey]) : undefined
          }
          getValueLabel={({ option }) =>
            typeof option?.[labelKey] !== 'undefined' ? String(option?.[labelKey]) : undefined
          }
          labelKey={labelKey}
          valueKey={valueKey}
        />
      </ChartSettingItemContainer>
      <ChartSettingItemContainer>
        <div className={css({ marginTop: '8px' })}>
          <StyledToggle
            checked={multi}
            onChange={(e) => {
              const newInputState = {
                ...inputState,
                settings: {
                  ...inputState.settings,
                  multi: e.currentTarget.checked,
                },
              };
              if (e.currentTarget.checked) {
                onChangeGlobalParamType('LIST');
              } else if (globalParam.type === 'LIST') {
                onChangeGlobalParamType(globalParam.elementType);
              }
              onChangeInputState(newInputState);
            }}
          >
            Multiple selection
          </StyledToggle>
        </div>
      </ChartSettingItemContainer>
      {inputState.dataType !== DataType.Bool && (
        <ChartSettingItemContainer>
          <div className={css({ marginTop: '8px' })}>
            <StyledToggle
              checked={!!inputState.settings.useQuery}
              onChange={(e) => {
                const newInputState = {
                  ...inputState,
                  settings: {
                    ...inputState.settings,
                    useQuery: e.currentTarget.checked,
                    open: e.currentTarget.checked,
                    searchPattern: [],
                    query: '',
                    graphName: graphName,
                    staticData: JSON.stringify([{ key: 'value' }], null, 4),
                    patternLimit: 300,
                    options: [],
                  },
                };
                onChangeInputState(newInputState);
              }}
            >
              Use query to generate options
            </StyledToggle>
          </div>
        </ChartSettingItemContainer>
      )}

      {inputState.settings.useQuery && (
        <>
          <ChartSettingItemContainer>
            <div
              className={css({
                display: 'flex',
                fontSize: '14px',
                lineHeight: '24px',
                justifyContent: 'space-between',
              })}
            >
              <div>Review search</div>
              <IconButton
                onClick={() => {
                  const newInputState = { ...inputState, settings: { ...inputState.settings, open: true } };
                  onChangeInputState(newInputState);
                }}
              >
                <ReviewSearch />
              </IconButton>
            </div>
          </ChartSettingItemContainer>
          {optionTypes.map((item) => (
            <ChartSettingItemContainer key={item} label={`${item.charAt(0).toUpperCase() + item.slice(1)} key`}>
              <StyledSelect
                options={columns.map((item) => ({ label: item, id: item }))}
                value={[{ id: inputState.settings[`${item}Key`] }]}
                onChange={(e) => {
                  const newInputState = {
                    ...inputState,
                    settings: { ...inputState.settings, [`${item}Key`]: e.value[0].id },
                  };
                  onChangeInputState(newInputState);
                }}
                placeholder={`${item} key`}
              />
            </ChartSettingItemContainer>
          ))}
        </>
      )}
      {!inputState.settings.useQuery && inputState.settings.options?.length > 0 && (
        <ChartSettingItemContainer label="Options">
          <div
            className={css({
              display: 'flex',
              flexDirection: 'column',
              rowGap: '4px',
            })}
          >
            {inputState.settings.options?.map((option: InputOption, i: number) => (
              <div key={'option' + i} className={css({ display: 'flex', columnGap: '4px' })}>
                {optionTypes.map((optionParam) => (
                  <StyledInput
                    key={optionParam}
                    value={option[optionParam] ?? ''}
                    onChange={(e) => {
                      const options = [...inputState.settings.options];
                      options[i] = { ...option, [optionParam]: e.target['value'] };
                      const newInputState = { ...inputState, settings: { ...inputState.settings, options } };
                      if (optionParam === 'value' && multi) {
                        if (
                          globalParam.elementType === 'STRING' &&
                          globalParam.value.findIndex((v) => String(v) === String(option[optionParam])) > -1
                        ) {
                          onChangeGlobalParameter({
                            ...globalParam,
                            value: [...globalParam.value].filter((v) => String(v) !== String(option[optionParam])),
                          } as GlobalParam);
                        } else if (
                          globalParam.elementType === 'NUMBER' &&
                          globalParam.value.findIndex((v) => Number(v) === Number(option[optionParam])) > -1
                        ) {
                          onChangeGlobalParameter({
                            ...globalParam,
                            value: [...globalParam.value].filter((v) => Number(v) !== Number(option[optionParam])),
                          } as GlobalParam);
                        }
                      }
                      onChangeInputState(newInputState);
                    }}
                    type={optionParam === 'value' ? DataType[inputState.dataType.toString()] : 'string'}
                    placeholder={optionParam}
                  />
                ))}
                <IconButton
                  onClick={() => {
                    const options = [...inputState.settings.options];
                    options.splice(i, 1);
                    const newInputState = { ...inputState, settings: { ...inputState.settings, options } };
                    onChangeInputState(newInputState);
                  }}
                >
                  <MdDeleteOutline width={16} height={16} color={theme.colors.accent} />
                </IconButton>
              </div>
            ))}
          </div>
        </ChartSettingItemContainer>
      )}
      {!inputState.settings.useQuery && inputState.dataType !== DataType.Bool && (
        <div>
          <Button
            kind="text"
            onClick={() => {
              const options = inputState.settings.options ? [...inputState.settings.options] : [];
              options.push({ label: '', value: '', isCreatable: true });
              const newInputState = { ...inputState, settings: { ...inputState.settings, options } };
              onChangeInputState(newInputState);
            }}
          >
            <MdAdd size={16} />
            <span className={css({ marginLeft: '4px' })}>Add option</span>
          </Button>
        </div>
      )}
    </>
  );
}
