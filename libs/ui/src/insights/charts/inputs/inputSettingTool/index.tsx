import React, { useMemo } from 'react';
import { useStyletron } from 'baseui';
import { DataType, InputState, InputWidgetType } from '../type';
import { ChartProps, ChartSettingProps } from '../../../chart/chart';
import { ChartSettingItemContainer } from '../../chartTools/chartSetting/chartSettingItemContainer';
import StyledToggle from '../../../components/styledToggle';
import ChartSettingSectionContainer from '../../chartTools/chartSetting/chartSettingSectionContainer';
import { MdAdd } from '@react-icons/all-files/md/MdAdd';
import InputDetail from './inputDetail';
import { getUID } from '../../../utils';
import { StyledDraggableList } from '../../../components/styledDraggableList';
import { arrayMove, arrayRemove } from 'baseui/dnd-list';
import IconButton from '../../../components/iconButton';
import { StyledToast } from '../../../components/styledToasterContainer';
import { KIND } from 'baseui/toast';
import HelpText from '../../chartTools/chartSetting/helpText';
import { GlobalParam } from '../../../chart/globalParams';
import { PopoverOverrides } from 'baseui/popover';

export default function InputSettingTool(props: ChartSettingProps) {
  const [css, theme] = useStyletron();

  const {
    settings,
    graphName,
    globalParameters,
    onSettingUpdate,
    getGlobalParameter,
    setGlobalParameter,
    deleteGlobalParameter,
    onChartStateUpdate,
  } = props;

  // some old dataType may be not in lower case
  // so we need to convert it to lower case
  const inputStates = useMemo(() => {
    const origInputStates = (settings['inputStates'] ?? []) as InputState[];

    return origInputStates.map((item) => {
      const dataType = item.dataType;
      return {
        ...item,
        dataType: typeof dataType === 'string' ? dataType.toLocaleLowerCase() : dataType,
      };
    });
  }, [settings]);

  const getNewInputState = () => {
    return {
      id: 'input_' + getUID(),
      name: 'variable',
      dataType: DataType.String,
      widgetType: InputWidgetType.INPUT,
      settings: {},
    } as InputState;
  };

  const [newInputState, setNewInputState] = React.useState(getNewInputState());

  const defaultGlobalParam = useMemo(
    () =>
      ({
        value: '',
        type: 'STRING',
        name: '',
        id: newInputState.id,
      } as GlobalParam),
    [newInputState.id]
  );

  const onChangeInputState = (input: InputState) => {
    const index = inputStates.findIndex((item) => item.id === input.id);
    const newInputStates = [...inputStates];
    if (index === -1) {
      onSettingUpdate('inputStates', [...inputStates, input]);
    } else {
      newInputStates[index] = input;
      onSettingUpdate('inputStates', newInputStates);
    }
  };

  const onChangeGlobalParameter = (globalParam: GlobalParam, input: InputState, oldInputState: InputState) => {
    if (oldInputState.id === input.id) {
      // only delete old global parameter
      // 1. is the same input
      // 2. and the input name is changed
      setGlobalParameter(input.name, globalParam, input.name === oldInputState.name ? undefined : oldInputState.name);
    } else {
      setGlobalParameter(input.name, globalParam);
    }
  };
  const popoverOverrides: PopoverOverrides = {
    Body: {
      style: {
        padding: 0,
      },
    },
  };

  return (
    <div>
      <ChartSettingSectionContainer title="Data">
        <ChartSettingItemContainer
          label="Inputs"
          helpText="The list of inputs to be shown on the dashboard"
          icon={
            <IconButton>
              <MdAdd size={16} color={theme.colors.accent} />
            </IconButton>
          }
          popoverOverrides={popoverOverrides}
          popover={({ close }) => {
            return (
              <InputDetail
                globalParam={defaultGlobalParam}
                inputState={newInputState}
                globalParameters={globalParameters}
                graphName={graphName}
                // for new inputs, we generate a new id for oldInputState, so it will treat as new input
                onChangeGlobalParameter={(value, input) => onChangeGlobalParameter(value, input, getNewInputState())}
                onChangeInputState={onChangeInputState}
                close={close}
                chartProps={props as unknown as ChartProps}
              />
            );
          }}
          onOpen={() => {
            onChartStateUpdate('curInputIndex', -1);
            setNewInputState(getNewInputState());
          }}
        >
          {inputStates.length === 0 && (
            <div className={css({ marginTop: '12px' })}>
              <StyledToast
                kind={KIND.info}
                closeable={false}
                message="No input is available for configure, you can click on + to add an input."
              />
            </div>
          )}
          <StyledDraggableList
            items={inputStates.map((item) => (
              <div
                className={css({
                  display: 'flex',
                  columnGap: '8px',
                })}
              >
                <div
                  className={css({
                    flex: 1,
                    overflow: 'hidden',
                    whiteSpace: 'nowrap',
                    textOverflow: 'ellipsis',
                  })}
                >
                  {item.label || item.name}
                </div>
                <div
                  className={css({
                    flex: 1,
                    overflow: 'hidden',
                    whiteSpace: 'nowrap',
                    textOverflow: 'ellipsis',
                  })}
                >
                  {item.widgetType}
                </div>
              </div>
            ))}
            popoverOverrides={popoverOverrides}
            popovers={inputStates.map((item: InputState) => ({ close }) => (
              <InputDetail
                globalParam={getGlobalParameter(item.name) ?? defaultGlobalParam}
                inputState={item}
                globalParameters={globalParameters}
                graphName={graphName}
                onChangeGlobalParameter={(value, input) => onChangeGlobalParameter(value, input, item)}
                onChangeInputState={onChangeInputState}
                close={close}
                chartProps={props as unknown as ChartProps}
              />
            ))}
            onChange={({ oldIndex, newIndex }) =>
              onSettingUpdate('inputStates', arrayMove(inputStates, oldIndex, newIndex))
            }
            onRemove={(index) => {
              deleteGlobalParameter(inputStates[index].name);
              onSettingUpdate('inputStates', arrayRemove(inputStates, index));
            }}
            onOpen={(index) => {
              onChartStateUpdate('curInputIndex', index);
            }}
          />
        </ChartSettingItemContainer>
      </ChartSettingSectionContainer>
      {inputStates.length > 0 && (
        <ChartSettingSectionContainer title="Display">
          <ChartSettingItemContainer>
            <StyledToggle
              checked={settings['invisibleInPreviewMode'] ?? false}
              onChange={(e) => onSettingUpdate('invisibleInPreviewMode', e.currentTarget.checked)}
            >
              <HelpText content="If this option is on, the input container will be invisible in preview mode.">
                Invisible In Preview Mode
              </HelpText>
            </StyledToggle>
          </ChartSettingItemContainer>
        </ChartSettingSectionContainer>
      )}
    </div>
  );
}
