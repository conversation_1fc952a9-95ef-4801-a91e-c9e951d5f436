import { useState } from 'react';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { DataType, GlobalInputProps, InputWidgetType } from '../type';
import CommonSetting from './commonSetting';
import DefaultInputSetting from './defaultInputSetting';
import DefaultSelectSetting from './defaultSelectSetting';
import GlobalTextArea from '../inputContainer/globalTextArea';
import GlobalDatePicker from '../inputContainer/globalDatePicker';
import ConfirmButtons from '../../../components/confirmButtons';
import { useGSQLQuery } from '../../query/useGSQLQuery';
import { useQueries, useGraphList, useSchema } from '../../../pages/chart/useMetaHook';
import GlobalSearch from './globalSearch';
import { showToast } from '../../../components/styledToasterContainer';
import { KIND } from 'baseui/toast';
import { AxiosError } from 'axios';
import { SearchItem } from '../../chartSlice';
import { ChartSettingItemContainer } from '../../chartTools/chartSetting/chartSettingItemContainer';
import { GlobalParam, GlobalParams } from '../../../chart';
import { getErrorMessage } from '../../../components/error';
import { getParamTypeDefaultValue } from '../../../chart/globalParams';
import { TypeInspector } from './components/TypeInspector';
import DefaultListSetting from './defaultListSetting';
import { FormProvider, useForm } from 'react-hook-form';
import DefaultMapSetting from './defaultMapSetting';
import { handleForm2InputStateGlobalParam } from './utils/form2InputStateGlobalParam';
import { InputFormDatatype } from './types';

interface InputDetailProps extends GlobalInputProps {
  globalParameters: GlobalParams;
  graphName: string;
  close?: () => void;
}

export default function InputDetail(props: InputDetailProps) {
  const [css] = useStyletron();

  const {
    globalParam,
    graphName: initialGraphName,
    chartProps,
    globalParameters,
    onChangeGlobalParameter,
    onChangeInputState,
    close,
  } = props;
  const form = useForm<InputFormDatatype>({
    defaultValues: {
      variableName: globalParam.name || 'variable',
      // input & dropdown & list
      selectedGraph: undefined,
      vertexType: undefined,

      // map
      keyType: 'string',
      valueType: 'string',

      // default list & map
      items: [{ value: '' }],
      // default single vertex
      item: undefined,
    },
  });

  const { baseURL, isClusterMode } = chartProps;

  const [inputState, setInputState] = useState(props.inputState);
  const [inputValue, setInputValue] = useState(globalParam);
  const keyType = form.watch('keyType');
  const valueType = form.watch('valueType');
  const vertexType = form.watch('vertexType');

  const { settings } = inputState;
  const searchPattern = settings['searchPattern'] ?? [];
  const patternLimit = settings['patternLimit'] ?? 500;
  const hopLimit = settings['hopLimit'] ?? 5;
  const query = settings['query'] ?? '';
  const timeLimit = settings['timeLimit'] ?? 1000;
  const memoryLimit = settings['memoryLimit'] ?? 1000;
  const staticData = settings['staticData'];
  const queryType = settings['queryType'] ?? 'pattern';
  const graphName = settings['graphName'] ?? initialGraphName;

  const [cachedQuery, setCachedQuery] = useState(query);
  const [cachedSearchPattern, setCachedSearchPattern] = useState(searchPattern);
  const [cachedPatternLimit, setCachedPatternLimit] = useState(patternLimit);

  const {
    schema,
    isLoading: isLoadingSchema,
    isError: isSchemaError,
    error: schemaError,
  } = useSchema(baseURL, graphName);
  const {
    queries,
    isLoading: isLoadingQueries,
    isError: isQueriesError,
    error: queriesError,
  } = useQueries(baseURL, graphName);
  const graphs = useGraphList(baseURL);

  const { error, chartData, refetch } = useGSQLQuery({
    baseURL,
    cache: false,
    force: false,
    queryType,
    searchPattern: settings['useQuery'] ? cachedSearchPattern : [],
    query: settings['useQuery'] ? cachedQuery : '',
    staticData: staticData,
    patternLimit: cachedPatternLimit,
    globalParameters,
    queries,
    queriesError,
    schema,
    schemaError,
    graphName,
    refreshRate: 0,
    isClusterMode,
    onSuccess: () => {
      if (!!settings.open && queryType !== 'static_data') {
        showToast({
          kind: KIND.positive,
          message: 'Query executed successfully.',
        });
      }
    },
    onError: (err: AxiosError<any, any>) => {
      if (!!settings.open && queryType !== 'static_data') {
        showToast({
          kind: KIND.negative,
          message: `Execute query failed. \n${getErrorMessage(err)}`,
        });
      }
    },
  });

  const [defaultValueError, setDefaultValueError] = useState('');
  const [minValueError, setMinValueError] = useState('');
  const [maxValueError, setMaxValueError] = useState('');

  const onChangeGlobalParamType = (type: string) => {
    if (['STRING', 'NUMBER', 'BOOL', 'DATETIME', 'VERTEX', 'LIST'].indexOf(type) === -1) {
      return;
    }
    if (type === 'LIST') {
      const tempGlobalParam = {
        ...inputValue,
        elementType: inputValue.type,
        value: [],
        type,
      } as GlobalParam;
      setInputValue(tempGlobalParam);
    } else {
      const tempGlobalParam = {
        ...inputValue,
        // @ts-ignore
        ...getParamTypeDefaultValue(type),
      } as GlobalParam;
      setInputValue(tempGlobalParam);
    }
  };

  const onChangeInputValue = (globalParam: GlobalParam) => {
    setInputValue(globalParam);
  };

  const onSettingUpdate = (key: string, value: any) => {
    const newInputState = {
      ...inputState,
      settings: {
        ...settings,
        [key]: value,
      },
    };
    setInputState(newInputState);
  };

  return (
    <div
      className={css({
        display: 'flex',
        flexDirection: 'column',
      })}
    >
      <FormProvider {...form}>
        <div
          className={css({
            padding: '12px',
            maxHeight: '75vh',
            overflow: 'auto',
          })}
        >
          {inputState && (
            <CommonSetting
              globalParameters={globalParameters}
              inputState={inputState}
              onChangeGlobalParamType={onChangeGlobalParamType}
              onChangeInputState={setInputState}
            />
          )}
          {(inputState?.widgetType === InputWidgetType.INPUT || inputState?.widgetType === InputWidgetType.SLIDER) && (
            <DefaultInputSetting
              globalParam={inputValue}
              inputState={inputState}
              chartProps={chartProps}
              onChangeGlobalParameter={onChangeInputValue}
              onChangeInputState={(inputState) => {
                setInputState(inputState);

                // reset error
                setDefaultValueError('');
                setMinValueError('');
                setMaxValueError('');
              }}
              defaultValueError={defaultValueError}
              minValueError={minValueError}
              maxValueError={maxValueError}
            />
          )}
          {inputState?.widgetType === InputWidgetType.TEXT && (
            <ChartSettingItemContainer label="Default Value">
              <GlobalTextArea
                globalParam={inputValue}
                inputState={inputState}
                onChangeGlobalParameter={onChangeInputValue}
                onChangeInputValue={onChangeInputValue}
                onChangeInputState={setInputState}
              />
            </ChartSettingItemContainer>
          )}
          {inputState?.widgetType === InputWidgetType.DATEPICKER && (
            <ChartSettingItemContainer label="Default Value">
              <GlobalDatePicker
                globalParam={inputValue}
                inputState={inputState}
                onChangeGlobalParameter={onChangeInputValue}
                onChangeInputValue={onChangeInputValue}
                onChangeInputState={setInputState}
              />
            </ChartSettingItemContainer>
          )}
          {inputState?.widgetType === InputWidgetType.DROPDOWN && (
            <DefaultSelectSetting
              chartData={chartData}
              graphName={graphName}
              schema={schema}
              labelKey={inputState.settings?.labelKey}
              valueKey={inputState.settings?.valueKey}
              globalParam={inputValue}
              inputState={inputState}
              onChangeGlobalParamType={onChangeGlobalParamType}
              onChangeGlobalParameter={onChangeInputValue}
              onChangeInputState={setInputState}
            />
          )}
          {inputState.widgetType === InputWidgetType.LIST && (
            <DefaultListSetting
              chartData={chartData}
              graphName={graphName}
              schema={schema}
              labelKey={inputState.settings?.labelKey}
              valueKey={inputState.settings?.valueKey}
              globalParam={inputValue}
              inputState={inputState}
              onChangeGlobalParamType={onChangeGlobalParamType}
              onChangeGlobalParameter={onChangeInputValue}
              onChangeInputState={setInputState}
              baseURL={chartProps.baseURL}
            />
          )}
          {inputState.widgetType === InputWidgetType.MAP && (
            <DefaultMapSetting
              chartData={chartData}
              graphName={graphName}
              schema={schema}
              labelKey={inputState.settings?.labelKey}
              valueKey={inputState.settings?.valueKey}
              globalParam={inputValue}
              inputState={inputState}
              onChangeGlobalParamType={onChangeGlobalParamType}
              onChangeGlobalParameter={onChangeInputValue}
              onChangeInputState={setInputState}
            />
          )}
          {inputState?.widgetType === InputWidgetType.DROPDOWN && (
            <GlobalSearch
              globalInputProps={{ ...props, inputState: inputState, onChangeInputState: setInputState }}
              chartInputProps={{
                queryType: queryType,
                searchPattern: searchPattern,
                patternLimit: patternLimit,
                hopLimit: hopLimit,
                timeLimit,
                memoryLimit,
                interpretQuery: query,
                staticData: staticData,
                graphName: graphName,
                schema: schema,
                onGraphChanged: (graphName) => {
                  setInputState({
                    ...inputState,
                    settings: { ...settings, graphName, searchPattern: [] },
                  });
                  setCachedSearchPattern([]);
                },
                graphs: graphs,
                chartProps: { ...chartProps, chartData, schema, graphName, settings, onSettingUpdate },
                globalParameters: globalParameters,
                queries: queries,
                isLoading: isLoadingSchema || isLoadingQueries,
                isError: isSchemaError || isQueriesError,
                onQueryTypeChange: (queryType) => onSettingUpdate('queryType', queryType),
                onSearchPatternChanged: (searchPattern) => onSettingUpdate('searchPattern', searchPattern),
                onPatternLimitChanged: (limit) => onSettingUpdate('patternLimit', limit),
                onHopLimitChanged: (limit) => onSettingUpdate('hopLimit', limit),
                onTimeLimitChanged: (limit) => onSettingUpdate('timeLimit', limit),
                onMemoryLimitChanged: (limit) => onSettingUpdate('memoryLimit', limit),
                onInterpretQueryChanged: (interpretQuery) => onSettingUpdate('query', interpretQuery),
                onStaticDataChanged: (staticData) => onSettingUpdate('staticData', staticData),
                onRunQuery: (value, limit?: number) => {
                  if (queryType === 'pattern') {
                    if (value === cachedSearchPattern && limit === cachedPatternLimit) {
                      // if the state is not changed, just run refetch
                      refetch();
                    } else {
                      // else update react-query input
                      setCachedSearchPattern(value as Array<SearchItem>);
                      setCachedPatternLimit(limit as number);
                    }
                  } else if (queryType === 'interactive') {
                    if (value === cachedQuery) {
                      // if the state is not changed, just run refetch
                      refetch();
                    } else {
                      // else update react-query input
                      setCachedQuery(value as string);
                    }
                  } else if (queryType === 'static_data') {
                    // there is no need to run query for static data
                  }
                },
              }}
              error={error}
            />
          )}
          <TypeInspector
            keyType={keyType}
            valueType={valueType}
            vertexType={vertexType}
            inputType={inputState.widgetType}
            dataType={inputState.dataType}
            isMultiple={inputState.settings?.multi}
          />
        </div>
      </FormProvider>
      <ConfirmButtons
        containerOverrides={{
          padding: '8px',
        }}
        onConfirm={async () => {
          let hasError = false;
          // validate number(default value, min/max)
          // only validate number on specific widget type
          const isNumberType =
            (inputState.widgetType === InputWidgetType.INPUT || inputState.widgetType === InputWidgetType.SLIDER) &&
            DataType[inputState.dataType.toString()] === DataType.Number;
          if (isNumberType) {
            const { min, max } = settings;
            const defaultValue = inputValue.value as number;

            if (min === '' || isNaN(min)) {
              setMinValueError('Min value is not a valid number');
              hasError = true;
            }
            if (max === '' || isNaN(max)) {
              setMaxValueError('Max value is not a valid number');
              hasError = true;
            }
            if (Number(max) < Number(min)) {
              setMaxValueError('Max value is less than min value');
              hasError = true;
            }

            // for params value, we allow NaN
            if (!isNaN(defaultValue)) {
              if (defaultValue > Number(max)) {
                setDefaultValueError('Default value is great than max value');
                hasError = true;
              }
              if (defaultValue < Number(min)) {
                setDefaultValueError('Default value is less than min value');
                hasError = true;
              }
            }
          }

          if (hasError) {
            return;
          }

          // validate form
          const pass = await form.trigger();
          if (!pass) {
            return;
          }

          const values = form.getValues();
          const { draftGlobalParams, draftInputState } = handleForm2InputStateGlobalParam(
            inputState,
            inputValue,
            values
          );

          onChangeInputState && onChangeInputState(draftInputState);
          onChangeGlobalParameter && onChangeGlobalParameter(draftGlobalParams, draftInputState);
          close();
        }}
        onCancel={() => {
          close();
        }}
      />
    </div>
  );
}

function isValidVariableName(variableName) {
  // Regular expression to match variable name criteria
  const regex = /^[a-zA-Z][a-zA-Z0-9_]*$/;

  // Test the variable name against the regular expression
  return regex.test(variableName);
}
