import React from 'react';
import { StyledInput } from '../../../components';
import { ChartSettingItemContainer } from '../../chartTools/chartSetting/chartSettingItemContainer';
import { GlobalInputProps } from '../type';
import { FormControl } from './formControl';

export default function NumberSetting(
  props: GlobalInputProps & {
    minValueError: string;
    maxValueError: string;
  }
) {
  const { inputState, onChangeInputState, minValueError, maxValueError } = props;

  return (
    <div>
      <ChartSettingItemContainer key="min" label="Min Value:">
        <FormControl error={minValueError}>
          <StyledInput
            value={String(inputState.settings['min'])}
            onChange={(e) => {
              const newInputState = {
                ...inputState,
                settings: { ...inputState.settings, min: e.target['value'] },
              };
              onChangeInputState(newInputState);
            }}
            type="number"
            placeholder="Min value"
            error={!!minValueError}
          />
        </FormControl>
      </ChartSettingItemContainer>
      <ChartSettingItemContainer key="max" label="Max Value:">
        <FormControl error={maxValueError}>
          <StyledInput
            value={String(inputState.settings['max'])}
            onChange={(e) => {
              const newInputState = {
                ...inputState,
                settings: { ...inputState.settings, max: e.target['value'] },
              };
              onChangeInputState(newInputState);
            }}
            type="number"
            placeholder="Max value"
            error={!!maxValueError}
          />
        </FormControl>
      </ChartSettingItemContainer>
      <ChartSettingItemContainer key="step" label="Step Value:">
        <FormControl>
          <StyledInput
            value={String(inputState.settings['step'])}
            onChange={(e) => {
              const newInputState = {
                ...inputState,
                settings: { ...inputState.settings, step: e.target['value'] },
              };
              onChangeInputState(newInputState);
            }}
            type="number"
            placeholder="Step value"
          />
        </FormControl>
      </ChartSettingItemContainer>
    </div>
  );
}
