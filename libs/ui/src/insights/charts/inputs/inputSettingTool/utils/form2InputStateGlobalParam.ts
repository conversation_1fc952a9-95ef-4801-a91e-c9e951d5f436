import { clone, upperCase } from 'lodash';
import { DataType, InputState, InputWidgetType } from '../../type';
import { GlobalParam } from '../../../../chart/globalParams';
import { InputFormDatatype } from '../types';
import {
  HybridFormMapItemType,
  HybridFormPrimitiveItemType,
  HybridFormVertexItemType,
} from '../../../chartSearch/chartSearchParam/chartSearchParamMultipleValue/types';

export const handleForm2InputStateGlobalParam = (
  inputState: InputState,
  globalParam: GlobalParam,
  values: InputFormDatatype
) => {
  const draftGlobalParams = clone(globalParam);
  const draftInputState = clone(inputState);

  draftGlobalParams.name = values.variableName;
  draftInputState.name = values.variableName;

  // for input vertex
  if (draftInputState.widgetType === InputWidgetType.INPUT) {
    if (draftInputState.dataType === DataType.Vertex) {
      draftInputState.selectedGraph = values.selectedGraph;
      Object.assign(draftGlobalParams, {
        type: 'VERTEX',
        value: { vertexType: values.item?.type, vertexID: values.item?.id },
      });
    }
  }

  // for dropdown vertex
  if (draftInputState.widgetType === InputWidgetType.DROPDOWN) {
    if (draftInputState.dataType === DataType.Vertex) {
      // TODO default dropdown vertex
      draftInputState.selectedGraph = values.selectedGraph;
    }
  }

  // for list
  if (draftInputState.widgetType === InputWidgetType.LIST) {
    if (values?.selectedGraph) {
      draftInputState.selectedGraph = values.selectedGraph;
    }

    if (values?.fixVertexType && values?.vertexType) {
      draftInputState.vertexType = values.vertexType;
    } else {
      draftInputState.vertexType = undefined;
    }
    const elementType = upperCase(draftInputState.dataType as string);

    if (elementType === 'VERTEX') {
      Object.assign(draftGlobalParams, {
        type: 'LIST',
        elementType,
        value: ((values.items || []) as HybridFormVertexItemType[]).map((i) => ({
          vertexType: i.type,
          vertexID: i.id,
        })),
      });
    } else {
      Object.assign(draftGlobalParams, {
        type: 'LIST',
        elementType,
        value: ((values.items || []) as HybridFormPrimitiveItemType[]).map((i) => i.value ?? ''),
      });
    }
  }

  // for map
  if (draftInputState.widgetType === InputWidgetType.MAP) {
    if (values.keyType && values.valueType) {
      draftInputState.keyType = values.keyType;
      draftInputState.valueType = values.valueType;
    }

    Object.assign(draftGlobalParams, {
      type: 'MAP',
      keyType: draftInputState.keyType,
      valueType: draftInputState.valueType,
      value: (values.items as HybridFormMapItemType[]).map((v) => ({ key: v.key ?? '', value: v.value ?? '' })),
    });
  }

  return {
    draftInputState,
    draftGlobalParams,
  };
};
