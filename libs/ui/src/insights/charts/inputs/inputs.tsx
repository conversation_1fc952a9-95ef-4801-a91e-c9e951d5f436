import React, { useEffect, useState, useRef, useMemo, useCallback } from 'react';
import { useStyletron } from 'baseui';

import { InputState } from './type';
import InputContainer from './inputContainer';
import InputHeader from './inputHeader';
import { ChartProps } from '../../chart/chart';
import { GlobalParam, GlobalParams } from '../../chart/globalParams';

const defaultGlobalParam: GlobalParam = {
  value: '',
  type: 'STRING',
  name: '',
  id: '',
};

const InputItem = ({
  item,
  curInputIndex,
  index,
  chartProps,
  globalParam,
  isEditMode,
  setGlobalParameter,
  onChangeInputState,
  setInputValue,
}) => {
  const [css, theme] = useStyletron();

  const onChangeGlobalParameter = useCallback(
    (globalParam) => setGlobalParameter(item.name, globalParam),
    [setGlobalParameter, item.name]
  );

  return (
    <div
      key={item.id}
      className={css({
        minWidth: '200px',
        minHeight: '60px',
        height: 'fit-content',
        margin: '4px',
        padding: '4px',
        backgroundColor: '#fff',
        borderRadius: theme.borders.radius300,
        outline: isEditMode && curInputIndex === index ? `2px solid ${theme.colors.primary}` : 'none',
      })}
    >
      <InputHeader name={item.name} label={item.label} />
      <InputContainer
        chartProps={chartProps}
        inputState={item}
        index={index}
        globalParam={globalParam}
        onChangeInputValue={(globalParam) => setInputValue(item.name, globalParam)}
        onChangeGlobalParameter={onChangeGlobalParameter}
        onChangeInputState={(input) => onChangeInputState(input, index)}
      />
    </div>
  );
};

export default function Inputs(props: ChartProps) {
  const [css, theme] = useStyletron();
  const { isEditMode, settings, chartStates, onSettingUpdate, getGlobalParameter, setGlobalParameter } = props;

  const inputStates = useMemo(() => settings['inputStates'] ?? [], [settings]);
  const curInputIndex = chartStates['curInputIndex'] ?? -1;

  // temporary input default value object
  const defaultValueObject = useMemo(() => {
    const inputStates = settings['inputStates'] ?? [];
    return inputStates.reduce((prev: GlobalParams, item: InputState) => {
      prev[item.name] = JSON.parse(
        JSON.stringify(
          getGlobalParameter(item.name) ?? {
            value: '',
            type: 'STRING',
            name: '',
            id: item.id,
          }
        )
      );
      return prev;
    }, {});
  }, [getGlobalParameter, settings]);
  const [inputValues, setInputValues] = useState<GlobalParams>(defaultValueObject);

  const stringifiedDefaultValueObject = JSON.stringify(defaultValueObject);
  const prevGlobalParamsRef = useRef(stringifiedDefaultValueObject);

  // reset the inputValues when an action is linking to inputs of the same page
  useEffect(() => {
    if (prevGlobalParamsRef.current !== stringifiedDefaultValueObject) {
      setInputValues(JSON.parse(stringifiedDefaultValueObject));
      prevGlobalParamsRef.current = stringifiedDefaultValueObject;
    }
  }, [stringifiedDefaultValueObject]);

  const onChangeInputState = useCallback(
    (input: InputState, index: number) => {
      const newInputStates = [...inputStates];
      if (index === newInputStates.length) {
        onSettingUpdate('inputStates', [...inputStates, input]);
      } else {
        newInputStates[index] = input;
        onSettingUpdate('inputStates', newInputStates);
      }
    },
    [inputStates, onSettingUpdate]
  );

  const setInputValue = useCallback(
    (name: string, globalParam: GlobalParam) => {
      if (!inputValues[name]) {
        return;
      }
      setInputValues({
        ...inputValues,
        [name]: globalParam,
      });
      if (isEditMode) {
        setGlobalParameter(name, globalParam);
      }
    },
    [inputValues, setGlobalParameter, isEditMode]
  );

  const getInputValue = useCallback(
    (name: string): GlobalParam => {
      if (isEditMode) {
        return getGlobalParameter(name) ?? defaultGlobalParam;
      } else {
        return inputValues[name];
      }
    },
    [getGlobalParameter, inputValues, isEditMode]
  );

  return (
    <div
      className={css({
        display: 'flex',
        alignItems: 'center',
        flexWrap: 'wrap',
      })}
    >
      {inputStates.map((item: InputState, index: number) => (
        <InputItem
          key={item.id}
          item={item}
          index={index}
          chartProps={props}
          isEditMode={isEditMode}
          curInputIndex={curInputIndex}
          setInputValue={setInputValue}
          globalParam={getInputValue(item.name)}
          setGlobalParameter={setGlobalParameter}
          onChangeInputState={onChangeInputState}
        />
      ))}
    </div>
  );
}
