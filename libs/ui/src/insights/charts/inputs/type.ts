import { ChartProps } from '../../chart/chart';
import { GlobalParam } from '../../chart/globalParams';
import { ChartSchema } from '../../chart/schema';

export enum DataType {
  Number = 'number',
  String = 'string',
  Datetime = 'datetime',
  Vertex = 'vertex',
  Bool = 'bool',
}

export enum InputWidgetType {
  INPUT = 'Input',
  TEXT = 'Text',
  SLIDER = 'Slider',
  DROPDOWN = 'Dropdown',
  DATEPICKER = 'Datepicker',
  MAP = 'Map',
  LIST = 'List',
}

export interface InputOption {
  label: string;
  value: string | number;
}

export interface InputState {
  id: string;
  name: string;
  dataType: DataType[keyof DataType];
  widgetType: string;
  // value: string | number | boolean | Date;
  settings: Record<string, any>;
  label?: string;
  placeholder?: string;

  // for map widget type
  keyType?: string;
  valueType?: string;

  // for list widget type (vertex list)
  selectedGraph?: string;
  vertexType?: string;
}

export interface GlobalInputProps {
  globalParam: GlobalParam;
  inputState: InputState;
  onChangeInputState?: (input: InputState) => void;
  schema?: ChartSchema;
  index?: number;
  chartProps?: ChartProps;
  onChangeInputValue?: (globalParam: GlobalParam) => void;
  onChangeGlobalParameter?: (globalParam: GlobalParam, inputState?: InputState) => void;
}
