import React, { useMemo } from 'react';
import { ChartProps } from '../../chart/chart';
import { MapWrapper } from '../../app-widgets/map/MapWrapper';
import { averageGeolocation, getMapData, generatePolylines } from '../../app-widgets/map/tg-map/util/util-functions';

import { MapConfiguration, MapData } from '../../app-widgets/map/tg-map/models';
import { CustomMapSettings } from './settings/types';

export default function MapChart(props: ChartProps) {
  const { chartData, schema, links, getLink, globalParameters, isEditMode } = props;
  const settings = props.settings as CustomMapSettings;
  const { clustering, coordinates, rulesByType, showEdges } = settings;
  const { graph } = chartData;

  /**
   * Data and configuration setup
   * Executes a series of functions in order to generate a valid
   * dataset. Markers are created from graph nodes while the edges are
   * being constructed as polygons. Also reacts to option changes.
   */
  const map: { data: MapData; config: MapConfiguration } = useMemo(() => {
    const markers = getMapData(graph.nodes, coordinates, schema, globalParameters, rulesByType);
    const polylines = generatePolylines(graph, markers, schema, showEdges, globalParameters, rulesByType);
    const center = averageGeolocation(markers);
    return {
      data: {
        markers,
        polylines,
      },
      config: {
        center: {
          latitude: center.latitude && !isNaN(center.latitude) ? center.latitude : 0,
          longitude: center.longitude && !isNaN(center.longitude) ? center.longitude : 0,
        },
        clustering,
        zoom: 3,
      },
    };
  }, [clustering, coordinates, graph, rulesByType, schema, globalParameters, showEdges]);

  return useMemo(
    () => (
      <div
        style={{
          height: '100%',
          width: '100%',
        }}
      >
        <MapWrapper
          config={map.config}
          data={map.data}
          id={props.id}
          actionsByType={settings.actionsByType}
          links={links}
          getLink={getLink}
          settings={settings}
          globalParameters={globalParameters}
          isEditMode={isEditMode}
        ></MapWrapper>
      </div>
    ),
    [map, props.id, links, getLink, settings, globalParameters, isEditMode]
  );
}
