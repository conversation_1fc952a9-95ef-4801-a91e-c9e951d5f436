import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { PLACEMENT, StatefulPopover } from 'baseui/popover';
import { StatefulSelect } from '@tigergraph/app-ui-lib/select';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { ChartSettingProps } from '../../../chart/chart';
import { StyleDesc } from '../../../components/ruleStyle';
import { ChartSettingItemContainer } from '../../chartTools/chartSetting/chartSettingItemContainer';
import GraphStyle from '../../graphNew/ui/graphStyle';
import { getNumTypeAttrs } from './functions';
import { CustomMapSettings, MapWmsSettings, TypeAttributeList } from './types';
import ChartSettingSectionContainer from '../../chartTools/chartSetting/chartSettingSectionContainer';
import { ChartSettingsInfo } from '../../../chart/chartRegister';
import ConfirmButtons from '../../../components/confirmButtons';
import { StyleLabel } from '../../../components/ruleStyle/components';
import { popoverOverrides, selectOverrides } from '../../../components/ruleStyle/baseui';
import HelpText from '../../chartTools/chartSetting/helpText';
import TemplateDrivenChartSettings from '../../chartTools/chartSetting/templateDrivenChartSettings';
import { Button } from '@tigergraph/app-ui-lib/button';
import { KIND, SIZE } from 'baseui/button';
import { isValidHttpUrl } from '../../../utils';
import GraphAction from '../../graphNew/ui/graphAction';

const nodeRuleConfigs: StyleDesc[] = [
  {
    styleKey: 'Marker Color',
    styleLabel: 'Marker Color',
    styleType: 'color',
  },
];

const edgeRuleConfigs: StyleDesc[] = [
  {
    styleKey: 'line-color',
    styleLabel: 'Edge color',
    styleType: 'color',
  },
  {
    styleKey: 'edge-width',
    styleLabel: 'Edge width',
    styleType: 'numeric',
    styleOptions: [
      {
        label: '2',
        value: 2,
      },
      {
        label: '3',
        value: 3,
      },
      {
        label: '4',
        value: 4,
      },
      {
        label: '5',
        value: 5,
      },
      {
        label: '6',
        value: 6,
      },
    ],
  },
];
const ruleConfigs = {
  nodeRuleConfigs,
  edgeRuleConfigs,
};

const defaultWmsSettings: MapWmsSettings = {
  serviceUrl: '',
  layers: '',
  styles: '',
  version: '1.1.1',
  format: 'image/jpeg',
  transparent: false,
  crs: 'EPSG3857',
};

export function MapSettingTool(
  props: ChartSettingProps & { TemplatedUI: ReturnType<ChartSettingsInfo['createUIComponent']> }
) {
  const [css, theme] = useStyletron();
  const { chartData, onSettingUpdate, TemplatedUI } = props;
  const mapSettings = props.settings as CustomMapSettings;

  const [isDataInit, setIsDataInit] = useState(false);

  const [wmsSettings, setWmsSettings] = useState<MapWmsSettings>({ ...defaultWmsSettings, ...(mapSettings.wms || {}) });
  const { serviceUrl, layers } = wmsSettings;
  const canReloadTiles = useMemo(() => {
    return isValidHttpUrl(serviceUrl) && layers;
  }, [serviceUrl, layers]);
  const reloadTiles = () => {
    onSettingUpdate('wms', { ...wmsSettings });
  };

  let selectValues = useRef<Record<string, { latitude?: string; longitude?: string }>>({});

  const vertexTypes = useMemo(() => {
    return getNumTypeAttrs(chartData?.graph.nodes);
  }, [chartData]);

  useEffect(() => {
    if (vertexTypes.length && !isDataInit) {
      tryAutoselect(vertexTypes, mapSettings, onSettingUpdate);
      for (const vertexType of vertexTypes) {
        selectValues.current[vertexType.name] = {};
      }
      setIsDataInit(true);
    }
  }, [vertexTypes, mapSettings, onSettingUpdate, isDataInit]);

  const renderLatlongSelect = (vertexType: TypeAttributeList, attrName: 'Latitude' | 'Longitude') => {
    return (
      <div>
        <StyleLabel>Select {attrName} Attribute:</StyleLabel>
        <StatefulSelect
          overrides={selectOverrides}
          options={vertexType.attrs.map((a) => ({ id: a, label: a }))}
          initialState={{
            value: [
              {
                id: mapSettings.coordinates?.[vertexType.name]?.[attrName] ?? null,
              },
            ],
          }}
          onChange={(event) => {
            selectValues.current[vertexType.name][attrName] = event.option ? String(event.option.id) : '';
          }}
          placeholder={`Select ${attrName.toLowerCase()} attribute`}
          clearable={true}
        />
      </div>
    );
  };

  return (
    <div>
      <ChartSettingSectionContainer title="Data" rowGap={'0'}>
        <div className={css({ paddingLeft: '4px', height: '32px', lineHeight: '32px', fontSize: '14px' })}>
          <HelpText content="Configure how a vertex type's attributes map to its longitude and latitude on the map. You should configure this to make a vertex type display on the map.">
            Vertices
          </HelpText>
        </div>
        {vertexTypes.map((vertexType) => {
          return (
            <div key={vertexType.name}>
              <StatefulPopover
                placement={PLACEMENT.bottomRight}
                onClose={() => {
                  selectValues.current[vertexType.name] = { latitude: undefined, longitude: undefined };
                }}
                content={({ close }) => (
                  <div
                    className={css({
                      ...theme.typography.Body2,
                    })}
                  >
                    {renderLatlongSelect(vertexType, 'Latitude')}
                    {renderLatlongSelect(vertexType, 'Longitude')}
                    <ConfirmButtons
                      onConfirm={() => {
                        const value = selectValues.current[vertexType.name];
                        const curLatitude = mapSettings['coordinates']?.[vertexType.name]?.latitude;
                        const curLongitude = mapSettings['coordinates']?.[vertexType.name]?.longitude;
                        if (
                          (typeof value.latitude === 'string' && value.latitude !== curLatitude) ||
                          (typeof value.longitude === 'string' && value.longitude !== curLongitude)
                        ) {
                          onSettingUpdate('coordinates', {
                            ...mapSettings['coordinates'],
                            [vertexType.name]: {
                              latitude: value.latitude ?? curLatitude,
                              longitude: value.longitude ?? curLongitude,
                            },
                          });
                        }
                        close();
                      }}
                      onCancel={close}
                    />
                  </div>
                )}
                returnFocus
                autoFocus
                ignoreBoundary
                popperOptions={{
                  modifiers: {
                    preventOverflow: {
                      enabled: false,
                    },
                    hide: {
                      enabled: false,
                    },
                  },
                }}
                overrides={popoverOverrides}
              >
                <div
                  className={css({
                    height: '32px',
                    cursor: 'pointer',
                    display: 'flex',
                    alignItems: 'center',
                    borderRadius: '4px',
                    paddingLeft: '6px',
                    ':hover': { background: '#EAECEF', color: theme.colors.black01 },
                    ':active': { background: '#E8F1FB' },
                    color:
                      mapSettings['coordinates']?.[vertexType.name]?.latitude &&
                      mapSettings['coordinates']?.[vertexType.name]?.longitude
                        ? theme.colors.black02
                        : '#BFC7CF',
                  })}
                >
                  {vertexType.name}
                </div>
              </StatefulPopover>
            </div>
          );
        })}
      </ChartSettingSectionContainer>
      <ChartSettingSectionContainer title="Display" rowGap={'12px'}>
        <TemplatedUI {...props} />
        {props.chartData?.graph ? (
          <ChartSettingItemContainer
            label="Conditional Styling"
            helpText="Specify a rule-based style to enrich visualization. You can define simple rules based on expression evaluation, select a color palette based on the range of an attribute value, or let the system pick a color based on the uniqueness of the value."
          >
            <GraphStyle {...props} ruleConfigs={ruleConfigs} />
          </ChartSettingItemContainer>
        ) : null}
      </ChartSettingSectionContainer>
      <ChartSettingSectionContainer title="Advanced">
        <ChartSettingItemContainer
          label="Map Actions"
          helpText="Map action allows you to create customized links on vertices, the action will be triggerred when you double click on each vertex on the map. For each action, you can link to another page or self with customized paramters."
        >
          <GraphAction {...props} />
        </ChartSettingItemContainer>
        <TemplateDrivenChartSettings
          settingsTemplate={{
            useWMS: {
              label: 'Use WMS',
              type: 'toggle',
              default: false,
              helpText:
                'A Web Map Service (WMS) is a standard protocol for serving georeferenced map images over the Internet.',
            },
          }}
          settings={mapSettings}
          updateSetting={onSettingUpdate}
        />
        {mapSettings.useWMS && (
          <>
            <TemplateDrivenChartSettings
              settingsTemplate={{
                serviceUrl: {
                  label: 'Service URL',
                  type: 'text',
                  default: '',
                  helpText: 'Comma-separated list of WMS layers to show',
                },
                layers: {
                  label: 'WMS Layers',
                  type: 'text',
                  default: '',
                  helpText: 'Comma-separated list of WMS layers to show',
                },
                styles: {
                  label: 'WMS Styles',
                  type: 'text',
                  default: '',
                  helpText: 'Comma-separated list of WMS styles',
                },
                format: {
                  label: 'Image Format',
                  type: 'text',
                  default: 'image/jpeg',
                  helpText: "WMS image format (use 'image/png' for layers with transparency)",
                },
                transparent: {
                  label: 'Transparent',
                  type: 'toggle',
                  default: false,
                  helpText: 'If true, the WMS service will return images with transparency',
                },
                version: {
                  label: 'Service Version',
                  type: 'text',
                  default: '1.1.1',
                  helpText: 'Version of the WMS service to use',
                },
                crs: {
                  label: 'CRS',
                  type: 'list',
                  default: 'EPSG3857',
                  values: ['EPSG3395', 'EPSG3857', 'EPSG4326'],
                  helpText: 'Coordinate Reference System to use for the WMS requests',
                },
              }}
              settings={wmsSettings}
              updateSetting={(field, value) => setWmsSettings({ ...wmsSettings, [field]: value })}
            />
            <Button
              overrides={{
                BaseButton: {
                  style: {
                    marginTop: '8px',
                    fontWeight: 600,
                  },
                },
              }}
              onClick={reloadTiles}
              kind={KIND.secondary}
              disabled={!canReloadTiles}
            >
              Reload tiles
            </Button>
          </>
        )}
      </ChartSettingSectionContainer>
    </div>
  );
}

function tryAutoselect(
  types: TypeAttributeList[],
  settings: CustomMapSettings,
  updateSetting: (key: string, value: any) => void
) {
  let settingCopy = { ...settings.coordinates };
  types.forEach(({ name, attrs: attributeList }) => {
    ['latitude', 'longitude'].forEach((attribute) => {
      if (attributeList.indexOf(attribute) > -1) {
        settingCopy[name] = { [attribute]: attribute, ...settingCopy[name] };
      }
    });
  });
  updateSetting('coordinates', settingCopy);
}

// function extractAttributeList(nodes: ExternalNode[], transparentAttributes = []): string[] {
//   const output: string[] = [];
//   // Get all the distinct keys across the data set.
//   const keys = Object.keys(nodes.reduce((result, e) => Object.assign(result, e.data), {}));
//   // Push the attribute types into the output.

//   for (const key of keys) {
//     if (transparentAttributes?.indexOf(key.toLowerCase()) === -1) {
//       const entity = nodes.find((e) => e.data[key] !== undefined && e.data[key] !== null);
//       if (entity) output.push(key);
//     }
//   }
//   return output;
// }
