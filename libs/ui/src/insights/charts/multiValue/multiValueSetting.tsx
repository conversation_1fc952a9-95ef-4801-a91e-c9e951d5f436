import React, { useMemo, useState } from 'react';
import { But<PERSON> } from '@tigergraph/app-ui-lib/button';
import { KIND, SHAPE, SIZE } from 'baseui/button';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { arrayMove, arrayRemove } from 'react-movable';
import shortUUID from 'short-uuid';
import { MdAdd } from '@react-icons/all-files/md/MdAdd';
import { Rules } from '../../components/ruleStyle';

import { ChartSettingProps } from '../../chart/chart';
import { StyledDraggableList } from '../../components/styledDraggableList';
import ChartSettingSectionContainer from '../chartTools/chartSetting/chartSettingSectionContainer';
import { ChartSettingItemContainer } from '../chartTools/chartSetting/chartSettingItemContainer';
import IconButton from '../../components/iconButton';
import IconButtonGroup from '../../components/iconButtonGroup';
import { ColorPicker } from '../../components/colorPicker';
import { StyledInput, StyledSelect, StyledSlider } from '../../components';

import { ReactComponent as TriangleDown } from '../../assets/images/triangleDown.svg';
import { ReactComponent as TriangleRight } from '../../assets/images/triangleRight.svg';
import AlignLeft from '../../assets/images/alignLeft.svg';
import AlignCenter from '../../assets/images/alignCenter.svg';
import AlignRight from '../../assets/images/alignRight.svg';
import AlignBottom from '../../assets/images/alignBottom.svg';
import AlignMiddle from '../../assets/images/alignMiddle.svg';
import AlignTop from '../../assets/images/alignTop.svg';
import { getKeyValues } from './util';
import { RadioGroup, Radio, ALIGN } from 'baseui/radio';
import { SelectLucideIconContainer } from '../../components/selectLucideIcon';

export type ValueType = {
  id: string;
  label: string;
  key?: string;

  iconURL?: string;
  iconSize: number;
  iconColor: string;

  fontColor: string;
  labelColor: string;
  fontSize: number;
  labelSize: number;
  styleRule: Rules;
  iconPostion?: string;
  labelPostion?: string;
};

export type SettingType = {
  values: ValueType[];
  horizontalAlign: 'left' | 'center' | 'right';
  verticalAlign: 'bottom' | 'middle' | 'top';
  textAlign?: 'start' | 'end' | 'left' | 'right' | 'center' | 'justify' | 'match-parent';
  labelPostion?: string;
};

function newValue(): ValueType {
  return {
    id: shortUUID.generate(),
    label: 'untitled',
    labelColor: '#000000',
    iconSize: 24,
    iconColor: '#000000',
    labelSize: 24,
    fontColor: '#000000',
    fontSize: 24,
    styleRule: [],
    iconPostion: '',
    labelPostion: '',
  };
}

export default function MultiValueSetting(props: ChartSettingProps) {
  const [css, theme] = useStyletron();

  const { onSettingUpdate, chartData } = props;
  const settings = props.settings as SettingType;

  const { horizontalAlign = 'center', verticalAlign = 'middle', values = [] } = settings;

  const keyValues = useMemo(() => {
    return getKeyValues(chartData);
  }, [chartData]);

  const onAddValue = () => {
    onSettingUpdate('values', [...values, newValue()]);
  };

  const onValueChange = (value: ValueType, index: number) => {
    onSettingUpdate('values', [...values.slice(0, index), value, ...values.slice(index + 1)]);
  };
  return (
    <ChartSettingSectionContainer title={'Display'}>
      <ChartSettingItemContainer
        label="Values"
        helpText="The list of values to be shown on the dashboard"
        right={
          <IconButton onClick={onAddValue}>
            <MdAdd size={16} color={theme.colors.accent} />
          </IconButton>
        }
      >
        <StyledDraggableList
          items={values.map((value, index) => (
            <ValueItem
              key={value.id}
              value={value}
              index={index}
              onValueChange={onValueChange}
              keyValues={keyValues}
              isCloud={props.isCloud}
            />
          ))}
          onChange={({ oldIndex, newIndex }) => onSettingUpdate('values', arrayMove(values, oldIndex, newIndex))}
          onRemove={(index) => {
            onSettingUpdate('values', arrayRemove(values, index));
          }}
        />
      </ChartSettingItemContainer>
      <ChartSettingItemContainer label="Alignment">
        <div>
          <div
            className={css({
              display: 'inline-block',
            })}
          >
            <IconButtonGroup
              labelAsClickValue={true}
              items={[
                {
                  icon: <AlignLeft />,
                  label: 'left',
                },
                {
                  icon: <AlignCenter />,
                  label: 'center',
                },
                {
                  icon: <AlignRight />,
                  label: 'right',
                },
              ]}
              columnGap={15}
              selected={horizontalAlign}
              onClick={(value) => {
                onSettingUpdate('horizontalAlign', value);
              }}
            />
          </div>
          <div
            className={css({
              display: 'inline-block',
              marginLeft: '20px',
            })}
          >
            <IconButtonGroup
              labelAsClickValue={true}
              columnGap={15}
              items={[
                {
                  icon: <AlignBottom />,
                  label: 'bottom',
                },
                {
                  icon: <AlignMiddle />,
                  label: 'middle',
                },
                {
                  icon: <AlignTop />,
                  label: 'top',
                },
              ]}
              selected={verticalAlign}
              onClick={(value) => {
                onSettingUpdate('verticalAlign', value);
              }}
            />
          </div>
        </div>
      </ChartSettingItemContainer>
    </ChartSettingSectionContainer>
  );
}

function ValueItem({
  value,
  index,
  onValueChange,
  keyValues,
  isCloud,
}: {
  value: ValueType;
  index: number;
  onValueChange: (value: ValueType, index: number) => void;
  keyValues: ReturnType<typeof getKeyValues>;
  isCloud: boolean;
}) {
  const [css] = useStyletron();
  const [expand, setExpand] = useState(false);
  const valueFontSize = value.fontSize ?? 24;

  return (
    <div>
      <div
        className={css({
          display: 'flex',
          alignItems: 'center',
          columnGap: '8px',
        })}
        onClick={() => setExpand(!expand)}
      >
        <Button kind="text" shape={SHAPE.square}>
          {expand ? <TriangleDown /> : <TriangleRight />}
        </Button>
        <div
          className={css({
            flex: 1,
            overflow: 'hidden',
            whiteSpace: 'nowrap',
            textOverflow: 'ellipsis',
          })}
        >
          {value.label}
        </div>
      </div>
      {expand ? (
        <>
          <ChartSettingItemContainer label="Label" helpText="The label that will appear above the value.">
            <StyledInput
              value={value.label}
              onChange={(e) => {
                onValueChange(
                  {
                    ...value,
                    label: e.currentTarget.value,
                  },
                  index
                );
              }}
            />
          </ChartSettingItemContainer>
          <ChartSettingItemContainer label="Value" helpText="Choose a property from the response to display.">
            <StyledSelect
              maxDropdownHeight={'400px'}
              options={Object.keys(keyValues).map((key) => ({ id: key, label: key }))}
              value={value.key ? [{ id: value.key }] : []}
              onChange={({ option }) => {
                onValueChange(
                  {
                    ...value,
                    key: String(option.id),
                  },
                  index
                );
              }}
              clearable={false}
            />
          </ChartSettingItemContainer>
          <ChartSettingItemContainer
            label="Label Font Color and Size"
            helpText="Specify the color and size of the text"
          >
            <div>
              <div
                className={css({
                  display: 'inline-block',
                  verticalAlign: 'middle',
                  width: '15%',
                })}
              >
                <ColorPicker
                  value={value.labelColor || '#000000'}
                  onChange={(color) => {
                    onValueChange(
                      {
                        ...value,
                        labelColor: color,
                      },
                      index
                    );
                  }}
                />
              </div>
              <div
                className={css({
                  display: 'inline-block',
                  verticalAlign: 'middle',
                  width: '80%',
                  marginLeft: '5%',
                })}
              >
                <StyledSlider
                  value={[value.labelSize]}
                  min={0}
                  max={100}
                  onChange={(prop) => {
                    onValueChange(
                      {
                        ...value,
                        labelSize: prop.value[0],
                      },
                      index
                    );
                  }}
                />
              </div>
            </div>
          </ChartSettingItemContainer>
          <ChartSettingItemContainer label="Label Font Position" helpText="Specify label font position">
            <div>
              <RadioGroup
                value={value?.labelPostion || ''}
                onChange={(e) => {
                  onValueChange(
                    {
                      ...value,
                      labelPostion: e.currentTarget.value,
                    },
                    index
                  );
                }}
                name="number"
                align={ALIGN.horizontal}
              >
                <Radio value="">
                  <span style={{ fontSize: '12px' }}>Top</span>
                </Radio>
                <Radio value="bottom">
                  <span style={{ fontSize: '12px' }}>Bottom</span>
                </Radio>
              </RadioGroup>
            </div>
          </ChartSettingItemContainer>
          <ChartSettingItemContainer
            label="Value Font Color and Size"
            helpText="Specify the color and size of the text"
          >
            <div>
              <div
                className={css({
                  display: 'inline-block',
                  verticalAlign: 'middle',
                  width: '15%',
                })}
              >
                <ColorPicker
                  value={value.fontColor || '#000000'}
                  onChange={(color) => {
                    onValueChange(
                      {
                        ...value,
                        fontColor: color,
                      },
                      index
                    );
                  }}
                />
              </div>
              <div
                className={css({
                  display: 'inline-block',
                  verticalAlign: 'middle',
                  width: '80%',
                  marginLeft: '5%',
                })}
              >
                <StyledSlider
                  value={[valueFontSize]}
                  min={0}
                  max={100}
                  onChange={(prop) => {
                    onValueChange(
                      {
                        ...value,
                        fontSize: prop.value[0],
                      },
                      index
                    );
                  }}
                />
              </div>
            </div>
          </ChartSettingItemContainer>
          <ChartSettingItemContainer
            label="Value Icon"
            helpText="Specify the icon to be shown on the widget before the value"
          >
            <SelectLucideIconContainer
              iconURL={value.iconURL}
              onIconSelected={(iconURL) => {
                onValueChange(
                  {
                    ...value,
                    // support toggle the iconURL
                    iconURL: value.iconURL === iconURL ? undefined : iconURL,
                  },
                  index
                );
              }}
              isCloud={isCloud}
            />
          </ChartSettingItemContainer>
          <ChartSettingItemContainer
            label="Value Icon Color and Size"
            helpText="Specify icon size and color. You cannot set the color of custom icons"
          >
            <div>
              <div
                className={css({
                  display: 'inline-block',
                  verticalAlign: 'middle',
                  width: '15%',
                })}
              >
                <ColorPicker
                  disabled={!value?.iconURL?.includes('builtin')}
                  value={value.iconColor || '#000000'}
                  onChange={(color) => {
                    onValueChange(
                      {
                        ...value,
                        iconColor: color,
                      },
                      index
                    );
                  }}
                />
              </div>
              <div
                className={css({
                  display: 'inline-block',
                  verticalAlign: 'middle',
                  width: '80%',
                  marginLeft: '5%',
                })}
              >
                <StyledSlider
                  value={[value.iconSize]}
                  min={0}
                  max={100}
                  onChange={(prop) => {
                    onValueChange(
                      {
                        ...value,
                        iconSize: prop.value[0],
                      },
                      index
                    );
                  }}
                />
              </div>
            </div>
          </ChartSettingItemContainer>
          <ChartSettingItemContainer label="Value Icon Position" helpText="Specify icon position">
            <div>
              <RadioGroup
                value={value?.iconPostion || ''}
                onChange={(e) => {
                  onValueChange(
                    {
                      ...value,
                      iconPostion: e.currentTarget.value,
                    },
                    index
                  );
                }}
                name="number"
                align={ALIGN.horizontal}
              >
                <Radio value="">
                  <span style={{ fontSize: '12px' }}>Default</span>
                </Radio>
                <Radio value="RightTop">
                  <span style={{ fontSize: '12px' }}>RightTop</span>
                </Radio>
              </RadioGroup>
            </div>
          </ChartSettingItemContainer>
        </>
      ) : null}
    </div>
  );
}
