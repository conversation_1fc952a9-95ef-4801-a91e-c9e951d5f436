import useLocalStorageState from 'ahooks/lib/useLocalStorageState';
import { useTour } from '@reactour/tour';

import ChartInputQuery from './chartInputQuery';
import ChartInputJSON from './chartInputJSON';
import { ChartSearch } from '../chartSearch';

import { QueryType, SearchItem } from '../chartSlice';
import { ChartProps, GlobalParams } from '../../chart';
import { QueryMeta } from '@tigergraph/tools-models/query';
import { ChartSchema } from '../../chart/schema';
import QueryTypeSelect from './queryTypeSelect';
import React, { useEffect } from 'react';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { useTimeMemoryDefaultLimit } from '../../pages/chart/useMetaHook';

export type ChartInputProps = {
  isFreeExplore?: boolean;
  isClusterMode?: boolean;
  onCopilot?: (prompt: string) => void;
  placeHolder?: string;
  sendIcon?: React.ReactNode;
  queryType: QueryType;
  searchPattern: Array<SearchItem>;
  patternLimit: number;
  hopLimit: number;
  memoryLimit: number;
  timeLimit: number;
  interpretQuery: string;
  staticData: any;
  graphName: string;
  graphs: string[];
  onGraphChanged: (graph: string) => void;
  chartProps: ChartProps;
  queries: { [key: string]: QueryMeta };
  globalParameters: GlobalParams;
  schema?: ChartSchema;
  isLoading: boolean;
  isError: boolean;
  className?: any;

  onQueryTypeChange: (queryType: QueryType) => void;
  onSearchPatternChanged: (searchPattern: Array<SearchItem>) => void;
  onPatternLimitChanged: (limit: number) => void;
  onHopLimitChanged: (limit: number) => void;
  onInterpretQueryChanged: (interpretQuery: string) => void;
  onStaticDataChanged: (staticData: any) => void;
  onTimeLimitChanged?: (limit: string | number) => void;
  onMemoryLimitChanged?: (limit: string | number) => void;

  onRunQuery: (query: string | Array<SearchItem>) => void;
};

export default function ChartInput({
  isFreeExplore,
  isClusterMode,
  onCopilot,
  placeHolder,
  sendIcon,
  queryType,
  searchPattern,
  patternLimit,
  hopLimit,
  timeLimit,
  memoryLimit,
  interpretQuery,
  staticData,
  graphName,
  graphs,
  isLoading,
  isError,
  onGraphChanged,
  chartProps,
  queries,
  globalParameters,
  onQueryTypeChange,
  onSearchPatternChanged,
  onPatternLimitChanged,
  onHopLimitChanged,
  onInterpretQueryChanged,
  onStaticDataChanged,
  onMemoryLimitChanged,
  onTimeLimitChanged,
  onRunQuery,
  className,
}: ChartInputProps) {
  const [css, theme] = useStyletron();

  const queryTypeSelectWidth = 50;

  const { setIsOpen } = useTour();

  const [showWriteQueryGuide, setShowWriteQueryGuide] = useLocalStorageState<string | undefined>('showWriteQueryGuide');
  const { data: defaultLimit } = useTimeMemoryDefaultLimit(true);

  useEffect(() => {
    setTimeout(() => {
      if (searchPattern.length === 0 && !showWriteQueryGuide) {
        setIsOpen(true);
        setShowWriteQueryGuide('1');
      }
    }, 100);
  }, [searchPattern, setIsOpen, showWriteQueryGuide, setShowWriteQueryGuide]);

  return (
    <div
      className={css({
        border: `1px solid ${theme.colors.divider}`,
        borderRadius: '2px',
        margin: '8px',
        overflow: 'hidden',
        position: 'relative',
        // need to create stacking context for code mirror: refer https://graphsql.atlassian.net/browse/TOOLS-1840
        zIndex: 0,
        ...className,
        backgroundColor: theme.colors['input.background'],
      })}
    >
      {queryType === 'pattern' ? (
        <ChartSearch
          isFreeExplore={isFreeExplore}
          isClusterMode={isClusterMode}
          onCopilot={onCopilot}
          placeHolder={placeHolder}
          sendIcon={sendIcon}
          {...chartProps}
          globalParameters={globalParameters}
          graphs={graphs}
          queries={queries}
          isLoading={isLoading}
          isError={isError}
          searchPattern={searchPattern}
          patternLimit={patternLimit}
          hopLimit={hopLimit}
          timeLimit={timeLimit ?? defaultLimit?.timeLimit}
          memoryLimit={memoryLimit ?? defaultLimit?.memoryLimit}
          onTimeLimitChanged={onTimeLimitChanged}
          onMemoryLimitChanged={onMemoryLimitChanged}
          onPatternLimitChanged={onPatternLimitChanged}
          onHopLimitChanged={onHopLimitChanged}
          onSearchPatternChanged={onSearchPatternChanged}
          onRunQuery={onRunQuery}
          onGraphChanged={onGraphChanged}
          queryTypeSelect={<QueryTypeSelect queryType={queryType} onQueryTypeChange={onQueryTypeChange} />}
        />
      ) : (
        <div
          className={css({
            display: 'flex',
          })}
        >
          <div
            className={css({
              margin: '6px 0 0 6px',
            })}
          >
            <QueryTypeSelect queryType={queryType} onQueryTypeChange={onQueryTypeChange} />
          </div>
          <div
            className={css({
              width: `calc(100% - ${queryTypeSelectWidth}px)`,
            })}
          >
            {queryType === 'interactive' && (
              <ChartInputQuery
                value={interpretQuery}
                onValueChanged={(value) => onInterpretQueryChanged(value)}
                graphName={graphName}
                globalParameters={globalParameters}
                schema={chartProps.schema}
                timeLimit={timeLimit ?? defaultLimit?.timeLimit}
                memoryLimit={memoryLimit ?? defaultLimit?.memoryLimit}
                onTimeLimitChanged={onTimeLimitChanged}
                onMemoryLimitChanged={onMemoryLimitChanged}
                onRunQuery={onRunQuery}
                sendIcon={sendIcon}
              />
            )}
            {queryType === 'static_data' && (
              <ChartInputJSON
                value={typeof staticData !== 'string' ? JSON.stringify(staticData, null, 4) : staticData}
                onValueChanged={(value) => onStaticDataChanged(value)}
              />
            )}
          </div>
        </div>
      )}
    </div>
  );
}
