import React, { lazy, memo, useCallback, useEffect, useState } from 'react';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { IconProp } from '@fortawesome/fontawesome-svg-core';
import { faPlay } from '@fortawesome/free-solid-svg-icons/faPlay';
import { Button } from '@tigergraph/app-ui-lib/button';
import { StatefulPopover } from '@tigergraph/app-ui-lib/popover';
import { PLACEMENT, TRIGGER_TYPE } from 'baseui/popover';
import { ChartSchema } from '../../chart/schema';
import { GlobalParams } from '../../chart/globalParams';
import { ParametersTable } from '../../components/parametersTable';
import { checkQuery } from '@tigergraph/tools-models';
import ChartSearchConfigButton from '../chartSearch/chartSearchConfigButton';

const Editor = lazy(
  () =>
    import(
      /* webpackChunkName: "editor" */
      '../../components/editor'
    )
);

export type QueryInputQueryProps = {
  value: string;
  memoryLimit: number;
  timeLimit: number;
  onValueChanged: (value: string) => void;
  onTimeLimitChanged?: (limit: string | number) => void;
  onMemoryLimitChanged?: (limit: string | number) => void;
  onRunQuery: (query: string) => void;
  graphName: string;
  globalParameters: GlobalParams;
  schema?: ChartSchema;
  sendIcon?: React.ReactNode;
};

export default function ChartInputQuery({
  value,
  memoryLimit,
  timeLimit,
  onTimeLimitChanged,
  onMemoryLimitChanged,
  onValueChanged,
  onRunQuery,
  graphName,
  globalParameters,
  schema,
  sendIcon,
}: QueryInputQueryProps) {
  const [css, theme] = useStyletron();

  const [isEdited, setIsEdited] = useState(false);

  useEffect(() => {
    if (isEdited) {
      return;
    }
    if (graphName && !value) {
      const query = `INTERPRET QUERY() FOR GRAPH ${graphName} {
  PRINT "it works";
}`;
      onValueChanged(query);
    }
  }, [graphName, value, isEdited, onValueChanged]);

  const checkCode = useCallback(
    async (code: string) => {
      // we want to extract the real graph name from code
      let regex = /FOR\s+GRAPH\s+(\S+)\s/i;
      let result = code.match(regex);
      // fallback to graphName
      const res = await checkQuery({
        code,
        graph: result ? result[1] : graphName,
      });
      // TODO fix this type
      return res.data as any;
    },
    [graphName]
  );

  return (
    <div>
      <Editor
        value={value}
        onValueChange={(newValue) => {
          if (newValue === value) {
            return;
          }
          setIsEdited(true);
          onValueChanged(newValue);
        }}
        lang="gsql"
        checkCode={checkCode}
        graphInfo={schema?.results}
      />
      <div
        className={css({
          position: 'absolute',
          left: '4px',
          bottom: '8px',
        })}
      >
        <StatefulPopover
          content={() => (
            <div>
              <ParametersTable params={globalParameters} />
              <ol
                className={css({
                  fontSize: '14px',
                  color: theme.colors.gray600,
                  padding: 0,
                  margin: '4px 0 0 16px ',
                })}
              >
                <li>
                  You can bind the variable to query by <br />
                  <strong>naming the query parameter the same name as the variable name.</strong>
                  <div>
                    <div
                      className={css({
                        marginTop: '8px',
                        marginBottom: '-8px',
                      })}
                    >
                      For example
                    </div>
                    <pre>
                      INTERPRET QUERY(String <strong>variableName</strong>)
                      {`FOR GRAPH ${graphName} {

}
`}
                    </pre>
                  </div>
                </li>
                <li>
                  You can create variables by creating <strong>Inputs Widget</strong>.
                </li>
              </ol>
            </div>
          )}
          triggerType={TRIGGER_TYPE.hover}
          returnFocus
          autoFocus
          showArrow={false}
          placement={PLACEMENT.bottom}
          overrides={{
            Body: {
              style: {
                backgroundColor: '#fff',
                marginTop: '8px',
              },
            },
            Inner: {
              style: {
                backgroundColor: '#fff',
                color: theme.colors.subText,
              },
            },
          }}
        >
          <Button kind="link">Variables</Button>
        </StatefulPopover>
      </div>
      <div
        className={css({
          position: 'absolute',
          right: '16px',
          bottom: '16px',
        })}
      >
        <ChartSearchConfigButton
          timeLimit={timeLimit}
          memoryLimit={memoryLimit}
          onMemoryLimitChanged={onMemoryLimitChanged}
          onTimeLimitChanged={onTimeLimitChanged}
        />
        <Button kind="text" onClick={() => onRunQuery(value)}>
          {sendIcon ? sendIcon : <FontAwesomeIcon icon={faPlay as IconProp} />}
        </Button>
      </div>
    </div>
  );
}
