import { useQuery } from 'react-query';
import { AxiosError } from 'axios';
import { useCallback } from 'react';

import { QueryType, SearchItem } from '../chartSlice';
import { buildPatternQuery, processPatternData } from '../data/pattern';
import { GlobalParams, buildURLSearchParams } from '../../chart/globalParams';
import { ChartData } from '../../chart/type';
import { QueryMeta, QueryParam } from '@tigergraph/tools-models/query';
import { isMultiple, isMap, isShowSchema, SEARCH_KEYWORDS } from '../chartSearch';
import { ChartSchema } from '../../chart/schema';
import { convert2Number, encodeURLSearchParams } from '../../utils';
import { parseGraph } from '@tigergraph/tools-models/gvis/insights';
import { parseTable } from '../data/table';
import { insertGraphNameForInterpretMode, parseJSON, stringifyJSON } from '@tigergraph/tools-models/utils';

import { emptyQueries, emptySchema } from '../../pages/chart/useMetaHook';
import { getErrorMessage } from '../../components/error';
import { getAxiosInstance, getQueriesInfo, runInterpret } from '@tigergraph/tools-models';
import set from 'lodash/set';
import { forEach, isObject } from 'lodash';

type QueryProp = {
  baseURL: string;
  params: string;
  query: string;
  cache: boolean;
  force: boolean;
  timeLimit?: number;
  memoryLimit?: number;
};

type InstalledQueryProp = {
  baseURL: string;
  params: { [key: string]: any };
  graphName: string;
  queryName: string;
  cache: boolean;
  force: boolean;
  timeLimit?: number;
  memoryLimit?: number;
};

export type APIResponse = {
  error: boolean;
  message: string;
  results: any;
};

const installedURL = '/api/restpp/query';

const cacheInterpretedURL = '/api/cache/run/gsql-server/interpreted_query';
const cacheInstalledURL = '/api/cache/run/restpp/query';

const interpretedAPI = async ({ baseURL, params, query, cache, force, memoryLimit, timeLimit }: QueryProp) => {
  if (force) {
    params = `${params}${params ? '&__force=true' : '__force=true'}`;
  }
  const commonHeaders = {};

  if (memoryLimit && Number(memoryLimit)) {
    commonHeaders['GSQL-QueryLocalMemLimitMB'] = memoryLimit;
  }
  if (timeLimit && Number(timeLimit)) {
    commonHeaders['Gsql-Timeout'] = Number(timeLimit) * 1000;
  }

  if (cache) {
    const response = await getAxiosInstance().post<APIResponse>(`${baseURL}${cacheInterpretedURL}?${params}`, query, {
      headers: {
        ...commonHeaders,
        'Content-Type': 'text/plain',
      },
    });
    return response.data;
  } else {
    const response = await runInterpret(
      {
        params: new URLSearchParams(params) || undefined,
        queryBody: query,
        headers: commonHeaders,
      },
      {
        baseURL,
      }
    );

    return response.data;
  }
};

const installedAPI = async ({
  baseURL,
  params,
  graphName,
  queryName,
  cache,
  force,
  memoryLimit,
  timeLimit,
}: InstalledQueryProp) => {
  const commonHeaders = {};
  if (memoryLimit && Number(memoryLimit)) {
    commonHeaders['GSQL-QueryLocalMemLimitMB'] = memoryLimit;
  }
  if (timeLimit && Number(timeLimit)) {
    commonHeaders['Gsql-Timeout'] = Number(timeLimit) * 1000;
  }
  const response = await getAxiosInstance().post<APIResponse>(
    `${baseURL}${cache ? cacheInstalledURL : installedURL}/${graphName}/${queryName}${force ? '?__force=true' : ''}`,
    params,
    {
      headers: {
        ...commonHeaders,
        'Content-Type': 'application/json',
      },
      transformRequest: (data) => {
        return stringifyJSON(data);
      },
      transformResponse: (data) => {
        if (typeof data !== 'string') return data;
        return parseJSON(data);
      },
    }
  );
  return response.data;
};

const pickVerticesAPI = async ({
  baseURL,
  graphName,
  vertexType,
  limit,
}: {
  baseURL: string;
  graphName: string;
  vertexType: string;
  limit: number;
}) => {
  const response = await getAxiosInstance().get<APIResponse>(
    `${baseURL}/api/restpp/graph/${graphName}/vertices/${vertexType}?_api_=v2&limit=${limit}`
  );
  return response.data;
};

const getVertexByID = async ({
  baseURL,
  graphName,
  vertexType,
  id,
}: {
  baseURL: string;
  graphName: string;
  vertexType: string;
  id: number | string;
}) => {
  const response = await getAxiosInstance().post<APIResponse>(
    `${baseURL}/api/restpp/kstep_expansion/${graphName}`,
    { source_vertices: [{ type: vertexType, id }], expansion_steps: [] },
    {
      headers: {
        'Content-Type': 'application/json',
      },
    }
  );
  return response.data;
};

// will return following error as error
// 1 code check error
// 2 params missing error
// 3 gsql run error
export function useGSQLQuery({
  baseURL,
  cache,
  force,
  queryType,
  searchPattern,
  query,
  staticData,
  patternLimit,
  globalParameters,
  queries,
  queriesError,
  schema,
  schemaError,
  graphName,
  refreshRate,
  isClusterMode,
  timeLimit,
  memoryLimit,
  manual,
  onSuccess,
  onError,
}: {
  baseURL: string;
  cache: boolean;
  force: boolean;
  queryType: QueryType;
  searchPattern: Array<SearchItem>;
  query: string;
  staticData: any;
  patternLimit: number;
  globalParameters: GlobalParams;
  queries: { [key: string]: QueryMeta };
  queriesError: AxiosError | undefined;
  schema: ChartSchema;
  schemaError: AxiosError | undefined;
  graphName: string;
  refreshRate: number;
  isClusterMode?: boolean;
  timeLimit?: number;
  memoryLimit?: number;
  manual?: boolean;
  onSuccess?: (chartData: ChartData) => void;
  onError?: (err: AxiosError<any, any>) => void;
}) {
  let enabled = true;
  let constructedQuery = '';
  let params = new URLSearchParams();
  const paramsJSONData = {};
  let queryName = '';
  let errorMessage = '';

  let handleAsSimplePatternSearch = false;

  // disable query when schema is not loaded
  if (schema === emptySchema) {
    enabled = false;
    if (schemaError) {
      errorMessage = getErrorMessage(schemaError);
    }
  } else if (queryType === 'interactive' && query) {
    constructedQuery = interpolateQuery(query, globalParameters);
  } else if (queryType === 'pattern') {
    if (searchPattern.length === 0) {
      enabled = false;
    } else if (isShowSchema(searchPattern[0])) {
      enabled = false;
    } else {
      if (searchPattern[0].type === SEARCH_KEYWORDS.query) {
        queryName = searchPattern[0].data;
        const query = queries[queryName];
        // for cached query, we require queries to be installed
        if (!query || (cache && query && !query.installed)) {
          enabled = false;
          // only report error after queries list is loaded
          if (queriesError) {
            errorMessage = getErrorMessage(queriesError);
          } else if (cache && query && !query.installed) {
            errorMessage = `The query "${queryName}" in graph "${graphName}" is not installed. Please install the query first.`;
          } else if (queries !== emptyQueries) {
            // we successfully fetched the query list but can not find query in query list
            errorMessage = `Unable to find query "${queryName} in graph "${graphName}".`;
          }
        } else {
          if (query?.installed) {
            generateParamsData(searchPattern, globalParameters, query, paramsJSONData);
          } else {
            generateQueryURLSearchParams(searchPattern, globalParameters, query, params);
          }

          constructedQuery = queries[queryName].originalCode;
        }
      } else {
        if (isClusterMode && isSimplePatternSearch(searchPattern)) {
          handleAsSimplePatternSearch = true;
        } else {
          try {
            constructedQuery = buildPatternQuery(searchPattern, schema.results, globalParameters, patternLimit);
          } catch (error: any) {
            enabled = false;
            errorMessage = `Unable to build the query. Please change the search content or graph schema. \n${error.message}`;
          }
        }
      }
    }
  }

  // pattern type no need for getParams
  const enableCodeCheck = constructedQuery && queryType === 'interactive' ? true : false;
  // processing constructedQuery
  if (constructedQuery) {
    // replace CREATE with INTERPRET
    const regexp = new RegExp(/\bCREATE\b[\s\S]*?\bQUERY\b[\s\S]*?\(/i);
    constructedQuery = constructedQuery.replace(regexp, 'INTERPRET QUERY (');
    constructedQuery = insertGraphNameForInterpretMode(constructedQuery, graphName);
  }

  // we currently skip code check as getParams also do the check
  // For interactive query, need to get parameters first.
  let {
    data: queryParams,
    isLoading: isCodeCheckLoading,
    isError: isCodeCheckError,
    error: codeCheckError,
    refetch: codeCheckRefetch,
    isFetching: isCodeCheckFetching,
  } = useQuery<QueryParam[], AxiosError>(
    ['queryParams', baseURL, constructedQuery],
    async () => {
      const response = await getQueriesInfo(
        {
          queryBody: constructedQuery,
          queryParams: {
            graph: graphName,
          },
        },
        {
          baseURL,
        }
      );

      let params = response.data.results.params;
      return params;
    },
    {
      enabled: enableCodeCheck,
    }
  );

  // todo(lin): seems react-query's isFetching is true when enabled is false;
  // so we reset isCodeCheckFetching to false when
  if (!enableCodeCheck) {
    isCodeCheckFetching = false;
  }

  let paramsCheckError: Error = null;
  if (queryType === 'interactive') {
    // wait until we the queryParams from tigergraph
    if (!queryParams) {
      enabled = false;
    } else {
      const { error, urlSearchParams } = buildURLSearchParams(globalParameters, queryParams, true);
      params = urlSearchParams;
      if (error) {
        paramsCheckError = error;
        enabled = false;
      }
    }
  }

  // always enable for static data
  if (queryType === 'static_data') {
    enabled = true;
  }

  let paramsStr = encodeURLSearchParams(params);

  const select = useCallback(
    (response: ChartData | APIResponse) => {
      if ('graph' in response) {
        return response;
      }
      if (queryType === 'static_data') {
        return parseResults(response, queryType);
      }
      let { results } = response;
      const chartData = parseResults(results, queryType, searchPattern, schema);
      return chartData;
    },
    [queryType, searchPattern, schema]
  );

  let { isLoading, isError, isSuccess, error, data, refetch, isFetching } = useQuery<
    APIResponse | ChartData,
    AxiosError,
    ChartData
  >(
    [
      'query',
      baseURL,
      constructedQuery,
      paramsStr,
      paramsJSONData,
      graphName,
      queryName,
      queryType,
      staticData,
      searchPattern,
      handleAsSimplePatternSearch,
      queries[queryName],
      cache,
      force,
    ],
    async () => {
      if (!enabled && errorMessage) {
        throw Error(errorMessage);
      }
      if (queryType === 'static_data') {
        try {
          return JSON.parse(staticData);
        } catch (error) {
          throw new Error(`Unable to parse static data. Please check the data format. \n${error}`);
        }
      }
      let response = { results: {}, error: false, message: '' } as APIResponse;

      if (handleAsSimplePatternSearch) {
        const [vertexItem, filterItem] = searchPattern;

        const vertexType = vertexItem.data;
        const paramName = filterItem.paramName;

        if (paramName === 'vertex_limit') {
          const limit = filterItem.data;
          response = await pickVerticesAPI({ baseURL, graphName, vertexType, limit: Number(limit) || 5 });
        } else {
          // the param is id
          const id = filterItem.data;
          response = await getVertexByID({ baseURL, graphName, vertexType, id });
        }
      } else if (
        queryType === 'pattern' &&
        searchPattern[0]?.type === SEARCH_KEYWORDS.query &&
        graphName &&
        queryName &&
        queries?.[queryName]?.installed
      ) {
        response = await installedAPI({
          baseURL,
          params: paramsJSONData,
          graphName,
          queryName,
          cache,
          force,
          timeLimit,
          memoryLimit,
        });
      } else {
        response = await interpretedAPI({
          baseURL,
          params: paramsStr,
          query: constructedQuery,
          cache,
          force,
          timeLimit,
          memoryLimit,
        });
      }

      return response;
    },
    {
      enabled: enabled && !manual,
      select,
      onSuccess,
      onError,
      refetchInterval: refreshRate && refreshRate * 1000,
      // we do not want to retry when error
      retry: false,
    }
  );

  if (!enabled) {
    isFetching = false;
  }

  // refetch only called with the same params
  // so if query changed, we will use setState() to trigger api request
  const refetchCallback = useCallback(async () => {
    if (queryType === 'interactive') {
      const codeCheckResult = await codeCheckRefetch();
      if (codeCheckResult.error) {
        return codeCheckResult;
      }
    }
    const result = await refetch({});
    return result;
  }, [codeCheckRefetch, refetch, queryType]);

  return {
    isLoading: isCodeCheckLoading || isLoading,
    isError: errorMessage !== '' || isCodeCheckError || paramsCheckError !== null || isError,
    isSuccess,
    error: errorMessage ? new Error(errorMessage) : codeCheckError || paramsCheckError || error,
    chartData: data,
    constructedQuery,
    refetch: refetchCallback,
    isFetching: isCodeCheckFetching || isFetching,
  };
}

function parseResults(
  results: any,
  queryType?: QueryType,
  searchPattern?: Array<SearchItem>,
  schema?: ChartSchema
): ChartData {
  if (queryType === 'pattern' && results && results.length > 0 && 'pattern_view' in results[0]) {
    results = processPatternData(results, searchPattern!, schema);
  }

  const mapData = results;
  const map = mapData;

  const graph = parseGraph(results);
  const tables = parseTable(results);
  let chartData = {
    graph,
    tables,
    results,
    map,
  };
  return chartData;
}

function formatPrimitiveValue(type: string, value: any) {
  switch (type) {
    case 'UINT':
    case 'INT':
    case 'INT8':
    case 'INT64':
    case 'FLOAT':
    case 'DOUBLE':
    case 'NUMBER':
      return convert2Number(value);
    case 'BOOL':
    case 'BOOLEAN':
      if (typeof value === 'boolean') {
        return value;
      }
      if (value === 'false') {
        return false;
      }
      return Boolean(value);
    default:
      break;
  }

  return value;
}

export function interpolateQuery(query: string, globalParameters: GlobalParams): string {
  let keys = Object.keys(globalParameters);
  for (let key of keys) {
    let param = globalParameters[key];
    let interpolateValue = '';

    if (param.type === 'NUMBER' || param.type === 'DATETIME') {
      interpolateValue = `${param.value}`;
    } else if (param.type === 'STRING') {
      interpolateValue = `"${param.value}"`;
    }

    query = query.replaceAll(`$${key}`, interpolateValue);
  }
  return query;
}

export const generateQueryURLSearchParams = (
  searchPattern: SearchItem[],
  globalParameters: GlobalParams,
  query: QueryMeta,
  params: URLSearchParams
) => {
  const paramsData: Record<string, any> = {};
  generateParamsData(searchPattern, globalParameters, query, paramsData);

  const serializeVertexObject = (obj: Record<string, string>, field: string, index: number = 0) => {
    if (obj?.id && obj?.type) {
      params.set(`${field}[${index}]`, obj.id);
      params.set(`${field}[${index}].type`, obj.type);
    }
  };

  // params serialize to URLSearchParams
  forEach(paramsData, (data, paramName) => {
    if (Array.isArray(data)) {
      data.forEach((d: Record<string, string> | string, idx) => {
        if (isObject(d)) {
          // map item or list item
          // but map item cannot be serialize to querystring
          serializeVertexObject(d, paramName, idx);
        } else {
          params.append(paramName, d);
        }
      });
    } else if (isObject(data)) {
      serializeVertexObject(data as Record<string, string>, paramName);
    } else {
      params.set(paramName, data);
    }
  });
};

// check if the search pattern is simple pattern search
// 1. the search pattern only contains two items
// 2. the first item is vertex item and the second item is param item with id or vertex_limit
function isSimplePatternSearch(searchPattern: SearchItem[]): boolean {
  if (searchPattern.length !== 2) {
    return false;
  }

  const [item1, item2] = searchPattern;

  if (item1.type !== SEARCH_KEYWORDS.vertex || item2.type !== SEARCH_KEYWORDS.param) {
    return false;
  }

  if (item2.paramName === 'vertex_limit' || item2.paramName === 'id') {
    return true;
  }

  return false;
}

// clean copilot
// clean free explorer
/**
 * only for installed query data params
 */
export const generateParamsData = (
  searchPattern: SearchItem[],
  globalParameters: GlobalParams,
  query: QueryMeta,
  paramsData: Record<string, any>
) => {
  for (let i = 1; i < searchPattern.length; i++) {
    const pattern = searchPattern[i];
    const { paramName, paramType, vertexType, paramGlobalInput, vertexTypeGlobalInput, data } = pattern;
    const isParamInGlobalInput = paramGlobalInput && paramGlobalInput in globalParameters;

    if (isMultiple(pattern)) {
      // list or dropdown
      const values = getMultipleParamsData({ pattern, query, globalParameters });
      set(paramsData, paramName, values);
    } else if (isMap(pattern)) {
      // map
      const values = getMapParamsData({ pattern, paramGlobalInput, globalParameters });
      set(paramsData, paramName, values);
    } else if (paramType === 'VERTEX') {
      // vertex input
      if (isParamInGlobalInput) {
        const globalParameter = globalParameters[paramGlobalInput];
        set(
          paramsData,
          [paramName, 'id'],
          globalParameter.type === 'VERTEX' ? globalParameter.value.vertexID : String(globalParameter.value)
        );
      } else {
        set(paramsData, [paramName, 'id'], pattern.data);
      }

      // vertex type
      if (vertexTypeGlobalInput && vertexTypeGlobalInput in globalParameters) {
        const globalParameter = globalParameters[vertexTypeGlobalInput];
        set(
          paramsData,
          [paramName, 'type'],
          globalParameter.type === 'VERTEX' ? globalParameter.value.vertexType : String(globalParameter.value)
        );
      } else {
        set(paramsData, [paramName, 'type'], vertexType);
      }
    } else if (isParamInGlobalInput) {
      // other primitive with input
      const globalParam = globalParameters[paramGlobalInput];
      set(paramsData, paramName, formatPrimitiveValue(globalParam.type, globalParam.value));
    } else if (isMap(pattern)) {
      set(paramsData, paramName, pattern.mapData || { keylist: [], valuelist: [] });
    } else {
      // other primitive in search pattern
      set(paramsData, paramName, formatPrimitiveValue(paramType, data));
    }
  }
};

const getMultipleParamsData = ({
  pattern,
  query,
  globalParameters,
}: {
  pattern: SearchItem;
  query: QueryMeta;
  globalParameters: GlobalParams;
}) => {
  const { paramList, elementType, vertexType, paramGlobalInput } = pattern;
  const result = [];
  const isParamInGlobalInput = paramGlobalInput && paramGlobalInput in globalParameters;
  const globalParameter = globalParameters[paramGlobalInput];

  if (isParamInGlobalInput && globalParameter.type === 'LIST') {
    if (globalParameter.elementType === 'VERTEX') {
      globalParameter.value.forEach((p) => {
        result.push({ id: p.vertexID, type: p.vertexType });
      });
    } else {
      globalParameter.value.forEach((p) => {
        result.push(formatPrimitiveValue(globalParameter.elementType, p));
      });
    }
    return result;
  }

  paramList?.forEach((p) => {
    if (p.isCreatable) {
      // special handling for call interpret query with list of vertex
      if (elementType === 'VERTEX' && (vertexType === '*' || !query.installed)) {
        result.push({ id: p.id, type: p.type });
      } else {
        result.push(p.value);
      }
    } else if (p.value in globalParameters) {
      const globalParameter = globalParameters[p.value];
      if (globalParameter.type === 'LIST') {
        // special handling for call interpret query with list of vertex
        if (elementType === 'VERTEX') {
          if (globalParameter.elementType === 'VERTEX') {
            for (let subValue of globalParameter.value) {
              if (vertexType === '*' || !query.installed) {
                result.push({ type: subValue.vertexType, id: subValue.vertexID });
              } else {
                // If it is vertex with type running in compiled mode, only send vertex id
                result.push({ type: subValue.vertexType, id: subValue.vertexID });
              }
            }
          } else {
            for (let subValue of globalParameter.value) {
              if (vertexType === '*' || !query.installed) {
                result.push({ type: p.type, id: subValue });
              } else {
                // If it is vertex with type running in compiled mode, only send vertex id
                result.push({ id: `${subValue}` });
              }
            }
          }
        } else {
          globalParameter.value.forEach((v) => result.push(v));
        }
      }
    }
  });

  return result;
};

const getMapParamsData = ({
  globalParameters,
  pattern,
  paramGlobalInput,
}: {
  globalParameters: GlobalParams;
  pattern: SearchItem;
  paramGlobalInput: string;
}) => {
  const keylist = [];
  const valuelist = [];
  const isParamInGlobalInput = paramGlobalInput && paramGlobalInput in globalParameters;
  const globalParameter = globalParameters[paramGlobalInput];

  if (isParamInGlobalInput && globalParameter.type === 'MAP') {
    globalParameter.value.forEach((gp) => {
      const key = formatPrimitiveValue(globalParameter.keyType, gp.key);
      const value = formatPrimitiveValue(globalParameter.valueType, gp.value);
      keylist.push(key);
      valuelist.push(value);
    });
    return { keylist, valuelist };
  } else {
    return pattern.mapData || { keylist: [], valuelist: [] };
  }
};
