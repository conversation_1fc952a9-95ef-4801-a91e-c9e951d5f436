import { useStyletron } from '@tigergraph/app-ui-lib/Theme';

import { FieldArray, NewRuleStylePopover, RuleList, Rules, StyleDesc } from '../../components/ruleStyle';
import { ChartSettingItemContainer } from '../../charts/chartTools/chartSetting/chartSettingItemContainer';
import { SelectIconContainer } from '../../components/selectIcon';
import { ColorPicker } from '../../components/colorPicker';
import StyledSlider from '../../components/styledSlider';
import IconButtonGroup from '../../components/iconButtonGroup';
import { ChartSettingProps } from '../../chart/chart';
import { getSingleKeyAndValue, SettingType } from './singleValueChart';
import ChartSettingSectionContainer from '../chartTools/chartSetting/chartSettingSectionContainer';
import AlignLeft from '../../assets/images/alignLeft.svg';
import AlignCenter from '../../assets/images/alignCenter.svg';
import AlignRight from '../../assets/images/alignRight.svg';
import AlignBottom from '../../assets/images/alignBottom.svg';
import AlignMiddle from '../../assets/images/alignMiddle.svg';
import AlignTop from '../../assets/images/alignTop.svg';
import React, { useMemo } from 'react';
import { Condition, FieldType, StyleOptions } from '../../components/ruleStyle/type';
import { SelectLucideIconContainer } from '../../components/selectLucideIcon';

export function SingleValueSettingTool(props: ChartSettingProps) {
  const [css] = useStyletron();

  const { onSettingUpdate } = props;
  const settings = props.settings as SettingType;

  let iconColor = settings.iconColor ?? '#F15A5A';
  let fontColor = settings.fontColor ?? 'black';
  let fontSize = settings.fontSize ?? 48;
  let iconSize = settings.iconSize ?? 48;
  let textAlign = settings.textAlign ?? 'center';
  let verticalAlign = settings.verticalAlign ?? 'middle';

  let fontSizeOptions: StyleOptions = useMemo(() => {
    let sizeOptions = [12, 14, 16, 18, 24, 36, 48, 64, 72, 96, 144];
    let res = [];
    for (let option of sizeOptions) {
      res.push({ label: option + '', value: option });
    }

    return res;
  }, []);

  let iconSizeOptions: StyleOptions = useMemo(() => {
    let res = [];
    for (let option = 24; option <= 160; option += 8) {
      res.push({ label: option + '', value: option });
    }
    return res;
  }, []);

  const ruleConfigs: StyleDesc[] = [
    {
      styleKey: 'iconColor',
      styleLabel: 'Icon Color',
      styleType: 'color',
    },
    {
      styleKey: 'iconSize',
      styleLabel: 'Icon Size',
      styleType: 'numeric',
      styleOptions: iconSizeOptions,
    },
    {
      styleKey: 'fontColor',
      styleLabel: 'Font Color',
      styleType: 'color',
    },
    {
      styleKey: 'fontSize',
      styleLabel: 'Font Size',
      styleType: 'numeric',
      styleOptions: fontSizeOptions,
    },
  ];

  const rules: Rules = settings['styleRules'] || [];

  let fields: FieldArray = useMemo(() => {
    const conditions: Condition[] = ['=', '!=', '>', '>=', '<', '<='];

    let [key, value] = getSingleKeyAndValue(props.chartData);
    let valueType: FieldType = 'string';
    if (typeof value === 'number' || typeof value === 'bigint') {
      valueType = 'number';
    }

    return [
      {
        type: valueType,
        name: key,
        values: [value],
        conditions,
      },
    ];
  }, [props.chartData]);

  return (
    <>
      <ChartSettingSectionContainer title={'Display'}>
        <ChartSettingItemContainer
          label="Single value icon"
          helpText="Specify the icon to be shown on the widget before the value"
        >
          <SelectLucideIconContainer
            iconURL={settings.iconURL}
            onIconSelected={(iconURL) => {
              onSettingUpdate('iconURL', iconURL);
            }}
            isCloud={props.isCloud}
          />
        </ChartSettingItemContainer>
        <ChartSettingItemContainer
          label="Icon color and size"
          helpText="Specify icon size and color. You cannot set the color of custom icons"
        >
          <div>
            <div
              className={css({
                display: 'inline-block',
                verticalAlign: 'middle',
                width: '15%',
              })}
            >
              <ColorPicker
                disabled={!settings?.iconURL?.includes('builtin')}
                value={iconColor}
                onChange={(color) => {
                  onSettingUpdate('iconColor', color);
                }}
              />
            </div>
            <div
              className={css({
                display: 'inline-block',
                verticalAlign: 'middle',
                width: '80%',
                marginLeft: '5%',
              })}
            >
              <StyledSlider
                value={[iconSize]}
                min={0}
                max={100}
                onChange={(prop) => {
                  onSettingUpdate('iconSize', prop.value);
                }}
              />
            </div>
          </div>
        </ChartSettingItemContainer>
        <ChartSettingItemContainer label="Font Alignment" helpText="Specify widget text appearance and orientation">
          <div>
            <div
              className={css({
                display: 'inline-block',
              })}
            >
              <IconButtonGroup
                labelAsClickValue={true}
                items={[
                  {
                    icon: <AlignLeft />,
                    label: 'left',
                  },
                  {
                    icon: <AlignCenter />,
                    label: 'center',
                  },
                  {
                    icon: <AlignRight />,
                    label: 'right',
                  },
                ]}
                columnGap={15}
                selected={textAlign}
                onClick={(value) => {
                  onSettingUpdate('textAlign', value);
                }}
              />
            </div>
            <div
              className={css({
                display: 'inline-block',
                marginLeft: '20px',
              })}
            >
              <IconButtonGroup
                labelAsClickValue={true}
                columnGap={15}
                items={[
                  {
                    icon: <AlignBottom />,
                    label: 'bottom',
                  },
                  {
                    icon: <AlignMiddle />,
                    label: 'middle',
                  },
                  {
                    icon: <AlignTop />,
                    label: 'top',
                  },
                ]}
                selected={verticalAlign}
                onClick={(value) => {
                  onSettingUpdate('verticalAlign', value);
                }}
              />
            </div>
          </div>
        </ChartSettingItemContainer>
        <ChartSettingItemContainer label="Font Color and Size" helpText="Specify the color and size of the text">
          <div>
            <div
              className={css({
                display: 'inline-block',
                verticalAlign: 'middle',
                width: '15%',
              })}
            >
              <ColorPicker
                value={fontColor}
                onChange={(color) => {
                  onSettingUpdate('fontColor', color);
                }}
              />
            </div>
            <div
              className={css({
                display: 'inline-block',
                verticalAlign: 'middle',
                width: '80%',
                marginLeft: '5%',
              })}
            >
              <StyledSlider
                value={[fontSize]}
                min={0}
                max={100}
                onChange={(prop) => {
                  onSettingUpdate('fontSize', prop.value);
                }}
              />
            </div>
          </div>
        </ChartSettingItemContainer>
        <ChartSettingItemContainer
          label="Conditional styling"
          helpText="Specify a rule-based style to enrich visualization. You can define simple rules based on expression evaluation, select a color palette based on the range of an attribute value, or let the system pick a color based on the uniqueness of the value."
          right={
            <NewRuleStylePopover
              ruleConfigs={ruleConfigs}
              fields={fields}
              onSave={(rule) => {
                onSettingUpdate('styleRules', rules.concat(rule));
              }}
              conditionSelectorHidden={true}
            />
          }
        >
          <RuleList
            ruleConfigs={ruleConfigs}
            fields={fields}
            conditionSelectorHidden={true}
            rules={rules}
            onRuleChanged={(rules) => onSettingUpdate('styleRules', rules)}
          />
        </ChartSettingItemContainer>
      </ChartSettingSectionContainer>
    </>
  );
}
