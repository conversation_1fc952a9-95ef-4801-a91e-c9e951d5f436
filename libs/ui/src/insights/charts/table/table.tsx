import React, { useEffect, useMemo, useState } from 'react';
import { Link } from 'react-router-dom';
import { styled, useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { TableBuilder, TableBuilderColumn } from 'baseui/table-semantic';
import { Pagination } from '@tigergraph/app-ui-lib/pagination';

import SearchLabel from '../chartSearch/searchLabel';
import { CheckboxState, SettingType, ShowColumns, TableColumns } from './type';
import { Action, ActionParam, buildLinkFromAction } from '../../components/action';
import { ChartProps } from '../../chart/chart';
import { evaluateRules, darkenColor } from '../../components/ruleStyle';
import { buildRow } from './util';
import { SEARCH_KEYWORDS } from '../chartSearch';
import useTableData from './useTableData';
import { valueComparator } from '../../utils';
import EventEmitter from 'eventemitter3';

const numberFormat = new Intl.NumberFormat('en-US');

export const tableChartEmitter = new EventEmitter();

export default function Table(props: ChartProps) {
  const [css] = useStyletron();

  const { chartData, schema, isEditMode, globalParameters, onSettingUpdate, onChartStateUpdate, getLink } = props;
  const settings = props.settings as SettingType;

  const { tables } = chartData;
  const { vertices, edges } = schema.graph;
  const [sortColumn, setSortColumn] = useState('');
  const [sortAsc, setSortAsc] = useState(true);
  const [pageNumber, setPageNumber] = useState(1);
  const pageSize = settings.pageSize ?? 5;
  const tableActions = settings.tableActions ?? [];
  const sortedColumns = settings.sortedColumns ?? [];
  const tableIndex = settings.tableIndex ?? 0;
  const currentTable = tables[tableIndex];

  const { isSame, initialTableData } = useTableData(props);

  const tableColumns = settings.tableColumns && isSame ? settings.tableColumns : (initialTableData as TableColumns);

  const handleSort = (id: string) => {
    if (id === sortColumn) {
      setSortAsc((asc) => !asc);
    } else {
      setSortColumn(id);
      setSortAsc(true);
    }
  };

  const sortedRows = useMemo(() => {
    const tableRows = currentTable?.rows || [];
    return tableRows
      .map((item) => buildRow(item, schema))
      .sort((a, b) => {
        const left = sortAsc ? a : b;
        const right = sortAsc ? b : a;
        const leftValue = left[sortColumn];
        const rightValue = right[sortColumn];
        return valueComparator(leftValue, rightValue);
      });
  }, [currentTable, sortAsc, sortColumn, schema]);

  const buildColumnsHelper = (tableColumns: TableColumns, columns: Array<string | number>, path = '') => {
    for (const item of tableColumns) {
      if ('children' in item && item['isChecked'] !== CheckboxState.FALSE) {
        buildColumnsHelper(item['children'], columns, `${path + item.id}.`);
      } else if (
        item['isChecked'] === CheckboxState.TRUE &&
        sortedRows.length > 0 &&
        path.substring(path.indexOf('.') + 1) + item.id in sortedRows[0]
      ) {
        columns.push(path.substring(path.indexOf('.') + 1) + item.id);
      }
    }
  };

  const buildColumns = (tableColumns: TableColumns): string[] => {
    const columns = [];
    if (!tableColumns || tableColumns.length === 0) return columns;
    for (const col of tableColumns) {
      if (!col.children) continue;
    }
    buildColumnsHelper(tableColumns, columns);
    return columns;
  };

  let columns = buildColumns(JSON.parse(JSON.stringify(tableColumns || [])));
  const showColumns =
    settings.showColumns && isSame
      ? settings.showColumns
      : sortedRows.length > 0
      ? Object.keys(sortedRows[0]).map((item) => ({ name: item, isChecked: true }))
      : ([] as ShowColumns);

  if (currentTable?.name !== 'patternView') {
    columns = showColumns.reduce((prev, item) => {
      if (item.isChecked) {
        prev.push(item.name);
      }
      return prev;
    }, []);
  }
  if (tableActions.length > 0 && columns.indexOf('Action') === -1) {
    columns.push('Action');
  } else if (tableActions.length === 0 && columns.indexOf('Action') > -1) {
    columns = columns.filter((col) => col !== 'Action');
  }
  columns.sort((a, b) => {
    if (sortedColumns.indexOf(a) === -1) {
      return 1;
    } else if (sortedColumns.indexOf(b) === -1) {
      return -1;
    } else {
      return sortedColumns.indexOf(a) - sortedColumns.indexOf(b);
    }
  });

  const tableData = sortedRows.slice((pageNumber - 1) * pageSize, pageNumber * pageSize);

  useEffect(() => {
    if (tableIndex >= tables.length && tableIndex !== 0) {
      onSettingUpdate('tableIndex', 0);
    }
  });

  useEffect(() => {
    if (!isSame) {
      setPageNumber(1);
    }
  }, [isSame]);

  useEffect(() => {
    if (!isSame && currentTable?.headers?.length > 0) {
      onSettingUpdate('tableColumns', tableColumns);
      onSettingUpdate('showColumns', showColumns);
      onSettingUpdate('tableHeaders', currentTable?.headers ?? []);
    }
  });

  useEffect(() => {
    // pass all the table data to settings
    onChartStateUpdate && onChartStateUpdate('tableData', sortedRows);
  }, [onChartStateUpdate, sortedRows]);

  useEffect(() => {
    tableChartEmitter.emit('updateColumns', {
      id: props.id,
      columns,
    });
  }, [columns, props.id]);

  const tryToFormatNumber = (value: any) => {
    if (typeof value === 'number' || typeof value === 'bigint') {
      value = numberFormat.format(value);
      return value;
    }

    return String(value);
  };

  return (
    <div
      className={css({
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
      })}
    >
      <TableBuilder
        data={tableData}
        emptyMessage={<h1>No data</h1>}
        sortColumn={sortColumn}
        sortOrder={sortAsc ? 'ASC' : 'DESC'}
        onSort={handleSort}
        overrides={{
          Root: {
            style: ({ $theme }) => ({
              backgroundColor: '#fff',
              borderRadius: $theme.borders.radius300,
              flexBasis: 0,
              flexGrow: 1,
              flexShrink: 1,
            }),
          },
          TableHeadCell: {
            style: {
              backgroundColor: '#F4F8FD',
              paddingTop: '8px',
              paddingBottom: '8px',
              paddingLeft: '0',
            },
          },
          TableHeadCellSortable: {
            style: {
              backgroundColor: '#F4F8FD',
              paddingTop: '8px',
              paddingBottom: '8px',
              paddingLeft: '0',
            },
          },
          TableBodyRow: {
            style: ({ $theme, $rowIndex }: any) => {
              const row = tableData[$rowIndex];
              const filteredRowRules = settings['styleRules']?.filter((i) => !i.columnName);
              const style = evaluateRules(row, filteredRowRules, globalParameters);

              return {
                backgroundColor: $rowIndex % 2 ? $theme.colors.backgroundPrimary : '#fff',
                ...style,
                ...(style.backgroundColor
                  ? {
                      ':hover': {
                        backgroundColor: darkenColor(style.backgroundColor),
                      },
                    }
                  : {}),
              };
            },
          },
          TableBodyCell: {
            style: ({ $rowIndex, $col }) => {
              const row = tableData[$rowIndex];
              const filteredRowRules = settings['styleRules']?.filter((i) => i.columnName && i.columnName === $col.id);
              const style = evaluateRules(row, filteredRowRules, globalParameters);

              return {
                ...style,
                verticalAlign: 'center',
                paddingTop: '8px',
                paddingBottom: '8px',
                paddingLeft: '0',
              };
            },
          },
        }}
      >
        {columns.map((col) =>
          col === 'Action' ? (
            <TableBuilderColumn
              key={'Action'}
              header={<TableHeader label={`Action${tableActions.length > 1 ? 's' : ''}`} />}
              id={'Action'}
            >
              {(row) => (
                <TableCell>
                  {tableActions.map((action: Action, index: number) => {
                    const getParam = (param: ActionParam) => {
                      if (param.paramGlobalInput?.length > 0) {
                        return {
                          [param.name]: globalParameters[param.paramGlobalInput].value,
                        };
                      }
                      return {
                        [param.name]: param.isCreatable ? param.value : row[param.value],
                      };
                    };
                    const href = buildLinkFromAction(action, getLink, getParam, isEditMode);
                    return action.pageID && !isEditMode ? (
                      <Link
                        key={'action' + index}
                        to={href}
                        className={css({
                          marginLeft: index > 0 ? '10px' : '0',
                        })}
                      >
                        {action.text ? action.text : href}
                      </Link>
                    ) : (
                      <a
                        key={'action' + index}
                        href={href}
                        target="_blank"
                        rel="noopener noreferrer"
                        className={css({
                          marginLeft: index > 0 ? '10px' : '0',
                        })}
                      >
                        {action.text ? action.text : href}
                      </a>
                    );
                  })}
                </TableCell>
              )}
            </TableBuilderColumn>
          ) : (
            <TableBuilderColumn
              key={col}
              header={<TableHeader label={col} />}
              id={col}
              sortable={col !== 'Matched pattern'}
            >
              {(row) =>
                col === 'Matched pattern' && 'Matched pattern' in row ? (
                  <TableCell>
                    {row[col].map((item, i) =>
                      item.type === SEARCH_KEYWORDS.vertex ? (
                        <SearchLabel
                          key={item.id + '_' + i}
                          item={item}
                          text={item.id}
                          color={vertices[item.data]?.style?.fillColor}
                        />
                      ) : (
                        <SearchLabel key={item.id + '_' + i} item={item} color={edges[item.data]?.style?.fillColor} />
                      )
                    )}
                  </TableCell>
                ) : (
                  <TableCell>
                    {col in row &&
                      (typeof row[col] === 'object' ? JSON.stringify(row[col]) : tryToFormatNumber(row[col]))}
                  </TableCell>
                )
              }
            </TableBuilderColumn>
          )
        )}
      </TableBuilder>
      {currentTable?.rows?.length > 0 && currentTable?.rows?.length > pageSize && (
        <div
          className={css({
            padding: '10px 20px 0',
            display: 'flex',
            justifyContent: 'flex-end',
            backgroundColor: '#fff',
            alignItems: 'center',
          })}
        >
          <div className={css({ marginRight: '10px' })}>
            {`${(pageNumber - 1) * pageSize + 1} - ${Math.min(pageNumber * pageSize, currentTable.rows.length)} of ${
              currentTable.rows.length
            }`}
          </div>
          <Pagination
            totalPage={Math.ceil(currentTable.rows.length / pageSize)}
            pageNumber={pageNumber}
            setPageNumber={(number) => {
              setPageNumber(number);
            }}
            type="default"
          />
        </div>
      )}
    </div>
  );
}

const TableHeader = ({ label }: { label: string }) => {
  const [css, theme] = useStyletron();
  return (
    <div
      className={css({
        fontWeight: 600,
        fontSize: '14px',
        lineHeight: '16px',
        color: theme.colors.gray900,
        paddingLeft: '8px',
      })}
    >
      {label}
    </div>
  );
};

const TableCell = styled('div', ({ $theme }) => ({
  display: 'flex',
  paddingLeft: '8px',
  fontSize: '14px',
  lineHeight: '16px',
  color: $theme.colors.black01,
}));
