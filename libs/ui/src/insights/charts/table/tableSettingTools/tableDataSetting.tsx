import React, { useState, useRef, ReactNode } from 'react';
import { useStyletron } from 'baseui';
import { Input, SIZE } from 'baseui/input';
import { TreeView, TreeLabelInteractable, toggleIsExpanded, TreeNode } from 'baseui/tree-view';
import { Checkbox } from '@tigergraph/app-ui-lib/checkbox';
import { arrayMove, arrayRemove } from 'baseui/dnd-list';
import { ChartSettingItemContainer } from '../../chartTools/chartSetting/chartSettingItemContainer';
import { ChartSettingProps } from '../../../chart/chart';
import { CheckboxState, SettingType, TableColumns } from '../type';
import { MdSearch } from '@react-icons/all-files/md/MdSearch';
import { StyledDraggableList } from '../../../components/styledDraggableList';
import { StyledSelect } from '../../../components';
import ConfirmButtons from '../../../components/confirmButtons';
import useTableData from '../useTableData';

export default function TableDataSetting(props: ChartSettingProps) {
  const [css] = useStyletron();

  const { chartData, onSettingUpdate } = props;
  const settings = props.settings as SettingType;
  const tables = chartData?.tables;

  const showColumns = settings['showColumns'] ?? [];
  const [columnName, setColumnName] = useState('');

  const { initialTableData } = useTableData(props);
  const tableColumns = settings['tableColumns'] ?? [];
  // keep consistent with table widget for get the initial table columns
  const tableColumnsRef = useRef<TableColumns>(tableColumns.length > 0 ? tableColumns : initialTableData);

  const tableActions = settings['tableActions'] ?? [];
  const tableIndex = settings['tableIndex'] ?? 0;
  const tableNames = tables?.map((table) => table.name) ?? [];

  const buildColumnsHelper = (tableColumns: TreeNode[], columns: Array<string | number>, path = '') => {
    for (const item of tableColumns) {
      if ('children' in item && item['isChecked'] !== CheckboxState.FALSE) {
        buildColumnsHelper(item['children'], columns, `${path + item.id}.`);
      } else if (item['isChecked'] === CheckboxState.TRUE) {
        columns.push(path.substring(path.indexOf('.') + 1) + item.id);
      }
    }
  };

  const buildColumns = (tableColumns: TreeNode[]): string[] => {
    const columns = [];
    if (!tableColumns || tableColumns.length === 0) return columns;
    for (const col of tableColumns) {
      if (!col.children) continue;
    }
    buildColumnsHelper(tableColumns, columns);
    return columns;
  };

  let columns = buildColumns(JSON.parse(JSON.stringify(tableColumns || [])));
  if (tables?.[0]?.name !== 'patternView') {
    columns = showColumns.reduce((prev, item) => {
      if (item.isChecked) {
        prev.push(item.name);
      }
      return prev;
    }, []);
  }
  const sortedColumns = JSON.parse(JSON.stringify(settings['sortedColumns'] ?? [])).filter(
    (item) => columns.indexOf(item) > -1 || (tableActions.length > 0 && item === 'Action')
  );

  columns.forEach((col) => {
    if (sortedColumns.indexOf(col) === -1) {
      sortedColumns.push(col);
    }
  });

  if (tableActions.length > 0 && sortedColumns.indexOf('Action') === -1) {
    sortedColumns.push('Action');
  } else if (tableActions.length === 0 && sortedColumns.indexOf('Action') > -1) {
    sortedColumns.splice(sortedColumns.indexOf('Action'), 1);
  }

  const buildTreeViewLabel = (treeNodes: TreeNode[], path = '') => {
    for (const node of treeNodes) {
      const curPath = path + node.id;
      if (node.children?.length > 0) {
        buildTreeViewLabel(node.children, curPath + '.');
      }
      node['label'] = () => buildLabelCheckbox(node, curPath.substring(curPath.indexOf('.') + 1));
    }
  };

  const setCheckboxState = (treeNodes: TreeNode[], id: string, type: string, isChecked: boolean) => {
    if (!treeNodes) return;
    const checkState = isChecked ? CheckboxState.TRUE : CheckboxState.FALSE;
    if (id) {
      const vertices = treeNodes.find((item) => item.id === 'Vertices');
      const edges = treeNodes.find((item) => item.id === 'Edges');
      const others = treeNodes.find((item) => item.id === 'Others');
      if (type === 'vertex' && vertices) {
        setCheckboxState(vertices.children, id, type, isChecked);
      } else if (type === 'edge' && edges) {
        setCheckboxState(edges.children, id, type, isChecked);
      } else if (type === 'data' && others) {
        setCheckboxState(others.children, id, type, isChecked);
      } else {
        const path = id.split('.', 2);
        const node = treeNodes.find((item) => item.id === path[0]);
        if (!node) return;
        if (path.length === 1) {
          node['isChecked'] = checkState;
        }
        setCheckboxState(node.children, path[1], type, isChecked);
      }
    } else {
      for (const node of treeNodes) {
        node['isChecked'] = checkState;
        if (node.children?.length > 0) {
          setCheckboxState(node.children, '', type, isChecked);
        }
      }
    }
  };

  const buildCheckState = (treeNodes: TreeNode[]) => {
    let count = 0;
    for (const node of treeNodes) {
      if (node.children?.length > 0) {
        node.isChecked = buildCheckState(node.children);
      }
      if (node.isChecked === CheckboxState.TRUE) {
        count++;
      } else if (node.isChecked === CheckboxState.INDETERMINATE) {
        count += 0.5;
      }
    }
    if (count > 0 && count === treeNodes.length) {
      return CheckboxState.TRUE;
    } else if (count === 0) {
      return CheckboxState.FALSE;
    } else {
      return CheckboxState.INDETERMINATE;
    }
  };

  const filterColumns = (treeNodes: TreeNode[]) => {
    const nodes = [];
    for (const node of treeNodes) {
      if (node.id.toString().toLowerCase().indexOf(columnName.toLowerCase()) !== -1) {
        nodes.push(node);
      } else if (node.children?.length > 0) {
        node.children = filterColumns(node.children);
        if (node.children?.length > 0) {
          nodes.push(node);
        }
      }
    }
    return nodes;
  };

  const tableColumnData = JSON.parse(JSON.stringify(tableColumns));
  buildCheckState(tableColumnData);
  const data = filterColumns(JSON.parse(JSON.stringify(tableColumnData)));
  buildTreeViewLabel(data);

  const buildLabelCheckbox = (node: TreeNode, label: string): ReactNode => {
    return (
      <TreeLabelInteractable>
        <Checkbox
          checked={node.isChecked === CheckboxState.TRUE}
          isIndeterminate={node.isChecked === CheckboxState.INDETERMINATE}
          onChange={(e) => {
            setCheckboxState(tableColumnData, label, node.type, e.target['checked']);
            buildCheckState(tableColumnData);
            onSettingUpdate('tableColumns', tableColumnData);
          }}
        >
          {label}
        </Checkbox>
      </TreeLabelInteractable>
    );
  };

  const isPatternView = tables?.length === 1 && tables[0].name === 'patternView';

  return (
    <>
      {tableNames.length > 1 && (
        <ChartSettingItemContainer label="Table Names" helpText="Select the table data to display">
          <StyledSelect
            options={tableNames.map((item, index) => ({
              id: index,
              label: item,
            }))}
            value={[{ label: tableNames[tableIndex], id: tableIndex }]}
            onChange={({ value }) => onSettingUpdate('tableIndex', value[0].id)}
            clearable={false}
            placeholder="Select table"
          />
        </ChartSettingItemContainer>
      )}
      <ChartSettingItemContainer
        label="Columns"
        helpText="Order, display and hide the data columns"
        popover={({ close }) => (
          <div>
            <Input
              value={columnName}
              onChange={(e) => setColumnName(e.target['value'])}
              startEnhancer={() => <MdSearch size={16} color={'#94A2AF'} />}
              clearable
              placeholder="Search column"
              overrides={{
                StartEnhancer: {
                  style: {
                    paddingLeft: '0',
                    paddingRight: '0',
                  },
                },
              }}
            />
            {isPatternView ? (
              <>
                <TreeView
                  data={data}
                  onToggle={(node) => {
                    onSettingUpdate('tableColumns', toggleIsExpanded(tableColumnData, node));
                  }}
                  overrides={{
                    Root: {
                      style: {
                        maxHeight: '400px',
                      },
                    },
                    IconContainer: {
                      style: {
                        color: ' #3F5870',
                      },
                    },
                  }}
                />
              </>
            ) : (
              <div
                className={css({
                  maxHeight: '400px',
                  overflow: 'scroll',
                })}
              >
                {showColumns.map((item: { name: string; isChecked: boolean }, index: number) => {
                  if (item.name.toLowerCase().indexOf(columnName.toLowerCase()) === -1) {
                    return <></>;
                  }
                  return (
                    <Checkbox
                      key={item.name}
                      checked={item.isChecked}
                      onChange={() => {
                        const newShowColumns = [...showColumns];
                        newShowColumns[index] = {
                          name: item.name,
                          isChecked: !item.isChecked,
                        };
                        onSettingUpdate('showColumns', newShowColumns);
                      }}
                    >
                      {item.name}
                    </Checkbox>
                  );
                })}
              </div>
            )}
            <ConfirmButtons
              onConfirm={() => {
                tableColumnsRef.current = tableColumns;
                close();
              }}
              onCancel={() => {
                onSettingUpdate('tableColumns', [...tableColumnsRef.current]);
                close();
              }}
            />
          </div>
        )}
      >
        <div
          className={css({
            maxHeight: '400px',
            overflow: 'auto',
          })}
        >
          <StyledDraggableList
            items={sortedColumns}
            onChange={({ oldIndex, newIndex }) =>
              onSettingUpdate('sortedColumns', arrayMove(sortedColumns, oldIndex, newIndex))
            }
            onRemove={(index) => {
              const value = sortedColumns[index];
              if (isPatternView) {
                setCheckboxState(tableColumnData, String(value), 'vertex', false);
                setCheckboxState(tableColumnData, String(value), 'edge', false);
                setCheckboxState(tableColumnData, String(value), 'data', false);
                buildCheckState(tableColumnData);
                onSettingUpdate('tableColumns', tableColumnData);
              } else {
                const newShowColumns = [...showColumns];
                const showColumnsIndex = newShowColumns.findIndex((item) => item.name === value);
                if (showColumnsIndex > -1) {
                  newShowColumns[showColumnsIndex] = {
                    name: value,
                    isChecked: false,
                  };
                  onSettingUpdate('showColumns', newShowColumns);
                }
              }
              onSettingUpdate('sortedColumns', arrayRemove(sortedColumns, index));
            }}
            removable={(value) => {
              return value !== 'Action';
            }}
          />
        </div>
      </ChartSettingItemContainer>
    </>
  );
}
