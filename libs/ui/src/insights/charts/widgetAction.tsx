import { useStyletron } from 'baseui';
import React from 'react';
import { Link } from 'react-router-dom';

import { Action, ActionParam, buildLinkFromAction } from '../components/action';
import { ChartProps } from '../chart/chart';

const isAbsoluteURL = (url: string) => {
  const pattern = new RegExp('^(https?:|mailto:|tel:)', 'i');
  return pattern.test(url);
};

export default function WidgetAction(props: ChartProps) {
  const [css] = useStyletron();
  const { settings, globalParameters, isEditMode, getLink } = props;
  const widgetActions = settings['widgetActions'] ?? [];

  const getParam = (param: ActionParam) => {
    if (param.paramGlobalInput?.length > 0) {
      return {
        [param.name]: globalParameters[param.paramGlobalInput].value,
      };
    }
    return {
      [param.name]: param.value,
    };
  };

  return (
    <>
      {widgetActions.length > 0 && (
        <div
          className={css({
            display: 'flex',
            justifyContent: 'flex-end',
            padding: '0 10px 10px 10px',
          })}
        >
          {widgetActions.map((action: Action, index: number) => {
            const href = buildLinkFromAction(action, getLink, getParam, isEditMode);
            const shouldUseReactRouterLink = !isAbsoluteURL(href);

            return shouldUseReactRouterLink ? (
              <Link
                key={'action' + index}
                to={href}
                className={css({
                  marginRight: '20px',
                })}
              >
                {action.text ? action.text : href}
              </Link>
            ) : (
              <a
                key={'action' + index}
                href={href}
                target="_blank"
                rel="noopener noreferrer"
                className={css({
                  marginLeft: index > 0 ? '10px' : '0',
                })}
              >
                {action.text ? action.text : href}
              </a>
            );
          })}
        </div>
      )}
    </>
  );
}
