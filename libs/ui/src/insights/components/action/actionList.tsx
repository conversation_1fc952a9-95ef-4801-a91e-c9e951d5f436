import { arrayMove, arrayRemove } from 'baseui/dnd-list';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import React from 'react';
import ActionDetail from './actionDetail';
import { StyledDraggableList } from '../styledDraggableList';
import { Action } from './type';
import { GlobalParams } from '../../chart';

type Props = {
  actions: Action[];
  onActionChanged: (actions: Action[]) => void;

  options?: string[];
  globalParameters: GlobalParams;
  links: { pageName: string; pageID: string; params: GlobalParams }[];
  supportVertexType?: boolean;
};

export function ActionList({ actions, onActionChanged, options, globalParameters, links, supportVertexType }: Props) {
  const [css] = useStyletron();

  return (
    <StyledDraggableList
      items={actions.map((item) => (
        <div
          className={css({
            display: 'flex',
            flex: '1',
          })}
        >
          <div
            className={css({
              overflow: 'hidden',
              whiteSpace: 'nowrap',
              textOverflow: 'ellipsis',
            })}
          >
            {item.text || 'Action'}
          </div>
        </div>
      ))}
      popovers={actions.map((item: Action, index) => ({ close }) => (
        <ActionDetail
          action={item}
          links={links}
          options={options}
          globalParameters={globalParameters}
          onUpdateAction={(updateAction) => {
            onActionChanged([...actions.slice(0, index), updateAction, ...actions.slice(index + 1)]);
          }}
          close={close}
          supportVertexType={supportVertexType}
        />
      ))}
      onChange={({ oldIndex, newIndex }) => onActionChanged(arrayMove(actions, oldIndex, newIndex))}
      onRemove={(index) => {
        onActionChanged(arrayRemove(actions, index));
      }}
    />
  );
}
