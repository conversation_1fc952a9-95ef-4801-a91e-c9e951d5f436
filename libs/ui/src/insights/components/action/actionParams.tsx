import { Button } from '@tigergraph/app-ui-lib/button';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import React, { Fragment } from 'react';
import { KIND as ButtonKIND } from 'baseui/button';
import { expand } from 'inline-style-expand-shorthand';
import { PLACEMENT } from 'baseui/popover';
import { MdAdd } from '@react-icons/all-files/md/MdAdd';
import { MdDeleteOutline } from '../../../react-icons';

import { StyledInput, StyledSelect } from '../../components';
import { ActionProps } from './type';
import { ChartSettingItemContainer } from '../../charts/chartTools/chartSetting/chartSettingItemContainer';
import { GlobalParams } from '../../chart';

export default function ActionParams(props: ActionProps) {
  const [css, theme] = useStyletron();
  const { action, options = [], globalParameters = {}, links, onUpdateAction, supportVertexType } = props;

  const params: GlobalParams = links.find((link) => link.pageID === action.pageID)?.params || {};
  const paramNames = Object.keys(params);

  return (
    <>
      {action.params?.length > 0 && (
        <ChartSettingItemContainer label="Parameters">
          <div
            className={css({
              display: 'grid',
              gridTemplateColumns: '132px 132px 32px',
              gap: '4px',
              overflow: 'auto',
            })}
          >
            {action.params.map((param, paramIndex) => (
              <Fragment
                key={'parameter' + paramIndex}
              >
                <StyledSelect
                  value={param.name ? [{ id: param.name }] : []}
                  options={paramNames.map((param) => ({ id: param })) || []}
                  onChange={({ value }) => {
                    const newAction = { ...action, params: [...action.params] };
                    newAction.params[paramIndex] = { ...param, name: String(value[0]?.id || '') };
                    onUpdateAction(newAction);
                  }}
                  labelKey="id"
                  creatable
                  placeholder="name"
                />
                {params[param.name]?.type === 'VERTEX' && supportVertexType ? (
                  <StyledInput readOnly placeholder="current vertex" />
                ) : options.length + Object.keys(globalParameters).length > 0 ? (
                  <StyledSelect
                    value={
                      param.value
                        ? param.paramGlobalInput
                          ? [{ id: 'global:' + param.value, label: param.value }]
                          : [{ id: param.value, label: param.value }]
                        : []
                    }
                    onChange={({ value }) => {
                      const newAction = { ...action, params: [...action.params] };
                      if (value.length > 0 && value[0]['isCreatable']) {
                        newAction.params[paramIndex] = { ...param, value: value[0].id, isCreatable: true };
                      } else {
                        if (value.length > 0 && value[0].type === 'globalInput') {
                          newAction.params[paramIndex] = {
                            ...param,
                            value: String(value[0].data),
                            paramGlobalInput: String(value[0].data),
                            isCreatable: false,
                          };
                        } else {
                          newAction.params[paramIndex] = {
                            ...param,
                            value: value.length > 0 ? String(value[0].id) : '',
                            paramGlobalInput: '',
                            isCreatable: false,
                          };
                        }
                      }
                      onUpdateAction(newAction);
                    }}
                    options={options
                      .map((item) => ({ id: item, label: item }))
                      .concat(
                        Object.keys(globalParameters).map((item) => {
                          const { value } = globalParameters[item];
                          return {
                            id: 'global:' + item,
                            label: `${item}: ${typeof value === 'object' ? JSON.stringify(value) : value}`,
                            data: item,
                            type: 'globalInput',
                          };
                        })
                      )}
                    overrides={{
                      Popover: {
                        props: {
                          placement: PLACEMENT.bottomLeft,
                        },
                      },
                    }}
                    creatable
                    placeholder="value"
                  />
                ) : (
                  <StyledInput
                    value={param.value}
                    onChange={(e) => {
                      const newAction = { ...action, params: [...action.params] };
                      newAction.params[paramIndex] = { ...param, value: e.target['value'], isCreatable: true };
                      onUpdateAction(newAction);
                    }}
                    clearable
                    placeholder="value"
                  />
                )}
                <div
                  className={css({
                    alignItems: 'center',
                    display: 'flex',
                  })}
                >
                  <Button
                    kind="text"
                    onClick={() => {
                      const newAction = { ...action, params: [...action.params] };
                      newAction.params.splice(paramIndex, 1);
                      onUpdateAction(newAction);
                    }}
                  >
                    <MdDeleteOutline width={16} height={16} color={theme.colors.gray900} />
                  </Button>
                </div>
              </Fragment>
            ))}
          </div>
        </ChartSettingItemContainer>
      )}
      <div>
        <Button
          kind="text"
          onClick={() => {
            const newParameter = {
              name: '',
              value: '',
            };
            const newAction = { ...action, params: [...action.params, newParameter] };
            onUpdateAction(newAction);
          }}
        >
          <MdAdd size={16} />
          <span className={css({ marginLeft: '4px' })}>Add parameter</span>
        </Button>
      </div>
    </>
  );
}
