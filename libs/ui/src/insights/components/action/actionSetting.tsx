import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import React from 'react';
import { Action, ActionsProps } from './type';
import { ChartSettingItemContainer } from '../../charts/chartTools/chartSetting/chartSettingItemContainer';
import { MdAdd } from '@react-icons/all-files/md/MdAdd';
import IconButton from '../../components/iconButton';
import ActionDetail from './actionDetail';
import { ActionList } from './actionList';

export function ActionSetting(props: ActionsProps) {
  const [, theme] = useStyletron();

  const {
    settings,
    globalParameters,
    onSettingUpdate,
    links,
    field,
    label = 'Actions',
    options,
    unique,
    helpText,
  } = props;
  const actions = settings[field] ? settings[field] : [];

  const onUpdateAction = (newAction: Action, index: number) => {
    const newTableActions = [...actions];
    if (index === newTableActions.length) {
      newTableActions.push(newAction);
    } else {
      newTableActions[index] = newAction;
    }
    onSettingUpdate(field, newTableActions);
  };

  return (
    <ChartSettingItemContainer
      label={label}
      icon={
        <IconButton>
          <MdAdd size={16} color={theme.colors.accent} />
        </IconButton>
      }
      helpText={helpText}
      popover={
        !(unique && actions.length > 0)
          ? ({ close }) => (
              <ActionDetail
                action={
                  {
                    text: '',
                    url: '',
                    params: [],
                  } as Action
                }
                links={links}
                options={options}
                globalParameters={globalParameters}
                onUpdateAction={(action) => onUpdateAction(action, actions.length)}
                close={close}
              />
            )
          : null
      }
    >
      <ActionList
        actions={actions}
        onActionChanged={(actions) => {
          onSettingUpdate(field, actions);
        }}
        options={options}
        globalParameters={globalParameters}
        links={links}
      />
    </ChartSettingItemContainer>
  );
}
