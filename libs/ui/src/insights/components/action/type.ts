import { GlobalParams } from '../../chart';
import { ChartSettingProps } from '../../chart/chart';

export interface ActionParam {
  name: string; // parameter name
  value: any; // parameter value
  paramGlobalInput?: string;
  isCreatable?: boolean; // if the parameter value is created by user
}

export interface Action {
  text: string; // action label
  url: string; // manually entered url
  urlName: string; // manually entered url or page name for showing in the select
  params: ActionParam[]; // page parameters
  pageID?: string; // page id
}

export interface ActionsProps extends ChartSettingProps {
  field: string; // chart customized setting key
  label?: string;
  options?: string[];
  unique?: boolean;
  helpText?: string;
}

export interface ActionProps {
  action: Action;
  links: { pageName: string; pageID: string; params: GlobalParams }[];
  onUpdateAction: (action: Action) => void;
  globalParameters?: GlobalParams;
  options?: string[];
  onDeleteAction?: () => void;
  close?: () => void;
  // only graph/map widget can easily bind vertex data.
  supportVertexType?: boolean;
}
