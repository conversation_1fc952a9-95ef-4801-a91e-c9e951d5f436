import React from 'react';
import { TableBuilder } from '@tigergraph/app-ui-lib/table';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { TableBuilderColumn, SIZE } from 'baseui/table-semantic';
import { GlobalParam, GlobalParams } from '../chart';
import { upperCase } from 'lodash';

type Props = {
  params: GlobalParams;
};

const getType = (param: GlobalParam) => {
  if (param.type === 'LIST') {
    return `List<${param.elementType}>`;
  }
  if (param.type === 'MAP') {
    return `MAP<${upperCase(param.keyType)}, ${upperCase(param.valueType)}>`;
  }
  return param.type;
};

export function ParametersTable({ params }: Props) {
  let keys = Object.keys(params);
  let rows = [];
  for (let key of keys) {
    let param = params[key];
    rows.push({
      name: key,
      type: getType(param),
      value:
        param.type === 'LIST' || param.type === 'VERTEX' || param.type === 'MAP'
          ? JSON.stringify(
              param.value,
              null,
              param.type === 'VERTEX' || (param.type === 'LIST' && param.elementType === 'VERTEX') ? 2 : 0
            )
          : param.value,
    });
  }

  const [css] = useStyletron();

  return (
    <TableBuilder
      data={rows}
      emptyMessage="No inputs available."
      overrides={{
        Root: { style: { maxHeight: '200px', overflow: 'auto' } },
      }}
    >
      <TableBuilderColumn header="Variable Name">{(row) => row.name}</TableBuilderColumn>
      <TableBuilderColumn header="Type">{(row) => row.type}</TableBuilderColumn>
      <TableBuilderColumn
        header="Current Value"
        overrides={{
          TableBodyCell: {
            style: {
              maxWidth: '400px',
            },
          },
        }}
      >
        {(row) => (
          <pre
            className={css({
              padding: 0,
              margin: 0,
              whiteSpace: 'pre-wrap',
              wordWrap: 'break-word',
            })}
          >
            {row.value}
          </pre>
        )}
      </TableBuilderColumn>
    </TableBuilder>
  );
}
