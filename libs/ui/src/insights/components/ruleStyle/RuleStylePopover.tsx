import React, { useState, useCallback, useMemo } from 'react';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { Button } from '@tigergraph/app-ui-lib/button';
import { KIND, SHAPE } from 'baseui/button';
import { HexColorPicker } from 'react-colorful';
import { StatefulPopover, PLACEMENT } from 'baseui/popover';
import { MdAdd } from '@react-icons/all-files/md/MdAdd';
import { expand } from 'inline-style-expand-shorthand';

import { Select, Input, popoverOverrides } from './baseui';
import { RangeColorStyle, RangeNumericStyle, RuleColor, StyleLabel, UniqueColorStyle } from './components';
import { getCategoricalPalates, getContinualColorsPalates } from './colors';
import { ruleConditionDescription, validateRule } from './evaluator';
import {
  FieldArray,
  FIELD_CONDITIONS,
  Rule,
  StyleDesc,
  FieldType,
  RuleStyleProps,
  Field,
  Condition,
  Palate,
} from './type';
import IconButton from '../iconButton';
import ConfirmButtons from '../confirmButtons';
import { ReactComponent as WarningIcon } from '../icons/warning.svg';
import { getFieldValueRange as getFieldValuesRange } from './utils';

export type NewRuleStylePopoverProps = {
  widgetType?: string;
  onSave: (rule: Rule) => void;
  ruleConfigs: StyleDesc[];
  fields: FieldArray;
  conditionSelectorHidden?: boolean;
};

export function NewRuleStylePopover(props: NewRuleStylePopoverProps) {
  const [, theme] = useStyletron();

  const { ruleConfigs } = props;
  const rule = useMemo(() => {
    const rule: Rule = {
      fieldType: props.fields.length ? props.fields[0].type : 'number',
      fieldName: props.fields.length ? props.fields[0].name : '',
      condition: '=',
      conditionValue: '',
      conditionStartValue: 0,
      conditionEndValue: 0,
      // init to first custom style
      styleKey: ruleConfigs[0].styleKey,
      styleType: ruleConfigs[0].styleType,
      styleLabel: ruleConfigs[0].styleLabel,
      styleValue: '#ff0000',
      palateName: '',
      styleStartValue: '',
      styleStartLabel: '',
      styleEndValue: '',
      styleEndLabel: '',
    };
    return rule;
  }, [ruleConfigs, props.fields]);

  return (
    <StatefulPopover
      showArrow={false}
      overrides={popoverOverrides}
      content={({ close }) => <EditRule {...props} rule={rule} onClose={close} />}
      placement={PLACEMENT.bottomRight}
      returnFocus
      autoFocus
      ignoreBoundary
      popperOptions={{
        modifiers: {
          preventOverflow: {
            enabled: false,
          },
          hide: {
            enabled: false,
          },
        },
      }}
    >
      <IconButton>
        <MdAdd size={16} color={theme.colors.accent} />
      </IconButton>
    </StatefulPopover>
  );
}

export type EditRuleStylePopoverProps = {
  rule: Rule;
} & NewRuleStylePopoverProps;

export function EditRuleStylePopover(props: EditRuleStylePopoverProps) {
  const [css, theme] = useStyletron();
  const { rule } = props;
  const { styleType } = rule;

  return (
    <StatefulPopover
      showArrow={false}
      overrides={popoverOverrides}
      content={({ close }) => <EditRule {...props} onClose={close} />}
      placement={PLACEMENT.bottom}
      returnFocus
      autoFocus
      ignoreBoundary
      popperOptions={{
        modifiers: {
          preventOverflow: {
            enabled: false,
          },
          hide: {
            enabled: false,
          },
        },
      }}
    >
      <button
        key="edit"
        className={css({
          outline: 'none',
          border: 'none',
          padding: 0,
          backgroundColor: 'transparent',
          display: 'flex',
          alignItems: 'center',
          textAlign: 'left',
          cursor: 'pointer',
          flexGrow: 1,
          flexBasis: 0,
          flexShrink: 1,
        })}
      >
        <div
          className={css({
            width: '48px',
            height: '24px',
          })}
        >
          {styleType === 'numeric' || styleType === 'edgetype' ? (
            <div
              className={css({
                width: '100%',
                height: '100%',
                borderRadius: '4px',
                border: `1px solid ${theme.colors.gray400}`,
                ...theme.typography.Label,
                color: theme.colors.black03,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                minWidth: 0,
              })}
            >
              {/* {styleLabel} */}
              {styleType === 'numeric' ? 'size' : 'type'}
            </div>
          ) : (
            <RuleColor rule={rule} />
          )}
        </div>
        <div
          className={css({
            ...expand({
              flex: 1,
            }),
            marginLeft: '8px',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            flexBasis: 0,
            flexGrow: 1,
          })}
        >
          <span
            className={css({
              wordBreak: 'break-word',
            })}
          >
            {rule.fieldName}
          </span>
          <span
            title={ruleConditionDescription(rule)}
            className={css({
              marginLeft: '4px',
              flexShrink: 0,
              textOverflow: 'ellipsis',
              maxWidth: '14ch',
              overflow: 'hidden',
            })}
          >
            {ruleConditionDescription(rule)}
          </span>
        </div>
      </button>
    </StatefulPopover>
  );
}

function EditRule({
  widgetType,
  fields,
  ruleConfigs,
  rule,
  conditionSelectorHidden,
  onSave,
  onClose,
}: {
  widgetType?: string;
  fields: FieldArray;
  ruleConfigs: StyleDesc[];
  rule: Rule;
  conditionSelectorHidden?: boolean;
  onSave: (rule: Rule) => void;
  onClose: () => void;
}) {
  const [css, theme] = useStyletron();

  const [editRule, setEditRule] = useState(rule);
  const [validateError, setValidateError] = useState<string>(undefined);

  const updateRuleField = useCallback((ruleField: keyof Rule, ruleFieldValue: any) => {
    setEditRule((editRule) => {
      return {
        ...editRule,
        [ruleField]: ruleFieldValue,
      };
    });
  }, []);

  const resetRuleField = (styleDesc: StyleDesc) => {
    const newEditRule: Rule = {
      ...editRule,
      styleKey: styleDesc.styleKey,
      styleType: styleDesc.styleType,
      styleLabel: styleDesc.styleLabel,
      styleValue: styleDesc.styleType === 'color' ? '#ff0000' : '',
      styleStartValue: '',
      styleStartLabel: '',
      styleEndValue: '',
      styleEndLabel: '',
    };
    setEditRule(newEditRule);
  };

  let field: Field | undefined = null;
  for (let f of fields) {
    if (f.name === editRule.fieldName) {
      field = f;
      break;
    }
  }

  const showColumnNameSelect = widgetType === 'table';
  const needVerticalColumnName =
    showColumnNameSelect &&
    (editRule.condition === 'range' || editRule.condition === 'unique value' || editRule.condition === 'contains');

  return (
    <div
      className={css({
        ...theme.typography.Body2,
      })}
    >
      <StyleLabel>If meets following condition</StyleLabel>
      <ConditionItem
        conditionSelectorHidden={conditionSelectorHidden}
        rule={editRule}
        fields={fields}
        updateRuleField={updateRuleField}
        ruleConfigs={ruleConfigs}
      />
      <div
        className={css({
          display: 'flex',
          gap: '4px',
          paddingBottom: '6px',
        })}
      >
        <div className={css({ flex: '1 1 auto', width: 0 })}>
          <StyleLabel>Then apply style</StyleLabel>
          <div
            className={css({
              display: 'flex',
              flexDirection: needVerticalColumnName ? 'column' : 'row',
              gap: '4px',
            })}
          >
            <Select
              clearable={false}
              value={[{ styleKey: editRule.styleKey, styleType: editRule.styleType }]}
              options={ruleConfigs}
              valueKey="styleKey"
              labelKey="styleLabel"
              onChange={(params) => {
                const styleDesc = params.value[0] as StyleDesc;
                resetRuleField(styleDesc);
              }}
              overrides={{
                Root: {
                  style: {
                    flex: '1 1 auto',
                    width: needVerticalColumnName ? '100%' : 0,
                    maxWidth: '176px',
                  },
                },
              }}
            />
            {showColumnNameSelect && (
              <Select
                clearable={false}
                value={editRule.columnName ? [{ id: editRule.columnName }] : undefined}
                labelKey="id"
                placeholder="Select column name"
                options={fields.map((field) => ({
                  id: field.name,
                }))}
                onChange={(params) => {
                  const columnName = params.value?.[0]?.id;
                  updateRuleField('columnName', columnName);
                }}
                overrides={{
                  Root: {
                    style: {
                      flex: '1 1 auto',
                      width: needVerticalColumnName ? '100%' : 0,
                    },
                  },
                }}
              />
            )}
          </div>
        </div>
        <div
          className={css({
            marginTop: '28px',
            display: 'flex',
            flexDirection: 'column',
          })}
        >
          <StyleItem rule={editRule} updateRuleField={updateRuleField} field={field} ruleConfigs={ruleConfigs} />
        </div>
      </div>
      {validateError ? (
        <div
          className={css({
            color: theme.colors.negative,
            marginTop: '12px',
            marginRight: '8px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'flex-end',
          })}
        >
          <WarningIcon />
          <span
            className={css({
              marginLeft: '6px',
            })}
          >
            {validateError}
          </span>
        </div>
      ) : null}
      <ConfirmButtons
        onConfirm={() => {
          const validateError = validateRule(editRule);
          if (!validateError) {
            onSave(editRule);
            onClose();
          } else {
            setValidateError(validateError);
          }
        }}
        onCancel={() => {
          onClose();
        }}
      />
    </div>
  );
}

function ConditionItem({
  rule,
  fields,
  conditionSelectorHidden,
  updateRuleField,
}: RuleStyleProps & {
  fields: FieldArray;
  conditionSelectorHidden?: boolean;
}) {
  const [css] = useStyletron();

  let conditionsForFields = FIELD_CONDITIONS[rule.fieldType];
  const showInput =
    rule.condition !== 'always' &&
    rule.condition !== 'range' &&
    rule.condition !== 'unique value' &&
    rule.fieldType !== 'boolean';
  const showSelect = rule.fieldType === 'boolean' && rule.condition !== 'always' && rule.condition !== 'unique value';
  for (let field of fields) {
    if (field.name === rule.fieldName && field.conditions) {
      conditionsForFields = field.conditions;
    }
  }

  return (
    <div
      className={css({
        display: 'flex',
      })}
    >
      {conditionSelectorHidden ? (
        <div
          className={css({
            display: 'flex',
          })}
        >
          <span
            className={css({
              fontSize: '14px',
              marginRight: '19px',
              alignSelf: 'center',
            })}
          >
            Value
          </span>
        </div>
      ) : (
        <Select
          clearable={false}
          value={[
            {
              id: rule.fieldName,
            },
          ]}
          options={fields.map((field) => ({
            id: field.name,
          }))}
          labelKey="id"
          onChange={(params) => {
            const filedName = params.value[0].id;
            updateRuleField('fieldName', filedName);
            let fieldType: FieldType = 'number'; // default to number
            for (let field of fields) {
              if (field.name === filedName) {
                fieldType = field.type;
                if (fieldType === 'number') {
                  const valuesRange = getFieldValuesRange(field);
                  if (valuesRange) {
                    updateRuleField('conditionStartValue', valuesRange[0]);
                    updateRuleField('conditionEndValue', valuesRange[1]);
                  }
                }
                break;
              }
            }
            updateRuleField('fieldType', fieldType);
            // after change fileType, reset condition
            if (rule.fieldType !== fieldType) {
              updateRuleField('condition', '=');
            }
          }}
          overrides={{
            Root: {
              style: {
                width: '176px',
                marginRight: '4px',
              },
            },
          }}
        />
      )}
      <Select
        clearable={false}
        value={[{ condition: rule.condition }]}
        options={conditionsForFields.map((condition) => ({
          condition: condition,
          label: condition,
        }))}
        valueKey="condition"
        labelKey="label"
        onChange={(params) => {
          const condition = params.value[0].condition as Condition;
          updateRuleField('condition', condition);

          // reset palate
          let palate: Palate = null;
          if (condition === 'range') {
            palate = getContinualColorsPalates()[0];
          } else if (condition === 'unique value') {
            palate = getCategoricalPalates()[0];
          }

          if (!palate) {
            return;
          }

          const colors = palate.colors;
          updateRuleField('palateName', palate.name);

          if (condition === 'range') {
            updateRuleField('styleStartValue', colors[0]);
            updateRuleField('styleEndValue', colors[colors.length - 1]);
          }
        }}
        overrides={{
          Root: {
            style: {
              width: conditionSelectorHidden ? '122px' : '56px',
              flexGrow: rule.condition === 'unique value' || rule.condition === 'range' ? 1 : 0,
            },
          },
          IconsContainer: {
            style: {
              paddingLeft: '3px',
            },
          },
        }}
      />
      {showInput ? (
        <Input
          value={rule.conditionValue as string}
          onChange={(e) => updateRuleField('conditionValue', e.currentTarget.value)}
          overrides={{
            Root: {
              style: {
                flex: 1,
                marginLeft: '4px',
              },
            },
            After: {
              style: {
                display: 'none',
              },
            },
          }}
        />
      ) : null}
      {showSelect && (
        <Select
          clearable={false}
          options={[true, false].map((value) => ({
            value,
            label: String(value),
          }))}
          onChange={(e) => updateRuleField('conditionValue', e.value[0].value)}
          valueKey="value"
          value={[{ value: rule.conditionValue }]}
          overrides={{
            Root: {
              style: {
                marginLeft: '4px',
                flex: '1',
              },
            },
          }}
        />
      )}
    </div>
  );
}

function StyleItem(props: RuleStyleProps) {
  const { rule } = props;

  if (rule.condition !== 'range' && rule.condition !== 'unique value') {
    return <SingleStyle {...props} />;
  } else if (rule.condition === 'range') {
    return <RangeStyle {...props} />;
  } else if (rule.condition === 'unique value') {
    return <UniqueStyle {...props} />;
  }
}

function SingleStyle(props: RuleStyleProps) {
  const { rule } = props;

  if (rule.styleType === 'color') {
    return <SingleColorStyle {...props} />;
  } else if (rule.styleType === 'numeric') {
    return <SingleNumericStyle {...props} />;
  } else {
    return <SingleEdgeTypeStyle {...props} />;
  }
}

function SingleEdgeTypeStyle({ rule, updateRuleField }: RuleStyleProps) {
  return (
    <Select
      clearable={false}
      value={[{ value: String(rule.styleValue) }]}
      options={[
        {
          label: (
            <svg width="20" height="10">
              <line x1="0" y1="5" x2="200" y2="5" style={{ stroke: 'black', strokeWidth: 2 }} />
            </svg>
          ),
          value: 'solid',
        },
        {
          label: (
            <svg width="20" height="10">
              <line x1="0" y1="5" x2="200" y2="5" style={{ stroke: 'black', strokeWidth: 2, strokeDasharray: '4 2' }} />
            </svg>
          ),
          value: 'dotted',
        },
        {
          label: (
            <svg width="20" height="10">
              <line
                x1="0"
                y1="5"
                x2="200"
                y2="5"
                style={{ stroke: 'black', strokeWidth: 2, strokeDasharray: '10 5' }}
              />
            </svg>
          ),
          value: 'dashed',
        },
      ]}
      valueKey="value"
      labelKey="label"
      onChange={(params) => {
        const value = params.value[0];
        updateRuleField('styleValue', String(value.value));
        updateRuleField('styleLabel', value.label);
      }}
      overrides={{
        Root: {
          style: {
            width: '100%',
          },
        },
        Dropdown: {
          style: {
            maxHeight: 5 * 32 + 'px',
          },
        },
      }}
    />
  );
}

function SingleColorStyle({ rule, updateRuleField }: RuleStyleProps) {
  const [css] = useStyletron();
  return (
    <StatefulPopover
      content={() => (
        <HexColorPicker color={rule.styleValue} onChange={(color) => updateRuleField('styleValue', color)} />
      )}
      returnFocus
      autoFocus
    >
      <Button kind="secondary" shape={SHAPE.square}>
        <span
          className={css({
            width: '24px',
            height: '24px',
            backgroundColor: rule.styleValue,
          })}
        />
      </Button>
    </StatefulPopover>
  );
}

function SingleNumericStyle({ rule, updateRuleField, ruleConfigs }: RuleStyleProps) {
  const { styleKey } = rule;

  let options = [];
  for (let ruleConfig of ruleConfigs) {
    if (ruleConfig.styleKey === styleKey) {
      options = ruleConfig.styleOptions;
    }
  }

  return (
    <Select
      clearable={false}
      value={[{ value: String(rule.styleValue) }]}
      options={options}
      valueKey="value"
      labelKey="label"
      onChange={(params) => {
        const value = params.value[0];
        updateRuleField('styleLabel', value.label);
        // for 4x, 2x, etc
        if (typeof value.label === 'string' && /^\d+\.?\d*x$/.test(value.label)) {
          updateRuleField('styleValue', Number(value.value));
        } else {
          updateRuleField('styleValue', String(value.value));
        }
      }}
      overrides={{
        Root: {
          style: {
            width: '100%',
          },
        },
        Dropdown: {
          style: {
            maxHeight: 5 * 32 + 'px',
          },
        },
      }}
    />
  );
}

function RangeStyle(props: RuleStyleProps) {
  const [css] = useStyletron();

  const { rule } = props;
  if (rule.styleType === 'color') {
    return <RangeColorStyle {...props} />;
  }
  return (
    <div
      className={css({
        marginTop: '-28px',
      })}
    >
      <RangeNumericStyle {...props} />
    </div>
  );
}

function UniqueStyle(props: RuleStyleProps) {
  const { rule } = props;
  if (rule.styleType === 'color') {
    return <UniqueColorStyle {...props} />;
  }
  // unique style only support color
  return (
    <StyleLabel
      $style={{
        paddingTop: '6px',
      }}
    >
      not applicable
    </StyleLabel>
  );
}
