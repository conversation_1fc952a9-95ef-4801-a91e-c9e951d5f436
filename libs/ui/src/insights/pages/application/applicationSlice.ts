import { getUID } from '../../utils';
import { DashboardState } from '../dashboard/dashboardSlice';

export type Role = 'editor' | 'viewer' | 'owner' | 'none';

export interface ApplicationState {
  id: string;
  iconURL?: string;
  screenshot?: string;
  defaultGraph?: string;
  title: string;
  pages: DashboardState[];
  owner?: string;
  version: string;
  // backend use the pageConfigSeparated to distinguish whether page configs have been separated from app config.
  pageConfigSeparated?: boolean;
  userRoleForApp: Role;
  force?: boolean;
  isCloud?: boolean;
  themeType?: 'light' | 'dark';
}

export const initializeApplication = (): ApplicationState => {
  return {
    id: getUID(),
    title: 'New Application',
    pages: [],
    version: '',
    // backend use the pageConfigSeparated to distinguish whether page configs have been separated from app config.
    pageConfigSeparated: true,
    userRoleForApp: 'owner',
  };
};
