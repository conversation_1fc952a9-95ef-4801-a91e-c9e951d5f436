import React, { useMemo, useState } from 'react';
import clsx from 'clsx';
import { useParams } from 'react-router-dom';

import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { <PERSON><PERSON><PERSON>eader, ModalBody, ModalButton, ModalFooter } from '@tigergraph/app-ui-lib/modal';
import { StatefulPopover, Popover } from '@tigergraph/app-ui-lib/popover';
import { TRIGGER_TYPE, PLACEMENT } from 'baseui/popover';
import { MdFullscreen } from '@react-icons/all-files/md/MdFullscreen';
import { MdFullscreenExit } from '@react-icons/all-files/md/MdFullscreenExit';
import { MdShare } from '@react-icons/all-files/md/MdShare';

import { StatefulMenu, NestedMenus, MenuOverrides, StyledListItem, ItemsT } from 'baseui/menu';

import { Ellipsis } from '../../components/icons';
import { ChartStatus } from './type';
import { useSandbox } from '../../chart/sandbox';
import { EditIcon, DuplicateIcon, DeleteIcon } from '../../components/icons';

import StyledModal from '../../components/styledModal';
import { GlobalParams } from '../../chart';
import { expand } from 'inline-style-expand-shorthand';
import { ApplicationState } from '../application/applicationSlice';

import { supportAccessToken } from '../../config/feature';
import { ChartState } from '../../charts';
import { Button } from '@tigergraph/app-ui-lib/button';

type Props = {
  application: ApplicationState;
  title: string;
  shareLink: string;
  chartStatus: ChartStatus;
  onChartDelete: () => void;
  onChartDuplicate: (pageID: string, isCurrentPage?: boolean) => void;
  onChartEdit: () => void;
  isFullScreen: boolean;
  setIsFullScreen: (boolean) => void;
  globalParams: GlobalParams;
  hideWidgetName: boolean;
  chart: ChartState;
};

const menuOverrides: MenuOverrides = {
  List: {},
  ListItem: {
    style: {
      paddingLeft: '8px',
      paddingRight: '8px',
    },
  },

  // todo: the popper flip is not work well with baseui/menu
  // ChildMenuPopover: {
  //   props: {
  //     placement: 'rightTop',
  //     popperOptions: {
  //       modifiers: {
  //         preventOverflow: {
  //           enabled: false,
  //         },
  //         flip: {
  //           enabled: true,
  //           options: {
  //             fallbackPlacements: ['right'],
  //           },
  //         },
  //       },
  //     },
  //   },
  // },
};

export default function ChartTitle({
  application,
  title,
  shareLink,
  chartStatus,
  onChartDelete,
  onChartDuplicate,
  onChartEdit,
  isFullScreen,
  setIsFullScreen,
  globalParams,
  hideWidgetName,
  chart,
}: Props) {
  const [css, theme] = useStyletron();
  const [showWarnDialog, setShowWarnDialog] = useState(false);

  const supportTokenShare = supportAccessToken();
  const [showShareDialog, setShowShareDialog] = useState(false);
  const [showTokenShareDialog, setShowTokenShareDialog] = useState(false);
  const [showMenu, setShowMenu] = useState(false);

  const { errMsg, evaluatedTitle } = useSandbox(title, globalParams);

  let finalTitle = title.indexOf('${variables.') >= 0 ? evaluatedTitle : title;

  const menuItems = [
    {
      label: MenuItem({ icon: <EditIcon />, label: 'Edit' }),
      id: 'Edit',
    },
    {
      label: MenuItem({ icon: <MdShare />, label: 'Share' }),
      id: 'Share',
    },
    {
      label: MenuItem({ icon: <DuplicateIcon />, label: 'Duplicate to' }),
      id: 'Duplicate',
    },
    {
      label: MenuItem({ icon: <DeleteIcon />, label: 'Delete' }),
      id: 'Delete',
    },
  ];

  const params = useParams<{ appID: string; pageID: string }>();
  const pageListItems: ItemsT = useMemo(() => {
    let ret: ItemsT = [];

    for (let page of application.pages) {
      if (page.id === params.pageID) {
        ret.push({
          label: 'Current Page',
          id: page.id,
          isCurrentPage: true,
        });
        if (application.pages.length > 1) {
          ret.push({
            divider: true,
          });
        }
        break;
      }
    }

    for (let page of application.pages) {
      if (page.id !== params.pageID) {
        ret.push({
          label: page.title,
          id: page.id,
        });
      }
    }

    return ret;
  }, [params, application]);

  const onCloseMenu = () => {
    setShowMenu(false);
  };
  const showScreenFull = chart.type !== 'value' && chart.type !== 'Inputs' && !hideWidgetName;
  return (
    <div
      className={
        css(
          hideWidgetName
            ? {
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'flex-end',
                position: 'absolute',
                top: '0px',
                left: '0px',
                height: '20px',
                width: '100%',
                zIndex: '2',
              }
            : {
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                margin: '8px 8px 0',
              }
        ) + ` widget-item-header`
      }
    >
      <div
        className={clsx(
          'draggable',
          css({
            cursor: chartStatus === 'preview' ? 'auto' : 'move',
            fontSize: '16px',
            lineHeight: '24px',
            overflow: 'hidden',
            whiteSpace: 'nowrap',
            textOverflow: 'ellipsis',
            color: errMsg ? theme.colors.negative : theme.colors['text.primary'],
            flexGrow: 1,
            minWidth: '14px',
            minHeight: '14px', // take space for empty title
          })
        )}
      >
        {errMsg || (hideWidgetName ? '' : finalTitle)}
      </div>

      <div className={css({ display: 'flex', alignItems: 'center' }) + ` widget-item-header-actions`}>
        {showScreenFull && (
          <StatefulPopover
            triggerType={TRIGGER_TYPE.hover}
            placement={PLACEMENT.bottom}
            content={isFullScreen ? 'Exit fullscreen' : 'Enter fullscreen'}
          >
            <Button kind="text" shape="square" onClick={() => setIsFullScreen(!isFullScreen)}>
              {isFullScreen ? <MdFullscreenExit size={24} /> : <MdFullscreen size={24} />}
            </Button>
          </StatefulPopover>
        )}
        {chartStatus !== 'preview' && !isFullScreen && (
          <div
            className={css(
              hideWidgetName
                ? { position: 'absolute', right: '8px', top: '8px' }
                : {
                    position: 'relative',
                  }
            )}
          >
            <StatefulPopover
              triggerType={TRIGGER_TYPE.hover}
              placement={PLACEMENT.bottom}
              content="Widget configuration"
            >
              <Button
                kind="text"
                shape="square"
                overrides={{
                  BaseButton: {
                    style: {
                      marginLeft: '4px',
                    },
                  },
                }}
                onClick={() => setShowMenu(true)}
              >
                <Ellipsis />
              </Button>
            </StatefulPopover>
            <Popover
              showArrow={false}
              isOpen={showMenu}
              onEsc={onCloseMenu}
              onClickOutside={onCloseMenu}
              popperOptions={{
                modifiers: {
                  preventOverflow: {
                    enabled: true,
                  },
                  hide: {
                    enabled: true,
                  },
                },
              }}
              content={() => (
                <NestedMenus>
                  <StatefulMenu
                    overrides={{
                      ...menuOverrides,
                      Option: {
                        props: {
                          // todo
                          // Nested Menu now will report warning: `Function components cannot be given refs.`
                          getChildMenu: (item: { id: string }) => {
                            if (item.id === 'Duplicate') {
                              return (
                                <StatefulMenu
                                  items={pageListItems}
                                  overrides={{
                                    ...menuOverrides,
                                    List: {
                                      style: {
                                        // need specifier the width here
                                        width: '176px',
                                        maxHeight: '265px',
                                        overflowY: 'scroll',
                                        marginLeft: '-4px',
                                      },
                                    },
                                    ListItem: (props) => {
                                      // to support divider
                                      if (props.item.divider) {
                                        return (
                                          <StyledListItem
                                            {...props}
                                            $style={{
                                              paddingTop: 0,
                                              paddingBottom: 0,
                                              marginLeft: '-16px',
                                              marginRight: '-16px',
                                            }}
                                          >
                                            {/* hardcode the divider here */}
                                            <div
                                              className={css({
                                                borderBottom: `1px solid ${theme.colors.divider}`,
                                              })}
                                            />
                                          </StyledListItem>
                                        );
                                      }
                                      return (
                                        <StyledListItem
                                          {...props}
                                          $style={{
                                            whiteSpace: 'nowrap',
                                            overflow: 'hidden',
                                            textOverflow: 'ellipsis',
                                          }}
                                        />
                                      );
                                    },
                                  }}
                                  onItemSelect={({
                                    item: { id, isCurrentPage },
                                  }: {
                                    item: { id: string; isCurrentPage?: boolean };
                                  }) => {
                                    if (id) {
                                      onChartDuplicate(id, isCurrentPage);
                                      onCloseMenu();
                                    }
                                  }}
                                />
                              );
                            }
                            return null;
                          },
                        },
                      },
                    }}
                    onItemSelect={({ item: { id } }: { item: { id: string } }) => {
                      if (id === 'Edit') {
                        onChartEdit();
                      } else if (id === 'Share') {
                        if (supportTokenShare) {
                          setShowTokenShareDialog(true);
                        } else {
                          setShowShareDialog(true);
                        }
                      } else if (id === 'Delete') {
                        setShowWarnDialog(true);
                      }
                      onCloseMenu();
                    }}
                    items={menuItems}
                  />
                </NestedMenus>
              )}
              overrides={{
                Body: {
                  style: {
                    marginTop: '8px',
                    boxShadow: theme.colors['shadow.popup'],
                    border: `1px solid ${theme.colors.divider}`,
                    backgroundColor: theme.colors['background.primary'],
                  },
                },
                Inner: {
                  style: {
                    backgroundColor: theme.colors['background.primary'],
                    ...expand({
                      padding: 0,
                    }),
                  },
                },
              }}
              placement={PLACEMENT.bottomLeft}
            >
              <span
                className={css({
                  position: 'absolute',
                  left: 0,
                  right: 0,
                  top: 0,
                  bottom: 0,
                  zIndex: -1,
                })}
              />
            </Popover>
          </div>
        )}
      </div>
      <StyledModal onClose={() => setShowWarnDialog(false)} isOpen={showWarnDialog}>
        <ModalHeader>Delete Widget</ModalHeader>
        <ModalBody>
          Are you sure you want to <strong>{title}</strong>
        </ModalBody>
        <ModalFooter>
          <ModalButton kind="secondary" onClick={() => setShowWarnDialog(false)}>
            Cancel
          </ModalButton>
          <ModalButton
            onClick={() => {
              onChartDelete();
              setShowWarnDialog(false);
            }}
          >
            OK
          </ModalButton>
        </ModalFooter>
      </StyledModal>
    </div>
  );
}

function MenuItem({ icon, label }: { icon: React.ReactNode; label: React.ReactNode }) {
  const [css, theme] = useStyletron();
  return (
    <div
      className={css({
        display: 'flex',
        alignItems: 'center',
      })}
    >
      <div
        className={css({
          width: '32px',
          display: 'flex',
          textAlign: 'center',
          alignItems: 'center',
        })}
      >
        {icon}
      </div>
      <span>{label}</span>
    </div>
  );
}
