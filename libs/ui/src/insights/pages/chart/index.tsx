import React, { useEffect, useRef, useReducer } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';

import Chart from '../../charts/chart';
import { chartReducer, ChartState } from '../../charts';
import ChartTitle from './chartTitle';
import { ApplicationState } from '../application/applicationSlice';
import { ChartStatus } from './type';
import { useGraphList, useQueries, useSchema } from './useMetaHook';
import { GlobalParam, GlobalParams } from '../../chart';
import { DashboardState } from '../dashboard/dashboardSlice';
import { getBaseURL } from '@tigergraph/tools-models';

// 1 the ChartContainer in library is different from ChartContainer in insights
// 2 remove backend api integration
// 3 remove links

const getLink = () => '';
const links: { pageName: string; pageID: string; params: GlobalParams }[] = [];

export function ChartContainer({
  application,
  page,
  initChart,
  chartStatus,
  globalParams,
  setGlobalParameter,
  deleteGlobalParameter,
  onChartSave,
  onChartDiscard,
  isFullScreen = false,
  isClusterMode = false,
  setIsFullScreen,
  onChartDuplicate,
}: {
  application: ApplicationState;
  page: DashboardState;
  initChart: ChartState;
  chartStatus: ChartStatus;
  globalParams: GlobalParams;
  setGlobalParameter: (name: string, value: GlobalParam) => void;
  deleteGlobalParameter: (name: string) => void;
  onChartSave: (chart: ChartState) => void;
  onChartDiscard: () => void;
  isFullScreen?: boolean;
  isClusterMode?: boolean;
  setIsFullScreen?: (boolean: boolean) => void;
  onChartDuplicate?: (pageID: string, isCurrentPage?: boolean) => void;
  baseURL?: string;
}) {
  const [css] = useStyletron();
  const baseURL = getBaseURL();

  const [chart, dispatch] = useReducer(chartReducer, initChart);

  // todo(lin)
  // some widget need schema/queries to run, so we need
  // 1 provider loading status
  // 2 handle error
  // 3 not delay widget data loading that no need for schema/queries
  const graphs = useGraphList(baseURL);
  const {
    schema,
    isLoading: isLoadingSchema,
    isError: isSchemaError,
    error: schemaError,
  } = useSchema(baseURL, chart.graphName);
  const {
    queries,
    isLoading: isLoadingQueries,
    isError: isQueriesError,
    error: queriesError,
  } = useQueries(baseURL, chart.graphName);

  const navigate = useNavigate();

  // const { mutate: deleteWidget } = useMutationDeleteWidget();

  const params = useParams<{ appID: string; pageID: string }>();

  const chartRef = useRef<HTMLDivElement>(null);

  // setup default graph name in edit mode
  useEffect(() => {
    if (chartStatus === 'edit' && graphs.length > 0 && !chart.graphName) {
      dispatch({
        graphName: graphs[0],
      });
    }
  }, [chart, dispatch, graphs, chartStatus]);

  // delete chart:
  // 1. if the chart is an Inputs widget and it contains global parameters, it removes all the parameters from page;
  // 2. otherwise, we remove the chart directly;

  // const onChartDelete = () => {
  //   let globalParameters = page.globalParameters;
  //   if (chart.type === 'Inputs') {
  //     globalParameters = { ...globalParams };
  //     if (chart.chartSettings.inputStates) {
  //       for (const inputState of chart.chartSettings.inputStates) {
  //         delete globalParameters[inputState.name];
  //       }
  //     }
  //   }

  //   deleteWidget(
  //     {
  //       appId: params.appID,
  //       page: {
  //         ...page,
  //         globalParameters,
  //       },
  //       id: chart.id,
  //       version: chart.version,
  //     },
  //     {
  //       onSuccess: () => {
  //         showToast({
  //           kind: 'positive',
  //           message: 'Widget deleted successfully.',
  //         });
  //       },
  //       onError: (err) => {
  //         showToast({
  //           kind: KIND.negative,
  //           message: `Delete widget failed. \n${getErrorMessage(err)}`,
  //         });
  //       },
  //     }
  //   );
  // };

  const onChartEdit = () => {
    navigate(`/app/${params.appID}/page/${params.pageID}?edit=true`, {
      replace: true,
    });
    setTimeout(() => {
      navigate(`widgetEdit/${chart.id}`);
    });
  };
  const { hideWidgetName } = chart;

  return (
    <div
      className={css({
        position: 'relative',
        height: '100%',
        display: 'flex',
        // refer: https://stackoverflow.com/questions/33605552/how-to-prevent-a-flex-item-height-to-overflow-due-to-its-content
        minHeight: 0,
        flexDirection: 'column',
      })}
    >
      {chartStatus !== 'edit' && (
        <ChartTitle
          application={application}
          title={chart.title}
          shareLink=""
          chartStatus={chartStatus}
          onChartDelete={() => {}}
          onChartDuplicate={onChartDuplicate}
          onChartEdit={onChartEdit}
          isFullScreen={isFullScreen}
          setIsFullScreen={setIsFullScreen}
          globalParams={globalParams}
          hideWidgetName={hideWidgetName}
          chart={chart}
        />
      )}
      <div
        className={css({
          display: 'flex',
          flexDirection: 'column',
          flexBasis: 0,
          flexGrow: 1,
          minHeight: 0,
        })}
        ref={chartRef}
      >
        <Chart
          chart={chart}
          force={application.force}
          isCloud={application.isCloud}
          themeType={application.themeType}
          dispatch={dispatch}
          schema={schema}
          queries={queries}
          graphs={graphs}
          isLoading={isLoadingSchema || isLoadingQueries}
          isClusterMode={isClusterMode}
          isError={isSchemaError || isQueriesError}
          schemaError={schemaError}
          queriesError={queriesError}
          onGraphChanged={(graph) =>
            dispatch({
              graphName: graph,
            })
          }
          chartStatus={chartStatus}
          globalParameters={globalParams}
          setGlobalParameter={setGlobalParameter}
          deleteGlobalParameter={deleteGlobalParameter}
          links={links}
          getLink={getLink}
          onChartDiscard={onChartDiscard}
          onChartSave={() => onChartSave(chart)}
          baseURL={baseURL}
        />
      </div>
    </div>
  );
}
