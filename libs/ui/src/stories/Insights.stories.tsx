import React, { useState, useCallback, Suspense } from 'react';
import { Meta } from '@storybook/react';
import { QueryClient, QueryClientProvider } from 'react-query';
import { BrowserRouter } from 'react-router-dom';

import { GlobalParam, GlobalParams } from '../insights/chart';
import { ChartContainer } from '../insights/pages/chart';
import { ChartState, initializeChart } from '../insights/charts/chartSlice';
import { ChartStatus } from '../insights/pages/chart/type';
import { ApplicationState, initializeApplication } from '../insights/pages/application/applicationSlice';
import { DashboardState, initializeDashboard } from '../insights/pages/dashboard/dashboardSlice';
import axios from 'axios';
import { ThemeProvider, useTheme } from './themeContext';
import StyledToasterContainer from '@tigergraph/app-ui-lib/styledToasterContainer';
import { setupAxiosInstance, setVersion } from '@tigergraph/tools-models';

setupAxiosInstance(axios.create(), 'http://localhost:6005', {});
setVersion('4.2.0');

const client = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
    },
  },
});

export default {
  title: 'Insights',
  component: ChartContainer,
  option: { showPanel: false },
  decorators: [
    (Story) => {
      return (
        <ThemeProvider>
          <QueryClientProvider client={client}>
            <BrowserRouter>
              <Suspense fallback={<div>Loading...</div>}>
                <Story />
              </Suspense>
              <StyledToasterContainer />
            </BrowserRouter>
          </QueryClientProvider>
        </ThemeProvider>
      );
    },
  ],
} as Meta;

export const Value = () => {
  const data = constructChartState('value');
  return <ChartWrapper {...data} />;
};

export const ValueChartNormal = () => {
  const data = constructChartState('value');
  return <ChartWrapper {...data} chartStatus="normal" />;
};

export const ValueChartEdit = () => {
  const data = constructChartState('value');
  const globalParameters: GlobalParams = {
    list_bool: {
      elementType: 'BOOL',
      id: 'input_hV2bdtK5HY8eC1sEHNuEHF',
      name: 'list_bool',
      type: 'LIST',
      value: [true, false],
    },
    list_datetime: {
      elementType: 'DATETIME',
      id: 'input_tXXW6wZENdTTSTPT3pK4a2',
      name: 'list_datetime',
      type: 'LIST',
      value: ['2025-02-24'],
    },
    list_vertex: {
      elementType: 'VERTEX',
      id: 'input_nMZfxW1LhgfJc8qoF5dkxU',
      name: '',
      type: 'LIST',
      value: [
        {
          vertexID: 'Cheongju-si',
          vertexType: 'City',
        },
      ],
    },
    map_bool: {
      id: 'input_pGGtDgroukrudvLJrLH3aq',
      keyType: 'bool',
      name: 'map_bool',
      type: 'MAP',
      value: [
        {
          key: true,
          value: false,
        },
      ],
      valueType: 'bool',
    },
    map_datetime: {
      id: 'input_4RvHMHySmag88WbVS7Gr4B',
      keyType: 'datetime',
      name: 'map_datetime',
      type: 'MAP',
      value: [
        {
          key: '2024-08-22',
          value: '2024-08-22',
        },
      ],
      valueType: 'datetime',
    },
    map_string: {
      id: 'input_r63L3dDDhXh9WcUpALJtkw',
      keyType: 'string',
      name: 'map_string',
      type: 'MAP',
      value: [
        {
          key: 'str',
          value: 'str',
        },
        {
          key: 'str2',
          value: 'str2',
        },
      ],
      valueType: 'string',
    },
    map_string_2: {
      id: 'input_r63L3dDDhXh9WcUpALJtkw',
      keyType: 'string',
      name: 'map_string_2',
      type: 'MAP',
      value: [
        {
          key: 'str',
          value: 'str',
        },
        {
          key: 'str2',
          value: 'str2',
        },
      ],
      valueType: 'string',
    },
    my_list_int: {
      elementType: 'NUMBER',
      id: 'input_3VNDseR2tQfoJXsjorwPZR',
      name: 'my_list_int',
      type: 'LIST',
      value: ['1', '2'],
    },
    my_list_int_2: {
      elementType: 'NUMBER',
      id: 'input_3VNDseR2tQfoJXsjorwPZR',
      name: 'my_list_int_2',
      type: 'LIST',
      value: ['1', '2'],
    },
    my_list_string: {
      elementType: 'STRING',
      id: 'input_3gyMQXa1S43YC87FN6Poyo',
      name: 'my_list_string',
      type: 'LIST',
      value: ['1', '2'],
    },
    my_list_string_2: {
      elementType: 'STRING',
      id: 'input_3gyMQXa1S43YC87FN6Poyo',
      name: 'my_list_string_2',
      type: 'LIST',
      value: ['1', '2'],
    },
    my_map: {
      id: 'input_2Dc5qQLWRfYMvZ8QgguT5J',
      keyType: 'number',
      name: 'my_map',
      type: 'MAP',
      value: [
        {
          key: '1',
          value: '1',
        },
      ],
      valueType: 'string',
    },
    my_map_2: {
      id: 'input_2Dc5qQLWRfYMvZ8QgguT5J',
      keyType: 'number',
      name: 'my_map_2',
      type: 'MAP',
      value: [
        {
          key: '1',
          value: '1',
        },
      ],
      valueType: 'string',
    },
  } as any;
  return <ChartWrapper {...data} initGlobalParam={globalParameters} chartStatus="edit" />;
};

export const InputsChartEdit = () => {
  const data = constructChartState('Inputs');
  const globalParameters: GlobalParams = {
    variable: {
      id: 'input_fuVsXuzjwEPexMEjqTDx5v',
      elementType: 'VERTEX',
      name: 'variable',
      type: 'LIST',
      value: [],
    },
  };
  data.chart.chartSettings = {
    inputStates: [
      {
        dataType: 'vertex',
        id: 'input_fuVsXuzjwEPexMEjqTDx5v',
        name: 'variable',
        settings: {},
        widgetType: 'List',
        selectedGraph: 'ldbc_snb',
        // "vertexType": "City"
      },
    ],
  };
  return <ChartWrapper {...data} initGlobalParam={globalParameters} chartStatus="edit" />;
};

export const InputsChartPreview = () => {
  const data = constructChartState('Inputs');
  const globalParameters = {
    variable: {
      id: 'input_fuVsXuzjwEPexMEjqTDx5v',
      name: 'variable',
      type: 'BOOLEAN',
      value: false,
    },
    variable_1: {
      value: 0,
      type: 'NUMBER',
      name: 'variable_1',
      id: 'input_cirHPBL4q4D5eY1Asf3AJZ',
    },
  } as const;
  data.chart.chartSettings = {
    graphName: 'ldbc_snb',
    inputStates: [
      {
        id: 'input_fuVsXuzjwEPexMEjqTDx5v',
        name: 'variable',
        // dataType: 'Bool',
        // settings: {},

        // list
        // widgetType: 'List',
        // dataType: 'Vertex',
        // selectedGraph: 'ldbc_snb',
        // vertexType: 'City',
        // settings: {
        //   defaultList: [
        //     { id: '123', type: 'City' },
        //     { id: '345', type: 'City' },
        //   ]
        // },

        // map
        widgetType: 'Map',
        keyType: 'string',
        valueType: 'string',
        settings: {
          defaultMap: [
            {
              key: '123',
              value: '123',
            },
            {
              key: '456',
              value: '456',
            },
          ],
        },
      },
      {
        id: 'input_jijQQcC8rSV6LzhYk5qyE3',
        name: 'variable_1',
        dataType: 'number',
        widgetType: 'Input',
        settings: {},
      },
    ],
  };
  // const globalParameters = {
  //   variable: {
  //     id: 'input_fuVsXuzjwEPexMEjqTDx5v',
  //     name: 'variable',
  //     type: 'VERTEX',
  //     value: { vertexID: '123', vertexType: 'POST' }
  //   }
  // } as const;
  // data.chart.chartSettings = {
  //   graphName: 'ldbc_snb',
  //   inputStates: [
  //     {
  //       "id": "input_whDR7eJvqniYJ9DvJq7QoD",
  //       "name": "variable",
  //       "dataType": "vertex",
  //       "widgetType": "Input",
  //       "settings": {
  //         "defaultVertex": {
  //           "type": "Post",
  //           "id": "12"
  //         }
  //       },
  //       "value": "",
  //       "placeholder": "",
  //       "selectedGraph": "ldbc_snb"
  //     }
  //   ]
  // };

  return <ChartWrapper {...data} initGlobalParam={globalParameters} chartStatus="normal" />;
};

export const Markdown = () => {
  const data = constructChartState('markdown');
  return <ChartWrapper {...data} chartStatus="edit" />;
};

export const MarkdownPreview = () => {
  const data = constructChartState('markdown');
  data.chart.chartSettings = {
    // @ts-ignore
    markdown: '${variables.string} ${variables.vertex.vertexID}',
  };
  return <ChartWrapper {...data} chartStatus="preview" />;
};

export const TableChart = () => {
  const globalParams: GlobalParams = {
    my_map_2: {
      id: 'input_2Dc5qQLWRfYMvZ8QgguT5J',
      keyType: 'string',
      name: 'my_map_2',
      type: 'MAP',
      value: [
        {
          key: '1',
          value: '1',
        },
        {
          key: '2',
          value: '2',
        },
      ],
      valueType: 'string',
    },
  };
  const data = constructChartState('table');
  return <ChartWrapper {...data} initGlobalParam={globalParams} />;
};

export const Graph = () => {
  const data = constructChartState('internal-graph');
  return <ChartWrapper {...data} chartStatus="edit" />;
};

export const Map = () => {
  const data = constructChartState('map');
  return <ChartWrapper {...data} chartStatus="edit" />;
};

export const Bar = () => {
  const data = constructChartState('bar');
  return <ChartWrapper {...data} chartStatus="edit" />;
};

export const Pie = () => {
  const data = constructChartState('pie');
  return <ChartWrapper {...data} chartStatus="edit" />;
};

function ChartWrapper({
  chart,
  application,
  page,
  chartStatus,
  initGlobalParam = {
    string: {
      id: '1',
      name: 'string',
      type: 'STRING',
      value: 'hello world',
    },
    vertex: {
      id: '2',
      name: 'vertex',
      type: 'VERTEX',
      value: {
        vertexID: 'Chang Sha',
        vertexType: 'City',
      },
    },
  },
}: {
  chart: ChartState;
  application: ApplicationState;
  page: DashboardState;
  chartStatus?: ChartStatus;
  initGlobalParam?: GlobalParams;
}) {
  const { themeType } = useTheme();

  // add container for sandbox
  if (!document.querySelector('.iframe__container')) {
    const div = document.createElement('div');
    div.classList.add('iframe__container');
    document.body.appendChild(div);
  }

  const [globalParameters, setGlobalParameters] = useState<GlobalParams>(initGlobalParam);

  const setGlobalParameter = useCallback((name: string, value: GlobalParam, prevName?: string) => {
    setGlobalParameters((globalParamsForEdit) => {
      let params = {
        ...globalParamsForEdit,
        [name]: value,
      };

      if (prevName) {
        delete params[prevName];
      }
      return params;
    });
  }, []);

  const deleteGlobalParameter = useCallback((name: string) => {
    setGlobalParameters((globalParameter) => {
      const newGlobalParameters = { ...globalParameter };
      delete newGlobalParameters[name];
      return newGlobalParameters;
    });
  }, []);

  return (
    <div
      style={{
        width: '100%',
        height: '100%',
      }}
    >
      <ChartContainer
        application={{
          ...application,
          themeType,
          isCloud: true,
        }}
        page={page}
        initChart={chart}
        chartStatus={chartStatus ?? 'edit'}
        globalParams={globalParameters}
        setGlobalParameter={setGlobalParameter}
        deleteGlobalParameter={deleteGlobalParameter}
        onChartSave={() => {}}
        onChartDiscard={() => {}}
        isFullScreen={false}
        setIsFullScreen={() => {}}
        onChartDuplicate={() => {}}
        // isClusterMode={true}
      />
    </div>
  );
}

function constructChartState(type: string) {
  let application = initializeApplication();
  let page = initializeDashboard();

  let chartState = initializeChart(type);
  chartState.graphName = process.env.GRAPH!;

  application.pages = [page];
  page.chartMap![chartState.id] = chartState;

  return {
    application,
    page,
    chart: chartState,
  };
}
