{
  "extends": "../../tsconfig.base.json",
  "compilerOptions": {
    /* Visit https://aka.ms/tsconfig.json to read more about this file */

    "outDir": "dist",

    "target": "ESNext",
    "lib": ["dom", "dom.iterable", "esnext"],
    "jsx": "react-jsx",

    "types": ["vitest/globals", "@testing-library/jest-dom"],

    "module": "esnext",
    "moduleResolution": "node",
    "esModuleInterop": true,
    "resolveJsonModule": true,
    "allowSyntheticDefaultImports": true,

    "declaration": true,
    "declarationDir": "dist/types",

    // copy lots of code from insights
    // we have to disable strict type check for easier code migration
    "noImplicitAny": false,
    "strictPropertyInitialization": false,
    "strictNullChecks": false,

    "strict": true,
    // "noUnusedLocals": true,
    // "noUnusedParameters": true,
    "skipLibCheck": true,

    "pretty": true,
    "allowJs": true,

    "baseUrl": ".",
    "paths": {
      "react": ["./node_modules/@types/react"]
    }
  },
  "include": ["./src/**/*"],
  "exclude": ["./dist/**/*", "node_modules"]
}
