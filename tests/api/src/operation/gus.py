import os
from src.api.tools_api import tools
from src.api.gsql_server_api import gsqlserver
from src.common.read_data import data
from src.common.logger import logger
from src.common.result_base import ResultBase
from pathlib import Path
import json
import mimetypes
import requests
import urllib.parse
from codecs import encode

RESOURCES_PATH = Path(__file__).resolve().parents[2].joinpath("data/gus")
LOCALFILES_PATH = Path(__file__).resolve().parents[3].joinpath("data/localFiles")


class GUS():
    def give_headers(self, cookie):
        header = {
            "Cookie": cookie,
            "Content-Type": "application/json"
        }
        return header

    # check the loading job meta
    def loading_job_meta(self, cookies):

        logger.info("check the loading_job_meta ...")
        header = self.give_headers(cookies)
        return ResultBase(tools.get_loading_job_meta(headers=header))
    
    # get the tigergraph config 
    def get_configs(self, cookies, param):

        logger.info("check the get_config ...")
        header = self.give_headers(cookies)
        #run the get api config with key
        configs=""
        if param != "":
            keys = param.split(",")
            configs="&".join(f"key={key}" for key in keys)
        return ResultBase(tools.get_config(headers=header, params=configs))
    
    # get the tigergraph loading data files 
    def get_loadingdata(self, cookies):

        logger.info("check the get_loadingdata ...")
        header = self.give_headers(cookies)
        return ResultBase(tools.get_loadingdata(headers=header))
    
    # check the loading data files with chunk
    def check_chunk(self, cookies, file_name="upload_data.csv"):
        logger.info("check the check_chunk ...")
        header = self.give_headers(cookies)
        param="resumableChunkNumber=1&resumableChunkSize=1048576&resumableCurrentChunkSize=11&resumableTotalSize=11&resumableIdentifier=11-upload_datacsv&resumableFilename={0}&resumableRelativePath={0}&resumableTotalChunks=1&resumableType=text%252Fcsv".format(file_name)
        return ResultBase(tools.check_chunk(headers=header, params=param))
    
    #delete files in data loading path
    def delete_localfiles(self, cookies, file_name):
        logger.info("check the delete_files ...")
        header = self.give_headers(cookies)
        param="path={}".format(file_name)
        return ResultBase(tools.delete_files(headers=header, params=param))
    
     #get files in data loading path
    def get_localfiles(self, cookies):
        logger.info("check the get_localfiles ...")
        header = self.give_headers(cookies)
        return ResultBase(tools.get_files(headers=header))
    
    def upload_chunk(self, cookies="", param="", file_name="upload_data.csv"):
        """
             upload files post call with cookies
            :return: return the response of api with custom keywords
        """
        if file_name=="upload_data.csv":
            file_path = str(RESOURCES_PATH.joinpath(file_name))
        else:
            file_path = str(LOCALFILES_PATH.joinpath(file_name))
        logger.info("file_path: " + file_path)

        #combine the data payload
        dataList = []
        boundary = 'wL36Yn8afVp8Ag7AmP8qZ0SA4n1v9T'
        dataList.append(encode('--' + boundary))
        dataList.append(encode('Content-Disposition: form-data; name=resumableChunkNumber;'))

        dataList.append(encode('Content-Type: {}'.format('text/plain')))
        dataList.append(encode(''))

        dataList.append(encode("1"))
        dataList.append(encode('--' + boundary))
        dataList.append(encode('Content-Disposition: form-data; name=resumableChunkSize;'))

        dataList.append(encode('Content-Type: {}'.format('text/plain')))
        dataList.append(encode(''))

        dataList.append(encode("10000"))
        dataList.append(encode('--' + boundary))
        dataList.append(encode('Content-Disposition: form-data; name=resumableCurrentChunkSize;'))

        dataList.append(encode('Content-Type: {}'.format('text/plain')))
        dataList.append(encode(''))

        dataList.append(encode("1"))
        dataList.append(encode('--' + boundary))
        dataList.append(encode('Content-Disposition: form-data; name=resumableTotalSize;'))

        dataList.append(encode('Content-Type: {}'.format('text/plain')))
        dataList.append(encode(''))

        dataList.append(encode("1"))
        dataList.append(encode('--' + boundary))
        dataList.append(encode('Content-Disposition: form-data; name=resumableType;'))

        dataList.append(encode('Content-Type: {}'.format('text/plain')))
        dataList.append(encode(''))

        dataList.append(encode("text/csv"))
        dataList.append(encode('--' + boundary))
        dataList.append(encode('Content-Disposition: form-data; name=resumableIdentifier;'))

        dataList.append(encode('Content-Type: {}'.format('text/plain')))
        dataList.append(encode(''))

        dataList.append(encode("11-upload_datacsv"))
        dataList.append(encode('--' + boundary))
        dataList.append(encode('Content-Disposition: form-data; name=resumableFilename;'))

        dataList.append(encode('Content-Type: {}'.format('text/plain')))
        dataList.append(encode(''))

        dataList.append(encode("{}".format(file_name)))
        dataList.append(encode('--' + boundary))
        dataList.append(encode('Content-Disposition: form-data; name=resumableRelativePath;'))

        dataList.append(encode('Content-Type: {}'.format('text/plain')))
        dataList.append(encode(''))

        dataList.append(encode("{}".format(file_name)))
        dataList.append(encode('--' + boundary))
        dataList.append(encode('Content-Disposition: form-data; name=resumableTotalChunks;'))

        dataList.append(encode('Content-Type: {}'.format('text/plain')))
        dataList.append(encode(''))

        dataList.append(encode("1"))
        dataList.append(encode('--' + boundary))
        dataList.append(encode('Content-Disposition: form-data; name=file; filename={0}'.format("{}".format(file_name))))

        fileType = mimetypes.guess_type(file_path)[0] or 'application/octet-stream'
        dataList.append(encode('Content-Type: {}'.format(fileType)))
        dataList.append(encode(''))

        with open(file_path, 'rb') as f:
            dataList.append(f.read())
            dataList.append(encode('--'+boundary+'--'))
            dataList.append(encode(''))
            body = b'\r\n'.join(dataList)
        payload = body
        headers = {
            'Content-Type': 'multipart/form-data',
            'Cookie': cookies,
            'Content-type': 'multipart/form-data; boundary={}'.format(boundary)
        }
        param="resumableChunkNumber=1&resumableChunkSize=1048576&resumableCurrentChunkSize=11&resumableTotalSize=11&resumableIdentifier=11-upload_datacsv&resumableFilename={0}&resumableRelativePath={0}&resumableTotalChunks=1&resumableType=text%252Fcsv".format(file_name)

        resp = ResultBase(tools.upload_chunk(headers=headers, data=payload, params=param))
        #logger.info("resp: " + resp.text)  
        return resp
    
    # upload local data files 
    def upload_assemble(self, cookies, file_name="upload_data.csv"):
        logger.info("check the upload_assemble ...")
        header = self.give_headers(cookies)
        param="path=&resumableIdentifier=11-upload_datacsv&resumableFilename={}".format(file_name)
        return ResultBase(tools.upload_assemble(headers=header, params=param))
    
    
    # post the tigergraph config 
    def post_configs(self, cookies, body):

        logger.info("check the post_config ...")
        header = self.give_headers(cookies)
        return ResultBase(tools.post_config(headers=header, data=body))
    
    # generate the nginx certification files
    def generate_cert(self, cookies, body):

        logger.info("check the generate_cert ...")
        header = self.give_headers(cookies)
        return ResultBase(tools.generate_cert(headers=header, data=body))
    
    # stop the tigergraph services
    def stop_service(self, cookies, param):

        logger.info("check the stop_service ...")
        header = self.give_headers(cookies)

        #run the get api config with key
        configs=""
        if param != "":
            keys = param.split(",")
            configs="&".join(f"serviceName={key}" for key in keys)
        return ResultBase(tools.stop_service(headers=header, params=configs))
    
    # start_service the tigergraph services
    def start_service(self, cookies, param):

        logger.info("check the start_service ...")
        header = self.give_headers(cookies)

        #run the get api config with key
        configs=""
        if param != "":
            keys = param.split(",")
            configs="&".join(f"serviceName={key}" for key in keys)
        return ResultBase(tools.start_service(headers=header, params=configs))
    
    # restart the tigergraph services
    def restart_service(self, cookies, param):

        logger.info("check the restart_service ...")
        header = self.give_headers(cookies)

        #run the get api config with key
        configs=""
        if param != "":
            keys = param.split(",")
            configs="&".join(f"serviceName={key}" for key in keys)
        return ResultBase(tools.restart_service(headers=header, params=configs))

    # check the explore graph
    def explore_graph_vertex(self, cookies, graph_name, param):

        logger.info("check the explore graph...")
        header = self.give_headers(cookies)
        return ResultBase(tools.explore_graph_v(graph_name=graph_name, param=param, headers=header))

    def get_algorithm_info(self, cookies):
        """
        Get all algorithm info
        :param cookies: Cookies gotten from GST
        :return: return the response of api with custom keywords
        """

        header = {"Cookie": cookies}
        res = ResultBase(tools.get_graph_algorithm(headers=header))
        return res

    def install_algorithm_query(self, cookies, graph_name, query_name):
        """
        Install algorithm query for specified target
        :param cookies: Cookies gotten from GST
        :param graph_name: graph name
        :return: return the response of api with custom keywords
        """
        header = self.give_headers(cookies)
        res = ResultBase(tools.install_algorithm(graph_name, query_name, headers=header))
        return res
    
    def get_queries_info(self, cookies, graph_name, query_name):
        """
        Get all queies info
        :param cookies: Cookies gotten from GST
        :param graph_name
        :return: return the response of api with custom keywords
        """
        header = {"Cookie": cookies}
        res = ResultBase(tools.get_query_info(graph_name, query_name, headers=header))
        return res

    def delete_query(self, cookies, graph_name, query_name):
        header = {"Cookie": cookies}
        res = ResultBase(tools.delete_query(graph_name, query_name, headers=header))
        return res

    def delete_all_query_drafts(self, cookies, graph_name, params):
        """
        delete all query draft
        :param cookies: Cookies gotten from GST
        :param graph_name
        :return: return the response of api with custom keywords
        """
        header = {"Cookie": cookies}
        res = ResultBase(tools.delete_all_query_drafts(graph_name, params, headers=header))
        return res

    def delete_query_draft_with_para(self, cookies, graph_name, draft_name, para):
        """
        delete query draft
        :param cookies: Cookies gotten from GST
        :param graph_name
        :return: return the response of api with custom keywords
        """
        header = {"Cookie": cookies}
        res = ResultBase(tools.delete_gsql_query_with_para(graph_name, draft_name, para, headers=header))
        return res

    def delete_query_draft(self, cookies, graph_name, draft_name):
        """
        delete query draft
        :param cookies: Cookies gotten from GST
        :param graph_name
        :return: return the response of api with custom keywords
        """
        header = {"Cookie": cookies}
        res = ResultBase(tools.delete_gsql_query(graph_name, draft_name, headers=header))
        return res

    def install_query_draft(self, cookies, graph_name, params):
        """
        install query draft
        :param cookies: Cookies gotten from GST
        :param graph_name
        :return: return the response of api with custom keywords
        """
        header = {"Cookie": cookies}
        res = ResultBase(tools.install_query_draft(graph_name, params, headers=header))
        return res

    """
        design schema model
    """
    def export_solution(self, cookies, params):
        """
                export solution
                :param cookies: Cookies gotten from GST
                :param url params
                :return: return the response of api
                """
        header = {"Cookie": cookies}
        return ResultBase(tools.export_solution(params, headers=header))

    def get_loading_jobs_info(self, cookies, graph_name, params):
        """
        Get all queies info
        :param cookies: Cookies gotten from GST
        :param graph_name
        :return: return the response of api with custom keywords
        """
        header = {"Cookie": cookies}
        res = ResultBase(tools.get_loading_jobs(graph_name, params, headers=header))
        return res

    def delete_loading_jobs_info(self, cookies, graph_name, params):
        """
        delete all loading jobs
        :param cookies: Cookies gotten from GST
        :param graph_name
        :return: return the response of api with custom keywords
        """
        header = {"Cookie": cookies}
        res = ResultBase(tools.delete_all_loading_jobs(graph_name, params, headers=header))
        return res

    def get_loading_jobs_name_info(self, cookies, graph_name, params):
        """
        Get all queies info
        :param cookies: Cookies gotten from GST
        :param graph_name
        :return: return the response of api with custom keywords
        """
        header = {"Cookie": cookies}
        res = ResultBase(tools.get_loading_jobs_name(graph_name, params, headers=header))
        return res

    def delete_loading_jobs_name_info(self, cookies, graph_name, params):
        """
        Get all queies info
        :param cookies: Cookies gotten from GST
        :param graph_name
        :return: return the response of api with custom keywords
        """
        header = {"Cookie": cookies}
        res = ResultBase(tools.delete_loading_jobs_name(graph_name, params, headers=header))
        return res

    def get_loading_jobs_progress_info(self, cookies, graph_name, params):
        """
        Get loading jobs progress info
        :param cookies: Cookies gotten from GST
        :param graph_name
        :return: return the response of api with custom keywords
        """
        header = {"Cookie": cookies}
        res = ResultBase(tools.get_loading_jobs_progress(graph_name, params, headers=header))
        return res

    def pause_loading_jobs_progress_info(self, cookies, graph_name, params):
        """
        pause loading jobs progress info
        :param cookies: Cookies gotten from GST
        :param graph_name
        :return: return the response of api with custom keywords
        """
        header = {"Cookie": cookies}
        res = ResultBase(tools.pause_loading_jobs_progress(graph_name, params, headers=header))
        return res

    def resume_loading_jobs_progress_info(self, cookies, graph_name, params):
        """
        resume loading jobs progress info
        :param cookies: Cookies gotten from GST
        :param graph_name
        :return: return the response of api with custom keywords
        """
        header = {"Cookie": cookies}
        res = ResultBase(tools.resume_loading_jobs_progress(graph_name, params, headers=header))
        return res

    def stop_loading_jobs_progress_info(self, cookies, graph_name, params):
        """
        stop loading jobs progress info
        :param cookies: Cookies gotten from GST
        :param graph_name
        :return: return the response of api with custom keywords
        """
        header = {"Cookie": cookies}
        res = ResultBase(tools.stop_loading_jobs_progress(graph_name, params, headers=header))
        return res

    def post_add_loading_jobs_name_info(self, cookies, graph_name, params, json_data):
        """
        add loading jobs
        :param cookies: Cookies gotten from GST
        :param graph_name
        :return: return the response of api with custom keywords
        """
        header = self.give_headers(cookies)
        res = ResultBase(tools.post_add_loading_jobs_name(graph_name, params, json_data, headers=header))
        return res

    def post_start_loading_jobs_info(self, cookies, graph_name, params, json_data):
        """
        start loading jobs
        :param cookies: Cookies gotten from GST
        :param graph_name
        :return: return the response of api with custom keywords
        """
        header = self.give_headers(cookies)
        res = ResultBase(tools.post_start_loading_jobs(graph_name, params, json_data, headers=header))
        return res

    def post_add_query_draft_info(self, cookies, graph_name, query_name, params, json_data):
        """
        add query draft
        :param cookies: Cookies gotten from GST
        :param graph_name
        :return: return the response of api with custom keywords
        """
        header = self.give_headers(cookies)
        res = ResultBase(tools.post_add_query_draft(graph_name, query_name, params, json_data, headers=header))
        return res

    def put_update_query_draft_info(self, cookies, graph_name, query_name, params, json_data):
        """
        add query draft
        :param cookies: Cookies gotten from GST
        :param graph_name
        :return: return the response of api with custom keywords
        """
        header = self.give_headers(cookies)
        res = ResultBase(tools.put_update_query_draft(graph_name, query_name, params, json_data, headers=header))
        return res



    """
        VQB
    """
    def get_all_patterns(self, cookies, graph_name):
        """
        Get all patterns info
        :param cookies: Cookies gotten from GST
        :return: return the response of api with custom keywords
        """

        header = {"Cookie": cookies}
        res = ResultBase(tools.get_graph_patterns(graph_name, headers=header))
        return res

    def delete_VQB_pattern(self, cookies, graph_name, params):
        """
        delete pattern
        :param cookies: Cookies gotten from GST
        :param graph_name
        :return: return the response of api with custom keywords
        """
        header = {"Cookie": cookies}
        res = ResultBase(tools.delete_VQB_patterns(graph_name, params, headers=header))
        return res
    
    def execute_v2(self, cookies, query, variables):
        #post the request with api V2, should transfer the body to retrive the result
        header = self.give_headers(cookies)
        body = {
            "query": query,
            "variables": variables
        }
        res = tools.execute_api_v2(headers=header, data=json.dumps(body))
        return ResultBase(res)


    """
    Graph Style Model
    """
    def get_graph_style(self, cookies, graph_name='', scope='global'):
        header={"Cookie": cookies}
        if scope=='local':
            return ResultBase(tools.get_local_graph_styles(graph_name, headers=header))
        else :
            return ResultBase(tools.get_global_graph_styles(headers=header))


    def put_graph_style(self, cookies, style_data, graph_name='', scope='global'):
        header={"Cookie": cookies}
        if scope=='local':
            return ResultBase(tools.put_local_graph_style(graph_name, json.dumps(style_data), headers=header))
        else:
            return ResultBase(tools.put_global_graph_styles(json.dumps(style_data), headers=header)) 

    def delete_graph_style(self, cookies, graph_name='', scope='global'):
        header={"Cookie": cookies}
        if scope=='local':
            return ResultBase(tools.delete_local_graph_style(graph_name, headers=header))
        else:
            return ResultBase(tools.delete_global_graph_style(headers=header))

    def post_graph_style(self, cookies, graph_name, style_data):
        header={"Cookie": cookies} 
        return ResultBase(tools.post_local_graph_style(graph_name, json.dump(style_data), headers=header))
    
    """
    Loading job info
    """
    def get_graph_loading_job_info(self, cookies, graph_name):
        header={"Cookie": cookies} 
        return ResultBase(tools.get_loading_job_info(graph_name, headers=header))

    def put_graph_loading_job_info(self, cookies, graph_name, info_data):
        header={"Cookie": cookies} 
        return ResultBase(tools.put_loading_job_info(graph_name, json.dumps(info_data), headers=header))

    def delete_graph_loading_job_info(self, cookies, graph_name):
        header={"Cookie": cookies} 
        return ResultBase(tools.delete_loading_job_info(graph_name, headers=header))

    """
    Exploration results model
    """
    def get_all_exploration_result(self, cookies, graph_name):
        header={"Cookie": cookies}
        return ResultBase(tools.get_all_exploration_results(graph_name, headers=header))

    def get_exploration_result(self, cookies, graph_name, file_name):
        header={"Cookie": cookies}
        return ResultBase(tools.get_exploration_result(graph_name, file_name, headers=header))

    def post_exploration_result(self, cookies, graph_name, file_name, exploration_data):
        header={"Cookie": cookies}
        # get schema object
        schema_info = ResultBase(gsqlserver.get_schema_info(graph_name, headers=header)).results
        ## request body
        exploration_result = json.loads(exploration_data)
        exploration_result["schema"] = schema_info
        exploration_result["name"] = file_name
        return ResultBase(tools.post_exploration_results(graph_name, file_name, json.dumps(exploration_result), headers=header))

    def delete_exploration_result(self, cookies, graph_name, file_name):
        header={"Cookie": cookies}
        return ResultBase(tools.delete_exploration_results(graph_name, file_name, headers=header))

    def put_exploration_result(self, cookies, graph_name, file_name, exploration_data, change_data=''):
        header={"Cookie": cookies}
        if change_data:
            change_data = json.loads(change_data)
            exploration_data["data"] = change_data["data"]
            exploration_data["previewImage"] = change_data["previewImage"]
            exploration_data["name"] = file_name
        else :
            # get schema object
            schema_info = ResultBase(gsqlserver.get_schema_info(graph_name, headers=header)).results
            ## request body
            exploration_data = json.loads(exploration_data)
            exploration_data["schema"] = schema_info
        return ResultBase(tools.put_exploration_results(graph_name, file_name, json.dumps(exploration_data), headers=header))


    """
    Data Source model
    """
    def get_dataSource_name_info(self, cookies, graph_name, dataSource_type):
        header={"Cookie": cookies}
        return ResultBase(tools.get_datasource_info(graph_name, dataSource_type, headers=header))

    def put_dataSource(self, cookies, graph_name, dataSource_type, alias, setting_data):
        header={"Cookie": cookies,
                "Content-Type": "text/plain"}
        return ResultBase(tools.put_datasource_info(graph_name, dataSource_type, alias, setting_data, headers=header))

    def post_dataSource_format_check(self,cookies, dataSource_type, payload):
        header={"Cookie": cookies,
                "Content-Type": "text/plain"}
        return ResultBase(tools.post_datasource_format_check(dataSource_type, payload, headers=header))

    def delete_dataSource(self, cookies, graph_name, dataSource_type, alias):
        header={"Cookie": cookies}
        return ResultBase(tools.delete_datasource(graph_name, dataSource_type, alias, headers=header))

    """
    Health Check
    """
    def get_health_check(self, cookies):
        header = {"Cookie": cookies}
        return ResultBase(tools.get_ping(headers=header))
