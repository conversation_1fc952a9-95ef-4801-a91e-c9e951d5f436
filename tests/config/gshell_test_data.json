{"Solution": "COVID-19 Analysis", "GraphName": "MyGraph", "gshell_paths": {"upload_data_path": "./tests/data/gshelldata/upload_data", "download_data_path": "./tests/data/gshelldata/download_data", "upload_toGST_path": "./tests/data/gshelldata/upload_toGST"}, "testcase_load_gsqlfile": {"USE_GLOBAL": {"gsqlfile": "USE_GLOBAL.gsql", "output": "", "assertTimeout": 30}}, "testcase_queries": {"0_drop_query": {"gsqlfile": "drop_gshell_age_distribution.gsql", "output": "", "assertTimeout": 100}, "createinstall_query": {"gsqlfile": "createinstall_gshell_age_distribution.gsql", "output": "Query installation finished", "assertTimeout": 600}, "run_query": {"gsqlfile": "run_gshell_age_distribution.gsql", "output": "@@age_map", "assertTimeout": 300, "jsonOutput": true}, "run_opencypher_query": {"gsqlfile": "opencypher_query.gsql", "output": "patient_count", "assertTimeout": 300}, "drop_query": {"gsqlfile": "drop_gshell_age_distribution.gsql", "output": "Successfully dropped queries", "assertTimeout": 100}}, "testcase_attributes": {"0_drop_attributesGraphs": {"gsqlfile": "drop_attributesGraphs.gsql", "output": "", "assertTimeout": 100}, "Graph_test_udt": {"gsqlfile": "Graph_test_udt.gsql", "output": "\"error\": false", "assertTimeout": 100}, "Graph_test_SetList": {"gsqlfile": "Graph_test_SetList.gsql", "output": "\"error\": false", "assertTimeout": 100}, "Graph_test_Map": {"gsqlfile": "Graph_test_Map.gsql", "output": "\"error\": false", "assertTimeout": 100}, "drop_attributesGraphs": {"gsqlfile": "drop_attributesGraphs.gsql", "output": "Successfully dropped vertex types: [Vertex_map]", "assertTimeout": 60}}, "testcase_create_queries_check_on_GST": {"0_drop_attributesGraphs": {"gsqlfile": "drop_queries.gsql", "output": "", "assertTimeout": 100}, "Key_null_json_result_check": {"gsqlfile": "Key_null_json.gsql", "output": "Successfully", "assertTimeout": 100}, "Value2_null_json_result_check": {"gsqlfile": "Value_null_json.gsql", "output": "Successfully", "assertTimeout": 100}, "Key_value_null_json_result_check": {"gsqlfile": "Key_value_null_json.gsql", "output": "Successfully", "assertTimeout": 100}}}