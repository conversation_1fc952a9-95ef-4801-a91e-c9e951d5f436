import logging

import pytest
from locators.tools.insights_locators import InsightLocators
from selenium.webdriver import ActionChains
from selenium.webdriver.common.by import By
from seleniumbase.common import decorators
from base.tools_basecase import ToolsBaseCase
from utils.data_util.login import LoginUtils
from os import listdir

LOGGER = logging.getLogger(__name__)
short_wait_time_out = 5
click_time_out = 60
assertTextTimeout = 60
wait_render_timeout = 120

class InsightsAppPage:
    application_create_confirm = "Created a new application."
    application_duplicate_confirm = "Duplicated the application."
    application_save_confirm = "Application saved successfully."
    application_update_confirm = "Updated the application."
    application_delete_confirm = "Deleted the application."
    page_create_confirm = "Page created successfully."
    page_change_confirm = "Page changed successfully."
    screenshot_save_confirm = "screenshot saved successfully."
    widget_save_confirm = "Widget saved successfully."
    widget_delete_confirm = "Widget deleted successfully."
    run_pattern_confirm = "Query executed successfully."
    no_data_confirm = "No data is available for configure."
    # copy_share_widget_confirm = "iframe code copied successfully."
    copy_share_widget_confirm = "Link copied successfully."
    run_algo_confirm = "Algorithm run successfully."
    application_default_name = "insights_e2e_default_app_test"
    application_default_name_filter = "insights_e2e_default_app_test1_filter"
    application_default_name_filter_under_app = "insights_e2e_default_app_test_filter_under_app"
    page_default_name = "insights_e2e_default_page_test"
    graph_widget_default_name = "insights_e2e_default_widget_test"
    widget_type = {
      "graph":"Graph",
      "bar": "Bar chart",
      "line": "Line chart",
      "pie": "Pie chart",
      "sankey": "Sankey chart",
      "table": "Table",
      "single": "Single value",
      "input": "Inputs",
      "map": "Map"
    }
    page_widget_auto_save = "Page changed successfully"
    share_app_auto_save = "Saved successfully"
    widget_auto_save = "Widget saved successfully"


    def __init__(self, sb):
        self.sb = sb

    def insight_login(self, login_way="undefined"):
        loginUtil = LoginUtils(self.sb)
        loginUtil.login(ToolsBaseCase.Insights_URL, login_way)

    def insight_login_with_username_and_passwd(self, name="", psd=""):
        loginUtil = LoginUtils(self.sb)
        loginUtil.login_cloud_with_name_and_passwd(ToolsBaseCase.Insights_URL, username=name, passwd=psd)

    def insight_login_cluster_and_add_users(self):
        loginUtil = LoginUtils(self.sb)
        loginUtil.login_cloud_cluster_and_add_users()

    def insight_logout(self):
        loginUtil = LoginUtils(self.sb)
        loginUtil.logout_cloud()

    def create_default_application_with_graph(self, app_name=application_default_name,
     page_name=page_default_name, widget_name=graph_widget_default_name):
        self.insight_login()
        # init env, delete all app
        self.delete_all_old_apps()

        self.create_application(app_name)

        #create page under application
        self.create_page(page_name)

        #add widget
        self.add_widget_with_type(self.widget_type["graph"], widget_name)

        #query graph data
        self.add_schema_with_ID("2")
        self.add_vertex_limit(10)
        self.add_schema_with_ID("2")
        self.run_pattern()
        self.sb.attach_allure_screenshot("run pattern result")
        self.sb.assert_text(self.run_pattern_confirm, timeout=assertTextTimeout)
        # self.sb.assertTrue((self.run_pattern_confirm in self.getText_if_element_present(
        #     InsightLocators.confirm_mess)) or (self.no_data_confirm in self.getText_if_element_present(
        #     InsightLocators.confirm_mess)))

        #apply and save
        self.apply_save_widget()


    def tg_version_420_check(self):
        loginUtil = LoginUtils(self.sb)
        cloud_env_flag = loginUtil.is_oncloud()
        if cloud_env_flag:
            version_tmp, is_three_version = loginUtil.get_cloud_tg_version()
        else:
            version_tmp, is_three_version = loginUtil.get_on_prem_tg_version()
        LOGGER.info("response version:" + version_tmp + ", is_three_version: " + str(is_three_version))

        if not is_three_version and int(version_tmp) >= 420:
            # need GLE and engin PR to run in tg_4.2.0
            LOGGER.info("db version match >= tg_4.2.0, continue")
        else:
            pytest.skip("Skip this test case because DB version should >= 4.2.0")


    def generate_access_token_in_default_app(self, app_name=application_default_name):
        self.search_application_name(app_name)
        self.click_application_configuration_with_name_and_click_share(app_name, operate="setting")
        self.edit_application_switch_tab_with_name()
        self.generate_new_token()

    def create_widget_auto_save(self, app_name=application_default_name,
     page_name=page_default_name, widget_name=graph_widget_default_name):
        # if exist, delete the application
        self.delete_existed_app(app_name=app_name)

        self.search_application_name("")
        self.click_create_application()
        self.update_application_name(app_name)
        self.click_submit()
        self.sb.attach_allure_screenshot("create application")
        # asert the application created and the user guide will display in created page
        self.sb.assert_element_present(InsightLocators.guide_img, By.XPATH, timeout=assertTextTimeout)
        # self.sb.assertIn(self.application_create_confirm, self.getText_if_element_present(InsightLocators.confirm_mess))
        self.sb.assert_text(app_name, timeout=click_time_out)

        #create page under application
        self.click_create_page()
        self.update_application_name(page_name)
        self.click_submit()
        self.assert_text_exist(page_name)
        self.sb.attach_allure_screenshot("create page under application")

        #add widget
        self.add_widget()
        self.create_widget_type(InsightLocators.graph_widget)
        self.update_widget_name(widget_name)
        self.sb.attach_allure_screenshot("add {0} widget".format(widget_name))

        #query graph data
        self.add_schema_with_ID("2")
        self.add_vertex_limit(10)
        self.add_schema_with_ID("2")
        self.run_pattern()
        self.sb.attach_allure_screenshot("run pattern result")
        self.sb.assert_text(self.run_pattern_confirm, timeout=assertTextTimeout)
        # self.sb.assertTrue((self.run_pattern_confirm in self.getText_if_element_present(
        #     InsightLocators.confirm_mess)) or (self.no_data_confirm in self.getText_if_element_present(
        #     InsightLocators.confirm_mess)))

        #apply and auto save
        self.apply_widget()
        self.sb.attach_allure_screenshot("save {0} widget".format(widget_name))
        self.sb.assert_text(self.page_widget_auto_save, timeout=assertTextTimeout)
        # self.sb.assertTrue((self.page_widget_auto_save in self.getText_if_element_present(InsightLocators.confirm_mess)))

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=5)
    def delete_all_old_apps(self):
        self.sb.wait_for_element(InsightLocators.search_app, timeout=60)
        self.sb.sleep(3) #improve succsss rate
        grid_list = self.sb.find_elements(InsightLocators.application_config_svg)
        LOGGER.info("all app_list lenth:" + str(len(grid_list)))
        for i in range(len(grid_list)):
            # self.sb.hover_and_click(hover_selector=InsightLocators.application_icon, click_selector=InsightLocators.application_config_svg, timeout=click_time_out)
            self.sb.find_elements(InsightLocators.application_config_svg)[0].click()
            self.wait_and_click(InsightLocators.config_delete)
            self.wait_and_click(InsightLocators.ok_button)
            self.sb.sleep(2)
            LOGGER.info("delete app " + str(i+1))
        self.sb.attach_allure_screenshot("delete all old application result")

    def create_and_share_default_application_with_graph(self, app_name=application_default_name, page_name=page_default_name, widget_name=graph_widget_default_name):

        self.delete_existed_app(app_name=app_name)
        self.search_application_name("")
        self.click_create_application()
        self.update_application_name(app_name)
        self.click_submit()
        self.sb.wait_for_element_not_visible(InsightLocators.cancel_button, timeout=assertTextTimeout)
        self.sb.attach_allure_screenshot("click create application done screenshot")
        # asert the page in the created application and the user guide will display in created page
        self.sb.assert_element_present(InsightLocators.guide_img, By.XPATH, timeout=assertTextTimeout)
        self.sb.assert_text(app_name, timeout=assertTextTimeout)

        #create page under application
        self.click_create_page()
        self.update_application_name(page_name)
        self.click_submit()
        self.assert_text_exist(page_name)
        self.sb.attach_allure_screenshot("create page under application")

        #add widget
        self.add_widget()
        self.create_widget_type(InsightLocators.graph_widget)
        self.update_widget_name(widget_name)
        self.sb.attach_allure_screenshot("add {0} widget".format(widget_name))

        #query graph data
        self.add_schema_with_ID("2")
        self.add_vertex_limit(10)
        self.add_schema_with_ID("2")
        self.run_pattern()
        self.sb.attach_allure_screenshot("run pattern done screenshot")
        # self.sb.assert_text(self.run_pattern_confirm, timeout=assertTextTimeout)

        #apply and save
        self.apply_widget()
        # self.save_application()
        self.sb.attach_allure_screenshot("save {0} widget".format(widget_name))

        #share the app
        self.sb.wait_for_element(InsightLocators.more_button, timeout=60)
        username = "u_globalUDF"
        self.more_page()
        self.wait_and_click(InsightLocators.config_share, timeout=click_time_out)
        self.sb.wait_for_element(InsightLocators.add_user_input, timeout=click_time_out)
        # before add, init env
        self.close_cluster_user()
        self.delete_granted_user(user_name=username)
        self.sb.wait_for_element(InsightLocators.add_user_input, timeout=click_time_out)
        if not self.sb.is_element_present(InsightLocators.contain_span_text.format(username)):
            LOGGER.info("can't find user {0}, ready to type".format(username))
            self.sb.type(InsightLocators.add_user_input, username, timeout=click_time_out)
            if self.sb.is_element_present(InsightLocators.no_result):
                LOGGER.info("can't find user {0}".format(username))
                self.sb.attach_allure_screenshot("can't find user:" + username)
                raise Exception("can't find user:" + username)
            self.sb.sleep(1)
            self.wait_and_click(InsightLocators.find_user)
            self.sb.find_elements(InsightLocators.set_role)[0].click()
            self.sb.sleep(3)
            self.wait_and_click(InsightLocators.user_editor_role)
            self.sb.sleep(2)
            self.wait_and_click(InsightLocators.add_role_button)
            self.sb.sleep(2)
            self.sb.attach_allure_screenshot("add user:" + username)
            if self.sb.is_element_present(InsightLocators.save_role_button):
                self.wait_and_click(InsightLocators.save_role_button)
                self.sb.attach_allure_screenshot("click save user:" + username)
                self.sb.sleep(1)
                self.sb.assert_text(self.share_app_auto_save, timeout=assertTextTimeout)
                LOGGER.info("assert_text done: Saved successfully")
            else:
                self.wait_and_click(InsightLocators.done_button)
        else:
            self.wait_and_click(InsightLocators.done_button)
        self.sb.attach_allure_screenshot("save user:" + username)

        # login with u_globalUDF
        self.insight_login(ToolsBaseCase.G_Role_Login)
        self.open_other_application_panel()
        self.search_application_name(app_name)
        grid_list = self.sb.find_elements(InsightLocators.application_grid)
        LOGGER.info("grid_list lenth:" + str(len(grid_list)))
        if len(grid_list) == 0:
            self.sb.attach_allure_screenshot("not found app:" + app_name)
            raise Exception("not found the app")
        self.open_first_application()
        self.sb.switch_to_newest_window()
        self.sb.attach_allure_screenshot("open other application with editor permission")
        self.assert_shared_app_permission(widget_name, permission="Editor")


    def import_appliction_from_home_page(self, app_name=""):
        self.delete_existed_app(app_name=app_name)
        self.search_application_name("")
        self.wait_and_click(InsightLocators.common_button.format("Import"))
        self.sb.attach_allure_screenshot("click import application")
        self.import_380_application(file_name=app_name)
        self.sb.attach_allure_screenshot("import application successfully screenshot")
        self.search_application_name(app_name)
        self.open_first_application()
        self.sb.wait_for_ready_state_complete()

        # assert the widget
        self.sb.assert_text("input variable", timeout=assertTextTimeout)
        self.sb.assert_text("list map variable", timeout=assertTextTimeout)
        self.sb.assert_text("widget action", timeout=assertTextTimeout)
        self.sb.assert_text("json input", timeout=assertTextTimeout)

        # assert the content
        self.wait_and_click(InsightLocators.common_div_button.format("input variable"))
        self.sb.wait_for_ready_state_complete()
        self.wait_widget_finish_render()
        if self.sb.is_text_visible("GSQL Error"):
            LOGGER.info("input variable page has errors, GSQL Error")
            raise Exception("input variable page has errors, GSQL Error")
        if self.sb.is_text_visible("undefined"):
            LOGGER.info("input variable page has errors, undefined")
            raise Exception("input variable page has errors,undefined")
        if not self.sb.is_text_visible("label1"):
            LOGGER.info("input variable page has errors, no label1")
            raise Exception("input variable page has errors, no label1")
        self.sb.attach_allure_screenshot("assert input variable page successfully screenshot")

        # assert the map and list
        loginUtil = LoginUtils(self.sb)
        cloud_env_flag = loginUtil.is_oncloud()
        if cloud_env_flag:
            version_tmp, is_three_version = loginUtil.get_cloud_tg_version()
        else:
            version_tmp, is_three_version = loginUtil.get_on_prem_tg_version()
        LOGGER.info("response version:" + version_tmp + ", is_three_version: " + str(is_three_version))

        if not is_three_version and int(version_tmp) >= 420:
            LOGGER.info("db version match >= tg_4.2.0, check the list and map page")
            self.wait_and_click(InsightLocators.common_div_button.format("list map variable"))
            self.sb.wait_for_ready_state_complete()
            self.wait_widget_finish_render()
            if self.sb.is_text_visible("GSQL Error") or self.sb.is_text_visible("undefined"):
                LOGGER.info("list map variable page has errors, GSQL Error or undefined")
                raise Exception("input variable page has errors, GSQL Error or undefined")
            self.sb.attach_allure_screenshot("assert list map variable page successfully screenshot")
        else:
            LOGGER.info("Skip list map variable page check because DB version should >= 4.2.0")

        # assert the widget action
        self.wait_and_click(InsightLocators.common_div_button.format("widget action"))
        self.sb.wait_for_ready_state_complete()
        self.wait_widget_finish_render()
        if self.sb.is_text_visible("GSQL Error"):
            LOGGER.info("widget action page has errors, GSQL Error")
            raise Exception("input variable page has errors, GSQL Error")
        if self.sb.is_text_visible("undefined"):
            LOGGER.info("widget action page has errors, undefined")
            raise Exception("input variable page has errors, undefined")
        self.sb.attach_allure_screenshot("assert widget action page successfully screenshot")

        # assert the json input
        self.wait_and_click(InsightLocators.common_div_button.format("json input"))
        self.sb.wait_for_ready_state_complete()
        self.wait_widget_finish_render()
        if self.sb.is_text_visible("GSQL Error"):
            LOGGER.info("json input page has errors, GSQL Error")
            raise Exception("input variable page has errors, GSQL Error")
        if self.sb.is_text_visible("undefined"):
            LOGGER.info("json input page has errors, undefined")
            raise Exception("input variable page has errors, undefined")
        self.sb.attach_allure_screenshot("assert json input page successfully screenshot")

    def import_380_appliction(self, app_name=application_default_name):
        self.delete_existed_app(app_name=app_name)
        self.search_application_name("")
        self.click_create_application()
        self.update_application_name(app_name)
        self.click_submit()
        self.sb.attach_allure_screenshot("create application")
        # asert the application created and the user guide will display in created page
        self.sb.assert_element_present(InsightLocators.guide_img, By.XPATH, timeout=assertTextTimeout)
        self.sb.assert_text(app_name, timeout=assertTextTimeout)

        #import V380 application
        self.more_page()
        self.import_380_application(file_name=app_name)
        self.sb.attach_allure_screenshot("import application.json successfully screenshot")
        # assert the widget
        self.sb.assert_text("Page00", timeout=assertTextTimeout)
        self.sb.assert_text("Page01", timeout=assertTextTimeout)
        self.sb.assert_text("Page03", timeout=assertTextTimeout)

        # edit an widget
        self.click_page_with_name("Page00")
        self.click_widget_configuration_with_name(name='SankeyWidget')
        self.wait_and_click(InsightLocators.config_edit)
        self.update_widget_name("SankeyWidget_new")
        # apply and auto save
        self.apply_widget()
        self.sb.attach_allure_screenshot("save {0} widget".format("SankeyWidget"))
        self.sb.assert_text(self.widget_auto_save, timeout=assertTextTimeout)
        # self.sb.assertTrue((self.widget_auto_save in self.getText_if_element_present(InsightLocators.confirm_mess)))

        # delete widget
        self.delete_widget("LineWidget")
        self.sb.attach_allure_screenshot("delete {0} widget".format("LineWidget"))
        self.sb.assert_text(self.page_widget_auto_save, timeout=assertTextTimeout)
        # self.sb.assertTrue((self.page_widget_auto_save in self.getText_if_element_present(InsightLocators.confirm_mess)))

        # add widget
        self.add_widget()
        self.create_widget_type(InsightLocators.graph_widget)
        self.update_widget_name("new_widget")
        self.sb.attach_allure_screenshot("add {0} widget".format("new_widget"))
        # apply and auto save
        self.apply_widget()
        self.sb.attach_allure_screenshot("save {0} widget".format("new_widget"))
        self.sb.assert_text(self.page_widget_auto_save, timeout=assertTextTimeout)
        # self.sb.assertTrue((self.page_widget_auto_save in self.getText_if_element_present(InsightLocators.confirm_mess)))

        # delete application
        self.click_home_button_on_page()
        # try to delete the imported application
        self.delete_existed_app(app_name=app_name)

        # double check the application is deleted
        self.search_application_name("")
        self.search_application_name(app_name)
        LOGGER.info("app_name:" + app_name)
        grid_list = self.sb.find_elements(InsightLocators.application_grid)
        LOGGER.info("delete app, grid_list lenth:" + str(len(grid_list)))
        self.sb.assert_equal(len(grid_list), 0)

    def table_widget_conditional_styling(self, app_name=application_default_name):

        self.delete_existed_app(app_name=app_name)
        self.click_create_application()
        self.update_application_name(app_name)
        self.click_submit()
        self.sb.attach_allure_screenshot("create application")
        # asert the application created and the user guide will display in created page
        self.sb.assert_element_present(InsightLocators.guide_img, By.XPATH, timeout=assertTextTimeout)
        self.sb.assert_text(app_name, timeout=assertTextTimeout)

        # add widget
        self.create_page(app_name)
        self.add_widget_with_type("Table", "table_widget")
        self.sb.attach_allure_screenshot("add {0} widget".format("table_widget"))

        # run query
        self.add_schema_with_ID("2")
        self.add_vertex_limit(10)
        self.run_pattern()
        self.sb.attach_allure_screenshot("run pattern result")
        self.sb.assert_text(self.run_pattern_confirm, timeout=assertTextTimeout)
        # self.sb.assertTrue((self.run_pattern_confirm in self.getText_if_element_present(
        #     InsightLocators.confirm_mess)) or (self.no_data_confirm in self.getText_if_element_present(
        #     InsightLocators.confirm_mess)))

        # add conditional styling
        self.sb.click_visible_elements(InsightLocators.add_conditional_styling_button, timeout=click_time_out)
        self.sb.click_visible_elements(InsightLocators.city_condition_input, timeout=click_time_out)
        if self.sb.is_element_present(InsightLocators.city_condition_no_result):
            self.sb.attach_allure_screenshot("condition no results screenshot")
            raise Exception("condition no results")

        city_name = self.sb.find_elements(InsightLocators.get_city_name)[1].text
        LOGGER.info("city_name: " + city_name)
        self.sb.find_elements(InsightLocators.city_condition_value_input)[1].clear()
        self.sb.find_elements(InsightLocators.city_condition_value_input)[1].send_keys(city_name)
        self.sb.click_visible_elements(InsightLocators.city_condition_OK_button, timeout=click_time_out)
        self.sb.attach_allure_screenshot("input condition results screenshot")
        self.apply_widget()


    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=32)
    def delete_existed_app(self, app_name=""):
        self.sb.wait_for_element_clickable(InsightLocators.search_app, timeout=60)
        self.sb.sleep(3)  # wait for input element
        try:
            # if exist, delete the application
            self.search_application_name(app_name)
            LOGGER.info("app_name:" + app_name)
            grid_list = self.sb.find_elements(InsightLocators.application_grid)
            LOGGER.info("grid_list lenth:" + str(len(grid_list)))
            for i in range(len(grid_list)):
                if self.sb.is_element_present(InsightLocators.application_grid):
                    self.click_application_configuration_with_name_and_click_share(app_name, operate="delete")
                    self.wait_and_click(InsightLocators.ok_button)
                    self.sb.attach_allure_screenshot("delete existed application result screenshot")
                    self.sb.wait_for_element_not_visible(InsightLocators.cancel_button, timeout=wait_render_timeout)
                else:
                    LOGGER.info(app_name + " not existed")
        except Exception as e:
            self.sb.attach_allure_screenshot("delete existed application exception screenshot")
            LOGGER.info("delete_existed_app exception: "+str(e))
        finally:
            self.search_application_name("")
            self.sb.type(InsightLocators.search_app, "")

    def init_access_token_env(self):
        self.sb.sleep(3)
        if self.sb.is_element_present(InsightLocators.generate_new_token_delete_button):
            self.wait_and_click(InsightLocators.generate_new_token_delete_button, timeout=click_time_out)
            self.sb.wait_for_element_clickable(InsightLocators.generate_new_token_button, timeout=click_time_out)
        self.sb.sleep(1)

    def close_guide_panel(self):
        self.sb.sleep(2)
        if self.sb.is_element_present(InsightLocators.close_button):
            self.wait_and_click(InsightLocators.close_button)
        self.sb.sleep(1)

    def close_app_panel(self):
        self.sb.attach_allure_screenshot("before close_app_panel screenshot")
        if self.sb.is_element_present(InsightLocators.close_app_panel_button):
            self.wait_and_click(InsightLocators.close_app_panel_button)
        self.sb.sleep(1)

    def click_create_application(self):
        self.close_guide_panel()
        self.wait_and_click(InsightLocators.addapp_button)

    def click_submit(self):
        self.wait_and_click(InsightLocators.submit_button)
        self.sb.attach_allure_screenshot("click submit button screenshot")
        self.wait_app_panel_circle_progress_bar()
        self.sb.wait_for_element_not_visible(InsightLocators.cancel_button, timeout=click_time_out)
        LOGGER.info("wait_for_element_not_visible: cancel_button")
        self.close_app_panel()

    def getText_if_element_present(self, selector):
        if self.sb.assert_element_present(selector, timeout=60):
            res = self.sb.get_text_content(selector, timeout=30)
            LOGGER.info(f"return result: {res}")
            return res
        else:
            self.sb.attach_allure_screenshot("wait pop window failed screenshot")
            LOGGER.info("Not get pop window content, since don't see pop window until 60s")
            return ""

    def update_application_name(self, app_name):
        self.sb.sleep(3)
        self.close_guide_panel()
        self.sb.type(InsightLocators.app_name, app_name,  timeout=5)
        self.sb.sleep(3)

    #select the graph name as provide name, and will use default one if not exists
    def select_graph_name(self, graph_name):
        try:
            locator_graph="//li[@role='option']/div[text()='" + graph_name + "']"
            self.wait_and_click(InsightLocators.select_default_graph)
            self.sb.sleep(2)
            if(self.sb.is_element_present(locator_graph)):
                self.wait_and_click(locator_graph)
            #close the graph dropdown anyway
            self.wait_and_click(InsightLocators.app_name)
            self.sb.sleep(1)
        except Exception as e:
            LOGGER.info("select_graph_name exception: " + str(e))
            self.sb.attach_allure_screenshot("select_graph_name exception")

    def search_user_name(self, user_name):
        return "//span[text()='" +user_name+ "']"

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=32)
    def search_application_name(self, app_name):
        self.close_guide_panel()
        self.sb.wait_for_element_clickable(InsightLocators.search_app, timeout=wait_render_timeout)
        self.sb.type(InsightLocators.search_app, app_name)

    def click_application_configuration_with_name(self, name):
        self.sb.wait_for_element_clickable(InsightLocators.search_app, timeout=wait_render_timeout)
        self.sb.hover_and_click(hover_selector="//span[text()='"+ name +"']", click_selector="//span[text()='"+ name +"']/../div", timeout=click_time_out)
        # self.sb.wait_for_element_clickable("//span[text()='"+ name +"']/../div", timeout=wait_render_timeout)
        # self.wait_and_click("//span[text()='"+ name +"']/../div", timeout=click_time_out)

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=5)
    def click_application_configuration_with_name_and_click_share(self, name, operate="share"):
        LOGGER.info("prepare to click config button")
        self.sb.wait_for_element_clickable(InsightLocators.search_app, timeout=wait_render_timeout)
        self.sb.hover_and_click(hover_selector="//span[text()='"+ name +"']", click_selector="//span[text()='"+ name +"']/../div/button", timeout=click_time_out)
        # self.sb.wait_for_element_clickable("//span[text()='"+ name +"']/../div", timeout=wait_render_timeout)
        if not self.sb.is_element_visible(InsightLocators.config_settings):
            LOGGER.info("setting is not visible, so click the configuration button again")
            self.wait_and_click("//span[text()='" + name + "']/../div/button")
        if operate == "share":
            self.wait_and_click(InsightLocators.config_share)
        elif operate == "setting":
            self.wait_and_click(InsightLocators.config_settings)
        elif operate == "duplicate":
            self.wait_and_click(InsightLocators.config_duplicate)
        elif operate == "delete":
            self.wait_and_click(InsightLocators.config_delete)
        self.sb.attach_allure_screenshot("click_application_configuration_with_name_and_click_share done")

    def click_page_with_name(self, name):
        self.close_guide_panel()
        self.wait_and_click("//div[text()='"+ name +"']", timeout=click_time_out)
        self.sb.sleep(2)

    def click_filter_with_widgetname(self, name):
        self.close_guide_panel()
        self.wait_and_click("//div[text()='"+ name +"']" + InsightLocators.filter_button_prefix, timeout=click_time_out)
        self.sb.sleep(2)

    def click_with_text_name(self, name):
        self.sb.sleep(1)
        locator_text = "//div[text()='"+ name +"']"
        if(self.sb.assert_element_present(locator_text, timeout=assertTextTimeout)):
            self.wait_and_click(locator_text)
        self.sb.sleep(1)

    def open_other_application_panel(self):
        try:
            self.close_guide_panel()
            # new ui in cloud branch
            # All
            # My Applications
            # Applications Shared With Me
            self.sb.click_nth_visible_element(InsightLocators.shared_app_with_me_button, number=3, timeout=click_time_out)
            # self.wait_and_click(InsightLocators.other_application_button)   # css has changed
            self.sb.sleep(2)
        except Exception as e:
            self.sb.attach_allure_screenshot("open_other_application_panel failed screenshot")
            LOGGER.info("open_other_application_panel failed:" + str(e))


    def open_shared_application_tab(self):
        self.close_guide_panel()
        self.wait_and_click(InsightLocators.common_button_text.format("Applications Shared With Me"))
        self.sb.sleep(2)

    def open_first_application(self):
        if self.sb.assert_element_visible(InsightLocators.first_application_area, timeout=click_time_out):
            self.sb.find_elements(InsightLocators.first_application_area)[0].click()
            self.sb.sleep(2)
        else:
            LOGGER.info(f"no application create by other, locator: {InsightLocators.first_application_area}")

    def open_application_with_name(self, name):
        self.wait_and_click("//span[text()='"+ name +"']/../../a")
        self.sb.sleep(2)

    # General/Security
    def edit_application_switch_tab_with_name(self, name="Security"):
        self.sb.wait_for_element_clickable('//button[text()="'+name+'"]', timeout=click_time_out)
        self.wait_and_click('//button[text()="'+name+'"]', timeout=click_time_out)
        self.wait_circle_progress_bar(wait_present_time=short_wait_time_out)

    def generate_new_token(self, db_user_name="u_localadmin", db_psd="localadmin"):
        self.init_access_token_env()
        self.sb.wait_for_element_clickable(InsightLocators.generate_new_token_button, timeout=click_time_out)
        self.wait_and_click(InsightLocators.generate_new_token_button, timeout=click_time_out)
        self.sb.attach_allure_screenshot("init env screenshot")
        # add username and psd
        self.sb.type(InsightLocators.generate_new_token_input_username_button, db_user_name, timeout=click_time_out)
        self.sb.type(InsightLocators.generate_new_token_input_psd_button, db_psd, timeout=click_time_out)
        self.sb.wait_for_element_clickable(InsightLocators.generate_new_token_submit_button, timeout=click_time_out)
        self.wait_and_click(InsightLocators.generate_new_token_submit_button, timeout=click_time_out)
        self.sb.attach_allure_screenshot("add username and psd")
        # assert result
        self.sb.assert_element_present(InsightLocators.generate_new_token_delete_button, timeout=click_time_out)
        self.sb.attach_allure_screenshot("generate access token successfully")
        self.close_app_panel()


    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=5)
    def click_create_page(self):
        self.close_guide_panel()
        self.wait_and_click(InsightLocators.create_page, timeout=click_time_out)

    def assert_text_exist_span(self, name, timeout=assertTextTimeout):
        self.sb.sleep(1)
        self.sb.assert_element_present("//span[text()='"+ name +"']", timeout=timeout)

    def assert_button_exist(self, name, timeout=assertTextTimeout):
        self.sb.sleep(1)
        self.sb.assert_element_present("//button[text()='"+ name +"']", timeout=timeout)

    def assert_text_exist(self, name, timeout=assertTextTimeout):
        self.sb.sleep(1)
        self.sb.assert_element_present("//div[text()='"+ name +"']", timeout=timeout)

    def assert_text_not_exist(self, name):
        self.sb.sleep(1)
        self.sb.assert_element_not_present("//div[text()='"+ name +"']", timeout=assertTextTimeout)

    def save_application(self):
        self.close_guide_panel()
        self.wait_and_click(InsightLocators.save_button, timeout=click_time_out)
        self.sb.sleep(1)

    def more_page(self):
        self.close_guide_panel()
        self.wait_and_click(InsightLocators.more_button, timeout=click_time_out)
        self.sb.sleep(1)

    def click_home_button_on_page(self):
        self.close_guide_panel()
        self.sb.wait_for_element_clickable(InsightLocators.home_button, timeout=click_time_out)
        self.wait_and_click(InsightLocators.home_button, timeout=click_time_out)
        self.sb.sleep(1)

    def import_380_application(self, file_name=""):
        if file_name == "":
            raise Exception("app name is null")
        # upload local files
        LOGGER.info("file_name: " + file_name)
        filePath = "./data/insights/"
        LOGGER.info(listdir(filePath))
        for f in listdir(filePath):
            LOGGER.info(f)
            if file_name in f:
                LOGGER.info("begin to upload file")
                self.sb.choose_file('input[type="file"]', filePath + f)
                self.wait_app_panel_circle_progress_bar(wait_present_time=click_time_out)
                # LOGGER.info(self.getText_if_element_present(InsightLocators.confirm_mess))
                # self.sb.assert_text(file_name, timeout=assertTextTimeout)
                # self.sb.assertTrue(("Page changed successfully" in self.getText_if_element_present(InsightLocators.confirm_mess)))
            else:
                LOGGER.info("not the right file, didn't upload, continue")

    def add_widget(self):
        self.close_guide_panel()
        self.wait_and_click(InsightLocators.create_widget, timeout=click_time_out)


    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=5)
    def create_widget_type(self, type):
        self.wait_and_click(type)

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=5)
    def create_widget_type_with_name(self, name):
        self.wait_and_click(InsightLocators.type_widget.format(name))
        self.sb.sleep(1)

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=5)
    def add_show_schema(self):
        self.select_graph()
        self.wait_and_click(InsightLocators.pattern_input)
        self.sb.sleep(1)
        self.wait_and_click(InsightLocators.show_schema)

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=5)
    def select_query_by_name(self, query_name=""):
        if query_name == "":
            raise Exception("query name is null")
        self.select_graph()
        self.wait_and_click(InsightLocators.pattern_input)
        # select query
        self.wait_and_click(InsightLocators.select_query.format(query_name))

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=5)
    def select_query_by_name_in_explore_graph(self, query_name=""):
        LOGGER.info("query_name:" + query_name)
        if query_name == "":
            raise Exception("query name is null")
        self.wait_and_click(InsightLocators.pattern_input)
        if self.sb.is_text_visible("in cluster mode") and "interpreted" in query_name:
            pytest.skip("cluster nodes didn't support interpreted query, skip")
        # select query
        self.wait_and_click(InsightLocators.select_query.format(query_name))

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=5)
    def add_query_parameter(self, query_name=""):
        if query_name == "":
            raise Exception("query name is null")
        if query_name == "run_interpreted_query_with_list":
            self.wait_and_click(InsightLocators.select_query_with_list_or_map.format("LIST"))
            self.sb.type(InsightLocators.select_query_int_input, 5678)
        elif query_name == "run_installed_query_with_list":
            self.wait_and_click(InsightLocators.select_query_with_list_or_map.format("LIST"))
            self.sb.type(InsightLocators.select_query_string_input, 5678)
        elif query_name == "run_installed_query_with_map":
            self.wait_and_click(InsightLocators.select_query_with_list_or_map.format("MAP"))
            self.sb.type(InsightLocators.select_query_string_input, "str")
            self.sb.type(InsightLocators.select_query_double_input, 5678)
        self.sb.attach_allure_screenshot("add_query_parameter done screenshot")
        self.wait_and_click(InsightLocators.ok_button)

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=5)
    def select_graph(self, graph="MyGraph"):
        try:
            self.close_guide_panel()
            elements = self.sb.find_elements("//div[text()='" + graph + "']")
            if len(elements) == 1:
                LOGGER.info("right graph, return")
                return
            else:
                LOGGER.info("wrong graph, begin to select graph")
                self.sb.wait_for_element_clickable(InsightLocators.select_graph, timeout=click_time_out)
                self.wait_and_click(InsightLocators.select_graph)
                self.sb.sleep(2)
                self.wait_and_click("//div[text()='" + graph + "']")
                self.sb.sleep(1)
        except Exception as e:
            self.sb.refresh()
            raise Exception(e)

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=5)
    def add_schema_with_ID(self, value):
        try:
            self.close_guide_panel()
            self.select_graph()
            self.sb.wait_for_element_clickable(InsightLocators.pattern_input, timeout=click_time_out)
            self.wait_and_click(InsightLocators.pattern_input)
            self.sb.sleep(2)
            self.wait_and_click("//ul[@aria-label='Menu']/li[" + value + "]")
            self.sb.sleep(1)
        except Exception as e:
            self.sb.refresh()
            raise Exception(e)

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=5)
    def add_schema_with_ID_in_explore_graph(self, value):
        try:
            self.wait_and_click(InsightLocators.pattern_input)
            self.wait_and_click("//ul[@aria-label='Menu']/li[" + value + "]")
        except Exception as e:
            self.sb.refresh()
            raise Exception(e)

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=5)
    def add_conditional_styling(self, value):
        try:
            self.close_guide_panel()
            self.sb.wait_for_element_clickable(InsightLocators.pattern_input, timeout=click_time_out)
            self.wait_and_click(InsightLocators.pattern_input)
            self.sb.sleep(2)
            self.wait_and_click("//ul[@aria-label='Menu']/li[" + value + "]")
            self.sb.sleep(1)
        except Exception as e:
            self.sb.refresh()
            raise Exception(e)

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=5)
    def add_schema_with_Name(self, value):
        self.close_guide_panel()
        self.select_graph()
        self.wait_and_click(InsightLocators.pattern_input)
        self.click_with_text_name(value)

    def apply_widget(self):
        self.wait_and_click(InsightLocators.apply_widget)
        self.sb.sleep(1)

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=5)
    def update_widget_name(self, widget_name):
        self.close_guide_panel()
        self.sb.type(InsightLocators.widget_name_input, widget_name)
        self.sb.sleep(2)

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=5)
    def click_widget_configuration_with_name(self, name):
        self.sb.hover_and_click("//div[text()='"+ name +"']", "//div[text()='"+ name +"']/../div[2]/div[1]", timeout=click_time_out)
        # self.sb.wait_for_element_clickable("//div[text()='"+ name +"']/../div[2]/div[1]", timeout=click_time_out)
        # self.wait_and_click("//div[text()='"+ name +"']/../div[2]/div[1]", timeout=click_time_out)
        self.sb.sleep(1)

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=5)
    def add_vertex_limit(self, num):
        self.wait_and_click(InsightLocators.add_filter)
        self.sb.sleep(1)
        self.wait_and_click(InsightLocators.vertex_limit)
        self.sb.sleep(1)
        self.sb.type(InsightLocators.int_input, num)
        self.sb.sleep(1)

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=5)
    def run_pattern(self):
        self.wait_and_click(InsightLocators.pattern_run_button)
        self.run_pattern_proceed()

    def check_disable_path_finding(self):
        # check the label
        if not self.sb.is_text_visible("Disable PathFinding"):
            self.sb.attach_allure_screenshot("didn't find Disable PathFinding label")
            raise Exception("didn't find Disable PathFinding label")
        else:
            LOGGER.info("Disable PathFinding label is visible")

        # enable the config
        self.sb.find_elements(InsightLocators.disable_path_finding_labels)[1].click()
        # check disable works
        self.click_center_position_and_right_click()
        if self.sb.is_element_visible(InsightLocators.path_finding_function):
            self.sb.attach_allure_screenshot("Disable PathFinding not work screenshot")
            raise Exception("Disable PathFinding not work")
        else:
            self.sb.attach_allure_screenshot("Disable PathFinding works")

    def click_center_position_and_right_click(self):
        try:
            width = self.sb.driver.get_window_size()['width']
            height = self.sb.driver.get_window_size()['height']

            center_x = width // 3
            center_y = height // 2
            actions = ActionChains(self.sb.driver)
            actions.move_by_offset(center_x, center_y).context_click().perform()
            self.sb.attach_allure_screenshot("click center position and right click successfully screenshot")
        except Exception as e:
            LOGGER.info("click_center_position exception:" + str(e))
            self.sb.attach_allure_screenshot("click_center_position_and_right_click exception screenshot")

    def run_pattern_proceed(self):
        self.sb.sleep(1)
        if self.sb.is_element_present(InsightLocators.proceed_button):
            self.wait_and_click(InsightLocators.proceed_button)

    def open_graph_analytics_panel(self):
        self.wait_and_click(InsightLocators.open_algorithm_panel)
        self.sb.sleep(1)

    def select_algo_query(self, algo_name):
        self.wait_and_click(InsightLocators.open_algo_dropdown)
        self.sb.sleep(1)
        if algo_name == "PageRank":
            self.wait_and_click(InsightLocators.pagerank_query)
        elif algo_name == "Louvain":
            self.wait_and_click(InsightLocators.louvain_query)
        self.sb.sleep(1)

    def run_algorithm(self):
        self.sb.sleep(1)
        self.wait_and_click(InsightLocators.run_algo_button)
        self.sb.sleep(1)

    def check_app_existed(self, app_name):
        self.search_application(app_name)
        grid_list = self.sb.find_elements(InsightLocators.application_config_svg)
        LOGGER.info("search app_list lenth:" + str(len(grid_list)))
        self.search_application_name("")
        self.sb.type(InsightLocators.search_app, "")
        self.sb.attach_allure_screenshot("restore app page")
        if len(grid_list) > 0:
            return True
        else:
            return False


    def close_filter(self):
        self.sb.sleep(1)
        self.wait_and_click(InsightLocators.close_filter)
        self.sb.sleep(1)

    def assert_widget_result(self, res):
        # self.sb.assert_text(res, timeout=assertTextTimeout)
        self.sb.assertTrue((res in self.getText_if_element_present(
            InsightLocators.confirm_mess)) or (self.page_change_confirm in self.getText_if_element_present(
            InsightLocators.confirm_mess)) or (self.screenshot_save_confirm in self.getText_if_element_present(
            InsightLocators.confirm_mess)))

    def assert_page_create(self):
        self.close_guide_panel()
        self.sb.assertIn(self.page_create_confirm, self.getText_if_element_present(
            InsightLocators.confirm_mess))

    def apply_save_widget(self):
        self.apply_widget()
        self.sb.attach_allure_screenshot("save widget")
        self.assert_widget_result(self.widget_save_confirm)


    def print_page_as_PDF(self):
        # click the page
        self.sb.hover_and_click(hover_selector=InsightLocators.page_list, click_selector=InsightLocators.page_list_button, timeout=click_time_out)
        # if click print will open system windows, can't close it and stuck
        # self.wait_and_click(InsightLocators.common_span_button.format("Print"))
        self.sb.assert_text("Print", timeout=assertTextTimeout)
        self.sb.attach_allure_screenshot("after click print")
        # hard to assert the print pop window, since it's a individual windows to print, can't find by css
        self.sb.attach_allure_screenshot("finally print_page")


    def add_table_conditional_styling(self):
        self.sb.assert_text("Conditional Styling", timeout=assertTextTimeout)
        self.sb.attach_allure_screenshot("Conditional Styling assert successfully")
        self.wait_and_click(InsightLocators.add_conditional_styling_button)
        # add equal condition
        self.sb.find_elements(InsightLocators.conditional_styling_input)[1].send_keys("Seongnam-si")
        self.wait_and_click(InsightLocators.common_button.format("OK"))
        self.sb.attach_allure_screenshot("equal condition added successfully")

        # add always condition
        self.wait_and_click(InsightLocators.add_conditional_styling_button)
        # click the second dropdown to check method
        self.wait_and_click(InsightLocators.conditional_styling_condition)
        self.sb.assert_text(">=", timeout=assertTextTimeout)
        self.sb.assert_text("<", timeout=assertTextTimeout)
        self.sb.assert_text("unique value", timeout=assertTextTimeout)
        self.sb.assert_text("always", timeout=assertTextTimeout)
        # click always
        self.wait_and_click(InsightLocators.common_div_button.format("always"))
        self.wait_and_click(InsightLocators.common_button.format("OK"))
        self.sb.attach_allure_screenshot("always condition add successfully")

        # delete all conditions
        elements = self.sb.find_elements(InsightLocators.added_conditions)
        LOGGER.info("find added conditions: " + str(len(elements)))
        if len(elements) != 2:
            raise Exception("didn't add 2 conditions Exception")
        else:
            for i in range(len(elements)):
                LOGGER.info("delete condition " + str(i))
                self.sb.hover_and_click(hover_selector=InsightLocators.added_conditions, click_selector=InsightLocators.delete_conditions, timeout=click_time_out)
                self.wait_and_click(InsightLocators.common_button.format("OK"))
                self.sb.attach_allure_screenshot("delete condition successfully")


    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=5)
    def run_query_with_list_map_tg_version_check(self):
        loginUtil = LoginUtils(self.sb)
        cloud_env_flag = loginUtil.is_oncloud()
        if cloud_env_flag:
            version_tmp, is_three_version = loginUtil.get_cloud_tg_version()
        else:
            version_tmp, is_three_version = loginUtil.get_on_prem_tg_version()
        LOGGER.info("response version:" + version_tmp + ", is_three_version: " + str(is_three_version))

        if not is_three_version and int(version_tmp) >= 420:
            # need GLE and engin PR to run in tg_4.2.0
            LOGGER.info("db version match >= tg_4.2.0, continue")
        else:
            pytest.skip("Skip this test case because DB version should >= 4.2.0")



    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=5)
    def create_application(self, name):
        try:
            self.delete_existed_app(app_name=name)
            self.click_create_application()
            self.update_application_name(name)
            self.click_submit()
            self.sb.wait_for_element_not_visible(InsightLocators.cancel_button, timeout=assertTextTimeout)
            self.sb.attach_allure_screenshot("create application screenshot")
            # asert the application created and the user guide will display in created page
            self.wait_circle_progress_bar(wait_present_time=short_wait_time_out)
            self.sb.assert_text(name, timeout=assertTextTimeout)
            self.sb.assert_element_present(InsightLocators.guide_img, By.XPATH, timeout=assertTextTimeout)
        except Exception as e:
            LOGGER.info(str(e))
            if self.sb.is_element_present(InsightLocators.homepage):
                self.wait_and_click(InsightLocators.homepage)
            raise Exception(e)


    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=5)
    def create_page(self, name):
        self.click_create_page()
        self.update_application_name(name)
        self.click_submit()
        self.sb.attach_allure_screenshot("create page")
        self.assert_text_exist(name)

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=5)
    def delete_application(self, name):
        try:
            self.search_application(name)
            self.click_application_configuration_with_name_and_click_share(name, operate="delete")
            self.wait_and_click(InsightLocators.ok_button)
            self.sb.assertIn(self.application_delete_confirm, self.getText_if_element_present(
                InsightLocators.confirm_mess))
            self.sb.attach_allure_screenshot("delete application result")
        except Exception as e:
            LOGGER.info("delete application exception:" + str(e))
            self.sb.attach_allure_screenshot("delete application exception screenshot")
        finally:
            self.search_application_name("")
            self.sb.type(InsightLocators.search_app, "")

    def search_application(self, name):
        try:
            self.search_application_name(name)
            self.sb.assert_element_present(InsightLocators.app_number)
            self.sb.attach_allure_screenshot("search application result")
            self.sb.sleep(2)
        except Exception as e:
            self.sb.attach_allure_screenshot("search_application exception screenshot")
            LOGGER.info(str(e))

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=5)
    def duplicate_application(self, name):
        self.click_application_configuration_with_name_and_click_share(name, operate="duplicate")

        self.sb.assertIn(self.application_duplicate_confirm,
                      self.getText_if_element_present(InsightLocators.confirm_mess))
        self.sb.attach_allure_screenshot("duplicate application result")

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=5)
    def change_application_name(self, current_name, update_name):
        self.click_application_configuration_with_name_and_click_share(current_name, operate="setting")
        self.update_application_name(update_name)
        self.click_submit()
        # self.sb.assertIn(self.application_update_confirm, self.getText_if_element_present(
        #     InsightLocators.confirm_mess))
        self.sb.assert_text(update_name, timeout=assertTextTimeout)
        self.sb.attach_allure_screenshot("update application result")

    def add_widget_with_type(self, type, name):
        self.add_widget()
        self.create_widget_type_with_name(type)
        self.assert_text_exist("Widget Type")
        self.update_widget_name(name)
        self.sb.attach_allure_screenshot("add {0} widget".format(name))


    def add_input_parameter(self, type="", name=""):
        self.close_guide_panel()
        self.sb.find_elements(InsightLocators.add_input_param_button)[1].click()
        self.sb.type(InsightLocators.input_variablename, name)
        self.wait_and_click(InsightLocators.input_variable_type)
        if type == "List":
            self.wait_and_click(InsightLocators.common_div_button.format(type))
            # assert list variable types
            self.sb.find_elements(InsightLocators.common_div_button.format("String"))[0].click()
            self.sb.assert_text("Number", timeout=click_time_out)
            self.sb.assert_text("String", timeout=click_time_out)
            self.sb.assert_text("Datetime", timeout=click_time_out)
            self.sb.assert_text("Vertex", timeout=click_time_out)
            self.sb.assert_text("Bool", timeout=click_time_out)
            self.sb.find_elements(InsightLocators.common_div_button.format("String"))[0].click()
            # add 2 items and input
            self.wait_and_click(InsightLocators.common_button.format("+ Add Item"))
            self.sb.find_elements(InsightLocators.input_string_variable)[0].send_keys("string1")
            self.sb.find_elements(InsightLocators.input_string_variable)[1].send_keys("string2")
            self.sb.attach_allure_screenshot("Add list and 2 items done screenshot")

        elif type == "Map":
            self.wait_and_click(InsightLocators.common_div_button.format(type))
            self.sb.find_elements(InsightLocators.common_div_button.format("String"))[0].click()
            self.sb.assert_text("Number", timeout=click_time_out)
            self.sb.assert_text("String", timeout=click_time_out)
            self.sb.assert_text("Datetime", timeout=click_time_out)
            self.sb.assert_text("Bool", timeout=click_time_out)
            self.sb.find_elements(InsightLocators.common_div_button.format("String"))[0].click()
            # add 2 items and input
            self.wait_and_click(InsightLocators.common_button.format("+ Add Item"))
            self.sb.find_elements(InsightLocators.input_string_variable)[0].send_keys("key1")
            self.sb.find_elements(InsightLocators.input_string_variable)[1].send_keys("value1")
            self.sb.find_elements(InsightLocators.input_string_variable)[2].send_keys("key2")
            self.sb.find_elements(InsightLocators.input_string_variable)[3].send_keys("value2")
            self.sb.attach_allure_screenshot("Add map and 2 items done screenshot")

        elif type == "Bool":
            self.wait_and_click(InsightLocators.common_div_button.format("Dropdown"))
            self.sb.find_elements(InsightLocators.common_div_button.format("String"))[0].click()
            self.sb.find_elements(InsightLocators.common_div_button.format("Bool"))[0].click()
            self.sb.find_elements(InsightLocators.input_bool_variable)[1].click()
            self.sb.find_elements(InsightLocators.common_div_button.format("true"))[0].click()
            self.sb.attach_allure_screenshot("Add bool and select default value true done screenshot")

        self.wait_and_click(InsightLocators.common_button.format("OK"))


    def add_input_with_list_map_bool(self, type, name):
        self.add_input_parameter(type=type, name=name)
        self.sb.attach_allure_screenshot("Click OK button and save input done screenshot")

    def assert_input_with_list_map_bool(self, type):
        if type == "List":
            self.wait_and_click(InsightLocators.assert_variable_value)
            self.sb.assert_element_visible(InsightLocators.assert_input_variable_value.format("string1"), timeout=assertTextTimeout)
            self.sb.assert_element_visible(InsightLocators.assert_input_variable_value.format("string2"), timeout=assertTextTimeout)
        elif type == "Map":
            self.wait_and_click(InsightLocators.assert_variable_value)
            self.sb.assert_element_visible(InsightLocators.assert_input_variable_value.format("key1"), timeout=assertTextTimeout)
            self.sb.assert_element_visible(InsightLocators.assert_input_variable_value.format("value1"), timeout=assertTextTimeout)
            self.sb.assert_element_visible(InsightLocators.assert_input_variable_value.format("key2"), timeout=assertTextTimeout)
            self.sb.assert_element_visible(InsightLocators.assert_input_variable_value.format("value2"), timeout=assertTextTimeout)
        elif type == "Bool":
            self.sb.assert_text("true", timeout=assertTextTimeout)
        self.sb.attach_allure_screenshot("Assert input variable in page screenshot")


    #  init env: should stay in page/widget of insights
    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=5)
    def share_widget(self, app_name="", widget_name=""):
        loginUtil = LoginUtils(self.sb)
        if loginUtil.is_oncloud():
            #  todo skip templately, wait add db user in TG cloud
            LOGGER.info("share widget on TG cloud")

        else:
            LOGGER.info("share widget on-prem")
            self.click_home_button_on_page()
            self.generate_access_token_in_default_app(app_name)
            self.open_application_with_name(app_name)
            self.click_widget_configuration_with_name(widget_name)
            self.wait_and_click(InsightLocators.config_share)
            self.sb.attach_allure_screenshot("give the share page info")
            self.assert_text_exist("Share Widget")
            URL_text= self.sb.find_element(InsightLocators.widget_share_URL).get_attribute('value')
            LOGGER.info("shared link: " + URL_text)
            self.wait_and_click(InsightLocators.widget_share_copy_button)
            self.assert_widget_result(self.copy_share_widget_confirm)
            self.wait_and_click(InsightLocators.close_share_widget)
            # assert share link can be opened successfully
            self.sb.open_new_window()
            self.sb.open(URL_text)
            # self.sb.switch_to_newest_window()

            self.sb.wait_for_element_visible(InsightLocators.assert_shared_link_elements, timeout=wait_render_timeout)
            self.sb.attach_allure_screenshot("open shared widget done, see the elements")
            try:
                elements = self.sb.find_elements(InsightLocators.assert_shared_link_elements)
                LOGGER.info("shared widget find len(elements): " + str(len(elements)))
                if len(elements) == 0:
                    self.sb.attach_allure_screenshot("open shared widget failed")
                    raise Exception("open shared widget failed")
            except Exception as e:
                raise Exception(str(e))
            finally:
                # self.sb.go_back() # didn't work because it will be u_localadmin user logining status
                self.sb.switch_to_default_window()



    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=5)
    def duplicate_widget(self, name, pagename='Current Page'):
        try:
            self.click_widget_configuration_with_name(name)
            loginUtil = LoginUtils(self.sb)
            if loginUtil.is_oncloud():
                LOGGER.info("login on TG cloud")
                self.sb.hover_on_element(InsightLocators.config_duplicate_compatible_widget)
                self.sb.sleep(1)
                pagename_element = "//li[text()='{}']".format(pagename)
                if self.sb.is_element_present(pagename_element):
                    LOGGER.info("Click Current Page")
                    self.wait_and_click(pagename_element)
                else:
                    self.wait_and_click(InsightLocators.config_duplicate_compatible_widget)
            else:
                LOGGER.info("login on-prem")
                self.sb.hover_on_element(InsightLocators.config_duplicate_compatible_widget)
                self.sb.sleep(1)
                pagename_element="//li[text()='{}']".format(pagename)
                if self.sb.is_element_present(pagename_element):
                    LOGGER.info("Click Current Page")
                    self.wait_and_click(pagename_element)
                else:
                    self.wait_and_click(InsightLocators.config_duplicate_compatible_widget)
            self.sb.attach_allure_screenshot("duplicate the widget")
            self.assert_text_exist("[duplicate] " + name)
        except Exception as e:
            # self.sb.refresh() # refresh will not work, and will update to u_localadmin logining status, can't duplicate or delete the widget
            # LOGGER.info("refresh page")
            # self.sb.sleep(3)
            self.sb.attach_allure_screenshot("duplicate_widget exception")
            raise Exception(str(e))

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=5)
    def delete_widget(self, name):
        self.click_widget_configuration_with_name(name)
        self.wait_and_click(InsightLocators.config_delete)
        self.wait_and_click(InsightLocators.ok_button)
        self.assert_text_not_exist(name)
        self.sb.attach_allure_screenshot("delete widget result")
        self.assert_widget_result(self.widget_delete_confirm)


    def wait_progress_bar(self, wait_present_time=30, wait_not_visible_time=30):
        try:
            self.sb.wait_for_element_present(InsightLocators.circle_progressbar, timeout=wait_present_time)
            LOGGER.info("wait_circle_progress_bar present PASS")
            self.sb.sleep(1)
            self.sb.wait_for_element_not_visible(InsightLocators.circle_progressbar, timeout=wait_not_visible_time)
            LOGGER.info("wait_circle_progress_bar not_visible PASS")
        except Exception as e:
            LOGGER.info("wait_progress_bar exception: " + str(e))
            self.sb.attach_allure_screenshot("wait_progress_bar exception screenshot")

    def wait_and_click(self, css, timeout=click_time_out):
        LOGGER.info("wait and click: " + css)
        self.sb.wait_for_element_clickable(css, timeout=timeout)
        self.sb.click(css, timeout=timeout)

    def assert_shared_app_permission(self, widget_name, permission="Editor"):
        LOGGER.info("assert_shared_app_permission: " + permission)
        self.click_widget_configuration_with_name(widget_name)
        if permission == "Editor":
            LOGGER.info("assert_shared_app_permission: editor")
            self.sb.assert_text("Delete", timeout=click_time_out)
            self.sb.attach_allure_screenshot("assert_shared_app_permission editor pass")
        elif permission == "Viewer":
            LOGGER.info("assert_shared_app_permission: viewer ")
            if self.sb.is_element_present(InsightLocators.config_delete):
                self.sb.attach_allure_screenshot("assert_shared_app_permission viewer fail")
                raise Exception("Viewer permission wrong, can delete widget")
            else:
                self.sb.attach_allure_screenshot("assert_shared_app_permission viewer pass")

    def delete_granted_user(self, user_name):
        LOGGER.info("delete_granted_user: " + user_name)
        self.wait_progress_bar(wait_present_time=10)
        try:
            if self.sb.is_element_present(InsightLocators.contain_span_text.format(user_name)):
                LOGGER.info("user existed, begin to delete " + user_name)
                elements = self.sb.find_elements(InsightLocators.set_role)
                LOGGER.info("all elements are " + str(len(elements)))
                if self.sb.is_element_present(InsightLocators.cluster_user_permission):
                    for i in range(len(elements)):
                        LOGGER.info("now element is " + str(i))
                        if i == 0 or i == (len(elements)-1) or i == (len(elements)-2):
                            LOGGER.info("skip " + str(i))
                            continue
                        LOGGER.info("begin to delete user " + str(i))
                        self.sb.find_elements(InsightLocators.set_role)[1].click()
                        self.wait_and_click(InsightLocators.remove_access)
                        self.sb.attach_allure_screenshot("delete user done")
                else:
                    for i in range(len(elements)):
                        LOGGER.info("now element is " + str(i))
                        if i == 0 or i == (len(elements)-1):
                            LOGGER.info("skip " + str(i))
                            continue
                        LOGGER.info("begin to delete user " + str(i))
                        self.sb.find_elements(InsightLocators.set_role)[1].click()
                        self.wait_and_click(InsightLocators.remove_access)
                        self.sb.attach_allure_screenshot("delete user done")
            else:
                LOGGER.info(user_name +" not added, skip delete user")
        except Exception as e:
            LOGGER.info("delete_granted_user exception: " + str(e))

    def close_cluster_user(self):
        try:
            LOGGER.info("init to close cluster users permission")
            # init cluster users permission
            if self.sb.is_element_present(InsightLocators.cluster_user_permission):
                LOGGER.info("cluster users is selected, restore")
                self.wait_and_click(InsightLocators.cluster_user_permission)
                self.wait_and_click(InsightLocators.cluster_user_restricted_permission)
            else:
                LOGGER.info("cluster users is not selected ")
            self.sb.attach_allure_screenshot("init to close cluster users permission done")

            # double check closed
            if self.sb.is_element_present(InsightLocators.cluster_user_permission):
                LOGGER.info("cluster users is selected, restore again")
                self.wait_and_click(InsightLocators.cluster_user_permission)
                self.wait_and_click(InsightLocators.cluster_user_restricted_permission)
        except Exception as e:
            LOGGER.info("init cluster users permission: " + str(e))

    def wait_circle_progress_bar(self, wait_present_time=30, wait_not_visible_time=30):
        try:
            self.sb.wait_for_element_present(InsightLocators.circle_progressbar, timeout=wait_present_time)
            LOGGER.info("wait_progress_bar present PASS")
            self.sb.sleep(1)
            self.sb.wait_for_element_not_visible(InsightLocators.circle_progressbar, timeout=wait_not_visible_time)
            LOGGER.info("wait_progress_bar not visible PASS")
        except Exception as e:
            LOGGER.info("wait_progress_bar exception: " + str(e))
            self.sb.attach_allure_screenshot("wait_circle_progress_bar exception screenshot")

    def wait_app_panel_circle_progress_bar(self, wait_present_time=short_wait_time_out, wait_not_visible_time=click_time_out):
        try:
            self.sb.wait_for_element_present(InsightLocators.click_submit_button_progressbar, timeout=wait_present_time)
            LOGGER.info("wait_click_submit_button_progressbar present PASS")
            self.sb.wait_for_element_not_present(InsightLocators.click_submit_button_progressbar, timeout=wait_not_visible_time)
            LOGGER.info("wait_click_submit_button_progressbar not visible PASS")
        except Exception as e:
            LOGGER.info("wait_click_submit_button_progressbar exception: " + str(e))
            self.sb.attach_allure_screenshot("wait_click_submit_button_progressbar exception screenshot")


    def wait_widget_finish_render(self, wait_present_time=short_wait_time_out, wait_not_visible_time=click_time_out):
        try:
            self.sb.wait_for_element_present(InsightLocators.widget_loading_div, timeout=wait_present_time)
            LOGGER.info("wait_widget_finish_render widget_loading_div present PASS")
            self.sb.wait_for_element_not_present(InsightLocators.widget_loading_div, timeout=wait_not_visible_time)
            LOGGER.info("wait_widget_finish_render widget_loading_div not visible PASS")
        except Exception as e:
            LOGGER.info("wait_widget_finish_render widget_loading_div exception: " + str(e))
            self.sb.attach_allure_screenshot("wait_widget_finish_render widget_loading_div exception screenshot")

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=5)
    def open_cluster_user(self):
        LOGGER.info("init to open cluster viewer permission")
        # init cluster users permission
        if self.sb.is_element_present(InsightLocators.cluster_user_restricted_permission):
            LOGGER.info("cluster users permission is not selected ")
            self.wait_and_click(InsightLocators.cluster_user_restricted_permission)
            self.wait_and_click(InsightLocators.cluster_user_permission)
        else:
            LOGGER.info("cluster users is selected ")
        self.sb.attach_allure_screenshot("init to open cluster users permission done")
        #  double check the cluster view permison
        if self.sb.is_element_present(InsightLocators.cluster_user_restricted_permission):
            LOGGER.info("cluster users permission is not selected ")
            raise Exception("didn't open cluster viewer permission")


    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=5)
    def share_application_with_name(self, appname, username, permission_role="Editor", cluster_user=False, cluster_user_permission="Viewer"):
        self.click_application_configuration_with_name_and_click_share(appname)

        self.wait_circle_progress_bar()
        if cluster_user:
            self.open_cluster_user()
            LOGGER.info("set cluster user permission")
            # set permission
            elements = self.sb.find_elements(InsightLocators.set_role)
            self.sb.find_elements(InsightLocators.set_role)[len(elements) - 1].click()
            self.sb.attach_allure_screenshot("click last dropdown before set ")
            if cluster_user_permission == "Editor":
                elements = self.sb.find_elements(InsightLocators.user_editor_role)
                self.sb.find_elements(InsightLocators.user_editor_role)[len(elements) - 1].click()
            elif cluster_user_permission == "Viewer":
                elements = self.sb.find_elements(InsightLocators.user_viewer_role)
                self.sb.find_elements(InsightLocators.user_viewer_role)[len(elements) - 1].click()
            self.sb.attach_allure_screenshot("set cluster_user_permission done")
            #  save change
            if self.sb.is_element_present(InsightLocators.save_role_button):
                LOGGER.info("click save button")
                self.wait_and_click(InsightLocators.save_role_button)
            else:
                LOGGER.info("click done button")
                self.wait_and_click(InsightLocators.done_button)
        else:
            # before add, init env
            self.close_cluster_user()
            self.delete_granted_user(user_name=username)
            self.sb.wait_for_element(InsightLocators.add_user_input, timeout=click_time_out)
            if not self.sb.is_element_present(InsightLocators.contain_span_text.format(username)):
                self.sb.type(InsightLocators.add_user_input, username, timeout=click_time_out)
                if self.sb.is_element_present(InsightLocators.no_result):
                    LOGGER.info("can't find user {0}".format(username))
                    self.sb.attach_allure_screenshot("can't find user:" + username)
                    raise Exception("can't find user:" + username)
                self.sb.sleep(1)
                self.wait_and_click(InsightLocators.find_user)
                self.sb.find_elements(InsightLocators.set_role)[0].click()
                self.sb.sleep(3)
                if permission_role == "Editor":
                    self.wait_and_click(InsightLocators.user_editor_role)
                elif permission_role == "Viewer":
                    self.wait_and_click(InsightLocators.user_viewer_role)
                self.sb.sleep(2)
                self.wait_and_click(InsightLocators.add_role_button)
                self.sb.sleep(2)
                self.sb.attach_allure_screenshot("add user:" + username)
                if self.sb.is_element_present(InsightLocators.save_role_button):
                    self.wait_and_click(InsightLocators.save_role_button)
                    self.sb.sleep(1)
                    self.sb.assert_text(self.share_app_auto_save, timeout=assertTextTimeout)
                else:
                    self.wait_and_click(InsightLocators.done_button)
            else:
                LOGGER.info("click done button")
                self.wait_and_click(InsightLocators.done_button)











