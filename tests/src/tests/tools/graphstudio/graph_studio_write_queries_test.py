import time
import pytest
import allure
import os

from pages.tools.graphstudio import GSTHomePage
from base.tools_basecase import ToolsBaseCase
from actions.tools.graph_studio.write_queries_action import WriteQueriesAction
from parameterized import parameterized
from utils.data_util.data_resolver import read_test_data


class TestGST_WriteQueries(ToolsBaseCase):

    read_data = read_test_data(file="tools_test_data.json")
    login_role_test_data = read_data.get("login_onprem")
    test_data = []
    test_data_without_local_role = []
    for i in login_role_test_data:
        # disable the global_designer,local_UDF_role,global_UDF_role , should be in Milestone#4 for gle update the rbac.
        if i.get("type") == "default_user" or i.get("type") == "local_admin" or i.get("type") == "local_UDF_role":
            if i.get("type") != "local_UDF_role":
                test_data_without_local_role.append((i.get("type")))
            test_data.append((i.get("type") ))

    @allure.title("discard draft  - smoke test")
    @allure.description("discard draft in GST")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.accessGST
    @pytest.mark.query
    @pytest.mark.run(order=6009)
    def test_discard_draft_GST(self):
        super().setUp()
        # self.execute_script("window.localStorage.clear();")
        gst = GSTHomePage(self)
        gst.GST_login()
        gst.discardDraft()



    @allure.title("delete query and draft  - smoke test")
    @allure.description("delete query and draft in GST")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.accessGST
    @pytest.mark.query
    @pytest.mark.run(order=1034)
    def test_delete_query_and_draft_GST(self):
        super().setUp()
        # self.execute_script("window.localStorage.clear();")
        gst = GSTHomePage(self)
        gst.GST_login()
        gst.deleteQueryAndDraft()


    @allure.title("download current query  - smoke test")
    @allure.description("download current query in GST")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.accessGST
    @pytest.mark.query
    @pytest.mark.run(order=6002)
    def test_download_current_query_GST(self):
        super().setUp()
        # self.execute_script("window.localStorage.clear();")
        gst = GSTHomePage(self)
        gst.GST_login()
        gst.downloadCurrentQuery()


    @allure.title("check interpreted query profiling, need DB version >=4.2.0")
    @allure.description(
        "TestType: positive \n" 
        "Target: Check run interpreted query profile result \n"
        "Description: Run interpreted query profile result \n"
        "TestDesigner: Song Sun \n"
        "Date: 2025-02-17 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2510"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.UDFrole
    @pytest.mark.query_profile
    @pytest.mark.run(order=27)
    def test_run_interpreted_query_for_profile(self):
        super().setUp()
        gst = GSTHomePage(self)
        gst.GST_login()
        gst.create_new_query_with_name(query_name="interpreted_query_for_profile")
        gst.assert_query_result()

    @allure.title("check installed query profiling, need DB version >=4.2.0")
    @allure.description(
        "TestType: positive \n" 
        "Target: Check run installed query profile result \n"
        "Description: Run installed query profile result \n"
        "TestDesigner: Song Sun \n"
        "Date: 2025-02-17 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2510"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.UDFrole
    @pytest.mark.query_profile
    @pytest.mark.run(order=28)
    def test_run_installed_query_for_profile(self):
        super().setUp()
        gst = GSTHomePage(self)
        gst.GST_login()
        gst.create_new_query_with_name(query_name="installed_query_for_profile", need_installed=True)
        gst.assert_query_result(install_status=True)

    @allure.title("check disable query profile, need DB version >=4.2.0")
    @allure.description(
        "TestType: positive \n" 
        "Target: Check disable query profile result \n"
        "Description: Check disable query profile result \n"
        "TestDesigner: Song Sun \n"
        "Date: 2025-02-17 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2510"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.UDFrole
    @pytest.mark.query_profile
    @pytest.mark.run(order=128)
    def test_disable_query_profile(self):
        super().setUp()
        gst = GSTHomePage(self)
        gst.GST_login()
        gst.create_new_query_with_name(query_name="disable_query_profile")
        gst.assert_disable_profile()



    @allure.title("download all queries  - smoke test")
    @allure.description("download  all queries in GST")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.accessGST
    @pytest.mark.query
    @pytest.mark.run(order=6034)  # mix into insights
    def test_download_all_queries_GST(self):
        super().setUp()
        # self.execute_script("window.localStorage.clear();")
        gst = GSTHomePage(self)
        gst.GST_login()
        gst.downloadAllQueries()

    @allure.title("install all queries  - smoke test")
    @allure.description("install all queries in GST")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.accessGST
    @pytest.mark.query_install
    @pytest.mark.basecases
    @pytest.mark.run(order=15.03)  # seperate with GAP wlm case
    def test_install_all_queries_GST(self):
        super().setUp()
        writeQueriesAction = WriteQueriesAction(self)
        gst = GSTHomePage(self)
        gst.GST_login()
        writeQueriesAction.install_all_queries()

    @allure.title("run all queries  - smoke test")
    @allure.description("run all queries in GST")
    @pytest.mark.high
    @pytest.mark.tools
    @pytest.mark.accessGST
    @pytest.mark.query_run
    @pytest.mark.release
    @pytest.mark.run(order=12)  # run in the front, it took long times about 8 mins
    def test_run_all_queries_GST(self):
        starterkitsConfigFile = "./config/starter_kit/starter_kits.json"
        solution = "covid-19"
        super().setUp()
        writeQueriesAction = WriteQueriesAction(self)
        gst = GSTHomePage(self)
        gst.GST_login()
        writeQueriesAction.run_query_with_parameter_file(starterkitsConfigFile, solution)

    """
        disable the case because dev engineer refactor the OpenCypher,
        will enable and update the cases after OpenCypher mit
    """
    # @allure.title("Run opencypher queries  - smoke test")
    # @allure.description(
    #     "TestType: Positive"
    #     "Target: Check the opencyper query can be created/run in GST"
    #     "Description: Write opencyper query and Run queries in interpret mode"
    #     "TestDesigner: Jing Yang"
    #     "Date: 2023-08-29"
    #     "Link: https://graphsql.atlassian.net/browse/QA-5454")
    # @pytest.mark.high
    # @pytest.mark.smoke
    # @pytest.mark.tools
    # @pytest.mark.query_run
    # @pytest.mark.opencypher
    # @pytest.mark.run(order=12)
    # def test_run_opencypher_queries_interpret_mode(self):
    #     super().setUp()
    #     # self.execute_script("window.localStorage.clear();")
    #     gst = GSTHomePage(self)
    #     gst.GST_login()
    #     if gst.is_cluster_available(393):
    #         gst.run_OpenCypher_query_interpret_mode()

    # @allure.title("Run opencypher querie  - smoke test")
    # @allure.description(
    #     "TestType: Positive"
    #     "Target: Check the opencyper query can be created/installed/run in GST"
    #     "Description: Write opencyper query and Run queries in installed mode"
    #     "TestDesigner: Jing Yang"
    #     "Date: 2023-08-29"
    #     "Link: https://graphsql.atlassian.net/browse/QA-5454")
    # @pytest.mark.high
    # @pytest.mark.smoke
    # @pytest.mark.tools
    # @pytest.mark.query_run
    # @pytest.mark.opencypher
    # @pytest.mark.run(order=1200)
    # def test_run_opencypher_queries_installed_mode(self):
    #     super().setUp()
    #     # self.execute_script("window.localStorage.clear();")
    #     gst = GSTHomePage(self)
    #     gst.GST_login()
    #     if gst.is_cluster_available(393):
    #         gst.run_OpenCypher_query_interpret_mode()

    @allure.title("run query with related privileges under different UDF role")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check the query can be retrive the data with related privileges under different UDF role \n"
        "Description: Run query with related privileges under different UDF role \n"
        "TestDesigner: Yuling Liu \n"
        "Date: 2023-08-29 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2510"
    )
    @parameterized.expand(test_data, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.onprem
    @pytest.mark.UDFrole
    @pytest.mark.query_run
    @pytest.mark.run(order=21)
    def test_run_query_with_differentrole_success(self, type):
        super().setUp()
        writeQueriesAction = WriteQueriesAction(self)
        gst = GSTHomePage(self)
        gst.GST_login(login_way=type)
        writeQueriesAction.run_query_with_name(query_name="with_read_data_query", assert_text="\"infection_case\": \"overseas inflow\"")

    @allure.title("run query with related privileges for some attributes under different role")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check the query can be retrive the data with related privileges for some attributes under different role \n"
        "Description: Run query with related privileges for some attributes under different role \n"
        "TestDesigner: Yuling Liu \n"
        "Date: 2023-09-01 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2510"
    )
    @parameterized.expand(test_data_without_local_role, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.onprem
    @pytest.mark.UDFrole
    @pytest.mark.query_run
    @pytest.mark.run(order=22)
    def test_run_query_for_some_attributes_with_differentrole_success(self, type):
        super().setUp()
        writeQueriesAction = WriteQueriesAction(self)
        gst = GSTHomePage(self)
        gst.GST_login(login_way=type)
        writeQueriesAction.run_query_with_name(query_name="without_read_data_query", expect_res="does not have permission to run the request")

    @allure.title("run query with related privileges for whole vertex under different role")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check the query can be retrive the data with related privileges for whole vertex under different role \n"
        "Description: Run query with related privileges for whole vertex under different role \n"
        "TestDesigner: Yuling Liu \n"
        "Date: 2023-09-01 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2510"
    )
    @parameterized.expand(test_data_without_local_role, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.onprem
    @pytest.mark.UDFrole
    @pytest.mark.query_run
    @pytest.mark.run(order=23)
    def test_run_query_for_whole_vertex_with_differentrole_success(self, type):
        super().setUp()
        writeQueriesAction = WriteQueriesAction(self)
        gst = GSTHomePage(self)
        gst.GST_login(login_way=type)
        writeQueriesAction.run_query_with_name(query_name="read_whole_vertex_data_query", expect_res="does not have permission to run the request")

    @allure.title("run query with related privileges under global UDF role")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check the query can be retrive the data with related privileges under global UDF role \n"
        "Description: Run query with related privileges under global UDF role \n"
        "TestDesigner: Yuling Liu \n"
        "Date: 2023-08-29 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2510"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.onprem
    @pytest.mark.UDFrole
    @pytest.mark.query_run
    @pytest.mark.run(order=24)
    def test_run_query_with_globalrole_success(self):
        super().setUp()
        writeQueriesAction = WriteQueriesAction(self)
        gst = GSTHomePage(self)
        gst.GST_login(login_way="global_UDF_role")
        # todo skip assert the run query result since APPS-3657
        # writeQueriesAction.run_query_with_name(query_name="with_read_data_query", expect_res="does not OWN or have EXECUTE_QUERY privilege on queries")
        writeQueriesAction.run_query_with_name(query_name="with_read_data_query", assert_text="", is_globalUDF_role=True)

    @allure.title("run query without related privileges for some attributes under local UDF role")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check the query can be retrive the data with related privileges for some attributes under local UDF role \n"
        "Description: Run query with related privileges for some attributes under local UDF role \n"
        "TestDesigner: Yuling Liu \n"
        "Date: 2023-09-01 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2510"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.onprem
    @pytest.mark.UDFrole
    @pytest.mark.query_run
    @pytest.mark.run(order=25)
    def test_run_query_for_some_attributes_with_localrole_failed(self):
        super().setUp()
        writeQueriesAction = WriteQueriesAction(self)
        gst = GSTHomePage(self)
        gst.GST_login(login_way="local_UDF_role")
        writeQueriesAction.run_query_with_name(query_name="without_read_data_query", expect_res="does not have permission to run the request")

    @allure.title("run query with related privileges for whole vertex under local UDF role")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check the query can be retrive the data with related privileges for whole vertex under local UDF role \n"
        "Description: Run query with related privileges for whole vertex under local UDF role \n"
        "TestDesigner: Yuling Liu \n"
        "Date: 2023-09-01 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2510"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.onprem
    @pytest.mark.UDFrole
    @pytest.mark.query_run
    @pytest.mark.run(order=26)
    def test_run_query_for_whole_vertex_with_localrole_failed(self):
        super().setUp()
        writeQueriesAction = WriteQueriesAction(self)
        gst = GSTHomePage(self)
        gst.GST_login(login_way="local_UDF_role")
        writeQueriesAction.run_query_with_name(query_name="read_whole_vertex_data_query", expect_res="does not have permission to run the request")

    @allure.title("run installed query with list parameter")
    @allure.description(
        "TestType: positive \n" 
        "Target: Check run installed query with list parameter \n"
        "Description: Run installed query with list parameter \n"
        "TestDesigner: Song Sun \n"
        "Date: 2025-01-21 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2510"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.UDFrole
    @pytest.mark.basecases  # separate with insights cases
    @pytest.mark.query_list_map
    @pytest.mark.run(order=27)
    def test_run_installed_query_for_list_parameter(self):
        super().setUp()
        writeQueriesAction = WriteQueriesAction(self)
        gst = GSTHomePage(self)
        gst.GST_login()
        writeQueriesAction.run_query_with_name(query_name="run_installed_query_with_list", assert_text="1234")

    @allure.title("run interpreted query with list parameter")
    @allure.description(
        "TestType: positive \n" 
        "Target: Check run interpreted query with list parameter \n"
        "Description: Run interpreted query with list parameter \n"
        "TestDesigner: Song Sun \n"
        "Date: 2025-01-21 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2510"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.basecases  # separate with insights cases
    @pytest.mark.UDFrole
    @pytest.mark.query_list_map
    @pytest.mark.run(order=26)
    def test_run_interpreted_query_for_list_parameter(self):
        super().setUp()
        writeQueriesAction = WriteQueriesAction(self)
        gst = GSTHomePage(self)
        gst.GST_login()
        writeQueriesAction.run_query_with_name(query_name="run_interpreted_query_with_list", assert_text="1234")

    @allure.title("run installed query with map parameter")
    @allure.description(
        "TestType: positive \n" 
        "Target: Check run query with map parameter \n"
        "Description: Run query with map parameter \n"
        "TestDesigner: Song Sun \n"
        "Date: 2025-01-21 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2510"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.UDFrole
    @pytest.mark.basecases  # separate with insights cases
    @pytest.mark.query_list_map
    @pytest.mark.run(order=26)
    def test_run_installed_query_for_map_parameter(self):
        super().setUp()
        writeQueriesAction = WriteQueriesAction(self)
        gst = GSTHomePage(self)
        gst.GST_login()
        writeQueriesAction.run_query_with_name(query_name="run_installed_query_with_map", assert_text="5678")


    @allure.title("run query with limit parameter")
    @allure.description(
        "TestType: positive \n" 
        "Target: Check run query with limit parameter \n"
        "Description: run query with limit parameter \n"
        "TestDesigner: Song Sun \n"
        "Date: 2025-04-21 \n"
        "Link: https://graphsql.atlassian.net/browse/GLE-6742, fixed in >=3.10.2 or 4.1.0"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.UDFrole
    @pytest.mark.basecases
    @pytest.mark.query_list_map
    @pytest.mark.run(order=36)
    def test_run_installed_query_for_limit_parameter(self):
        super().setUp()
        writeQueriesAction = WriteQueriesAction(self)
        gst = GSTHomePage(self)
        gst.GST_login()
        writeQueriesAction.run_query_with_name(query_name="run_installed_query_with_limit_parameter_tg393", graphName="mysql_yuling", assert_text="sql3")


    @allure.title("install all queries with partial success")
    @allure.description(
        "TestType: positive \n" 
        "Target: Check install all queries with partial success \n"
        "Description: install all queries with partial success \n"
        "TestDesigner: Song Sun \n"
        "Date: 2025-04-21 \n"
        "Link: https://graphsql.atlassian.net/browse/APPS-3352, fix in >= 4.1.3"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.UDFrole
    @pytest.mark.basecases
    @pytest.mark.query_list_map
    @pytest.mark.run(order=36)
    def test_install_all_with_partial_success(self):
        super().setUp()
        writeQueriesAction = WriteQueriesAction(self)
        gst = GSTHomePage(self)
        gst.GST_login()
        writeQueriesAction.install_all_queries_with_partial_success()

    @allure.title("quick save and install query test")
    @allure.description(
        "TestType: positive \n" 
        "Target: Check quick save and install query functionality \n"
        "Description: Test quick save query with syntax error and corrected version, then install and run \n"
        "TestDesigner: Yaxin Li \n"
        "Date: 2025-05-16 \n"
        "Link: https://graphsql.atlassian.net/browse/"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.UDFrole
    @pytest.mark.query_list_map
    @pytest.mark.run(order=28)
    def test_quick_save_and_install_query(self):
        super().setUp()
        gst = GSTHomePage(self)
        gst.GST_login()
        gst.quick_save_and_install_query()

    @allure.title("Verify save button disabled after click")
    @allure.description(
        "TestType: positive \n" 
        "Target: Test save button should be disabled after clicking \n"
        "Description: Create a new query, save it and verify the save button is disabled \n"
        "TestDesigner: Yaxin Li \n"
        "Date: 2025-05-26 \n"
        "Link: https://graphsql.atlassian.net/browse/"
    )
    @pytest.mark.high
    @pytest.mark.release
    @pytest.mark.tools
    @pytest.mark.UDFrole
    @pytest.mark.query_list_map
    @pytest.mark.run(order=30)
    def test_save_button_disabled_after_click(self):
        super().setUp()
        gst = GSTHomePage(self)
        gst.GST_login()
        gst.verify_save_button_disabled()
