import pytest
import allure

from pages.tools.insights.insights_application_page import InsightsAppPage
from base.tools_basecase import ToolsBaseCase
from selenium.webdriver.common.by import By
from locators.tools.insights_locators import InsightLocators
from utils.data_util.data_resolver import read_test_data


class TestInsightsTools(ToolsBaseCase):
    
    application_name = "insights_e2e_test"
    application_operation_name = "insights_e2e_test_operation"
    application_updatename = "insights_e2e_test_update"

    @allure.title("Insights Create Application - smoke test")
    @allure.description("Check the application can be created")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.insights
    # @pytest.mark.basecases
    @pytest.mark.run(order=1)
    def test_insight_app_create(self):
        super().setUp()
        apppage = InsightsAppPage(self)
        apppage.insight_login()
        apppage.create_application(self.application_name)
        self.click(InsightLocators.homepage)
        self.attach_allure_screenshot("go to application homepage")

    @allure.title("Insights Update Application - smoke test")
    @allure.description("Check operation(edit/delete/duplicate) of the application")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.insights
    @pytest.mark.run(order=6002)
    def test_insight_app_operation(self):
        apppage = InsightsAppPage(self)
        apppage.insight_login()
        apppage.create_application(self.application_operation_name)
        self.click(InsightLocators.homepage)
        # update the application name
        apppage.change_application_name(
            self.application_operation_name, self.application_updatename)
        self.sleep(8)

        # duplicate the application
        apppage.duplicate_application(self.application_updatename)
        self.sleep(5)

        # search application
        apppage.search_application(self.application_updatename)

        # delete the application
        apppage.delete_application(self.application_updatename)


    @allure.title("Insights edit widget auto save - smoke test")
    @allure.description("Insights edit widget auto save ")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.insights
    @pytest.mark.songInsights
    @pytest.mark.run(order=6015)
    def test_insight_widget_auto_save(self):
        super().setUp()
        apppage = InsightsAppPage(self)
        apppage.insight_login()
        apppage.create_widget_auto_save(app_name="TG widget auto save")


    @allure.title("Insights Create Application and share to u_globalUDF - smoke test")
    @allure.description("Check the application can be shared and edited")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.insights
    @pytest.mark.onprem
    @pytest.mark.basecases
    @pytest.mark.songInsights
    @pytest.mark.run(order=6010)
    def test_insight_app_share(self):
        super().setUp()
        apppage = InsightsAppPage(self)
        apppage.insight_login()
        apppage.create_and_share_default_application_with_graph(app_name="TG share mygraph application")


    @allure.title("Insights application compatible for V380 - smoke test")
    @allure.description("Check import V380 application.json file")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.insights
    @pytest.mark.songInsights
    @pytest.mark.run(order=3020)
    def test_insight_import_380_application(self):
        super().setUp()
        apppage = InsightsAppPage(self)
        apppage.insight_login()
        apppage.import_380_appliction(app_name="Application_380")

    @allure.title("Import input widgets regression test app - smoke test")
    @allure.description(
        "TestType: positive \n"
        "Target: Import input widgets regression test app \n"
        "Description: Import input widgets regression test app \n"
        "TestDesigner: Song Sun \n"
        "Date: 2025-04-14 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2510"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.insights
    @pytest.mark.songInsights
    @pytest.mark.run(order=5021)
    def test_insight_import_input_widget_regression_test_application(self):
        super().setUp()
        apppage = InsightsAppPage(self)
        apppage.insight_login()
        apppage.import_appliction_from_home_page(app_name="input_variables_regression_test_set")